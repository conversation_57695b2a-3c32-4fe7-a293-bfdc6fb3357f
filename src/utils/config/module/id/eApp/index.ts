import { EAppConfig } from 'types/moduleConfig';

const eAppConfig: EAppConfig = {
  debugPolicyNo: false,
  debugUWMe: false,
  ocrInfoIcon: false,
  shouldCloseHQBeforeDecision: false,
  hasSignaturePlaceholder: false,
  disableFormAfterDecision: false,
  maintainSignaturePlaceOfSigning: true,
  documentUpload: {
    selectFromLibrary: false,
    attachFile: false,
  },
  shouldTrackGeoLocation: true,
  uwmeDismissible: true,
  checkSubmissionReadiness: true,
  verifyReplacementInfoInHealthQuestion: false,
};

export default eAppConfig;
