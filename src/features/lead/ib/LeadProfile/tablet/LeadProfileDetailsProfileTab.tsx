import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { RouteProp, useRoute } from '@react-navigation/native';
import {
  Box,
  Button,
  Column,
  H6,
  H7,
  Icon,
  LargeBody,
  Row,
  SmallBody,
  Typography,
} from 'cube-ui-components';
import { Close } from 'cube-ui-components/dist/cjs/icons';
import { Card } from 'features/lead/components/LeadProfile/Card';
import useClearCaseFocusEffect from 'features/lead/hooks/useClearCaseFocusEffect';
import { useGetCustomerDetailsByCustomerId } from 'hooks/useGetCustomerDetails';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Pressable,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { RootStackParamList, TransactionsInLead } from 'types';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { useMappedLeadProfile } from 'features/lead/ib/LeadProfile/tablet/config';
import CubeFonts from 'cube-ui-components/dist/cjs/fonts';
import { country } from 'utils/context';
import LeadProfileModal, {
  LeadProfileModalProps,
  ModalBody,
  ModalMainContainer,
} from './LeadProfileModal';

type ModalControlProps = {
  isModalVisible: boolean;
  onPressClose: () => void;
};

export default function LeadProfileDetailsProfileTab() {
  const route = useRoute<RouteProp<RootStackParamList, 'LeadProfile'>>();
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['lead', 'leadProfile', 'proposal']);

  const { isLoading: isLoadingLeadData, data: lead } = useGetLeadByLeadId(
    route?.params?.id,
  );

  const { isIndividualLead } = route.params;

  const { data: customerDetails, isLoading: isLoadingCustomerDetails } =
    useGetCustomerDetailsByCustomerId(route?.params?.customerId);

  /*
    need to reset the active case when re-entering the lead profile
    to avoid using it to create a new proposal
  */
  useClearCaseFocusEffect();

  const {
    contactDetailData: roleContactDetailData,
    rolePersonalInfoData,
    roleProfileDetailData,
    referralDetailData,
    nationalityDetailData,
    occupationDetailData,
    addressInfoData,
  } = useMappedLeadProfile({
    lead,
    customerDetails,
    isIndividualLead,
    mode: 'normal',
  });

  const leadActivityList =
    lead?.transactions.filter(
      ({ action }) => action === 'contact' || action === 'appointment',
    ) ?? [];

  const profileDetailData = roleProfileDetailData ?? [];
  const personalInfoData = rolePersonalInfoData ?? [];
  const contactDetailData = roleContactDetailData ?? [];

  return (
    <>
      <MainContainer>
        {/* ------------ Header -------------- */}
        <Row
          gap={space[8]}
          justifyContent="space-between"
          alignItems="center"
          paddingTop={space[5]}
          paddingBottom={space[5]}>
          <Title fontWeight="bold">
            {t('leadProfile:leadProfile.profile')}
          </Title>
          <ViewAllButton
            Modal={(modalProps: ModalControlProps) => (
              <ProfileModal
                activityList={leadActivityList}
                personalInfoData={personalInfoData}
                contactDetailData={contactDetailData}
                referralDetailData={referralDetailData}
                nationalityDetailData={nationalityDetailData}
                occupationDetailData={occupationDetailData}
                addressInfoData={addressInfoData}
                {...modalProps}
              />
            )}
          />
        </Row>

        {/* ------------ Selected Details Box -------------- */}
        <ProfileDetailContainer>
          <Row flexWrap="wrap" flex={1}>
            {profileDetailData.map((item, index) => (
              <ProfileField
                dataLength={profileDetailData.length}
                columnNum={3}
                key={index}
                label={item.label}
                infoModel={item.infoModel}
                content={item.content ? item.content : '--'}
                idx={index}
              />
            ))}
          </Row>
        </ProfileDetailContainer>
      </MainContainer>
    </>
  );
}

function ViewAllButton({
  Modal,
}: {
  Modal: (props: ModalControlProps) => React.ReactNode;
}) {
  const [viewDetailModal, setViewDetailModal] = useState<boolean>(false);
  const { colors, typography } = useTheme();
  const { t } = useTranslation('leadProfile');
  const isCountryIdOrIb = ['id', 'ib'].includes(country);

  return (
    <>
      <Button
        text={
          isCountryIdOrIb
            ? t('leadProfile.leadInfoSection.viewMore')
            : t('leadProfile.leadInfoSection.viewDetails')
        }
        variant={isCountryIdOrIb ? 'text' : 'secondary'}
        size={isCountryIdOrIb ? undefined : 'medium'}
        textStyle={
          isCountryIdOrIb
            ? {
                fontSize: typography.largeLabel.size,
                fontFamily: CubeFonts.FWDCircularTT.Bold,
                color: colors.palette.fwdAlternativeOrange[100],
              }
            : { fontWeight: 'bold' }
        }
        onPress={() => setViewDetailModal(true)}
      />
      <Modal
        isModalVisible={viewDetailModal}
        onPressClose={() => setViewDetailModal(false)}
      />
    </>
  );
}

//-------------------- view details modal --------------------
const ProfileField = ({
  label,
  infoModel,
  content,
  columnNum,
  dataLength,
  idx,
}: {
  label?: string;
  infoModel?: React.ReactNode;
  content: string;
  columnNum: number;
  dataLength: number;
  idx: number;
}) => {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['lead', 'leadProfile', 'proposal']);
  const [isModalVisible, setIsModalVisible] = useState(false);

  return (
    <Column
      width={
        idx == dataLength - 1 && dataLength % columnNum != 0
          ? `${
              (100 / columnNum) *
              (round(dataLength, columnNum) - (dataLength - 1))
            }%`
          : `${100 / columnNum}%`
      }
      // height={
      //   dataLength % columnNum != 0
      //     ? `${(100 / round(dataLength, columnNum)) * columnNum}%`
      //     : `${(100 / dataLength) * columnNum}%`
      // }
      gap={sizes[1]}
      paddingBottom={space[4]}>
      <Row gap={space[1]}>
        <Typography.SmallLabel color={colors.placeholder}>
          {label}
        </Typography.SmallLabel>
        {infoModel && (
          <Pressable onPress={() => setIsModalVisible(true)}>
            <Icon.InfoCircle
              size={sizes[4]}
              fill={colors.palette.fwdAlternativeOrange[100]}
            />
          </Pressable>
        )}
      </Row>

      <Typography.H7 color={colors.palette.fwdDarkGreen[100]}>
        {content ? content : '--'}
      </Typography.H7>
      {/* see if mario can split that scoreInfoModal out later */}
      {infoModel &&
        React.cloneElement(infoModel, {
          isVisible: isModalVisible,
          onClose: () => setIsModalVisible(false),
        })}
    </Column>
  );
};

const ProfileModal = ({
  isModalVisible,
  personalInfoData,
  contactDetailData,
  referralDetailData,
  activityList,
  onPressClose,
}: LeadProfileModalProps) => {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['lead', 'leadProfile', 'proposal']);

  return (
    <Modal visible={isModalVisible} transparent={true} animationType="fade">
      <ModalMainContainer>
        <ModalBody>
          <Row justifyContent="flex-end">
            <TouchableOpacity onPress={onPressClose}>
              <Close size={sizes[6]} fill={colors.palette.fwdDarkGreen[100]} />
            </TouchableOpacity>
          </Row>
          <Column px={space[6]} marginBottom={space[4]}>
            <H6 fontWeight="bold">
              {t('leadProfile:leadProfile.profileDetails.profileDetails')}
            </H6>
          </Column>
          <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
            <Box px={space[6]} flex={1}>
              {/* // Profile Detail */}
              <Box style={{ flexBasis: 'auto' }}>
                <Column>
                  <Column paddingBottom={space[4]}>
                    <H7
                      fontWeight="bold"
                      color={colors.palette.fwdDarkGreen[50]}>
                      {t('leadProfile:leadProfile.profileDetails.personalInfo')}
                    </H7>
                  </Column>
                  <Row flexWrap="wrap">
                    {personalInfoData?.map((item, index) => (
                      <ProfileField
                        columnNum={4}
                        label={item.label}
                        infoModel={item?.infoModel}
                        key={'personal-' + index}
                        dataLength={personalInfoData?.length}
                        content={item.content ? item.content : ' '}
                        idx={index}
                      />
                    ))}
                  </Row>
                </Column>
              </Box>
              <Divider />
              {/* // Contact Detail */}

              <Column flex={1}>
                <Box flex={2}>
                  <Column flex={2}>
                    <Column paddingBottom={space[4]}>
                      <H7
                        fontWeight="bold"
                        color={colors.palette.fwdDarkGreen[50]}>
                        {t(
                          'leadProfile:leadProfile.profileDetails.contactDetails',
                        )}
                      </H7>
                    </Column>
                    <Row flexWrap="wrap">
                      {contactDetailData?.map((item, index) => (
                        <ProfileField
                          dataLength={contactDetailData?.length}
                          columnNum={4}
                          key={'contact-' + index}
                          label={item.label}
                          content={item.content ? item.content : '--'}
                          idx={index}
                        />
                      ))}
                    </Row>
                  </Column>
                </Box>
                <Divider />

                {/* // Referral Detail */}
                {(country === 'ib' || country === 'id') && (
                  <>
                    <Box flex={2}>
                      <Column flex={2}>
                        <Column paddingBottom={space[4]}>
                          <H7
                            fontWeight="bold"
                            color={colors.palette.fwdDarkGreen[50]}>
                            {country === 'id'
                              ? t(
                                  'leadProfile:leadProfile.profileDetails.sourceDetails',
                                )
                              : t(
                                  'leadProfile:leadProfile.profileDetails.referralDetails',
                                )}
                          </H7>
                        </Column>
                        <Row flexWrap="wrap">
                          {referralDetailData?.map((item, index) => (
                            <ProfileField
                              dataLength={referralDetailData?.length}
                              columnNum={4}
                              key={'referral-' + index}
                              label={item.label}
                              infoModel={item?.infoModel}
                              content={item.content ? item.content : '--'}
                              idx={index}
                            />
                          ))}
                        </Row>
                      </Column>
                    </Box>
                    <Divider />
                  </>
                )}

                {/* // Remark Detail */}
                <Column flex={1}>
                  <Column>
                    <H7
                      fontWeight="bold"
                      color={colors.palette.fwdDarkGreen[50]}>
                      {t('leadProfile:leadProfile.profileDetails.remark')}
                    </H7>
                  </Column>
                  <ScrollView>
                    <Column flex={1} gap={space[3]}>
                      {activityList
                        ? activityList.map((item, index) => (
                            <Column key={index} gap={space[1]}>
                              <LargeBody>
                                {t(
                                  `leadProfile:leadProfile.activityRecord.action.${item?.action}`,
                                )}
                                {' - '}
                                {item?.extra?.feedback
                                  ? t(
                                      `leadProfile:leadProfile.activityRecord.feedback.${item?.extra?.feedback}`,
                                    )
                                  : t(
                                      `leadProfile:leadProfile.activityRecord.action.${item?.action}`,
                                    )}
                                {item?.extra?.feedbackDetails &&
                                  ' - ' + item?.extra?.feedbackDetails}
                              </LargeBody>
                              <SmallBody>
                                {dateFormatUtil(item?.actionAt)}
                              </SmallBody>
                            </Column>
                          ))
                        : '--'}
                    </Column>
                  </ScrollView>
                </Column>
              </Column>
            </Box>
          </ScrollView>
        </ModalBody>
      </ModalMainContainer>
    </Modal>
  );
};

const MainContainer = styled(View)(({ theme: { colors } }) => ({
  flex: 1,
  backgroundColor: colors.palette.white,
}));

const Title = styled(H6)(() => ({}));

const ProfileDetailContainer = styled(Card)(
  ({ theme: { borderRadius, space } }) => ({
    width: '100%',
    padding: space[5],
    lineHeight: 1,
    borderRadius: borderRadius.large,
  }),
);

// const ModalBody = styled(View)(
//   ({ theme: { borderRadius, space, colors } }) => ({
//     backgroundColor: colors.palette.white,
//     minHeight: '60%',
//     maxHeight: '100%',
//     borderRadius: borderRadius.large,
//     paddingHorizontal: space[6],
//     paddingTop: space[6],
//     paddingBottom: space[12],
//   }),
// );

const Divider = styled(View)(({ theme: { colors, space } }) => ({
  marginBottom: space[4],
  height: 1,
  backgroundColor: colors.palette.fwdGrey[100],
}));

function formatPhoneNumber(phoneNumber: string) {
  return phoneNumber.slice(0, 3) + ' ' + phoneNumber.slice(3);
}

const round = (number: number, step: number) => {
  return Math.ceil(number / step) * step;
};
