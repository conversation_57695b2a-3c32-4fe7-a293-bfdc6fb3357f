import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

import styled from '@emotion/native';
import {
  LayoutChangeEvent,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
} from 'react-native';
import { useSharedValue } from 'react-native-reanimated';
import useLatest from 'hooks/useLatest';
import { AnimatedScrollDownPdfV2 } from 'features/pdfViewer/components/AnimatedScrollDownPdfV2';
import PdpContent from '../../assets/pdp';
import { Box, H7, H8, Icon, Row, Shadow } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { AddLeadConsentRef } from '../AddNewLeadForm';

const Container = styled.View(({ theme: { colors, borderRadius, space } }) => ({
  backgroundColor: colors.palette.fwdGrey[20],
  borderRadius: borderRadius.medium,
  flex: 1,
  overflow: 'hidden',
  borderWidth: 1,
  borderColor: colors.primary,
}));

const AddLeadConsent = forwardRef(
  (
    {
      onCompleteConsent,
    }: {
      onCompleteConsent: () => void;
    },
    ref,
  ) => {
    const [pageData, setPageData] = useState({
      currentPage: 1,
      totalPages: 100,
    });
    const [completed, setCompleted] = useState(false);
    const [error, setError] = useState(false);

    const { space, colors, borderRadius } = useTheme();

    const scrollViewRef = useRef<ScrollView>(null);

    const containerHeight = useSharedValue(0);
    const contentHeight = useSharedValue(0);
    const contentWidth = useSharedValue(0);
    const latestPageData = useLatest(pageData);

    useImperativeHandle(ref, () => ({
      setError,
    }));

    const onScroll = useCallback(
      (event: NativeSyntheticEvent<NativeScrollEvent>) => {
        setError(false);
        const scrollY = event.nativeEvent.contentOffset.y;

        contentHeight.value = event.nativeEvent.contentSize.height;

        contentWidth.value = event.nativeEvent.contentSize.width;

        if (scrollY < 0 || completed) {
          return;
        }

        if (contentHeight.value > 0) {
          let currentPage = 1;
          const totalHeight = contentHeight.value - containerHeight.value;
          if (totalHeight <= 0 || scrollY >= totalHeight) {
            currentPage = 100;
          } else {
            currentPage = Math.floor((scrollY / totalHeight) * 99) + 1;
          }
          if (currentPage !== latestPageData.current.currentPage) {
            setPageData({
              currentPage,
              totalPages: 100,
            });
          }
          if (currentPage === 100) {
            setCompleted(true);
            onCompleteConsent();
          }
        }
      },
      [containerHeight, contentHeight, contentWidth, latestPageData, completed],
    );

    const onLayout = useCallback(
      (e: LayoutChangeEvent) => {
        const { height } = e.nativeEvent.layout;
        containerHeight.value = height;
      },
      [containerHeight],
    );

    const onScrollToEnd = useCallback(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, []);

    return (
      <Shadow
        containerStyle={{
          flex: 1,
        }}
        style={{
          flex: 1,
          borderRadius: borderRadius.medium,
        }}
        startColor={colors.primaryVariant}
        endColor={colors.onPrimary}
        startOpacity={0.26}
        endOpacity={0.1}
        distance={10}
        stretch={true}>
        <Container>
          <Box flex={1}>
            <ScrollView
              style={{ flex: 1 }}
              onScroll={onScroll}
              ref={scrollViewRef}
              onLayout={onLayout}>
              <PdpContent />
            </ScrollView>
            <AnimatedScrollDownPdfV2
              pageData={pageData}
              width={contentWidth.value + space[24]}
              onScrollToEnd={onScrollToEnd}
              bottom={error ? space[12] : undefined}
            />
          </Box>
        </Container>
        {error && <ReviewConsentError />}
      </Shadow>
    );
  },
);

const ReviewConsentError = () => {
  const { space, colors } = useTheme();
  const { t } = useTranslation('lead');
  return (
    <Row
      position="absolute"
      bottom={-1}
      left={0}
      right={0}
      zIndex={999}
      backgroundColor={colors.palette.alertRedLight}
      alignItems={'center'}
      gap={space[1]}
      padding={space[2]}
      borderRadius={space[1]}
      borderWidth={1}
      borderColor={colors.palette.alertRed}>
      <Icon.Warning size={space[4]} fill={colors.palette.alertRed} />
      <H8 color={colors.palette.alertRed}>{t('addLead.consent.error')}</H8>
    </Row>
  );
};

export default AddLeadConsent;
