import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { RouteProp, useRoute } from '@react-navigation/native';
import DatePickerCalendar from 'components/DatePickerCalendar';
import Input from 'components/Input';
import PhoneField from 'components/PhoneField';
import SearchableDropdown from 'components/SearchableDropdown';
import { defaultCountryCode } from 'constants/defaultValues';
import {
  addToast,
  Box,
  Button,
  Column,
  H5,
  Icon,
  Picker,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { format, sub } from 'date-fns';
import { Image } from 'expo-image';
import { leadMrIbPNG, leaMsIbPNG } from 'features/lead/assets/image';
import LeadIconSVG from 'features/lead/assets/image/LeadIconSVG';
import NameTextField from 'features/lead/components/AddLeadForm/NameTextField';
import { useCreateLead } from 'features/lead/hooks/useCreateLead';
import { useLookupLead } from 'features/lead/hooks/useLookupLead';
import useBoundStore from 'hooks/useBoundStore';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useRootStackNavigation } from 'hooks/useRootStack';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  SubmitErrorHandler,
  SubmitHandler,
  useForm,
  UseFormHandleSubmit,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Pressable, StyleSheet, View } from 'react-native';
import RootSiblings from 'react-native-root-siblings';
import {
  CreateLeadRequest,
  LeadFormValues,
  LookupLeadRequest,
  RootStackParamList,
  TypesOfNewLeadForm,
} from 'types';
import { CountryCode } from 'types/optionList';
import { calculateAge as calculateAgeAsANB } from 'utils/helper/calculateAge';
import { useValidationYupResolver } from 'utils/validation';
import {
  addNewLeadSchema as addNewLeadSchemaID,
  initialLeadData as initialLeadDataID,
} from 'utils/validation/id/addNewLeadSchema';
import AddLeadConsent from './components/AddLeadConsent';

export type AddLeadConsentRef = {
  setError: (error: boolean) => void;
};

export type AddNewLeadFormProps = {
  onClose: () => void;
  onShowAddLeadModal: () => void;
  setLeadType: React.Dispatch<React.SetStateAction<TypesOfNewLeadForm>>;
};

const BUTTON_WIDTH = 200;

export default function AddNewLeadForm({
  onClose,
  onShowAddLeadModal,
}: AddNewLeadFormProps) {
  const defaultDate = new Date(new Date().getFullYear() - 18, 0, 1);
  const { t } = useTranslation('lead');
  const { t: ct } = useTranslation('common');

  const { space, colors, borderRadius } = useTheme();
  const { data: optionList } = useGetOptionList();

  const draftForm = useBoundStore(state => state.lead.draftAddLeadForm);
  const saveDraft = useBoundStore(
    state => state.leadActions.addLeadForm.saveDraft,
  );
  const clearDraft = useBoundStore(
    state => state.leadActions.addLeadForm.clearDraft,
  );

  const consentRef = useRef<AddLeadConsentRef>(null);

  const route = useRoute<RouteProp<RootStackParamList>>();
  const { countryCodeOptions } = useMemo(() => {
    return {
      titleOptions: optionList?.TITLE?.options || [],
      // extensionOptions: optionList?.EXTENSION?.options || [],
      countryCodeOptions:
        optionList?.COUNTRY_CODE?.options?.map(data => ({
          label: data.label,
          value: '+' + parseInt(data.value),
        })) || [],
      natureOfBusinessOptions:
        optionList?.BUSINESS_NATURE_ENTITY?.options || [],
    };
  }, [optionList]);

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isValid },
    getValues,
    setValue,
  } = useForm<LeadFormValues>({
    mode: 'onBlur',
    defaultValues: {
      ...(draftForm?.data || initialLeadDataID),
      mobilePhoneCountryCode: defaultCountryCode,
    },
    resolver: useValidationYupResolver(addNewLeadSchemaID),
  });

  useEffect(() => {
    if (!draftForm) {
      reset({
        ...initialLeadDataID,
        mobilePhoneCountryCode: defaultCountryCode,
      });
    }
  }, [draftForm, reset]);

  // const [newLeadData, setNewLeadData] = useState<LeadFormValues | null>(null);
  const [loading, setLoading] = useState(false);
  const fullName = watch('fullName');
  const birthDate = watch('birthDate');
  const mobilePhoneNumber = watch('mobilePhoneNumber');
  const genderCode = watch('genderCode');
  const age = birthDate ? calculateAgeAsANB(new Date(birthDate)) : '';
  const { navigate } = useRootStackNavigation();

  const { mutate: mutateToCreateLead, isLoading } = useCreateLead();
  const { mutateAsync: mutateLookupLead, isLoading: isLoadingLookupLead } =
    useLookupLead();

  const resetTodayFilters = useBoundStore(
    state => state.leadActions.resetTodayFilters,
  );

  const firstName = useMemo(() => {
    return fullName.split(' ')[0];
  }, [fullName]);

  type HandleSubmitSchema = typeof handleSubmit extends UseFormHandleSubmit<
    infer U
  >
    ? U
    : never;

  const closeModalHandler = () => {
    onClose();
    reset();
  };

  const onValidSubmit: SubmitHandler<HandleSubmitSchema> = async (
    data: LeadFormValues,
  ) => {
    const newData = {
      ...data,
      firstName: data.fullName,
      // * lastName is required at least one character from ib tablet caption
      lastName: ' ',
      interestedCategories: data.interestedCategories
        ? [data.interestedCategories]
        : [],
      mobilePhoneCountryCode: String(parseInt(data.mobilePhoneCountryCode)),
      birthDate: data.birthDate
        ? format(new Date(data.birthDate), 'yyyy-MM-dd')
        : undefined,
    } satisfies CreateLeadRequest;

    const lookupLeadReq: LookupLeadRequest = {
      // If the lead is an entity, isIndividual should be false
      isIndividual: true,
      firstName: data.fullName,
      lastName: ' ',
      mobilePhoneCountryCode: String(parseInt(data.mobilePhoneCountryCode)),
      mobilePhoneNumber: data.mobilePhoneNumber,
    };

    console.log('=====lookupLeadReq-----: ', JSON.stringify(lookupLeadReq));

    try {
      setLoading(true);
      await mutateLookupLead(lookupLeadReq, {
        onSuccess: res => {
          saveDraft(getValues());
          setLoading(false);
          const countryCode = res?.mobilePhoneCountryCode || '';
          const phoneNumber = res?.mobilePhoneNumber || '';
          const mobile = `${countryCode} ${phoneNumber.replace(
            /^\d{4}(\d.)/g,
            '****$1',
          )}`.trim();
          const parsedEmail = (res?.email || '').replace(
            /^\w{4}(\w.)/g,
            '****$1',
          );
          const email = parsedEmail ? parsedEmail : '--';
          setTimeout(() => {
            const existingLeadFound = new RootSiblings(
              (
                <BackgroundContainer>
                  <ViewBackground />
                  <Box
                    backgroundColor={colors.background}
                    borderRadius={borderRadius.large}
                    maxWidth={380}
                    minHeight={300}
                    p={space[12]}>
                    <CloseIconContainer
                      onPress={() => {
                        onShowAddLeadModal?.();
                        existingLeadFound.destroy();
                      }}>
                      <Icon.Close fill={colors.palette.fwdDarkGreen[100]} />
                    </CloseIconContainer>
                    <Typography.H6 fontWeight="bold">
                      {t('addLead.existing.lead.found')}
                    </Typography.H6>
                    <Typography.LargeBody
                      style={{
                        marginTop: space[4],
                        marginBottom: space[6],
                        width: 288,
                      }}>
                      {t('addLead.existing.lead.tip', { mobile, email })}
                    </Typography.LargeBody>
                    <Box alignItems="center">
                      <Button
                        text={ct('viewProfile')}
                        variant="primary"
                        onPress={() => {
                          navigate('LeadProfile', {
                            id: res.id ?? '',
                            // If the lead is an entity, isIndividual should be false
                            isIndividualLead: true,
                          });
                          existingLeadFound.destroy();
                          clearDraft();
                          onClose();
                        }}
                        style={{ width: 156 }}
                      />
                    </Box>
                  </Box>
                </BackgroundContainer>
              ),
            );
          }, 1000);
          closeModalHandler();
          return;
        },
        onError: error => {
          const errorObj: any = error as any;
          if (errorObj?.response?.status == '404') {
            mutateToCreateLead(newData, {
              onSuccess: () => {
                clearDraft();
                resetTodayFilters();
                onClose();
                setTimeout(() => {
                  addToast([
                    {
                      IconLeft: Icon.Tick,
                      message: t('addLead.formFields.newLeadIsAdd'),
                    },
                  ]);
                }, 1000);
                reset();
                setLoading(false);
              },
              onError: e => {
                setLoading(false);
                addToast([
                  {
                    message: t('addLead.formFields.pleaseTryAgainLater'),
                  },
                ]);
                console.log('onError: ', e);
              },
            });
          } else {
            setLoading(false);
            addToast([
              {
                message: t('addLead.formFields.pleaseTryAgainLater'),
              },
            ]);
          }
        },
      });
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  const onInvalidSubmit: SubmitErrorHandler<HandleSubmitSchema> = error => {
    if (error.consent) {
      consentRef.current?.setError(true);
    }
  };

  const DatePickerMaxDate = sub(new Date(), { months: 1 });

  return (
    <View
      style={{
        paddingHorizontal: sizes[12],
        paddingTop: sizes[15],
        paddingBottom: sizes[12],
        width: '100%',
      }}>
      <LeadAvatar genderCode={genderCode} />
      <FormContainer>
        <Box
          alignItems="center"
          gap={space[4]}
          paddingTop={space[3]}
          paddingBottom={space[1]}>
          <HeaderText>
            {isValid
              ? `👋  ${firstName} ` + t('addLead.formFields.isReady')
              : t('addLead.formFields.addNewLead')}
          </HeaderText>
        </Box>
        <Row gap={space[4]}>
          <Column flex={1} gap={space[5]}>
            <Input
              control={control}
              as={NameTextField}
              name="fullName"
              label={t('addLead.formFields.fullName')}
              error={errors.fullName?.message}
            />

            <Row style={{ gap: space[3] }}>
              <Input
                control={control}
                as={SearchableDropdown<CountryCode, string>}
                name="mobilePhoneCountryCode"
                label={t('addLead.formFields.countryCode')}
                modalTitle={'Country code'}
                data={countryCodeOptions}
                style={{ flex: 0.7 }}
                searchable
                isModal
                getItemValue={item => item.value}
                getItemLabel={item => item.label}
                getDisplayedLabel={item => getCountryCodeValue(item)}
                keyExtractor={item => item.value + item.label}
                modalStyle={{ position: 'relative' }}
              />

              <Input
                control={control}
                as={PhoneField}
                name="mobilePhoneNumber"
                label={t('addLead.formFields.mobileNumber')}
                style={{ flex: 1 }}
                keyboardType="numeric"
                error={errors.mobilePhoneNumber?.message}
                size={'large'}
              />
            </Row>
            <Row style={{ gap: space[3] }}>
              <Input
                control={control}
                as={DatePickerCalendar}
                name="birthDate"
                style={{ flex: 1 }}
                label={t('addLead.formFields.dateOfBirth.optional')}
                hint="DD/MM/YYYY"
                error={errors.birthDate?.message}
                defaultDate={defaultDate}
                maxDate={DatePickerMaxDate}
                modeSwitchEnabled
              />
              <TextField
                disabled={true}
                label={t('addLead.formFields.age')}
                value={age}
                style={{ flex: 0.3 }}
              />
            </Row>
            <Row style={{ gap: space[5] }}>
              <Input
                control={control}
                as={Picker}
                name="genderCode"
                type="text"
                label={t('addLead.formFields.gender.optional')}
                style={{ flex: 1 }}
                error={errors.genderCode?.message}
                items={[
                  {
                    value: 'M',
                    text: 'Male',
                  },
                  {
                    value: 'F',
                    text: 'Female',
                  },
                ]}
              />
            </Row>
            <Input
              control={control}
              as={TextField}
              name="email"
              label={t('addLead.formFields.email.optional')}
              error={errors.email?.message}
            />
          </Column>

          <AddLeadConsent
            ref={consentRef}
            onCompleteConsent={() =>
              setValue('consent', true, { shouldValidate: true })
            }
          />
        </Row>

        <Row style={{ gap: space[5] }}>
          <Button
            text={t('addLead.formFields.cancel')}
            variant="secondary"
            onPress={closeModalHandler}
            style={{ width: BUTTON_WIDTH }}
          />
          <Button
            text={t('addLead.formFields.agreeAndSave')}
            variant="primary"
            onPress={handleSubmit(onValidSubmit, onInvalidSubmit)}
            style={{
              width: BUTTON_WIDTH,
              backgroundColor: isValid
                ? colors.palette.fwdOrange[100]
                : colors.palette.fwdOrange[50],
              opacity: isValid ? 1 : 0.5,
            }}
            gaParams={{
              eventType: 'lead_created',
              formSource: route.name.includes('Lead')
                ? 'menu_leads_and_customers'
                : 'homepage_cta',
            }}
            loading={loading}
          />
        </Row>
      </FormContainer>
    </View>
  );
}

function LeadAvatar({
  genderCode,
}: {
  genderCode: LeadFormValues['genderCode'];
}) {
  const { space } = useTheme();
  const iconSize = space[30];

  return (
    <LeadIconStyle>
      {genderCode === 'F' ? (
        <Image
          source={leaMsIbPNG}
          style={{ width: iconSize, height: iconSize }}
        />
      ) : genderCode === 'M' ? (
        <Image
          source={leadMrIbPNG}
          style={{ width: iconSize, height: iconSize }}
        />
      ) : (
        <LeadIconSVG />
      )}
    </LeadIconStyle>
  );
}

const HeaderText = styled(H5)(({ theme }) => ({
  fontFamily: 'FWDCircularTT-Bold',
  color: theme.colors.palette.fwdDarkGreen[100],
}));

const LeadIconStyle = styled(View)(({ theme }) => ({
  alignSelf: 'center',
  zIndex: 10,
  position: 'absolute',
  top: -60,
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.full,
}));

const FormContainer = styled.View(({ theme }) => ({
  alignItems: 'center',
  justifyContent: 'center',
  gap: sizes[5],
}));

const BackgroundContainer = styled(Box)(({ theme }) => ({
  ...StyleSheet.absoluteFillObject,
  justifyContent: 'center',
  alignItems: 'center',
}));

const ViewBackground = styled(View)(() => ({
  ...StyleSheet.absoluteFillObject,
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  alignItems: 'center',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
}));

const CloseIconContainer = styled(Pressable)(() => ({
  alignSelf: 'flex-end',
}));

const getCountryCodeValue = (item: CountryCode) => item.value.split(' - ')[0];
