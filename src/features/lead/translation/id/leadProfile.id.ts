export default {
  'leadProfile.leadDetail': 'Detail prospek',
  'leadProfile.profileDetails.entityName': 'Company name',
  'leadProfile.leadInfoSection.natureOfBusiness':
    'Industry / Nature of business',
  'leadProfile.customerDetail': 'Customer details',
  'leadProfile.profile': 'Profil',
  'leadProfile.opp': '<PERSON><PERSON>uang',
  'leadProfile.cff': 'Customer Fact Find',
  'leadProfile.fna': 'FNA & RPQ',
  'leadProfile.opportunity': 'Opportunity',
  'leadProfile.rpq': 'RPQ',
  'leadProfile.proposals': 'Proposal',
  'leadProfile.activities': 'Aktivitas',
  'leadProfile.policies': 'Existing policies',
  'leadProfile.insureds': 'Insureds',
  'leadProfile.primaryPolicy': 'Primary policy',
  'leadProfile.subsidiaryPolicy': 'Subsidiary Policy',
  // Lead info. section
  'leadProfile.leadInfoSection.viewProfile': 'Lihat profil',
  'leadProfile.leadInfoSection.viewDetails': 'Lihat detail',
  'leadProfile.leadInfoSection.viewMore': 'Lihat lainnya',
  'leadProfile.leadInfoSection.occupation': 'Pekerjaan',
  'leadProfile.leadInfoSection.basicInfo': 'Informasi dasar',
  'leadProfile.leadInfoSection.yearsOld': 'tahun',
  'leadProfile.leadInfoSection.leadScore': 'Skor prospek',
  'leadProfile.leadInfoSection.leadSource': 'Sumber prospek',
  'leadProfile.leadInfoSection.businessPhoneNumber': 'Business phone number',
  'leadProfile.leadInfoSection.email': 'Email',

  // Lead profile bottom bar
  'leadProfile.bottomBar.proposal': 'Proposal',
  'leadProfile.bottomBar.activity': 'Aktivitas',
  'leadProfile.bottomBar.contact': 'Kontak',
  // Lead profile tablet Side bar
  'leadProfile.sideBar.profile': 'Profil',
  'leadProfile.sideBar.si': 'Ilustrasi manfaat',
  'leadProfile.sideBar.cff': 'Customer Fact Find',
  'leadProfile.sideBar.inApp': 'Dalam aplikasi',
  'leadProfile.sideBar.activitiesRecord': 'Catatan aktivitas',

  // Lead profile details
  'leadProfile.profileDetails.leadCreateDate': 'Tanggal pembuatan lead',
  'leadProfile.profileDetails.natureOfBusiness': 'Jenis usaha',
  'leadProfile.profileDetails.authorisedSignatoryName':
    'Nama penandatangan resmi',
  'leadProfile.profileDetails.companyName': 'Nama perusahaan',
  'leadProfile.profileDetails.businessPhoneNumber': 'Nomor telepon bisnis',
  'leadProfile.profileDetails.interestProduct': 'Produk yang diminati',
  'leadProfile.profileDetails.contactNo': 'No. kontak',
  'leadProfile.profileDetails.leadsId': 'ID Lead',
  'leadProfile.profileDetails.birthday': 'Tanggal lahir',
  'leadProfile.profileDetails.receiveDate': 'Tanggal diterima',
  'leadProfile.profileDetails.source': 'Sumber',
  'leadProfile.profileDetails.profile': 'Profil',
  'leadProfile.profileDetails.profileDetails': 'Detail profil',
  'leadProfile.profileDetails.personalInfo': 'Informasi pribadi',
  'leadProfile.profileDetails.salutation': 'Sapaan/Gelar',
  'leadProfile.profileDetails.fullName': 'Nama lengkap',
  'leadProfile.profileDetails.firstName': 'Nama depan',
  'leadProfile.profileDetails.middleName': 'Nama tengah',
  'leadProfile.profileDetails.lastName': 'Nama belakang',
  'leadProfile.profileDetails.extensionName': 'Nama tambahan',
  'leadProfile.profileDetails.gender': 'Jenis kelamin',
  'leadProfile.profileDetails.dob': 'Tanggal lahir',
  'leadProfile.profileDetails.age': 'Usia',
  'leadProfile.profileDetails.maritalStatus': 'Status pernikahan',
  'leadProfile.profileDetails.leadSource': 'Sumber lead',
  'leadProfile.profileDetails.contactDetails': 'Detail kontak',
  'leadProfile.profileDetails.mobileNumber': 'No. ponsel',
  'leadProfile.profileDetails.leadScore': 'Lead score',
  'leadProfile.profileDetails.leadOrigin': 'Lead origin',
  'leadProfile.profileDetails.campaignName': 'Campaign name',
  'leadProfile.profileDetails.notes': 'Notes',
  'leadProfile.profileDetails.email': 'Email',
  'leadProfile.profileDetails.referralDetails': 'Detail referensi',
  'leadProfile.profileDetails.sourceDetails': 'Detail sumber',
  'leadProfile.profileDetails.affiliateCode': 'Kode Affiliate',
  'leadProfile.profileDetails.campaignCode': 'Kode kampanye',
  'leadProfile.profileDetails.householdDetails': 'Detail rumah tangga',
  'leadProfile.profileDetails.noInfo': 'Tidak ada informasi',
  'leadProfile.profileDetails.bltsRefNumber': 'Nomor referensi BLTS',
  'leadProfile.profileDetails.servicingBranch': 'Cabang layanan',
  'leadProfile.profileDetails.bankCustomerId': 'ID Nasabah Bank',
  'leadProfile.profileDetails.referrerCode': 'Kode pemberi referensi',
  'leadProfile.profileDetails.name': 'Nama',
  'leadProfile.profileDetails.preferredContactMode':
    'Mode kontak yang diinginkan',
  'leadProfile.profileDetails.preferredDocumentLanguage':
    'Bahasa dokumen yang diinginkan',
  'leadProfile.profileDetails.leadInfo': 'Info lead',
  'leadProfile.profileDetails.remark': 'Catatan',
  'leadProfile.profileDetails.companyDetails': 'Detail perusahaan',
  'leadProfile.profileDetails.registrationNumberLatest':
    'No. registrasi (Terbaru)',
  'leadProfile.profileDetails.registrationNumberOld': 'No. registrasi (Lama)',
  'leadProfile.profileDetails.dateOfRegistration': 'Tanggal registrasi',
  'leadProfile.profileDetails.businessRegistration': 'Registrasi bisnis',
  'leadProfile.profileDetails.correspondenceAddress': 'Alamat korespondensi',
  'leadProfile.profileDetails.taxDetails': 'Detail pajak',
  'leadProfile.profileDetails.taxPurposeOfTakafulCoverage':
    'Pajak - Tujuan perlindungan takaful',
  'leadProfile.profileDetails.taxRegistrationNumber':
    'No. registrasi pajak penjualan dan jasa',
  'leadProfile.maritalStatus.Unknown': 'Tidak diketahui',
  'leadProfile.maritalStatus.Single': 'Lajang',
  'leadProfile.maritalStatus.Married': 'Menikah',
  'leadProfile.maritalStatus.Annulled': 'Dibatalkan',
  'leadProfile.maritalStatus.Divorced': 'Bercerai',
  'leadProfile.maritalStatus.Separated': 'Berpisah',
  'leadProfile.maritalStatus.Widower': 'Duda',
  'leadProfile.maritalStatus.Widowed': 'Janda',
  'leadProfile.maritalStatus.Company': 'Perusahaan',
  'leadProfile.maritalStatus.Others': 'Lainnya',
  'leadProfile.button.startCFF': 'Mulai CFF',
  'leadProfile.button.redo': 'Ulangi',
  'leadProfile.profileDetails.entityDetails': 'Profile details',
  'leadProfile.profileDetails.registrationDate': 'Date of registration',

  //Lead profile protection score section
  'leadProfile.termLife': 'Perlindungan Jiwa/Keluarga',
  'leadProfile.criticalIllness': 'Perlindungan Penyakit Kritis',
  'leadProfile.medical': 'Jumlah Bulanan Perlindungan Medis/Kesehatan',
  'leadProfile.investmentSavings': 'Jumlah Bulanan Tabungan/Investasi',
  'leadProfile.recommendedTarget': 'Target yang disarankan: ',
  'leadProfile.month': ' / bulan',
  'leadProfile.yourProtectionScore': 'Skor perlindungan Anda',
  'leadProfile.yourScore': 'Skor Anda',
  'leadProfile.current': 'Saat ini',

  // Lead profile for SI tab
  'leadProfile.si.addNewSI': 'Buat Ilustrasi Manfaat Baru',
  'leadProfile.si.addNewSIv2': 'Buat Proposal Baru',
  'leadProfile.si.tableHeaderField.si': 'Nama Proposal\nNama Tertanggung',
  'leadProfile.si.tableHeaderField.status': 'Status',
  'leadProfile.si.tableHeaderField.product': 'Produk',
  'leadProfile.si.tableHeaderField.sumCovered': 'Jumlah Pertanggungan (RM)',
  'leadProfile.si.tableHeaderField.premium': 'Kontribusi (RM)',
  'leadProfile.si.tableHeaderField.lastUpdated': 'Tanggal Pembaruan Terakhir',

  // Lead profile for InApp tab
  'si.title': 'Proposal yang Disimpan',
  'leadProfile.inApp.addNewSI': 'Buat Ilustrasi Manfaat Baru',
  'leadProfile.inApp.tableHeaderField.si': 'Nama Proposal\nNama Tertanggung',
  'leadProfile.inApp.tableHeaderField.status': 'Status',
  'leadProfile.inApp.tableHeaderField.product': 'Produk',
  'leadProfile.inApp.tableHeaderField.sumCovered': 'Jumlah Pertanggungan (RM)',
  'leadProfile.inApp.tableHeaderField.premium': 'Kontribusi (RM)',
  'leadProfile.inApp.tableHeaderField.lastUpdated':
    'Tanggal Pembaruan Terakhir',

  // Lead profile for saved proposals tab
  'leadProfile.savedProposals.tableHeaderField.proposalName':
    'Nama Proposal yang Disimpan\nNama Tertanggung',
  'leadProfile.savedProposals.tableHeaderField.status': 'Status',
  'leadProfile.savedProposals.tableHeaderField.sumAssured':
    'Jumlah Pertanggungan\n(RM)',
  'leadProfile.savedProposals.tableHeaderField.modalPremium':
    'Premi Modal (RM)',
  'leadProfile.savedProposals.tableHeaderField.lastUpdated':
    'Tanggal Pembaruan Terakhir',

  // Lead profile for CFF tab

  'cff.title': '(CFF)',
  'cff.documentStatus': 'Dokumen CFF',
  'leadProfile.cff.expiryDate': 'Tanggal kedaluwarsa',
  'leadProfile.cff.customerPriority':
    'Urutan prioritas nasabah & tujuan keuangan',
  'leadProfile.cff.addNewSI': 'Buat Ilustrasi Manfaat Baru',
  'leadProfile.cff.expiredCaseReminder': `Customer Fact Find Anda telah kedaluwarsa lebih dari 30 hari.
Anda dapat melanjutkan dengan membuat Ilustrasi Manfaat baru.`,
  'leadProfile.cff.tableHeaderField.si': 'Nama Proposal',
  'leadProfile.cff.tableHeaderField.product': 'Produk',
  'leadProfile.cff.tableHeaderField.sumCovered': 'Jumlah Pertanggungan (RM)',
  'leadProfile.cff.tableHeaderField.premium': 'Kontribusi (RM)',
  'leadProfile.cff.tableHeaderField.lastUpdated': 'Tanggal Pembaruan Terakhir',

  // Activities Screen
  'leadProfile.activityRecord.title': 'Catatan Aktivitas',
  'leadProfile.activityRecord.chart.currentStatus': 'Status saat ini',
  'leadProfile.activityRecord.chart.not_contacted': 'Belum dihubungi',
  'leadProfile.activityRecord.chart.contacted': 'Sudah dihubungi',
  'leadProfile.activityRecord.chart.appointment': 'Janji temu',
  'leadProfile.activityRecord.chart.illustration': 'Ilustrasi',
  'leadProfile.activityRecord.chart.submitted': 'Dikirim',
  'leadProfile.activityRecord.action.contact': 'Sudah dihubungi',
  'leadProfile.activityRecord.action.appointment': 'Janji temu',
  'leadProfile.activityRecord.action.submit': 'Dikirim',
  'leadProfile.activityRecord.action.create': 'Dibuat',
  'leadProfile.activityRecord.action.assign': 'Ditugaskan',
  'leadProfile.activityRecord.action.consolidate': 'Dikonsolidasikan',
  'leadProfile.activityRecord.action.update': 'Diperbarui',
  'leadProfile.activityRecord.action.recommend': 'Direkomendasikan',
  'leadProfile.activityRecord.action.not_interested': 'Tidak tertarik',
  'leadProfile.activityRecord.action.illustrate': 'Ilustrasi',
  'leadProfile.activityRecord.action.defer': 'Ditunda',
  'leadProfile.activityRecord.action.applicationSubmitted': 'Aplikasi dikirim',
  'leadProfile.activityRecord.action.illustrateCreated': 'Ilustrasi dibuat',
  'leadProfile.activityRecord.feedback.deferred': 'Ditunda',
  'leadProfile.activityRecord.feedback.interested': 'Tertarik',
  'leadProfile.activityRecord.feedback.notInterested':
    'Lead kemungkinan tidak tertarik',
  'leadProfile.overallLeadFeedback.title': 'Umpan balik keseluruhan lead',
  'leadProfile.overallLeadFeedback.lastUpdate': 'Pembaruan terakhir',
  'leadProfile.activityRecord.logTable.type': 'Jenis aktivitas',
  'leadProfile.activityRecord.logTable.description': 'Deskripsi',
  'leadProfile.activityRecord.logTable.lastCreatedDate':
    'Tanggal dibuat terakhir',
  'leadProfile.activityRecord.logTable.date': 'Tanggal',
  'leadProfile.activityRecord.log.title': 'Log aktivitas',
  'leadProfile.activityRecord.log.emptyRecord': 'Tidak ada catatan',

  // Log activity form
  'leadProfile.logActivityForm.title': 'Log aktivitas',
  'leadProfile.logActivityForm.logActivity': 'Log aktivitas',
  'leadProfile.logActivityForm.activityDate': 'Tanggal aktivitas',
  'leadProfile.logActivityForm.typeOfActivity': 'Jenis aktivitas',
  'leadProfile.logActivityForm.leadsFeedback': 'Umpan balik lead',
  'leadProfile.logActivityForm.contacted': 'Sudah dihubungi',
  'leadProfile.logActivityForm.appointment': 'Janji temu',
  'leadProfile.logActivityForm.interested': 'Tertarik',
  'leadProfile.logActivityForm.notInterested': 'Tidak tertarik',
  'leadProfile.logActivityForm.deferred': 'Ditunda',
  'leadProfile.logActivityForm.notInterested.question':
    'Mengapa lead tidak tertarik:',
  'leadProfile.logActivityForm.notInterested.tooExpensive':
    'Premi terlalu mahal',
  'leadProfile.logActivityForm.notInterested.unsuitable':
    'Manfaat tidak sesuai',
  'leadProfile.logActivityForm.notInterested.windowShopping':
    'Nasabah hanya melihat-lihat',
  'leadProfile.logActivityForm.yourFeedbackOptional':
    'Umpan balik Anda (opsional)',
  'leadProfile.logActivityForm.otherFeedbackOptional':
    'Umpan balik lain (opsional)',
  'leadProfile.logActivityForm.subText':
    'Terima kasih telah bertemu dengan prospek. Anda dapat menggunakan formulir ini untuk melaporkan atau membagikan umpan balik atau penyalahgunaan oleh prospek. Kami juga dapat membagikan komentar Anda dengan agen lain yang mungkin menerima permintaan di kemudian hari.',
  'leadProfile.logActivityForm.logSuccessToast':
    'Aktivitas baru telah dicatat.',
  'leadProfile.logActivityForm.logSuccessToastV2':
    'Aktivitas baru telah dicatat.',
  'leadProfile.logActivityForm.logFailedToast': 'Gagal mencatat aktivitas.',
  'leadProfile.logActivityForm.notInterested.noResponse':
    'Tidak ada respons dari klien',
  'leadProfile.logActivityForm.notInterested.noFunds': 'Tidak ada dana',
  'leadProfile.logActivityForm.notInterested.alreadyHasIes':
    'Sudah memiliki polis asuransi',
  'leadProfile.logActivityForm.notInterested.alreadyHasTcs':
    'Sudah memiliki sertifikat takaful',
  'leadProfile.logActivityForm.notInterested.undecided': 'Belum memutuskan',
  'leadProfile.logActivityForm.notInterested.interestedButNotReady':
    'Tertarik tetapi belum siap saat ini',
  'leadProfile.logActivityForm.notInterested.quoteTooHigh':
    'Penawaran terlalu tinggi',
  'leadProfile.logActivityForm.notInterested.deferred': 'Ditunda',

  // Contact
  'leadProfile.contact.contact.title': 'Hubungi lead Anda',
  'leadProfile.contact.call': 'Telepon',
  'leadProfile.contact.sms': 'SMS',
  'leadProfile.contact.whatsapp': 'WhatsApp',
  'leadProfile.contact.viber': 'Viber',
  'leadProfile.contact.email': 'Email',
  'leadProfile.contact.more': 'Lainnya',

  //FNA
  'leadProfile.fna.fna': 'FNA',
  'leadProfile.fna.expiryDate': 'Tanggal kedaluwarsa',
  'leadProfile.fna.startFNA': 'Mulai FNA',
  'leadProfile.fna.startNewFNA': 'Mulai FNA baru',
  'leadProfile.fna.editFNA': 'Edit FNA',
  'leadProfile.fna.viewPDF': 'Lihat PDF',
  'leadProfile.fna.valid': 'Berlaku',
  'leadProfile.fna.expired': 'Kedaluwarsa',
  'leadProfile.fna.redoFNA': 'Ulangi FNA',
  'leadProfile.fna.redoRPQ': 'Ulangi RPQ',
  'leadProfile.fna.reviseRPQ': 'Revisi RPQ',
  'leadProfile.fna.recommendProduct': 'Produk yang disarankan',
  'leadProfile.fna.savingNeeds': 'Kebutuhan tabungan',
  'leadProfile.fna.savingNeeds.totalNeeds': 'Total kebutuhan:',
  'leadProfile.fna.savingNeeds.totalCurrentCoverage':
    'Total perlindungan saat ini:',
  'leadProfile.fna.savingNeeds.totalGapToTargetAmount':
    'Total selisih ke jumlah target:',
  'leadProfile.fna.bar.target': 'Target: RM {{target}}',
  'leadProfile.fna.bar.current': 'Saat ini: RM {{current}}',
  'leadProfile.fna.bar.gap': 'Selisih: RM {{gap}}',
  'leadProfile.fna.totalNeeds': 'Total kebutuhan',
  'leadProfile.fna.totalCurrentAssets': 'Total aset saat ini',
  'leadProfile.fna.totalGapToTargetAmount': 'Total selisih ke jumlah target',
  'leadProfile.fna.breakDown': 'Rincian',
  'leadProfile.fna.currentAssets': 'Aset saat ini',
  'leadProfile.fna.gapToTargetAmount': 'Selisih ke jumlah target',
  'leadProfile.fna.goals.currentSaving': 'Tabungan saat ini',
  'leadProfile.fna.goals.recommendedTarget': 'Target yang disarankan',
  'leadProfile.fna.protectionNeeds': 'Kebutuhan perlindungan',
  'leadProfile.fna.coverage': 'Coverage',
  'leadProfile.fna.incomeProtection': 'Perlindungan penghasilan',
  'leadProfile.fna.php': 'PHP',
  'leadProfile.fna.totalNeedsTarget': 'Target total kebutuhan',
  'leadProfile.fna.retirement': 'Pendapatan Pensiun',
  'leadProfile.fna.investment': 'Investasi',
  'leadProfile.fna.childEducation': 'Pendidikan Anak',
  'leadProfile.fna.medicalPlanning': 'Perencanaan Medis/Kesehatan',
  'leadProfile.fna.lifeAchievement': 'Pencapaian hidup',
  'leadProfile.fna.healthProtection': 'Perencanaan Medis/Kesehatan',
  'leadProfile.fna.loanCoverage': 'Pelunasan utang',
  'leadProfile.fna.saving': 'Tabungan',
  'leadProfile.fna.target': 'Target',
  'leadProfile.fna.rpq': 'RPQ',
  'leadProfile.fna.startRPQ': 'Mulai RPQ',
  'leadProfile.fna.notDoneYet': 'Belum dilakukan',
  'leadProfile.fna.you': 'Anda',
  'leadProfile.fna.score': 'Skor',
  'leadProfile.fna.riskLevel': 'Tingkat Resiko',
  'leadProfile.fna.suitableProductRisk': 'Risiko produk yang sesuai',
  'leadProfile.fna.conservative': 'Konservatif',
  'leadProfile.fna.balanced': 'Seimbang',
  'leadProfile.fna.low': 'Rendah',
  'leadProfile.fna.aggressive': 'Agresif',
  'leadProfile.fna.riskProfileTitle':
    'Profil Risiko Investor dan Pernyataan Kebijakan Investasi',
  'leadProfile.fna.riskProfileDescription':
    'Mengacu pada investor yang cocok untuk kelas aset dengan risiko relatif rendah dan fluktuasi harga yang memberikan hasil lebih baik daripada deposito dan tingkat inflasi. Investor dengan profil ini dapat berinvestasi dalam dana yang menargetkan pertumbuhan jangka panjang melalui investasi dalam campuran beragam sekuritas berkualitas tinggi, jangka menengah hingga panjang seperti surat berharga pemerintah, obligasi korporasi, dan surat utang.',
  'leadProfile.fna.knowYourCustomer.title': 'Kenali Nasabah Anda',
  'leadProfile.fna.knowYourCustomer.content':
    'Lengkapi FNA untuk memahami kebutuhan nasabah Anda dan dapatkan rekomendasi produk',
  'leadProfile.fna.knowYourCustomer.resume': 'Lanjutkan FNA',
  'leadProfile.fna.summary.title': 'Ringkasan Temuan Fakta Nasabah',
  'leadProfile.fna.summary.viewDetails': 'Lihat detail',
  'leadProfile.fna.summary.productRecommendation': 'Rekomendasi produk',
  'leadProfile.fna.summary.productRecommendation.sequential.option':
    '{{sequence}} opsi yang disarankan',
  'leadProfile.fna.summary.financialGoals': 'Tujuan keuangan',
  'leadProfile.fna.summary.protection.low': 'Perlindungan rendah',
  'leadProfile.fna.summary.protection.fair': 'Perlindungan cukup',
  'leadProfile.fna.summary.protection.high': 'Perlindungan tinggi',
  'leadProfile.fna.fnaResult': 'Hasil FNA',
  'leadProfile.fna.redo': 'Ulangi',
  'leadProfile.fna.viewPdf': 'Lihat PDF',
  'leadProfile.fna.fnaRecommended': 'FNA disarankan',
  'leadProfile.fna.recommendedProducts': 'Produk yang disarankan',
  'leadProfile.fna.forYou': 'Untuk Anda',
  'leadProfile.fna.howMuchYouNeed': 'Berapa banyak yang Anda butuhkan',
  'leadProfile.fna.disposableIncome': 'Pendapatan yang dapat digunakan',
  'leadProfile.fna.currentLifeStage': 'Tahap kehidupan saat ini',
  'leadProfile.fna.haveAPartner': 'Punya pasangan?',
  'leadProfile.fna.partnerName': 'Nama pasangan',
  'leadProfile.fna.wantToMarry': 'Ingin menikah',
  'leadProfile.fna.noOfKids': 'Jumlah anak',
  'leadProfile.fna.noOfDependent': 'Jumlah tanggungan',
  'leadProfile.fna.ageToRetire': 'Usia pensiun',
  'leadProfile.fna.dependant': 'Tanggungan',
  'leadProfile.fna.totalSavingNeeds': 'Total kebutuhan tabungan:',
  'leadProfile.fna.currency': 'RM',
  'leadProfile.fna.priority': 'Prioritas ke-{{number}}',
  'leadProfile.fna.otherGoal': 'Tujuan lainnya',
  'leadProfile.fna.inYears': 'Dalam {{years}} tahun',
  'leadProfile.fna.protectionGoals': 'Tujuan perlindungan',
  'leadProfile.fna.totalCoverNeeds': 'Total kebutuhan perlindungan:',
  'leadProfile.fna.covers': '{{number}} perlindungan',
  'header.productBrochure': 'Product brochure',

  // opportunity tab
  'leadProfile.opportunities.investment&savings': 'Investasi & Tabungan',
  'leadProfile.opportunities.coverageGap': 'Kesenjangan perlindungan: ',
  'leadProfile.opportunities.you': 'Anda',
  'leadProfile.opportunities.php': 'PHP',
  'leadProfile.opportunities.peopleLikeYou': 'Orang seperti Anda',
  'leadProfile.opportunities.recommendation':
    'Produk yang disarankan untuk meningkatkan perlindungan Anda:',
  'leadProfile.opportunities.criticalIllness': 'Penyakit kritis',
  'leadProfile.opportunities.protection': 'Perlindungan',
  'leadProfile.opportunities.accident&disability': 'Kecelakaan & Disabilitas',
  'leadProfile.opportunities.boostYourProtection':
    'Tingkatkan perlindungan Anda',
  'leadProfile.opportunities.protected':
    'Bagus sekali! Anda sudah terlindungi dengan baik di area ini. Terus tingkatkan skor Anda dengan menjelajahi area lainnya.',

  'leadProfile.protection.underProtected': 'Perlindungan Anda kurang.',
  'leadProfile.protection.moderatelyProtected': 'Perlindungan Anda cukup.',
  'leadProfile.protection.wellProtected': 'Perlindungan Anda baik.',
  'leadProfile.protection.viewAllScoreLevels': 'Lihat semua tingkat skor',
  'leadProfile.protection.protectionScore': 'Skor perlindungan',
  'leadProfile.viewAllScoreLevels.protectionScore': 'Skor perlindungan AI',
  'leadProfile.viewAllScoreLevels.underProtected': 'Perlindungan kurang',
  'leadProfile.viewAllScoreLevels.underProtectedDesc':
    'Perlindungan Anda tampaknya belum mencukupi. Untuk meningkatkan skor, pertimbangkan menambah perlindungan di area yang disarankan atau bagikan perlindungan asuransi lain yang Anda miliki untuk evaluasi yang lebih akurat.',
  'leadProfile.viewAllScoreLevels.moderatelyProtected': 'Perlindungan cukup',
  'leadProfile.viewAllScoreLevels.moderatelyProtectedDesc':
    'Kerja bagus sejauh ini! Mari kita tingkatkan ke level berikutnya. Kami menemukan beberapa area di mana Anda bisa menambah perlindungan dan meningkatkan perlindungan keseluruhan Anda.',
  'leadProfile.viewAllScoreLevels.wellProtected': 'Perlindungan baik',
  'leadProfile.viewAllScoreLevels.wellProtectedDesc':
    'Luar biasa! Perlindungan Anda sangat baik! Terus pertahankan dan ingat untuk meninjau perlindungan Anda secara berkala. Kami juga menemukan beberapa area kecil yang bisa Anda tingkatkan.',
  'leadProfile.viewAllScoreLevels.digitalAward': 'Penghargaan Digital CX',
  'leadProfile.scoreInfo.title': 'Apa itu Skor Perlindungan AI?',
  'leadProfile.scoreInfo.first':
    'Ini berfungsi sebagai panduan tentang bagaimana Anda dapat meningkatkan perlindungan untuk menutup kesenjangan perlindungan Anda.',
  'leadProfile.scoreInfo.second':
    'Skor Perlindungan mencerminkan tingkat perlindungan Anda dibandingkan dengan orang-orang yang serupa dengan Anda.',
  // Profile section

  'leadProfile.profileDetails.smoker': 'Smoker',
  'leadProfile.profileDetails.nonSmoker': 'Non-smoker',
  'leadProfile.profileDetails.countryOfResidence': 'Country of Residence',
  'leadProfile.fna.profile.details': 'Personal details',
  'leadProfile.fna.existing.policies': 'Existing policies',

  // Log activity form
  'leadProfile.logActivityForm.date': 'Tanggal',

  'leadProfile.pdf.fnaDocument': 'Dokumen FNA',
  'leadProfile.pdf.rpqDocument': 'Dokumen RPQ',

  'leadProfile.profileDetails.personalDetails': 'Detail pribadi',
  'leadProfile.profileDetails.nationalityDetails': 'Detail kewarganegaraan',
  'leadProfile.profileDetails.occupationDetails': 'Detail pekerjaan',
  'leadProfile.profileDetails.addressInformation': 'Informasi alamat',
  'leadProfile.profileDetails.correspondenceViaEmail':
    'Korespondensi melalui email',
  'leadProfile.profileDetails.homePhone': 'Telepon rumah',
  'leadProfile.profileDetails.officePhone': 'Telepon kantor',
  'leadProfile.profileDetails.motherMaidenName': 'Nama gadis ibu',
  'leadProfile.profileDetails.idType': 'Jenis ID',
  'leadProfile.profileDetails.idNumber': 'Nomor ID',
  'leadProfile.profileDetails.taxId': 'NPWP',
  'leadProfile.profileDetails.pob.country': 'Tempat lahir - Negara',
  'leadProfile.profileDetails.pob.state': 'Tempat lahir - Provinsi',
  'leadProfile.profileDetails.pob.city': 'Tempat lahir - Kota',
  'leadProfile.profileDetails.cityName': 'Nama kota',
  'leadProfile.profileDetails.industry': 'Industri',
  'leadProfile.profileDetails.occupation': 'Pekerjaan',
  'leadProfile.profileDetails.occupationClass': 'Kelas pekerjaan',
  'leadProfile.profileDetails.occupationSector': 'Sektor pekerjaan',
  'leadProfile.profileDetails.occupationPosition': 'Posisi pekerjaan',
  'leadProfile.profileDetails.annualIncome': 'Pendapatan tahunan',
  'leadProfile.profileDetails.address': 'Alamat',
  'leadProfile.profileDetails.country': 'Negara',
  'leadProfile.profileDetails.province': 'Provinsi',
  'leadProfile.profileDetails.city': 'Kota',
  'leadProfile.profileDetails.postCode': 'Kode pos',
  'leadProfile.profileDetails.smokingHabit': 'Kebiasaan merokok',
  'leadProfile.profileDetails.religion': 'Agama',
  'leadProfile.profileDetails.nationality': 'Kewarganegaraan',
  'leadProfile.scoreInfo.v2.title': 'Lead score',
  'leadProfile.scoreInfo.v2.first':
    'Using Al-driven behavioural insights to predict lead engagement and likelihood to purchase.',
  'leadProfile.scoreInfo.v2.highInterest': 'High interest',
  'leadProfile.scoreInfo.v2.moderateInterest': 'Moderate interest',
  'leadProfile.scoreInfo.v2.openToExplore': 'Open to explore',
  'leadProfile.select': 'Select',
};
