import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Button, H6, H7, Icon, Row, Typography } from 'cube-ui-components';
import { Image, TouchableOpacity, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import DialogPhone from 'components/Dialog.phone';
import LivenessCheckInstructionGif from '../../assets/liveness-check_instruction.gif';
import DialogTablet from 'components/Dialog.tablet';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { Fragment } from 'react';
import { capitalizeFirstLetterOfEachWord } from 'utils';

type Props = {
  name: string;
  isVisible: boolean;
  onDismiss?: () => void;
  onStartVerification?: () => void;
};

export const LivenessCheckInstruction = ({
  name,
  isVisible,
  onDismiss,
  onStartVerification,
}: Props) => {
  const { colors, space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { t } = useTranslation('livenessCheck');

  const DialogContainer = isTabletMode ? DialogTablet : DialogPhone;

  return (
    <DialogContainer visible={isVisible}>
      <TouchableOpacity
        hitSlop={ICON_HIT_SLOP}
        onPress={onDismiss}
        style={{ alignSelf: 'flex-end' }}>
        <Icon.Close fill={colors.secondary} />
      </TouchableOpacity>
      <StyledView isTabletMode={isTabletMode}>
        <H7 color={'#636566'}>
          {t('instruction.passDevice', { roleName: name })}
        </H7>
        <H6 fontWeight="bold" style={{ marginTop: space[2] }}>
          {capitalizeFirstLetterOfEachWord(
            t('instruction.identityVerification', {
              roleName: name,
            }),
          )}
        </H6>
        <ImageContainer isTabletMode={isTabletMode}>
          <Image
            source={LivenessCheckInstructionGif}
            resizeMode="contain"
            style={{
              width: 284,
              height: 164,
            }}
          />
        </ImageContainer>
        <DetailStepsContainer isTabletMode={isTabletMode}>
          {(['1', '2', '3'] as const).map((_, index) => {
            return (
              <Fragment key={index}>
                <DetailStep isTabletMode={isTabletMode}>
                  <StepperIcon>
                    <Typography.LargeBody
                      fontWeight={'bold'}
                      style={{ color: colors.palette.white }}>
                      {index + 1}
                    </Typography.LargeBody>
                  </StepperIcon>
                  <StepperText>{t(`instruction.info.point.${_}`)}</StepperText>
                </DetailStep>
              </Fragment>
            );
          })}
        </DetailStepsContainer>
        <Row justifyContent="center">
          <Button
            style={{
              flex: 1,
              maxWidth: isTabletMode ? 200 : undefined,
            }}
            text={t('instruction.start')}
            onPress={onStartVerification}
          />
        </Row>
      </StyledView>
    </DialogContainer>
  );
};

const StyledView = styled(View)<{ isTabletMode: boolean }>(
  ({ theme: { space }, isTabletMode }) => ({
    paddingHorizontal: isTabletMode ? space[6] : undefined,
    paddingBottom: isTabletMode ? space[4] : undefined,
  }),
);

const ImageContainer = styled(Box)<{ isTabletMode: boolean }>(
  ({ theme: { space }, isTabletMode }) => ({
    paddingHorizontal: isTabletMode ? space[24] : undefined,
    paddingVertical: isTabletMode ? space[9] : space[4],
    alignItems: 'center',
  }),
);

const DetailStepsContainer = styled(View)<{ isTabletMode: boolean }>(
  ({ theme: { space }, isTabletMode }) => ({
    marginTop: isTabletMode ? space[3] : space[2],
    marginBottom: space[6],
    display: 'flex',
    gap: isTabletMode ? space[4] : space[3],
  }),
);

const DetailStep = styled(View)<{ isTabletMode: boolean }>(
  ({ theme: { space } }) => ({
    maxWidth: '100%',
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: space[2],
  }),
);

const StepperIcon = styled(View)(
  ({ theme: { sizes, colors, borderRadius } }) => ({
    borderRadius: borderRadius.full,
    backgroundColor: colors.palette.fwdOrange[100],
    alignItems: 'center',
    justifyContent: 'center',
    width: sizes[7],
    height: sizes[7],
  }),
);

const StepperText = styled(Typography.LargeBody)(() => ({
  marginTop: 2,
  flex: 1,
  flexWrap: 'wrap',
  maxWidth: 440,
}));
