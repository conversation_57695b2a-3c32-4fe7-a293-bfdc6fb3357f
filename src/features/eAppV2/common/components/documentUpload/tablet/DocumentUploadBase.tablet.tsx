import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box } from 'cube-ui-components';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { useIncompleteSync } from 'features/eAppV2/common/hooks/useIncompleteSync';
import { RouteItemKey } from 'features/eAppV2/common/types/progressBarTypes';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';
import { DocumentCustomerType } from 'types/document';
import { PartyRole } from 'types/party';
import { country } from 'utils/context';
import GATracking from 'utils/helper/gaTracking';
import { shallow } from 'zustand/shallow';
import EAppFooterTablet from '../../footer/EAppFooter.tablet';
import TabletSections, {
  TabletSectionsProps,
  flattenSectionNameList,
} from '../../TabletSections';
import {
  DocumentUploadType,
  InternalDocumentUploadProps,
} from '../DocumentUploadBase';
import F2FQuestion from '../F2FQuestion';
import Uploader, { UploaderRef } from './Uploader';
import { useFormSubmissionContext } from 'features/eAppV2/id/components/FormSubmissionContext';
import { useProgressBarContext } from '../../progressBar/ProgressBarContext';
import { countryModuleEAppConfig } from 'utils/config/module';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';

export default function DocumentUploadBaseTablet({
  tabs,
  shouldRenderF2F,
  f2fLevel,
  highlight,
  isValid,
  progressLock,
  arePreviousStepsComplete = true,
  disabledNext,
  useSectionSpecificIncompleteCount = false,
  sequentialUnlock = false,
}: InternalDocumentUploadProps) {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  const [activePath, setActivePath] = useState('');
  const { nextGroup, itemKey, setActiveItemKey, group } =
    useEAppProgressBarStore(
      state => ({
        nextGroup: state.nextGroup,
        itemKey: state.itemKey,
        setActiveItemKey: state.setActiveItemKey,
        group: state.group,
      }),
      shallow,
    );
  const isGroupCompleted = Boolean(group?.completed);

  /**
   * `maxCompletedDocumentUploadIndex` represents the highest index of the document upload tab that the user has reached.
   * This value is used to ensure that all tabs up to this index remain enabled, even if the user navigates to a previous tab.
   * For example, if the user reaches tab 4 and then navigates back to tab 1, tabs 1 through 4 will remain enabled.
   */
  const {
    maxCompletedDocumentUploadIndex,
    setMaxCompletedDocumentUploadIndex,
  } = useEAppStore(
    state => ({
      maxCompletedDocumentUploadIndex: state.maxCompletedDocumentUploadIndex,
      setMaxCompletedDocumentUploadIndex:
        state.setMaxCompletedDocumentUploadIndex,
    }),
    shallow,
  );

  const activeTab = useMemo(() => {
    if (countryModuleEAppConfig.checkSubmissionReadiness) {
      if (itemKey?.includes('.')) {
        const [key, index] = itemKey.split('.');
        return tabs.filter(tab => tab.key === key)[Number(index)];
      }
      return tabs.find(tab => tab.key === itemKey);
    } else {
      if (activePath?.includes('.')) {
        const [key, index] = activePath.split('.');
        return tabs.filter(tab => tab.key === key)[Number(index)];
      }
      return tabs.find(tab => tab.key === activePath);
    }
  }, [activePath, itemKey, tabs]);

  useEffect(() => {
    if (activeTab?.index && activeTab.index > maxCompletedDocumentUploadIndex) {
      setMaxCompletedDocumentUploadIndex(activeTab.index);
    }
  }, [
    activeTab,
    maxCompletedDocumentUploadIndex,
    setMaxCompletedDocumentUploadIndex,
  ]);

  const scrollViewRef = useRef<ScrollView[]>([]);
  const uploaderRef = useRef<UploaderRef>(null);

  const { caseObj } = useGetActiveCase();

  const isEntity = useCheckEntity();

  const isCurrentTabValid = useMemo(() => {
    const partyFiles =
      caseObj?.files?.filter(f => f.partyId === activeTab?.partyId) || [];
    return activeTab?.documents.every(
      doc =>
        doc.optional ||
        partyFiles.filter(f => f.docType === doc.type).length > 0,
    );
  }, [activeTab?.documents, activeTab?.partyId, caseObj?.files]);

  const sections = useMemo(() => {
    const insureds =
      caseObj?.parties?.filter(
        p =>
          p.roles.includes(PartyRole.INSURED) &&
          !p.roles.includes(PartyRole.PROPOSER),
      ) || [];
    const isSingleInsured = insureds.length <= 1;
    const multiInsuredSection: TabletSectionsProps['items'][0] = {
      name: '',
      title: '',
      subItems: [],
    };
    const sections = tabs.reduce<TabletSectionsProps['items']>(
      (sections, tab, tabIndex) => {
        const renderDocumentUploadForm = ({
          partyId,
          title,
          items,
          customerType,
          customerSeq,
        }: {
          partyId: string;
          title: string;
          items: DocumentUploadType[];
          customerType: DocumentCustomerType;
          customerSeq: string;
        }) => {
          return (
            <ScrollViewContainer
              ref={el => {
                el && (scrollViewRef.current[tabIndex] = el);
              }}>
              <Box mt={space[6]} />
              {shouldRenderF2F && (
                <F2FQuestion
                  f2fLevel={f2fLevel}
                  partyId={partyId}
                  disabledF2F={tab.disabledF2F || tab.disabledForm}
                  hiddenF2F={tab.hiddenF2F}
                />
              )}
              <Uploader
                ref={uploaderRef}
                scrollViewRef={scrollViewRef}
                title={title}
                documents={items}
                partyId={partyId}
                customerType={customerType}
                customerSeq={customerSeq}
                highlight={highlight}
                itemKey={tab.key}
                disabled={tab.disabledForm}
              />
              <Box mb={space[6]} />
            </ScrollViewContainer>
          );
        };
        const party = caseObj?.parties?.find(p => p.id === tab.partyId);
        const isInsured =
          party?.roles.includes(PartyRole.INSURED) &&
          !party?.roles.includes(PartyRole.PROPOSER);

        const formTitle = isEntity
          ? t('eApp:documentUpload.documentsOf', {
              label: `${tab.name} (${tab.role})`,
            })
          : t('eApp:documentUpload.documentsOf', {
              label: `${tab.role}: ${tab.name}`,
            });
        const isTabDisabled =
          sequentialUnlock && !isGroupCompleted
            ? tabIndex >
              Math.max(maxCompletedDocumentUploadIndex, activeTab?.index || 0)
            : false;
        if (isInsured && !isSingleInsured) {
          multiInsuredSection.title = tab.role;
          multiInsuredSection.name = tab.key;
          multiInsuredSection.subItems?.push({
            name: String(multiInsuredSection.subItems?.length || 0),
            title: tab.name ?? '',
            content: renderDocumentUploadForm({
              partyId: tab.partyId,
              title: tab.title ?? formTitle,
              items: tab.documents,
              customerSeq: tab.customerSeq,
              customerType: tab.customerType,
            }),
            disabled: isTabDisabled,
          });
        } else {
          sections.push({
            name: tab.key,
            title: tab.role,
            subtitle: tab.name ?? '',
            content: renderDocumentUploadForm({
              partyId: tab.partyId,
              title: tab.title ?? formTitle,
              items: tab.documents,
              customerSeq: tab.customerSeq,
              customerType: tab.customerType,
            }),
            disabled: isTabDisabled,
          });
        }

        return sections;
      },
      [],
    );
    if (
      multiInsuredSection.subItems?.length &&
      multiInsuredSection.subItems.length > 0
    ) {
      sections.push(multiInsuredSection);
    }
    return sections;
  }, [
    caseObj?.parties,
    f2fLevel,
    highlight,
    isEntity,
    shouldRenderF2F,
    space,
    t,
    tabs,
    sequentialUnlock,
    maxCompletedDocumentUploadIndex,
    activeTab?.index,
    isGroupCompleted,
  ]);

  const { flattenPaths, activePathIndex, isLastTabActive } = useMemo(() => {
    const flattenPaths = flattenSectionNameList(sections);
    const activePathIndex = flattenPaths.findIndex(
      path =>
        path ===
        (countryModuleEAppConfig.checkSubmissionReadiness
          ? itemKey
          : activePath),
    );
    const isLastTabActive = activePathIndex === flattenPaths.length - 1;
    return {
      flattenPaths,
      activePathIndex,
      isLastTabActive,
    };
  }, [activePath, itemKey, sections]);

  const onContinue = useCallback(() => {
    GATracking.logCustomEvent('application', {
      action_type: 'eapp_submit_documents',
      application_type: 'F2F',
    });

    if (isLastTabActive && isValid) {
      nextGroup(true);
    } else {
      if (activeTab?.key) {
        if (countryModuleEAppConfig.checkSubmissionReadiness) {
          setActiveItemKey(
            flattenPaths[
              Math.min(activePathIndex + 1, flattenPaths.length - 1)
            ],
          );
        } else {
          setActivePath(
            flattenPaths[
              Math.min(activePathIndex + 1, flattenPaths.length - 1)
            ],
          );
        }
      }
    }
  }, [
    activePathIndex,
    activeTab?.key,
    flattenPaths,
    isLastTabActive,
    isValid,
    nextGroup,
    setActiveItemKey,
  ]);

  // OLD LOGIC: Global incomplete count across all tabs (default behavior)
  const calculateGlobalInvalidIndices = useCallback(() => {
    const indices = [];
    for (let tabIndex = 0; tabIndex < tabs.length; tabIndex++) {
      const tab = tabs[tabIndex];
      const partyFiles =
        caseObj?.files?.filter(f => f.partyId === tab.partyId) || [];
      for (let docIndex = 0; docIndex < tab.documents.length; docIndex++) {
        const doc = tab.documents[docIndex];
        if (
          !doc.optional &&
          partyFiles.filter(f => f.docType === doc.type).length === 0
        ) {
          indices.push({ tabIndex, docIndex });
        }
      }
    }
    return indices;
  }, [caseObj?.files, tabs]);

  // NEW LOGIC: Section-specific incomplete count for currently active section
  const calculateSectionSpecificInvalidIndices = useCallback(() => {
    if (!activeTab) {
      return [];
    }

    const indices = [];
    const key = countryModuleEAppConfig.checkSubmissionReadiness
      ? itemKey
      : activePath;

    // For compound paths like 'insured.0', we need to handle multi-insured sections
    if (key?.includes('.')) {
      const [sectionKey, subIndex] = key.split('.');
      const subIndexNum = Number(subIndex);

      // Find all tabs that belong to this section
      const sectionTabs = tabs.filter(tab => tab.key === sectionKey);

      if (sectionTabs.length > subIndexNum) {
        const currentTab = sectionTabs[subIndexNum];
        const tabIndex = tabs.findIndex(tab => tab === currentTab);

        if (tabIndex !== -1) {
          const partyFiles =
            caseObj?.files?.filter(f => f.partyId === currentTab.partyId) || [];

          for (
            let docIndex = 0;
            docIndex < currentTab.documents.length;
            docIndex++
          ) {
            const doc = currentTab.documents[docIndex];
            if (
              !doc.optional &&
              partyFiles.filter(f => f.docType === doc.type).length === 0
            ) {
              indices.push({ tabIndex, docIndex });
            }
          }
        }
      }
    } else {
      // For simple paths, find the single active tab
      const tabIndex = tabs.findIndex(tab => tab.key === key);

      if (tabIndex !== -1) {
        const currentTab = tabs[tabIndex];
        const partyFiles =
          caseObj?.files?.filter(f => f.partyId === currentTab.partyId) || [];

        for (
          let docIndex = 0;
          docIndex < currentTab.documents.length;
          docIndex++
        ) {
          const doc = currentTab.documents[docIndex];
          if (
            !doc.optional &&
            partyFiles.filter(f => f.docType === doc.type).length === 0
          ) {
            indices.push({ tabIndex, docIndex });
          }
        }
      }
    }

    return indices;
  }, [activeTab, itemKey, activePath, tabs, caseObj?.files]);

  const invalidIndices = useMemo(() => {
    return useSectionSpecificIncompleteCount
      ? calculateSectionSpecificInvalidIndices()
      : calculateGlobalInvalidIndices();
  }, [
    useSectionSpecificIncompleteCount,
    calculateSectionSpecificInvalidIndices,
    calculateGlobalInvalidIndices,
  ]);

  const focusOnIncompleteField = useCallback(() => {
    if (uploaderRef.current && invalidIndices.length) {
      uploaderRef.current?.scrollToItem(
        invalidIndices?.[0]?.tabIndex,
        invalidIndices?.[0]?.docIndex,
      );
    }
  }, [invalidIndices]);

  //Only apply for PH first
  useIncompleteSync(
    isLastTabActive ? !isValid || !isCurrentTabValid : !isCurrentTabValid,
    'documentUpload',
    undefined,
    itemKey as RouteItemKey | undefined,
    country === 'ph',
    country === 'ph',
  );

  const disabled = isLastTabActive
    ? !isValid || !isCurrentTabValid || !arePreviousStepsComplete
    : !isCurrentTabValid;

  const primaryDisabled = disabled || disabledNext;

  // validation on page switching, only for ID
  const { set } = useProgressBarContext();
  const { isReadyToSave, setIsNavigateEnabled } = useFormSubmissionContext();

  useEffect(() => {
    if (!countryModuleEAppConfig.checkSubmissionReadiness) return;
    set(isReadyToSave, 'documentUpload', undefined);
  }, [isReadyToSave, set]);

  useEffect(() => {
    if (!countryModuleEAppConfig.checkSubmissionReadiness) return;
    setIsNavigateEnabled(!primaryDisabled);
    return () => {
      setIsNavigateEnabled(true);
    };
  }, [primaryDisabled, setIsNavigateEnabled]);

  return (
    <Fragment>
      <TabletSections
        items={sections}
        activePath={
          countryModuleEAppConfig.checkSubmissionReadiness
            ? itemKey
            : activePath
        }
        setActivePath={
          countryModuleEAppConfig.checkSubmissionReadiness
            ? setActiveItemKey
            : setActivePath
        }
        isReadyToSave={
          countryModuleEAppConfig.checkSubmissionReadiness
            ? isReadyToSave
            : undefined
        }
      />
      <EAppFooterTablet
        progressLock={progressLock}
        onPrimaryPress={onContinue}
        primaryDisabled={primaryDisabled}
        focusOnIncompleteField={focusOnIncompleteField}
        focusOnIncompleteFieldLabel={t(
          'eApp:documentUpload.footer.focusOnIncompleteField',
        )}
        totalIncompleteRequiredFields={highlight ? invalidIndices.length : 0}
      />
    </Fragment>
  );
}

const ScrollViewContainer = styled(ScrollView)(({ theme: { space } }) => ({
  flex: 1,
  paddingRight: space[8],
}));
