import { useSaveParty } from 'hooks/useParty';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import {
  useGenerateHealthQuestions,
  useGetHealthQuestionByIdManually,
} from 'hooks/useHealthQuestion';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useGetOwb } from '../useGetOwb';
import { shallow } from 'zustand/shallow';
import { useGetActiveCase } from 'hooks/useGetActiveCase';

type TResponse = {
  startGeneratingHealthQuestion: () => void;
  isLoadingHealthQuestions: boolean;
  isError: boolean;
};

const useHealthQuestions = (): TResponse => {
  const { caseObj } = useGetActiveCase();
  const hasGeneratedHealthQuestions = useMemo(
    () =>
      Number(caseObj?.parties?.filter(p => p.uw?.enquiryId).length || 0) > 0,
    [caseObj?.parties],
  );
  const {
    mutateAsync: generateHealthQuestions,
    isLoading: isGeneratingHealthQuestions,
  } = useGenerateHealthQuestions();
  const { saveParty, isLoading: isSavingParty } = useSaveParty();
  const { getOwb, isLoading: isGettingOwb } = useGetOwb();
  const [isError, setIsError] = useState(false);
  const [isGenerating, setGenerating] = useState(false);

  const { subgroupKey } = useEAppProgressBarStore(
    state => ({
      subgroupKey: state.subgroupKey,
    }),
    shallow,
  );

  const {
    isLoading: isGettingHealthQuestions,
    mutateAsync: getHealthQuestions,
  } = useGetHealthQuestionByIdManually();

  const startGeneratingHealthQuestion = useCallback(async () => {
    if (!caseObj) return;
    setIsError(false);
    setGenerating(true);
    try {
      const owbModel = await getOwb();
      const healthQuestion = await generateHealthQuestions(owbModel);
      if (healthQuestion) {
        const parties = healthQuestion?.policy?.parties || [];

        for (const element of parties) {
          let uwType = undefined;
          if (element.role.includes('Owner')) {
            const healthQuestions = await getHealthQuestions(element.enquiryId);
            uwType = healthQuestions.buckets?.find(s => s.name === 'UW_IND')
              ?.max?.value;
          }
          const party = caseObj.parties?.find(p => p.id === element.id);

          if (party) {
            await saveParty({
              ...party,
              uw: {
                ...party.uw,
                enquiryId: element.enquiryId,
                tsarFinancialLife: element.preUWMEAnswers?.tsarFinancialLife,
                uwType,
              },
            });
          }
        }
      }
    } catch (e) {
      setIsError(true);
      console.log(e);
    } finally {
      setGenerating(false);
    }
  }, [caseObj, getOwb, generateHealthQuestions, getHealthQuestions, saveParty]);

  useEffect(() => {
    if (
      subgroupKey === 'healthQuestion' &&
      !hasGeneratedHealthQuestions &&
      !isGenerating &&
      !isError
    ) {
      startGeneratingHealthQuestion();
    }
  }, [
    subgroupKey,
    startGeneratingHealthQuestion,
    hasGeneratedHealthQuestions,
    isGenerating,
    isError,
  ]);

  return {
    startGeneratingHealthQuestion,
    isLoadingHealthQuestions:
      isGettingOwb ||
      isGeneratingHealthQuestions ||
      isSavingParty ||
      isGettingHealthQuestions,
    isError,
  };
};

export default useHealthQuestions;
