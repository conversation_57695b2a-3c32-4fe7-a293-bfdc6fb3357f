import { useTheme } from '@emotion/react';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetModalProps,
  KEYBOARD_BEHAVIOR,
} from '@gorhom/bottom-sheet';
import { BackdropPressBehavior } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop/types';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { BackHandler } from 'react-native';

export const HANDLE_HEIGHT = 29;

export const useBottomSheet = (
  keyboardBehavior?: keyof typeof KEYBOARD_BEHAVIOR,
  backdropPressBehavior?: BackdropPressBehavior,
): Omit<BottomSheetModalProps, 'snapPoints' | 'children'> & {
  ref: React.RefCallback<BottomSheetModal>;
  bottomSheetRef: React.RefObject<BottomSheetModal>;
} => {
  const { space, colors, borderRadius } = useTheme();
  const bottomSheetRef = useRef<BottomSheetModal | null>(null);

  const keyboardShown = useKeyboardShown();
  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => {
      return React.createElement(BottomSheetBackdrop, {
        ...props,
        appearsOnIndex: 0,
        disappearsOnIndex: -1,
        pressBehavior: backdropPressBehavior,
      });
    },
    [backdropPressBehavior],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const sheetStyle = useMemo(
    () => ({
      paddingHorizontal: space[isNarrowScreen ? 3 : 4],
      paddingBottom: space[4],
      paddingTop: 0,
    }),
    [space, isNarrowScreen],
  );
  const backgroundStyle = useMemo(
    () => ({
      borderRadius: borderRadius.large,
    }),
    [borderRadius],
  );
  const handleStyle = useMemo(
    () => ({
      paddingTop: 8,
      paddingBottom: 16,
    }),
    [],
  );
  const handleIndicatorStyle = useMemo(
    () => ({
      backgroundColor: colors.palette.fwdGrey[100],
      width: 40,
      height: 5,
    }),
    [colors],
  );

  useEffect(() => {
    const backAction = () => {
      bottomSheetRef?.current?.dismiss();
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, []);

  const ref = useCallback((ref: BottomSheetModal | null) => {
    if (ref) {
      ref.present();
      bottomSheetRef.current = ref;
    }
  }, []);

  return {
    ref,
    bottomSheetRef,
    handleIndicatorStyle,
    handleStyle,
    backgroundStyle,
    backdropComponent: renderBackdrop,
    keyboardBehavior: keyboardBehavior ?? 'extend',
    keyboardBlurBehavior: 'restore',
    enableHandlePanningGesture: !keyboardShown,
    enableContentPanningGesture: !keyboardShown,
    enablePanDownToClose: true,
    style: sheetStyle,
  };
};
