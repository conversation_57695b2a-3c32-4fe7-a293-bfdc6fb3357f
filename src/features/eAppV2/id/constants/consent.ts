import { PartyRole } from 'types/party';

export const ID_PDPA_LINK = 'https://www.fwd.com.my/privacy-statement/';
export const statements = [
  'consents.statementAndPowerOfAttorney.content.1',
  'consents.statementAndPowerOfAttorney.content.2',
  'consents.statementAndPowerOfAttorney.content.3',
  'consents.statementAndPowerOfAttorney.content.4',
  'consents.statementAndPowerOfAttorney.content.5',
  'consents.statementAndPowerOfAttorney.content.6',
  'consents.statementAndPowerOfAttorney.content.7',
  'consents.statementAndPowerOfAttorney.content.8',
  {
    title: 'consents.statementAndPowerOfAttorney.content.9.title',
    content: [
      'consents.statementAndPowerOfAttorney.content.9.content.1',
      'consents.statementAndPowerOfAttorney.content.9.content.2',
    ],
  },
  'consents.statementAndPowerOfAttorney.content.10',
  {
    title: 'consents.statementAndPowerOfAttorney.content.11.title',
    content: [
      'consents.statementAndPowerOfAttorney.content.11.content.1',
      'consents.statementAndPowerOfAttorney.content.11.content.2',
      'consents.statementAndPowerOfAttorney.content.11.content.3',
      'consents.statementAndPowerOfAttorney.content.11.content.4',
    ],
  },
  'consents.statementAndPowerOfAttorney.content.12',
  {
    title: 'consents.statementAndPowerOfAttorney.content.13.title',
    content: [
      'consents.statementAndPowerOfAttorney.content.13.content.1',
      'consents.statementAndPowerOfAttorney.content.13.content.2',
    ],
  },
];
export const temporaries = [
  'consents.temporaryCoverage.content.1',
  'consents.temporaryCoverage.content.2',
];

export const underwritingDecision = [
  'consents.underwritingDecision.checkBox.content.1',
  'consents.underwritingDecision.checkBox.content.2',
  'consents.underwritingDecision.checkBox.content.3',
];

export interface ClosingAgentQuestion {
  key: string;
  question: string;
  isNoReasonRequired: boolean;
}

export const closingAgentQuestions: ClosingAgentQuestion[] = [
  {
    key: 'identityVerified',
    question: 'consents.closingAgent.question.1',
    isNoReasonRequired: true,
  },
  {
    key: 'clientHealthy',
    question: 'consents.closingAgent.question.2',
    isNoReasonRequired: true,
  },
  {
    key: 'truthfulAnswersEmphasized',
    question: 'consents.closingAgent.question.3',
    isNoReasonRequired: true,
  },
  {
    key: 'sumAssuredAppropriate',
    question: 'consents.closingAgent.question.4',
    isNoReasonRequired: false,
  },
  {
    key: 'incomeDataAccurate',
    question: 'consents.closingAgent.question.5',
    isNoReasonRequired: false,
  },
  {
    key: 'goodFaithConfirmed',
    question: 'consents.closingAgent.question.6',
    isNoReasonRequired: false,
  },
];

export const statementOfTruthContent = [
  {
    title: 'consents.statementOfTruth.content.1.title',
    content: [
      'consents.statementOfTruth.content.1.content.a',
      'consents.statementOfTruth.content.1.content.b',
    ],
  },
  {
    title: 'consents.statementOfTruth.content.2.title',
    content: [
      'consents.statementOfTruth.content.2.content.a',
      'consents.statementOfTruth.content.2.content.b',
      'consents.statementOfTruth.content.2.content.c',
      'consents.statementOfTruth.content.2.content.d',
      'consents.statementOfTruth.content.2.content.e',
      'consents.statementOfTruth.content.2.content.f',
    ],
  },

  {
    title: 'consents.statementOfTruth.content.3.title',
    content: [
      'consents.statementOfTruth.content.3.content.a',
      'consents.statementOfTruth.content.3.content.b',
    ],
  },
];

// Determine which consent array to save based on party role
export const consentFieldMapping: Record<
  PartyRole.PROPOSER | PartyRole.PAYER | PartyRole.RENEWAL_PAYER,
  'proposerConsent' | 'beneficiaryOwnerConsent' | 'payerConsent'
> = {
  [PartyRole.PROPOSER]: 'proposerConsent',
  [PartyRole.PAYER]: 'beneficiaryOwnerConsent',
  [PartyRole.RENEWAL_PAYER]: 'payerConsent',
};
