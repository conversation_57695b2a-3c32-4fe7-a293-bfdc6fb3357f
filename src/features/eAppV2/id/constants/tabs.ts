import { EAppTabs } from 'features/eAppV2/common/hooks/useGetEAppTabs';
import { IDCombinedRouteKey } from 'features/eAppV2/common/types/progressBarTypes';
import DeclarationTablet from '../components/applicationDetails/declaration/Declaration.tablet';
import HealthQuestions, {
  HealthQuestionsPI,
  HealthQuestionsPO,
} from '../components/applicationDetails/healthQuestions/HealthQuestions';
import NominationDetailsPhone from '../components/applicationDetails/others/nominationDetails/NominationDetails.phone';
import OthersInfoTablet from '../components/applicationDetails/others/OthersInfo.tablet';
import PayorDetailsPhone from '../components/applicationDetails/others/payorDetails/PayorDetails.phone';
import RenewalPayorDetailsPhone from '../components/applicationDetails/others/renewalPayorDetails/RenewalPayorDetails.phone';
import PolicyInsuredInfoPhone from '../components/applicationDetails/policyOwner/PolicyInsuredInfo.phone';
import PolicyOwnerInfoPhone from '../components/applicationDetails/policyOwner/PolicyOwnerInfo.phone';
import PolicyOwnerInfoTablet from '../components/applicationDetails/policyOwner/PolicyOwnerInfo.tablet';
import ConsentsTablet from '../components/consents/Consents.tablet';
import TemporaryCoveragePhone from '../components/consents/temporaryCoverage/TemporaryCoverage.phone';

import {
  FatcaPayer,
  FatcaPO,
  FatcaRenewalPayer,
} from '../components/applicationDetails/declaration/fatca/Fatca.phone';
import ClosingAgent from '../components/consents/closingAgent';
import StatementOfTruth from '../components/consents/statementOfTruth';
import StatementPowerOfAttorneyPhone from '../components/consents/statementPowerOfAttorney/StatementPowerOfAttorney.phone';
import UnderwritingDecision from '../components/consents/underwritingDecision';
import DocumentUploadTablet, {
  DocumentUploadPay,
  DocumentUploadPI,
  DocumentUploadPO,
  DocumentUploadRenewalPay,
} from '../components/documentUpload/DocumentUpload';
import Payment from '../components/payment/Payment';
import PaymentSetupTablet from '../components/paymentSetup/PaymentSetup.tablet';
import RenewalPremiumPaymentPhone from '../components/paymentSetup/renewalPremiumPayment/RenewalPremiumPayment.phone';
import WithdrawalPaymentPhone from '../components/paymentSetup/withdrawalPayment/WithdrawalPayment.phone';
import ReviewSummaryPhone from '../components/reviewSummary/ReviewSummary.phone';
import ReviewSummaryTablet from '../components/reviewSummary/ReviewSummary.tablet';

export const IDTabs: EAppTabs<IDCombinedRouteKey> = {
  tablet: [
    {
      route: 'appDetail-policyOwner-',
      component: PolicyOwnerInfoTablet,
    },
    {
      route: 'appDetail-others-',
      component: OthersInfoTablet,
    },
    {
      route: 'appDetail-declaration-',
      component: DeclarationTablet,
    },
    {
      route: 'appDetail-healthQuestion-',
      component: HealthQuestions,
    },
    {
      route: 'consents--',
      component: ConsentsTablet,
    },
    {
      route: 'renewalPaymentSetup--',
      component: PaymentSetupTablet,
    },
    {
      route: 'documentUpload--',
      component: DocumentUploadTablet,
    },
    {
      route: 'review--',
      component: ReviewSummaryTablet,
    },
    {
      route: 'payment--',
      component: Payment,
    },
  ],
  phone: [
    {
      route: 'appDetail-policyOwner-policyOwner',
      component: PolicyOwnerInfoPhone,
    },
    {
      route: 'appDetail-policyOwner-insured',
      component: PolicyInsuredInfoPhone,
    },
    {
      route: 'appDetail-others-payor',
      component: PayorDetailsPhone,
    },
    {
      route: 'appDetail-others-renewalPayor',
      component: RenewalPayorDetailsPhone,
    },
    {
      route: 'appDetail-others-beneficiary',
      component: NominationDetailsPhone,
    },
    {
      route: 'appDetail-declaration-fatca',
      component: FatcaPO,
    },
    {
      route: 'appDetail-declaration-initialFatca',
      component: FatcaPayer,
    },
    {
      route: 'appDetail-declaration-renewalFatca',
      component: FatcaRenewalPayer,
    },
    {
      route: 'appDetail-healthQuestion-policyOwner',
      component: HealthQuestionsPO,
    },
    {
      route: 'appDetail-healthQuestion-insured',
      component: HealthQuestionsPI,
    },
    {
      route: 'documentUpload--policyOwner',
      component: DocumentUploadPO,
    },
    {
      route: 'documentUpload--insured',
      component: DocumentUploadPI,
    },
    {
      route: 'documentUpload--payor',
      component: DocumentUploadPay,
    },
    {
      route: 'documentUpload--renewalPayor',
      component: DocumentUploadRenewalPay,
    },
    {
      route: 'consents--underwritingDecision',
      component: UnderwritingDecision,
    },
    {
      route: 'consents--temporaryCoverage',
      component: TemporaryCoveragePhone,
    },
    {
      route: 'consents--statementOfTruth',
      component: StatementOfTruth,
    },
    {
      route: 'consents--closingAgent',
      component: ClosingAgent,
    },
    {
      route: 'consents--statementAndPowerOfAttorney',
      component: StatementPowerOfAttorneyPhone,
    },
    {
      route: 'renewalPaymentSetup--renewalPremiumPayment',
      component: RenewalPremiumPaymentPhone,
    },
    {
      route: 'renewalPaymentSetup--withdrawalPayment',
      component: WithdrawalPaymentPhone,
    },
    {
      route: 'payment--',
      component: Payment,
    },
    {
      route: 'review--',
      component: ReviewSummaryPhone,
    },
  ],
};
