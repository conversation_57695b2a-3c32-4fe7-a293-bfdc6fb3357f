import { useTheme } from '@emotion/react';
import BasicModal from 'components/BasicModal';
import { OTPCard } from 'components/OTP/OTPCard';
import { Box, Button, Column, H6, LargeBody, Row } from 'cube-ui-components';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { PartyRole } from 'types/party';
import { useSendEAppOTP, useVerifyEAppOTP } from '../hooks/useSendEAppOTP';

import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { CHANNELS } from 'types/channel';

interface ConfirmPopupProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const OTPConfirmPopup: React.FC<ConfirmPopupProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const { t } = useTranslation(['eApp']);
  const { space, colors, borderRadius } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const { caseObj } = useGetActiveCase();

  const policyOwner = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );

  const email = policyOwner?.contacts?.email || '';
  const phoneNumber =
    policyOwner?.contacts?.phones?.find(p => p.type === 'MOBILE')?.number || '';

  const sendOTPMutation = useSendEAppOTP();
  const verifyOTPMutation = useVerifyEAppOTP();

  const [step, setStep] = useState<'confirm' | 'otp'>('confirm');
  const [requestId, setRequestId] = useState<string>('');
  const [otpError, setOtpError] = useState<string | null>(null);
  const [disabledResend, setDisabledResend] = useState(false);

  const { data: agentProfile } = useGetAgentProfile();

  const handleSendOTP = async () => {
    if (!phoneNumber || !email) {
      console.error('Phone number or email is missing');
      return;
    }

    try {
      setOtpError(null);
      const response = await sendOTPMutation.mutateAsync({
        channel: agentProfile?.channel || CHANNELS.AGENCY,
        refNum: caseObj?.id || '',
        phone: phoneNumber,
        email: email,
      });

      if (response.status === 'SUCCESS') {
        setRequestId(response?.requestId);
        setStep('otp');
      } else {
        setOtpError(response?.message);
        console.error('Failed to send OTP:', response?.message);
      }
    } catch (error) {
      console.error('Error sending OTP:', error);
      setOtpError('Failed to send OTP. Please try again.');
    }
  };

  const handleResendOTP = async () => {
    await handleSendOTP();
    setDisabledResend(true);
    // Re-enable resend after 60 seconds
    setTimeout(() => setDisabledResend(false), 60000);
  };

  const handleVerifyAndContinue = async (otpInput: string | undefined) => {
    const otp = otpInput || '';
    if (!requestId || !otp) {
      console.error('Request ID or OTP is missing');
      return;
    }

    try {
      setOtpError(null);
      const response = await verifyOTPMutation.mutateAsync({
        channel: agentProfile?.channel || CHANNELS.AGENCY,
        requestId: requestId,
        otp: otp,
      });
      if (response?.status === 'SUCCESS') {
        console.log('OTP verified successfully');
        // Reset state before calling success callback
        setStep('confirm');
        setRequestId('');
        setOtpError(null);
        setDisabledResend(false);

        if (onSuccess) {
          onSuccess();
        } else {
          handleClose();
        }
      } else {
        setOtpError(response?.message);
        console.error('OTP verification failed:', response?.message);
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      setOtpError('Failed to verify OTP. Please try again.');
    }
  };

  const handleClose = () => {
    setStep('confirm'); // Reset to confirm step
    setRequestId('');
    setOtpError(null);
    setDisabledResend(false);
    onClose();
  };

  return (
    <BasicModal visible={visible} onRequestClose={handleClose}>
      {step === 'confirm' ? (
        <AnimatedViewWrapper>
          <Column
            gap={space[4]}
            mx={isTabletMode ? space[12] : space[6]}
            alignSelf="center"
            bgColor={colors.palette.white}
            p={isTabletMode ? space[12] : space[6]}
            borderRadius={borderRadius['large']}>
            <H6 fontWeight="bold">{t('eApp:confirmPopup.title')}</H6>
            <LargeBody>{t('eApp:confirmPopup.introText')}</LargeBody>
            <LargeBody>
              {t('eApp:confirmPopup.securityText', {
                phoneNumber,
              })}
            </LargeBody>
            <Box>
              <LargeBody>{t('eApp:confirmPopup.stepsIntro')}</LargeBody>
              <Box h={space[2]} />
              <Row alignItems="flex-start" gap={space[2]}>
                <Box
                  mt={space[2]}
                  w={6}
                  h={6}
                  backgroundColor={colors.palette.black}
                  borderRadius={borderRadius['full']}
                />
                <LargeBody>{t('eApp:confirmPopup.step1')}</LargeBody>
              </Row>
              <Row alignItems="flex-start" gap={space[2]}>
                <Box
                  mt={space[2]}
                  w={6}
                  h={6}
                  backgroundColor={colors.palette.black}
                  borderRadius={borderRadius['full']}
                />
                <LargeBody>{t('eApp:confirmPopup.step2')}</LargeBody>
              </Row>
              <Row alignItems="flex-start" gap={space[2]}>
                <Box
                  mt={space[2]}
                  w={6}
                  h={6}
                  backgroundColor={colors.palette.black}
                  borderRadius={borderRadius['full']}
                />
                <LargeBody>{t('eApp:confirmPopup.step3')}</LargeBody>
              </Row>
            </Box>
            <Box
              borderRadius={8}
              backgroundColor={colors.palette.fwdOrange[5]}
              p={space[4]}>
              <LargeBody fontWeight="medium">
                {t('eApp:confirmPopup.warning')}
              </LargeBody>
            </Box>
            {otpError && (
              <Box
                borderRadius={8}
                backgroundColor={colors.palette.fwdOrange[5]}
                p={space[4]}>
                <LargeBody fontWeight="medium" color={colors.error}>
                  {otpError}
                </LargeBody>
              </Box>
            )}

            <Row mt={space[6]} gap={space[4]} justifyContent="center">
              <Button
                style={{ minWidth: 168 }}
                variant="secondary"
                text={t('eApp:cancel')}
                onPress={handleClose}
              />
              <Button
                style={{ minWidth: 168 }}
                text={t('eApp:confirmPopup.sendOTP')}
                onPress={handleSendOTP}
                loading={sendOTPMutation.isLoading}
                disabled={sendOTPMutation.isLoading}
              />
            </Row>
          </Column>
        </AnimatedViewWrapper>
      ) : (
        <OTPCard
          onClose={handleClose}
          phoneNumber={phoneNumber}
          onVerifyAndContinue={handleVerifyAndContinue}
          onResend={handleResendOTP}
          isVerifyLoading={verifyOTPMutation.isLoading}
          isResendLoading={sendOTPMutation.isLoading}
          disabledResend={disabledResend}
          errorVerify={otpError}
          errorSend={otpError}
        />
      )}
    </BasicModal>
  );
};

export default OTPConfirmPopup;
