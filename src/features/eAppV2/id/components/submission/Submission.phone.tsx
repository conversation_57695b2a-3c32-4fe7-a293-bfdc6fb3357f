import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { fwdLoadingAnimation } from 'assets/images';
import {
  Box,
  Center,
  H7,
  Icon,
  LargeBody,
  Row,
  H6,
  H5,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { Image, ScrollView, View } from 'react-native';
import { formatCurrencyWithMask, i18n } from 'utils';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getProductName } from 'features/eAppV2/common/utils/eAppFormat';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import useSubmission from './useSubmission';

interface RowInfoFieldProps {
  isHighlight?: boolean;
  isMini?: boolean;
  isVertical?: boolean;
  fieldName?: string | null;
  fieldValue?: string | number | null;
}

const RowInfoField = ({ fieldName, fieldValue }: RowInfoFieldProps) => {
  const { colors, space } = useTheme();
  return (
    <Row gap={space[2]}>
      <Box flex={1}>
        <H7 color={colors.palette.fwdGreyDarkest}>{fieldName}</H7>
      </Box>
      <Box flex={1}>
        <H7 color={colors.secondary}>{fieldValue}</H7>
      </Box>
    </Row>
  );
};

const SubmissionPhone = () => {
  const { colors, sizes, space } = useTheme();
  const { t } = useTranslation(['eApp', 'common']);

  const {
    shouldShowWaitingForApproval,
    policyOwner,
    caseObj,
    loading,
    insuredName,
    dateSubmitted,
    productName,
    basicPlan,
    paymentMode,
    basicAnnualPremium,
    initialPremium,
    riderPairs,
    riders,
    navigation,
    currency,
  } = useSubmission();

  return (
    <>
      {loading ? (
        <Center flex={1} backgroundColor={colors.background}>
          <Image
            style={{
              width: sizes[50],
              height: sizes[50],
            }}
            resizeMode="contain"
            source={fwdLoadingAnimation}
          />
        </Center>
      ) : (
        <Box flex={1} bgColor={colors.background}>
          <Wrapper edges={['top']}>
            <Box alignItems="center" mb={space[3]}>
              <Icon.TickCircle size={42} />
              <H6 fontWeight="bold">{t('eApp:payment.congratulation')}</H6>
            </Box>
            <Box alignItems="center" mb={space[3]} mx={space[4]}>
              <H7 style={{ textAlign: 'center' }}>
                {t('eApp:application.submit.note')}
              </H7>
            </Box>
            <Box flex={1} bgColor={colors.palette.fwdOrange[5]} width={'100%'}>
              <Box padding={space[4]}>
                <ScrollView showsVerticalScrollIndicator={false}>
                  <H7 color="#333">{t('eApp:payment.yourPolicySummary')}</H7>
                  <Row mt={space[2]} mb={space[3]}>
                    <Icon.DocumentCertified
                      size={24}
                      fill={colors.palette.fwdDarkGreen[100]}
                    />
                    <H6 fontWeight="bold">{productName}</H6>
                  </Row>

                  <H7 color={colors.palette.fwdGreyDarkest}>
                    {t('eApp:review.sumAssured')}
                  </H7>
                  <Row mt={space[1]} gap={2} mb={space[3]}>
                    <LargeBody color={colors.primary}>{currency}</LargeBody>
                    <H5 fontWeight="bold" color={colors.primary}>
                      {formatCurrencyWithMask(basicPlan?.sumAssured, 2)}
                    </H5>
                  </Row>

                  <Box gap={space[2]}>
                    <RowInfoField
                      fieldName={t('eApp:review.policyTerm')}
                      fieldValue={t('common:withYears', {
                        year: basicPlan?.policyTerm ?? 0,
                      })}
                    />
                    <RowInfoField
                      fieldName={t('eApp:review.premiumTerm')}
                      fieldValue={t('common:withYears', {
                        year: basicPlan?.premiumTerm ?? 0,
                      })}
                    />
                    <RowInfoField
                      fieldName={t('eApp:review.paymentMode')}
                      fieldValue={paymentMode}
                    />

                    <RowInfoField
                      fieldName={t('eApp:review.basicAnnualPremium')}
                      fieldValue={`${currency} ${formatCurrencyWithMask(
                        basicAnnualPremium,
                        2,
                      )}`}
                    />
                    <RowInfoField
                      fieldName={t('eApp:review.initialPremium')}
                      fieldValue={`${currency} ${formatCurrencyWithMask(
                        initialPremium,
                        2,
                      )}`}
                    />
                  </Box>

                  <Divider />
                  <Box gap={space[2]}>
                    <H7 fontWeight="bold" color="#333">
                      {t('eApp:payment.application.details')}
                    </H7>
                    <RowInfoField
                      fieldName={t('eApp:application.submit.policyNumber')}
                      fieldValue={caseObj?.application?.policyNum}
                      isHighlight={true}
                      isVertical={true}
                    />
                    <RowInfoField
                      fieldName={t('eApp:bar.policyOwner')}
                      fieldValue={
                        policyOwner?.person?.name?.firstName ??
                        policyOwner?.entity?.name ??
                        ''
                      }
                      isVertical={true}
                    />
                    <RowInfoField
                      fieldName={t('eApp:bar.insured')}
                      fieldValue={insuredName}
                      isVertical={true}
                    />
                    <RowInfoField
                      fieldName={t('eApp:email')}
                      fieldValue={policyOwner?.contacts?.email ?? ''}
                      isVertical={true}
                    />
                    <RowInfoField
                      fieldName={t('eApp:application.submit.dateSubmitted')}
                      fieldValue={dateSubmitted}
                      isVertical={true}
                    />
                  </Box>

                  {riderPairs.length > 0 && <Divider />}
                  {riderPairs.length > 0 && (
                    <Box gap={space[2]}>
                      <H7 fontWeight="bold" color="#333">
                        {t('eApp:review.coverage')}
                      </H7>
                      <Row gap={space[2]}>
                        <Box flex={1} />
                        <Box flex={1}>
                          <H7 color={colors.secondary}>
                            {t('eApp:review.sumAssured')}
                          </H7>
                        </Box>
                        <Box flex={1}>
                          <H7 color={colors.secondary}>
                            {t('eApp:review.premium')}
                          </H7>
                        </Box>
                      </Row>
                      <Box gap={space[2]}>
                        {riders.map(rider => {
                          return (
                            <Row
                              key={`${rider.pid}`}
                              gap={space[2]}
                              justifyContent="space-between">
                              <Box flex={1}>
                                <H7 color={colors.palette.fwdGreyDarkest}>
                                  {getProductName(
                                    rider.productName,
                                    i18n.language,
                                  )}
                                </H7>
                              </Box>
                              <Box flex={1}>
                                <H7 color={colors.secondary}>
                                  {typeof rider.sumAssured === 'number'
                                    ? `${currency} ${formatCurrencyWithMask(
                                        rider.sumAssured,
                                        2,
                                      )}`
                                    : '--'}
                                </H7>
                              </Box>
                              <Box flex={1}>
                                <H7 color={colors.secondary}>
                                  {typeof rider.annualPrem === 'number'
                                    ? `${currency} ${formatCurrencyWithMask(
                                        rider.annualPrem,
                                        2,
                                      )}`
                                    : '--'}
                                </H7>
                              </Box>
                            </Row>
                          );
                        })}
                      </Box>
                    </Box>
                  )}
                </ScrollView>
              </Box>
            </Box>
          </Wrapper>

          <Box p={space[4]} bgColor={colors.background}>
            <H7
              color={colors.palette.fwdDarkGreen[50]}
              style={{ textAlign: 'center' }}>
              {t('eApp:application.submit.hint')}
            </H7>
          </Box>
          <EAppFooterPhone
            onPrimaryPress={() =>
              navigation.navigate('Main', {
                screen: 'Home',
              })
            }
            primaryDisabled={false}
            primaryLabel={t('eApp:application.submit.back')}>
            {shouldShowWaitingForApproval && (
              <Row gap={space[1]} alignItems="flex-start">
                <Icon.InfoCircle size={20} fill={colors.primary} />
                <H7 style={{ flex: 1 }}>
                  {t('eApp:submission.waitForApproval')}
                </H7>
              </Row>
            )}
          </EAppFooterPhone>
        </Box>
      )}
    </>
  );
};

export default SubmissionPhone;

const Wrapper = styled(SafeAreaView)(({ theme: { space } }) => ({
  flex: 1,
  alignContent: 'center',
  justifyContent: 'center',
  alignItems: 'center',
  marginTop: space[12],
  backgroundColor: 'white',
}));

export const Divider = styled(View)(({ theme: { space, colors } }) => {
  return {
    height: 1,
    backgroundColor: '#D9D9D9',
    marginVertical: space[3],
  };
});
