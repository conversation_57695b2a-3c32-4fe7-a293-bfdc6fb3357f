import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { fwdLoadingAnimation } from 'assets/images';
import {
  Body,
  Box,
  Center,
  H4,
  H7,
  Icon,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { Image, ScrollView, View } from 'react-native';
import { formatCurrencyWithMask, i18n } from 'utils';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getProductName } from 'features/eAppV2/common/utils/eAppFormat';
import SubmissionCoverageText from './SubmissionCoverageText';
import FWDInsuranceLogo from './FWDInsuranceLogo';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import useSubmission from './useSubmission';

interface RowInfoFieldProps {
  isHighlight?: boolean;
  isMini?: boolean;
  isVertical?: boolean;
  fieldName?: string | null;
  fieldValue?: string | number | null;
}

const RowInfoField = ({
  fieldName,
  fieldValue,
  isHighlight,
  isMini,
  isVertical,
}: RowInfoFieldProps) => {
  const { colors, space } = useTheme();
  return (
    <Box flex={isVertical ? 0 : 1}>
      <Body color={colors.placeholder} fontWeight="bold">
        {fieldName}
      </Body>
      <Box height={space[isMini ? 1 : 2]} />
      {isMini ? (
        <LargeBody color={isHighlight ? colors.primary : colors.secondary}>
          {fieldValue}
        </LargeBody>
      ) : (
        <LargeLabel color={isHighlight ? colors.primary : colors.secondary}>
          {fieldValue}
        </LargeLabel>
      )}
    </Box>
  );
};

const SubmissionTablet = () => {
  const { colors, sizes, space, borderRadius } = useTheme();
  const { t } = useTranslation(['eApp', 'common']);

  const {
    shouldShowWaitingForApproval,
    policyOwner,
    caseObj,
    loading,
    insuredName,
    dateSubmitted,
    productName,
    basicPlan,
    paymentMode,
    basicAnnualPremium,
    initialPremium,
    riderPairs,
    riders,
    navigation,
    currency,
  } = useSubmission();

  return (
    <>
      {loading ? (
        <Center flex={1} backgroundColor={colors.background}>
          <Image
            style={{
              width: sizes[50],
              height: sizes[50],
            }}
            resizeMode="contain"
            source={fwdLoadingAnimation}
          />
        </Center>
      ) : (
        <Box flex={1} bgColor={colors.surface}>
          <Wrapper edges={['top']}>
            <Row alignItems="center" gap={space[3]} mb={space[3]}>
              <Center>
                <Box
                  pos="absolute"
                  width={sizes[16]}
                  height={sizes[16]}
                  bgColor={colors.background}
                  borderRadius={borderRadius.full}
                />
                <Icon.TickCircleFill size={sizes[18] - 1} />
              </Center>
              <H4 fontWeight="bold">{t('eApp:payment.congratulation')}</H4>
            </Row>
            <H7 fontWeight="normal">{t('eApp:application.submit.note')}</H7>
            <Content alignSelf="stretch" mt={space[6]}>
              <Box padding={space[6]} width={210}>
                <ScrollView showsVerticalScrollIndicator={false}>
                  <Box gap={space[6]}>
                    <RowInfoField
                      fieldName={t('eApp:application.submit.policyNumber')}
                      fieldValue={caseObj?.application?.policyNum}
                      isHighlight={true}
                      isVertical={true}
                    />
                    <RowInfoField
                      fieldName={t('eApp:bar.policyOwner')}
                      fieldValue={
                        policyOwner?.person?.name?.firstName ??
                        policyOwner?.entity?.name ??
                        ''
                      }
                      isVertical={true}
                    />
                    <RowInfoField
                      fieldName={t('eApp:bar.insured')}
                      fieldValue={insuredName}
                      isVertical={true}
                    />
                    <RowInfoField
                      fieldName={t('eApp:application.submit.dateSubmitted')}
                      fieldValue={dateSubmitted}
                      isVertical={true}
                    />
                    <RowInfoField
                      fieldName={t('eApp:email')}
                      fieldValue={policyOwner?.contacts?.email ?? ''}
                      isVertical={true}
                    />
                  </Box>
                </ScrollView>
              </Box>
              <ApplicationContent>
                <Row
                  mb={space[6]}
                  alignItems="center"
                  justifyContent="space-between">
                  <Box flex={1}>
                    <H4 fontWeight="bold">{productName}</H4>
                  </Box>
                  <FWDInsuranceLogo width={86} height={44} />
                </Row>
                <ScrollView showsVerticalScrollIndicator={false}>
                  <Row gap={space[4]}>
                    <SubmissionCoverageText
                      label={t('eApp:review.sumAssured')}
                      currency={currency}
                      value={formatCurrencyWithMask(basicPlan?.sumAssured, 2)}
                      isNormal={true}
                    />
                  </Row>
                  <Divider />

                  <Row gap={space[4]}>
                    <RowInfoField
                      fieldName={t('eApp:review.policyTerm')}
                      fieldValue={t('common:withYears', {
                        year: basicPlan?.policyTerm ?? 0,
                      })}
                    />
                    <RowInfoField
                      fieldName={t('eApp:review.premiumTerm')}
                      fieldValue={t('common:withYears', {
                        year: basicPlan?.premiumTerm ?? 0,
                      })}
                    />
                    <RowInfoField
                      fieldName={t('eApp:review.paymentMode')}
                      fieldValue={paymentMode}
                    />
                  </Row>
                  <Divider />
                  <Row gap={space[4]}>
                    <RowInfoField
                      fieldName={t('eApp:review.basicAnnualPremium')}
                      fieldValue={`${currency} ${formatCurrencyWithMask(
                        basicAnnualPremium,
                        2,
                      )}`}
                    />
                    <RowInfoField
                      fieldName={t('eApp:review.initialPremium')}
                      fieldValue={`${currency} ${formatCurrencyWithMask(
                        initialPremium,
                        2,
                      )}`}
                    />
                    <Box flex={1} />
                  </Row>
                  {riderPairs.length > 0 && <Divider />}
                  {riderPairs.length > 0 && (
                    <Box>
                      <Row flex={1} justifyContent="space-between">
                        <Box flex={1}>
                          <Body color={colors.placeholder} fontWeight="bold">
                            {t('eApp:review.coverage')}
                          </Body>
                        </Box>
                        <Row
                          gap={space[6]}
                          flex={1}
                          alignContent="flex-start"
                          justifyContent="space-between"
                          alignItems="flex-start">
                          <Box flex={1} alignContent="flex-start">
                            <Body color={colors.placeholder} fontWeight="bold">
                              {t('eApp:review.sumAssured')}
                            </Body>
                          </Box>
                          <Box flex={1} alignContent="flex-start">
                            <Body color={colors.placeholder} fontWeight="bold">
                              {t('eApp:review.premium')}
                            </Body>
                          </Box>
                        </Row>
                      </Row>
                      <Box marginTop={space[2]} gap={space[2]}>
                        {riders.map(rider => {
                          return (
                            <Row
                              flex={1}
                              justifyContent="space-between"
                              key={`${rider.pid}`}>
                              <Box flex={1}>
                                <Body fontWeight="normal">
                                  {getProductName(
                                    rider.productName,
                                    i18n.language,
                                  )}
                                </Body>
                              </Box>
                              <Row
                                flex={1}
                                gap={space[6]}
                                alignContent="flex-start"
                                justifyContent="space-between"
                                alignItems="flex-start">
                                <Box flex={1} alignContent="flex-start">
                                  <Body fontWeight="normal">
                                    {typeof rider.sumAssured === 'number'
                                      ? `${currency} ${formatCurrencyWithMask(
                                          rider.sumAssured,
                                          2,
                                        )}`
                                      : '--'}
                                  </Body>
                                </Box>
                                <Box flex={1} alignContent="flex-start">
                                  <Body fontWeight="normal">
                                    {typeof rider.annualPrem === 'number'
                                      ? `${currency} ${formatCurrencyWithMask(
                                          rider.annualPrem,
                                          2,
                                        )}`
                                      : '--'}
                                  </Body>
                                </Box>
                              </Row>
                            </Row>
                          );
                        })}
                      </Box>
                    </Box>
                  )}
                </ScrollView>
              </ApplicationContent>
            </Content>

            {shouldShowWaitingForApproval && (
              <Row
                alignSelf="stretch"
                mt={space[3]}
                borderRadius={borderRadius.small}
                backgroundColor={colors.primaryVariant3}
                p={space[4]}
                gap={space[1]}
                alignItems="center">
                <Icon.InfoCircle size={18} fill={colors.primary} />
                <LargeBody>{t('eApp:submission.waitForApproval')}</LargeBody>
              </Row>
            )}

            <Box height={shouldShowWaitingForApproval ? space[6] : space[2]} />
            <LargeBody color={colors.secondaryVariant}>
              {t('eApp:application.submit.hint')}
            </LargeBody>
          </Wrapper>
          <EAppFooterTablet
            onPrimaryPress={() =>
              navigation.navigate('Main', {
                screen: 'Home',
              })
            }
            primaryDisabled={false}
            primaryLabel={t('eApp:application.submit.back')}
          />
        </Box>
      )}
    </>
  );
};

export default SubmissionTablet;

const Wrapper = styled(SafeAreaView)(({ theme: { space } }) => ({
  flex: 1,
  alignContent: 'center',
  justifyContent: 'center',
  alignItems: 'center',
  marginTop: space[12],
  marginBottom: space[10],
  marginHorizontal: space[30],
}));

const Content = styled(Box)(({ theme: { colors, borderRadius } }) => ({
  backgroundColor: colors.palette.fwdGrey[20],
  borderRadius: borderRadius['large'],
  flexDirection: 'row',
  flexShrink: 1,
}));

const ApplicationContent = styled(View)(
  ({ theme: { colors, borderRadius, space } }) => ({
    backgroundColor: colors.background,
    borderTopRightRadius: borderRadius['large'],
    borderBottomRightRadius: borderRadius['large'],
    flex: 1,
    padding: space[6],
  }),
);

export const Divider = styled(View)(({ theme: { space, colors } }) => {
  return {
    height: 1,
    backgroundColor: colors.palette.fwdGrey[100],
    marginVertical: space[4],
  };
});
