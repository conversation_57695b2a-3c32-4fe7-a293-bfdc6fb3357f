import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, H6 } from 'cube-ui-components';
import * as ScreenOrientation from 'expo-screen-orientation';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import UwmeDecisionModal from 'features/eAppV2/common/components/uwmeDecision/UwmeDecisionModal';
import useUWMEDecision from 'features/eAppV2/common/hooks/useUWMEDecision';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import useToggle from 'hooks/useToggle';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';
import { PartyRole } from 'types/party';
import GATracking from 'utils/helper/gaTracking';
import Signature from '../signature/Signature';

import ConfirmToProgressModal from 'features/eAppV2/common/components/uwmeDecision/ConfirmToProgressModal';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';
import OTPConfirmPopup from '../OTPConfirmPopup';
import ApplicationSummaryPhone from './sections/applicationSummary/ApplicationSummary.phone';
import BeneficiaryAllocationSummary from './sections/beneficiaryAllocationSummary/BeneficiaryAllocationSummary';
import ConsentAndDeclarationSummary from './sections/consentAndDeclarationSummary/ConsentAndDeclarationSummary';
import DocumentsSummary from './sections/documentsSummary/DocumentsSummary.tablet';
import HealthQuestionsSummary from './sections/healthQuestionsSummary/HealthQuestionsSummary';
import PaymentSetupReviewPhone from './sections/paymentSetup/PaymentSetupReview.phone';
import PdfSummary from './sections/pdfSummary/PdfSummary';
import PersonalInformationSummary from './sections/personalInformationSummary/PersonalInformationSummary';

export default function ReviewSummaryPhone() {
  const { t } = useTranslation(['eApp']);
  const { colors } = useTheme();
  const [signatureVisible, showSignature, hideSignature] = useToggle();
  const [confirmVisible, showConfirmModal, hideConfirmModal] = useToggle();
  const [decisionVisible, showDecisionModal, hideDecisionModal] = useToggle();
  const [otpModalVisible, setOtpModalVisible] = useState(false);
  const { initialize, decisionState, isLoading } =
    useUWMEDecision(showDecisionModal);
  const { disabledForm } = useDisabledEAppForm();

  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);
  const { caseObj } = useGetActiveCase();
  const hasBeneficiary = useMemo(
    () =>
      Boolean(
        caseObj?.parties?.find(p => p.roles.includes(PartyRole.BENEFICIARY)),
      ),
    [caseObj?.parties],
  );

  const onNext = useCallback(() => {
    if (disabledForm) {
      initialize();
    } else {
      showConfirmModal();
    }
  }, [initialize, disabledForm, showConfirmModal]);

  const onSuccessOTPVerified = () => {
    setOtpModalVisible(false);
    initialize();
  };

  const showOTPModal = () => {
    setOtpModalVisible(true);
  };

  return (
    <Box flex={1} backgroundColor={colors.surface}>
      <ScrollView keyboardDismissMode="on-drag" nestedScrollEnabled>
        <Container>
          <Title fontWeight="bold">{t('eApp:review.reviewSummary')}</Title>
          <ApplicationSummaryPhone />
          <PersonalInformationSummary />
          {hasBeneficiary && <BeneficiaryAllocationSummary />}
          <HealthQuestionsSummary />
          <ConsentAndDeclarationSummary />
          <PaymentSetupReviewPhone />
          <DocumentsSummary />
          <PdfSummary />
        </Container>
      </ScrollView>
      <EAppFooterPhone
        progressLock="review--"
        primaryLoading={isLoading}
        onPrimaryPress={showOTPModal}
      />
      {decisionVisible && (
        <UwmeDecisionModal
          decisionState={decisionState}
          decisions={decisionState?.scenario || 'Standard'}
          onClose={hideDecisionModal}
          onPress={async () => {
            hideDecisionModal();
            await ScreenOrientation.lockAsync(
              ScreenOrientation.OrientationLock.LANDSCAPE_LEFT,
            );
            showSignature();
          }}
        />
      )}
      {confirmVisible && (
        <ConfirmToProgressModal
          onPress={() => {
            hideConfirmModal();
            initialize();
          }}
          onDismiss={hideConfirmModal}
          visible={confirmVisible}
        />
      )}
      <Signature
        visible={signatureVisible}
        onDismiss={async () => {
          await ScreenOrientation.lockAsync(
            ScreenOrientation.OrientationLock.PORTRAIT_UP,
          );
          hideSignature();
        }}
        onFinish={async () => {
          GATracking.logCustomEvent('application', {
            action_type: 'eapp_submit_sign',
          });

          await ScreenOrientation.lockAsync(
            ScreenOrientation.OrientationLock.PORTRAIT_UP,
          );
          hideSignature();
          nextGroup(true);
        }}
        startingIndex={0}
      />

      {otpModalVisible && (
        <OTPConfirmPopup
          visible={otpModalVisible}
          onClose={() => setOtpModalVisible(false)}
          onSuccess={onSuccessOTPVerified}
        />
      )}
    </Box>
  );
}

const Container = styled.View(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
    paddingVertical: theme.space[4],
  };
});

const Title = styled(H6)(({ theme }) => ({
  color: theme.colors.onSurface,
}));
