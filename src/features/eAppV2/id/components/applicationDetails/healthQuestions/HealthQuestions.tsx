import HealthQuestionsBasePhone from 'features/eAppV2/common/components/healthQuestions/HealthQuestionsBase.phone';
import HealthQuestionsBaseTablet, {
  HealthQuestionsBaseTabletProps,
} from 'features/eAppV2/common/components/healthQuestions/HealthQuestionsBase.tablet';
import { getPartiesByRole } from 'features/eAppV2/common/utils/partyUtils';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import useSyncActivePath from 'features/eAppV2/id/hooks/useSyncActivePath';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useCrossSellingValidation } from 'hooks/useCrossSellingValidation';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PartyRole } from 'types/party';
import { shallow } from 'zustand/shallow';

export default function HealthQuestions() {
  const { t } = useTranslation(['eApp']);

  const { caseObj } = useGetActiveCase();
  const { isPIEqualPO, policyOwnerId, insuredId } = useMemo(() => {
    const policyOwner = getPartiesByRole(caseObj, PartyRole.PROPOSER)?.[0];
    const insured = getPartiesByRole(caseObj, PartyRole.INSURED)?.[0];
    return {
      isPIEqualPO: policyOwner?.id === insured?.id,
      policyOwnerId: policyOwner?.id,
      insuredId: insured?.id,
    };
  }, [caseObj]);

  const { subgroup } = useEAppProgressBarStore(
    state => ({
      subgroup: state.subgroup,
    }),
    shallow,
  );

  const {
    isCompletedHealthQuestionPolicyOwner,
    setCompletedHealthQuestionPolicyOwner,
    setCompletedHealthQuestionInsured,
  } = useEAppStore(
    state => ({
      isCompletedHealthQuestionPolicyOwner:
        state.isCompletedHealthQuestionPolicyOwner,
      setCompletedHealthQuestionPolicyOwner:
        state.setCompletedHealthQuestionPolicyOwner,
      setCompletedHealthQuestionInsured:
        state.setCompletedHealthQuestionInsured,
    }),
    shallow,
  );

  const isSubgroupCompleted = Boolean(subgroup?.completed);

  const isPIHealthQuestionDisabled = useMemo(
    () => !(isSubgroupCompleted || isCompletedHealthQuestionPolicyOwner),
    [isSubgroupCompleted, isCompletedHealthQuestionPolicyOwner],
  );
  useSyncActivePath('appDetail', 'healthQuestion', 'policyOwner');

  const tabs = useMemo(
    () =>
      [
        {
          key: 'policyOwner',
          partyId: policyOwnerId,
          role: t('eApp:bar.policyOwner'),
          onNext: () => setCompletedHealthQuestionPolicyOwner(true),
        },
        !isPIEqualPO && {
          key: 'insured',
          partyId: insuredId,
          role: t('eApp:bar.insured'),
          disabled: isPIHealthQuestionDisabled,
          onNext: () => setCompletedHealthQuestionInsured(true),
        },
      ].filter(Boolean) as HealthQuestionsBaseTabletProps['tabs'],
    [
      insuredId,
      isPIEqualPO,
      policyOwnerId,
      t,
      isPIHealthQuestionDisabled,
      setCompletedHealthQuestionPolicyOwner,
      setCompletedHealthQuestionInsured,
    ],
  );

  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();

  const { mutateAsync: crossSellingValidation } = useCrossSellingValidation();

  useEffect(() => {
    const updateCrossSellingFlag = async () => {
      if (caseObj?.id) {
        const crossSellingValidationData = await crossSellingValidation({
          caseId: caseObj?.id,
        });

        if (crossSellingValidationData?.flag) {
          await saveApplication({
            caseId: caseObj?.id,
            data: {
              ...caseObj.application,
              crossSelling: {
                ...caseObj.application?.crossSelling,
                isCrossSellingFlag: crossSellingValidationData.flag,
              },
            },
          });
        }
      }
    };

    updateCrossSellingFlag();
  }, [caseObj?.id, crossSellingValidation, saveApplication]);

  return (
    <HealthQuestionsBaseTablet
      progressLock="appDetail-healthQuestion"
      tabs={tabs}
    />
  );
}

export function HealthQuestionsPO() {
  const { t } = useTranslation(['eApp']);
  const { caseObj } = useGetActiveCase();
  const policyOwnerId = useMemo(
    () => getPartiesByRole(caseObj, PartyRole.PROPOSER)?.[0]?.id,
    [caseObj],
  );

  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();

  const { mutateAsync: crossSellingValidation } = useCrossSellingValidation();

  const { setCompletedHealthQuestionPolicyOwner } = useEAppStore(
    state => ({
      setCompletedHealthQuestionPolicyOwner:
        state.setCompletedHealthQuestionPolicyOwner,
    }),
    shallow,
  );

  useEffect(() => {
    const updateCrossSellingFlag = async () => {
      if (caseObj?.id) {
        const crossSellingValidationData = await crossSellingValidation({
          caseId: caseObj?.id,
        });

        if (crossSellingValidationData?.flag) {
          await saveApplication({
            caseId: caseObj?.id,
            data: {
              ...caseObj.application,
              crossSelling: {
                ...caseObj.application?.crossSelling,
                isCrossSellingFlag: crossSellingValidationData.flag,
              },
            },
          });
        }
      }
    };

    updateCrossSellingFlag();
  }, [caseObj?.id, crossSellingValidation, saveApplication]);

  return (
    <HealthQuestionsBasePhone
      progressLock="appDetail-healthQuestion-policyOwner"
      activePartyId={policyOwnerId || ''}
      role={t('eApp:bar.policyOwner')}
      onNext={() => setCompletedHealthQuestionPolicyOwner(true)}
    />
  );
}

export function HealthQuestionsPI() {
  const { t } = useTranslation(['eApp']);
  const { caseObj } = useGetActiveCase();
  const insuredId = useMemo(
    () => getPartiesByRole(caseObj, PartyRole.INSURED)?.[0]?.id,
    [caseObj],
  );

  const { setCompletedHealthQuestionInsured } = useEAppStore(
    state => ({
      setCompletedHealthQuestionInsured:
        state.setCompletedHealthQuestionInsured,
    }),
    shallow,
  );

  return (
    <HealthQuestionsBasePhone
      progressLock="appDetail-healthQuestion-insured"
      activePartyId={insuredId || ''}
      role={t('eApp:bar.insured')}
      onNext={() => setCompletedHealthQuestionInsured(true)}
    />
  );
}
