import { useTheme } from '@emotion/react';
import { Box, Row } from 'cube-ui-components';
import {
  TabletSectionsItem,
  flattenSectionNameList,
} from 'features/eAppV2/common/components/TabletSections';
import TabletSectionsV2 from 'features/eAppV2/common/components/TabletSectionsV2';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PartyRole } from 'types/party';
import FatcaTablet from './fatca/Fatca.tablet';
import useSyncActivePath from 'features/eAppV2/id/hooks/useSyncActivePath';
import { useFormSubmissionContext } from '../../FormSubmissionContext';
import { useProgressBarContext } from 'features/eAppV2/common/components/progressBar/ProgressBarContext';
import { shallow } from 'zustand/shallow';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';

export default function DeclarationTablet() {
  const { t } = useTranslation(['eApp']);

  const { colors, space } = useTheme();
  const { setActiveItemKey, itemKey, nextSubgroup, nextGroup, subgroup } =
    useEAppProgressBarStore(
      state => ({
        nextSubgroup: state.nextSubgroup,
        nextGroup: state.nextGroup,
        setActiveItemKey: state.setActiveItemKey,
        itemKey: state.itemKey,
        subgroup: state.subgroup,
      }),
      shallow,
    );

  const { set } = useProgressBarContext();
  const { isReadyToSave } = useFormSubmissionContext();
  useSyncActivePath('appDetail', 'declaration', 'fatca');

  useEffect(() => {
    set(isReadyToSave, 'appDetail', 'declaration');
  }, [isReadyToSave, set]);

  const { caseObj } = useGetActiveCase();
  const policyOwner = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );
  const policyOwnerFullName = policyOwner?.person?.name?.fullName;

  const initialBeneficiaryOwner = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PAYER)),
    [caseObj?.parties],
  );

  const renewalPayer = useMemo(
    () =>
      caseObj?.parties?.find(p => p.roles.includes(PartyRole.RENEWAL_PAYER)),
    [caseObj?.parties],
  );

  const isOwnerRenewalPayer = renewalPayer?.id === policyOwner?.id;

  const { fatca, updateFatcaState } = useEAppStore(
    state => ({
      fatca: state.fatca,
      updateFatcaState: state.updateFatcaState,
    }),
    shallow,
  );

  const isPOFatcaCompleted = Boolean(fatca?.isPOCompleted);
  const isInitialBeneficiaryOwnerFatcaCompleted = Boolean(
    fatca?.isPayerCompleted,
  );

  const isSubgroupCompleted = Boolean(subgroup?.completed);

  const isInitialFatcaDisabled = useMemo(
    () =>
      !(
        isSubgroupCompleted ||
        isPOFatcaCompleted ||
        isInitialBeneficiaryOwnerFatcaCompleted
      ),
    [
      isSubgroupCompleted,
      isPOFatcaCompleted,
      isInitialBeneficiaryOwnerFatcaCompleted,
    ],
  );

  const isRenewalFatcaDisabled = useMemo(
    () => !(isSubgroupCompleted || isInitialBeneficiaryOwnerFatcaCompleted),
    [isSubgroupCompleted, isInitialBeneficiaryOwnerFatcaCompleted],
  );

  const sections = useMemo(() => {
    const policyOwnerFatca: TabletSectionsItem = {
      name: 'fatca',
      title: t('eApp:declaration.fatca'),
      subtitle: policyOwnerFullName,
    };
    const initialBeneficiaryOwnerFatca: TabletSectionsItem = {
      name: 'initialFatca',
      title: t('eApp:declaration.fatca'),
      subtitle: initialBeneficiaryOwner?.person?.name.fullName,
      disabled: isInitialFatcaDisabled,
    };
    const renewalPayerFatca: TabletSectionsItem = {
      name: 'renewalFatca',
      title: t('eApp:declaration.fatca'),
      subtitle: renewalPayer?.person?.name.fullName,
      disabled: isRenewalFatcaDisabled,
    };

    const sections: TabletSectionsItem[] = [policyOwnerFatca];

    if (initialBeneficiaryOwner && caseObj?.havePayer) {
      sections.push(initialBeneficiaryOwnerFatca);
    }

    if (!isOwnerRenewalPayer) {
      sections.push(renewalPayerFatca);
    }

    return sections;
  }, [
    t,
    policyOwnerFullName,
    initialBeneficiaryOwner,
    renewalPayer,
    caseObj?.havePayer,
    isOwnerRenewalPayer,
    isInitialFatcaDisabled,
    isRenewalFatcaDisabled,
  ]);

  const onNext = useCallback(async () => {
    const flattenPaths = flattenSectionNameList(sections);
    const activePathIndex = flattenPaths.findIndex(path => path === itemKey);
    const isOnLastSection = activePathIndex === flattenPaths.length - 1;
    if (itemKey === 'fatca') updateFatcaState({ isPOCompleted: true });
    if (itemKey === 'initialFatca')
      updateFatcaState({ isPayerCompleted: true });
    if (isOnLastSection) {
      nextSubgroup(true);
    } else {
      setActiveItemKey(flattenPaths[activePathIndex + 1]);
    }
  }, [itemKey, nextSubgroup, sections, nextGroup, caseObj]);

  type FatcaKey = 'fatca' | 'initialFatca' | 'renewalFatca';

  const PARTY_TYPE_MAP: Record<
    FatcaKey,
    PartyRole.PROPOSER | PartyRole.PAYER | PartyRole.RENEWAL_PAYER
  > = {
    fatca: PartyRole.PROPOSER,
    initialFatca: PartyRole.PAYER,
    renewalFatca: PartyRole.RENEWAL_PAYER,
  };

  return (
    <Box flex={1}>
      <Row flex={1} pl={space[8]} bgColor={colors.surface} gap={space[6]}>
        <TabletSectionsV2
          activePath={itemKey}
          setActivePath={setActiveItemKey}
          items={sections}
          isReadyToSave={isReadyToSave}
        />
        {itemKey === 'fatca' && (
          <FatcaTablet onNext={onNext} partyType={PartyRole.PROPOSER} />
        )}
        {itemKey === 'initialFatca' && (
          <FatcaTablet onNext={onNext} partyType={PartyRole.PAYER} />
        )}
        {itemKey === 'renewalFatca' && (
          <FatcaTablet onNext={onNext} partyType={PartyRole.PAYER} />
        )}
      </Row>
    </Box>
  );
}
