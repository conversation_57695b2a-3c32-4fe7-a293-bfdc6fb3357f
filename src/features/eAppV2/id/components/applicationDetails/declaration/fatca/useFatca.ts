import { consentFieldMapping } from 'features/eAppV2/id/constants/consent';
import {
  FatcaForm,
  initialTaxResidencyFormData,
  toFatca,
} from 'features/eAppV2/id/validations/applicationDetails/declarationValidation';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetCaseManually } from 'hooks/useGetCase';
import usePrevious from 'hooks/usePrevious';
import { useCallback, useEffect } from 'react';
import {
  Control,
  useFieldArray,
  UseFormGetValues,
  UseFormHandleSubmit,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';
import { PartyRole } from 'types/party';

export const useFatca = ({
  control,
  getValues,
  setValue,
  handleSubmit,
  defaultValues,
  partyType,
}: {
  handleSubmit: UseFormHandleSubmit<FatcaForm>;
  control: Control<FatcaForm>;
  setValue: UseFormSetValue<FatcaForm>;
  getValues: UseFormGetValues<FatcaForm>;
  defaultValues: FatcaForm;
  partyType: PartyRole.PROPOSER | PartyRole.PAYER | PartyRole.RENEWAL_PAYER;
}) => {
  const { caseObj } = useGetActiveCase();

  const { fields, append, remove, update, replace } = useFieldArray({
    name: 'taxResidencies',
    control,
  });

  const hasOutsideTaxResidency = useWatch({
    name: 'hasOutsideTaxResidency',
    control: control,
  });

  const hasUsTaxResidency = useWatch({
    name: 'hasUsTaxResidency',
    control: control,
  });

  const updateTINReasonWhenSelectingNoOption = useCallback(
    (index: number) => {
      // if (hasSingaporeAddress) {
      //   if (incomeRank === IncomeRank.LOW) {
      //     const taxResidencies = getValues('taxResidencies');
      //     if (taxResidencies && taxResidencies[index]) {
      //       update(index, {
      //         ...taxResidencies[index],
      //         hasTin: 'no',
      //         noTinReason: NoTinReason.UNABLE_TO_OBTAIN,
      //       });
      //     }
      //   }
      // }
    },
    [getValues, update],
  );

  const previousHasOutsideTaxResidency = usePrevious(hasOutsideTaxResidency);
  const previousHasUsTaxResidency = usePrevious(hasUsTaxResidency);

  useEffect(() => {
    const hasUserModifiedForm =
      previousHasOutsideTaxResidency !== hasOutsideTaxResidency ||
      previousHasUsTaxResidency !== hasUsTaxResidency;

    if (
      hasOutsideTaxResidency !== 'yes' &&
      hasUsTaxResidency !== 'yes' &&
      fields.length > 0 &&
      hasUserModifiedForm
    ) {
      remove();
    } else if (
      (hasOutsideTaxResidency === 'yes' || hasUsTaxResidency === 'yes') &&
      fields.length === 0
    ) {
      if (
        defaultValues.taxResidencies &&
        defaultValues.taxResidencies.length > 0
      ) {
        replace(defaultValues.taxResidencies);
      } else {
        replace([
          {
            ...initialTaxResidencyFormData,
          },
        ]);
      }
    }
  }, [
    replace,
    fields.length,
    hasOutsideTaxResidency,
    hasUsTaxResidency,
    remove,
  ]);

  const onAdd = useCallback(() => {
    append(initialTaxResidencyFormData, { shouldFocus: false });
  }, [append]);

  const agentId = useBoundStore(state => state.auth.agentCode);
  const { mutateAsync: getCase } = useGetCaseManually();
  const { mutateAsync: saveApplication } = useCreateApplication();

  const saveFatca = useCallback(
    async (partyId?: string) => {
      if (!caseObj || !agentId || !partyId) return;
      return handleSubmit(async value => {
        const latestCase = await getCase(caseObj.id);
        const fatcaData = toFatca(value, agentId);

        const consentKey = consentFieldMapping[partyType];

        const fatcaConsent = {
          ...fatcaData,
          partyId,
        };

        await saveApplication({
          caseId: latestCase.id,
          data: {
            ...latestCase.application,
            [consentKey]: [fatcaConsent],
          },
        });
      })();
    },
    [agentId, caseObj, getCase, handleSubmit, saveApplication, partyType],
  );

  return {
    fields,
    append,
    remove,
    update,
    hasOutsideTaxResidency,
    hasUsTaxResidency,
    updateTINReasonWhenSelectingNoOption,
    onAdd,
    saveFatca,
  };
};
