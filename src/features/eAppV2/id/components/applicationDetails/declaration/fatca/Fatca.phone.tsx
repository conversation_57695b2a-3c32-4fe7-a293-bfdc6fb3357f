import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  Button,
  Icon,
  LargeBody,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import { useIncompleteFields } from 'features/eApp/hooks/useIncompleteFields';
import SectionWithTitle from 'features/eAppV2/common/components/SectionWithTitle';
import {
  FatcaForm,
  fatcaSchema,
  fromFatca,
} from 'features/eAppV2/id/validations/applicationDetails/declarationValidation';

import { ID_COUNTRY } from 'constants/optionList';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import { useEAppForm } from 'features/eAppV2/common/hooks/useEAppForm';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import React, {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { FormProvider } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { PartyRole } from 'types/party';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { useFormSubmissionContext } from '../../../FormSubmissionContext';
import CountryTaxInfoModal from './CountryTaxModal';
import TaxResidencyForm from './TaxResidencyForm';
import { useFatca } from './useFatca';

interface FatcaProps {
  role: PartyRole.PROPOSER | PartyRole.PAYER | PartyRole.RENEWAL_PAYER;
}
const MAX_TAX_RESIDENCY = 5;

const FatcaPhone = ({ role }: FatcaProps) => {
  const { t } = useTranslation(['eApp']);
  const { space, sizes } = useTheme();
  const fatcaResolver = useYupResolver(fatcaSchema);
  const { caseObj } = useGetActiveCase();

  const scrollRef = useRef<KeyboardAwareScrollView>(null);

  const partyId = caseObj?.parties?.find(p => p.roles.includes(role))?.id;

  const hasPremiumPayment =
    caseObj?.application?.paymentMethod &&
    caseObj.application.paymentMethod !== 'N';
  const issuedCountry = caseObj?.application?.creditCardIssuedCountry;

  const showNoReasonCondition = useMemo(() => {
    return (
      role === PartyRole.PROPOSER &&
      hasPremiumPayment &&
      issuedCountry !== ID_COUNTRY
    );
  }, [hasPremiumPayment, issuedCountry, role]);

  const defaultValues = useMemo(() => {
    const values = fromFatca(role, caseObj?.application);
    return values;
  }, [role, caseObj?.application]);

  const next = useEAppProgressBarStore(state => state.next);

  const methods = useEAppForm<FatcaForm>({
    mode: 'onBlur',
    defaultValues,
    resolver: fatcaResolver,
    context: {
      showNoReasonCondition,
    },
  });

  const {
    control,
    handleSubmit,
    getValues,
    setValue,
    watch,
    formState: { isValid, errors, isSubmitting },
  } = methods;

  const { fields, remove, onAdd, saveFatca } = useFatca({
    handleSubmit,
    control,
    setValue,
    getValues,
    defaultValues,
    partyType: role,
  });

  const { registerSubmitHandler, setIsNavigateEnabled } =
    useFormSubmissionContext();

  const handleOnSubmit = useCallback(async () => {
    await saveFatca(partyId);
    next();
  }, [saveFatca, next, partyId]);

  const [countryTaxModalVisible, setCountryTaxModalVisible] = useState(false);

  const closeCountryTaxModal = useCallback(() => {
    setCountryTaxModalVisible(false);
  }, []);

  const showCountryTaxModal = useCallback(() => {
    setCountryTaxModalVisible(true);
  }, []);

  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields<FatcaForm>({
      control,
      watch,
      schema: fatcaSchema,
      scrollRef,
      scrollTo: ({ y }) => {
        scrollRef.current?.scrollToPosition(0, y || 0, true);
      },
    });

  const hasOutsideTaxResidency = watch('hasOutsideTaxResidency');
  const hasUsTaxResidency = watch('hasUsTaxResidency');

  const progressLock = useMemo(() => {
    switch (role) {
      case PartyRole.PROPOSER:
        return 'appDetail-declaration-fatca';
      case PartyRole.PAYER:
        return 'appDetail-declaration-initialFatca';
      case PartyRole.RENEWAL_PAYER:
        return 'appDetail-declaration-renewalFatca';
    }
  }, [role]);

  const showExplanationQuestion = useMemo(() => {
    return (
      hasOutsideTaxResidency === 'no' &&
      hasUsTaxResidency === 'no' &&
      showNoReasonCondition
    );
  }, [hasOutsideTaxResidency, hasUsTaxResidency, showNoReasonCondition]);

  useEffect(() => {
    setIsNavigateEnabled(isValid);
    return () => {
      setIsNavigateEnabled(false);
    };
  }, [isValid, setIsNavigateEnabled]);

  useEffect(() => {
    registerSubmitHandler(handleOnSubmit);
    return () =>
      registerSubmitHandler(() => {
        console.log('submit handler is not registered');
      });
  }, [registerSubmitHandler, handleOnSubmit]);

  const roleLabel = useMemo(() => {
    switch (role) {
      case PartyRole.PROPOSER:
        return t('eApp:bar.policyOwner');
      case PartyRole.PAYER:
        return t('eApp:bar.payor');
      case PartyRole.RENEWAL_PAYER:
        return t('eApp:bar.renewalPayer');
    }
  }, [role]);

  return (
    <FormProvider {...methods}>
      <ScrollViewContainer
        keyboardDismissMode="interactive"
        keyboardShouldPersistTaps="handled"
        extraHeight={250}
        ref={scrollRef}
        contentContainerStyle={{ paddingBottom: space[30] }}>
        <Typography.H6 fontWeight="bold">{`${roleLabel}'s ${t(
          'eApp:bar.fatca',
        )}`}</Typography.H6>
        <Box paddingY={space[5]}>
          <SectionWithTitle title={t('eApp:declaration.fatca.title')}>
            <Content>
              <LargeBody fontWeight="medium">
                {t('eApp:declaration.fatca.message')}
              </LargeBody>

              <Box flex={1} mt={space[3]} />
              <LargeBody>{t('eApp:declaration.fatca.question.1')}</LargeBody>
              <Row mt={space[4]} gap={space[8]}>
                <Input
                  control={control}
                  as={RadioButtonGroup}
                  name={'hasOutsideTaxResidency'}>
                  <RadioButton value="yes" label={t('eApp:yes')} />
                  <RadioButton value="no" label={t('eApp:no')} />
                </Input>
              </Row>
              <Box flex={1} mt={space[4]} />
              <Divider />

              <Box flex={1} mt={space[4]} />
              <LargeBody>{t('eApp:declaration.fatca.question.2')}</LargeBody>
              <Row mt={space[4]} gap={space[8]}>
                <Input
                  control={control}
                  as={RadioButtonGroup}
                  name={'hasUsTaxResidency'}>
                  <RadioButton value="yes" label={t('eApp:yes')} />
                  <RadioButton value="no" label={t('eApp:no')} />
                </Input>
              </Row>

              {showExplanationQuestion && (
                <Box flex={1} mt={space[4]}>
                  <Divider />
                  <Box flex={1} mt={space[4]} />
                  <LargeBody>
                    {t('eApp:application.declaration.fatca.explanation')}
                  </LargeBody>
                  <Box flex={1} mt={space[4]} />
                  <Input
                    control={control}
                    as={TextField}
                    name="noReason"
                    multiline
                    numberOfLines={4}
                    placeholder={t(
                      'eApp:application.declaration.fatca.explanation.placeholder',
                    )}
                  />
                </Box>
              )}

              {(getValues('hasUsTaxResidency') === 'yes' ||
                getValues('hasOutsideTaxResidency') === 'yes') && (
                <Box flex={1} mt={space[4]}>
                  <LargeBody fontWeight="medium">
                    {t('eApp:declaration.fatca.taxResidency.description')}
                    <ShowCountryTaxModalButton onPress={showCountryTaxModal}>
                      <Icon.InfoCircle size={24} />
                    </ShowCountryTaxModalButton>
                  </LargeBody>
                </Box>
              )}
              {fields.map((field, index) => (
                <Fragment key={field.id}>
                  <TaxResidencyForm
                    index={index}
                    onDelete={index > 0 ? () => remove(index) : undefined}
                  />
                  {index < fields.length - 1 && (
                    <Box flex={1} mt={space[6]}>
                      <Divider />
                    </Box>
                  )}
                </Fragment>
              ))}

              {fields.length > 0 && fields.length < MAX_TAX_RESIDENCY && (
                <>
                  <Box flex={1} height={sizes[5]} />
                  <Button
                    text={t('eApp:declaration.fatca.taxResidency.tin.add')}
                    icon={Icon.Plus}
                    variant="secondary"
                    size="small"
                    mini={true}
                    onPress={onAdd}
                  />
                </>
              )}
            </Content>
          </SectionWithTitle>
        </Box>
      </ScrollViewContainer>
      <EAppFooterPhone
        progressLock={progressLock}
        onPrimaryPress={handleOnSubmit}
        primaryDisabled={!isValid || isSubmitting}
        primaryLoading={isSubmitting}
        primaryLabel={t('eApp:next')}
        primarySubLabel={`${t('eApp:bar.fatca')} - ${roleLabel}`}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        focusOnIncompleteField={focusOnNextIncompleteField}
      />

      <CountryTaxInfoModal
        dialogVisible={countryTaxModalVisible}
        onCancel={closeCountryTaxModal}
      />
    </FormProvider>
  );
};

export const FatcaPO = () => {
  return <FatcaPhone role={PartyRole.PROPOSER} />;
};

export const FatcaPayer = () => {
  return <FatcaPhone role={PartyRole.PAYER} />;
};

export const FatcaRenewalPayer = () => {
  return <FatcaPhone role={PartyRole.RENEWAL_PAYER} />;
};

const Content = styled(Box)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
  marginVertical: space[6],
}));

export const Divider = styled(View)(({ theme }) => {
  return {
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

export const RadioButtonTopAlign = styled(RadioButton)(() => {
  return {
    alignItems: 'flex-start',
  };
});

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    padding: space[4],
  }),
);

const ShowCountryTaxModalButton = styled(TouchableOpacity)(
  ({ theme: { space } }) => ({
    marginTop: -space[1],
    paddingLeft: space[1],
    zIndex: 1,
  }),
);
