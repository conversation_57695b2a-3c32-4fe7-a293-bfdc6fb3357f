import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, PictogramIcon } from 'cube-ui-components';
import ApplicationDetailsPhoneSection from 'features/eAppV2/common/components/ApplicationDetailsPhoneSection';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';
import { getPartiesByRole } from 'features/eAppV2/common/utils/partyUtils';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { usePartySubmission } from 'features/eAppV2/id/hooks/usePartySubmission';
import useSyncActivePath from 'features/eAppV2/id/hooks/useSyncActivePath';
import {
  PassionSurveyForm,
  passionSurveySchema,
} from 'features/eAppV2/id/validations/applicationDetails/passionSurveyValidation';
import {
  partyToPolicyOwnerInfo,
  PolicyOwnerInfoForm,
  policyOwnerInfoSchema,
} from 'features/eAppV2/id/validations/applicationDetails/policyOwner/policyOwnerInfoValidation';
import {
  AdditionalDetailsForm,
  additionalDetailsSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/additionalDetails';
import {
  MainPartyAddressInformationForm,
  mainPartyAddressInformationSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/addressInformation';
import {
  ContactDetailsForm,
  contactDetailsSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/contactDetails';
import {
  MainPartyPersonalDetailsForm,
  mainPartyPersonalDetailsSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/mainPartyPersonalDetails';
import {
  OccupationDetailsForm,
  occupationDetailsSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/occupationDetails';
import {
  PremiumPaymentForm,
  premiumPaymentSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/premiumPayment';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';
import { PartyRole } from 'types/party';
import { shallow } from 'zustand/shallow';
import ApplicationDetailsHeader from '../sections/ApplicationDetailsHeader';
import AddressInfoPhone from './addressInfo/AddressInfo.phone';
import AdditionalDetailsPhone from './addtionalDetails/AdditionalDetails.phone';
import ContactDetailsPhone from './contactDetails/ContactDetails.phone';
import OccupationDetailsPhone from './occupationDetails/OccupationDetails.phone';
import PassionSurveyIcon from './passionSurvey/PassionSurvey.icon';
import PassionSurveyPhone from './passionSurvey/PassionSurvey.phone';
import PersonalDetailsPhone from './personalDetails/PersonalDetails.phone';
import PremiumPaymentPhone from './premiumPayment/PremiumPayment.phone';

export default function PolicyOwnerInfoPhone() {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  const { next, setActiveItemKey, itemKey } = useEAppProgressBarStore(
    state => ({
      setActiveItemKey: state.setActiveItemKey,
      itemKey: state.itemKey,
      nextSubgroup: state.nextSubgroup,
      next: state.next,
    }),
    shallow,
  );
  useSyncActivePath('appDetail', 'policyOwner', 'policyOwner');

  const { disabledForm } = useDisabledEAppForm();
  const { data: optionList } = useGetOptionList<'id'>();
  const { data: agentProfile } = useGetAgentProfile();
  const { caseObj } = useGetActiveCase();

  const policyOwner = useMemo(
    () => getPartiesByRole(caseObj, PartyRole.PROPOSER)?.[0],
    [caseObj],
  );

  const policyInsured = useMemo(
    () => getPartiesByRole(caseObj, PartyRole.INSURED)?.[0],
    [caseObj],
  );
  const isOwnerInsured = policyOwner?.id === policyInsured?.id;

  const [poData, setPoData] = useState(() =>
    partyToPolicyOwnerInfo(policyOwner, caseObj?.application, optionList),
  );

  const onSectionDone = useCallback((data?: Partial<PolicyOwnerInfoForm>) => {
    setPoData(poData => ({
      ...poData,
      ...data,
    }));
  }, []);

  const { onSave } = usePartySubmission({
    role: PartyRole.PROPOSER,
    setError: (_name, _error) => {
      console.log('error', _name, _error);
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const onSubmit = useCallback(async () => {
    try {
      setIsSubmitting(true);
      const id = await onSave({
        values: poData,
        skipDukcapilValidation: true, // to do: remove this, skip the validation for now
      });
      if (!id) {
        return;
      }

      if (isOwnerInsured) {
        next(true);
      } else {
        setActiveItemKey('insured');
      }
    } catch (error) {
      console.error('Error saving policy owner:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [onSave, poData, isOwnerInsured, next, setActiveItemKey]);

  const disableNext =
    disabledForm ||
    !policyOwnerInfoSchema.isValidSync(poData, {
      context: {
        optionList,
        agentId: agentProfile?.person?.idNumber,
        agentMobileNumber: agentProfile?.contact?.mobilePhone,
        primaryId: poData.primaryId,
        agentEmail: agentProfile?.contact?.email,
      },
    });

  return (
    <Box flex={1}>
      <ScrollView
        contentContainerStyle={{
          paddingBottom: space[4],
          paddingHorizontal: space[4],
        }}>
        <ApplicationDetailsHeader
          title={t('eApp:policyOwnerInfo')}
          content={t('eApp:policyOwnerInfo.declaration')}
        />

        <Container>
          <ApplicationDetailsPhoneSection
            icon={PictogramIcon.ManWithShield}
            name={t('eApp:applicationDetails.personalDetails')}
            done={mainPartyPersonalDetailsSchema.isValidSync(poData)}
            form={PersonalDetailsPhone}
            value={poData as MainPartyPersonalDetailsForm}
            onDone={data => onSectionDone(data)}
            nationality={poData.nationality || ''}
            role={PartyRole.PROPOSER}
          />
          <ApplicationDetailsPhoneSection
            icon={PictogramIcon.Briefcase}
            name={t('eApp:applicationDetails.occupationDetails')}
            done={occupationDetailsSchema.isValidSync(poData)}
            form={OccupationDetailsPhone}
            value={poData as OccupationDetailsForm}
            onDone={data => onSectionDone(data)}
          />
          <ApplicationDetailsPhoneSection
            icon={PictogramIcon.ActivePhoneCall}
            name={t('eApp:applicationDetails.contactDetails')}
            done={contactDetailsSchema.isValidSync(poData, {
              context: {
                agentId: agentProfile?.person?.idNumber,
                agentMobileNumber: agentProfile?.contact?.mobilePhone,
                primaryId: poData.primaryId,
                agentEmail: agentProfile?.contact?.email,
              },
            })}
            form={ContactDetailsPhone}
            value={poData as ContactDetailsForm}
            onDone={data => onSectionDone(data)}
            primaryId={poData.primaryId}
            role={PartyRole.PROPOSER}
          />
          <ApplicationDetailsPhoneSection
            icon={PictogramIcon.House2}
            name={t('eApp:applicationDetails.addressInformation')}
            done={mainPartyAddressInformationSchema.isValidSync(poData)}
            form={AddressInfoPhone}
            value={poData as MainPartyAddressInformationForm}
            onDone={data => onSectionDone(data)}
            role={PartyRole.PROPOSER}
          />
          <ApplicationDetailsPhoneSection
            icon={PictogramIcon.InformationDocument}
            name={t('eApp:additionalDetails')}
            done={additionalDetailsSchema.isValidSync(poData, {
              context: { optionList },
            })}
            form={AdditionalDetailsPhone}
            value={poData as AdditionalDetailsForm}
            onDone={data => onSectionDone(data)}
          />
          <ApplicationDetailsPhoneSection
            icon={PictogramIcon.CreditCard}
            name={t('eApp:premiumPayment')}
            done={premiumPaymentSchema.isValidSync(poData)}
            form={PremiumPaymentPhone}
            value={poData as PremiumPaymentForm}
            onDone={data => onSectionDone(data)}
          />
          <ApplicationDetailsPhoneSection
            icon={PassionSurveyIcon}
            name={t('eApp:declaration.passionSurvey.title')}
            done={passionSurveySchema.isValidSync(poData)}
            form={PassionSurveyPhone}
            value={poData as PassionSurveyForm}
            onDone={data => onSectionDone(data)}
          />
        </Container>
      </ScrollView>
      <EAppFooterPhone
        primaryLoading={isSubmitting}
        primaryDisabled={disableNext}
        primaryLabel={t('eApp:next')}
        onPrimaryPress={onSubmit}
        primarySubLabel={t('eApp:details', {
          screen: policyOwner?.roles.includes(PartyRole.INSURED)
            ? t('eApp:bar.payor')
            : t('eApp:bar.insured'),
        })}
      />
    </Box>
  );
}

const Container = styled.View(({ theme: { colors, space, borderRadius } }) => {
  return {
    borderRadius: borderRadius.large,
    paddingVertical: space[2],
    backgroundColor: colors.background,
    overflow: 'hidden',
  };
});
