import { useTheme } from '@emotion/react';
import { Box, Row } from 'cube-ui-components';
import { isEmpty } from 'lodash';
import { memo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import EAppFooterTablet from '../../../../common/components/footer/EAppFooter.tablet';
import PaymentMethods from './methods/PaymentMethods.tablet';
import { ScrollView, View } from 'react-native';
import { FormProps } from './PaymentForm';
import PaymentInfoTablet from '../info/PaymentInfo.tablet';

const PaymentFormTablet = memo(function ({
  advancePayment,
  selectedMethod,
  onSelectPaymentMethod,
  onPress,
}: FormProps) {
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();

  const scrollViewRef = useRef<ScrollView>(null);
  const paymentMethodViewRef = useRef<View>(null);

  return (
    <Box flex={1}>
      <Row
        flex={1}
        bgColor={colors.surface}
        gap={space[10]}
        paddingRight={space[10]}>
        <Box
          width={'37%'}
          bgColor={colors.background}
          paddingLeft={space[10]}
          paddingRight={space[8]}>
          <PaymentInfoTablet advancePayment={advancePayment} />
        </Box>
        <Box flex={1} alignContent="center">
          <ScrollView
            bounces={false}
            showsVerticalScrollIndicator={false}
            ref={scrollViewRef}>
            <Box h={space[6]} />
            <View ref={paymentMethodViewRef}>
              <PaymentMethods
                paymentMethod={selectedMethod}
                setPaymentMethod={onSelectPaymentMethod}
              />
            </View>
            <Box h={space[6]} />
          </ScrollView>
        </Box>
      </Row>
      <EAppFooterTablet
        onPrimaryPress={onPress}
        primaryDisabled={isEmpty(selectedMethod)}
        primaryLabel={t('eApp:payment.proceed')}
      />
    </Box>
  );
});

export default PaymentFormTablet;
