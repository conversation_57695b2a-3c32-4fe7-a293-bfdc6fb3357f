import { useTheme } from '@emotion/react';
import { isEmpty } from 'lodash';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

import { Box, H6 } from 'cube-ui-components';
import EAppFooterPhone from '../../../../common/components/footer/EAppFooter.phone';
import PaymentMethodsPhone from './methods/PaymentMethods.phone';
import { FormProps } from './PaymentForm';
import PaymentInfoPhone from '../info/PaymentInfo.phone';

const PaymentFormPhone = memo(function ({
  advancePayment,
  selectedMethod,
  onSelectPaymentMethod,
  onPress,
}: FormProps) {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();

  return (
    <Box flex={1}>
      <KeyboardAwareScrollView>
        <Box px={space[4]} pt={space[4]}>
          <H6 color="#333" fontWeight="bold">
            {t('eApp:bar.payment')}
          </H6>
        </Box>
        <Box p={space[4]} gap={space[4]}>
          <PaymentInfoPhone advancePayment={advancePayment} />
          <PaymentMethodsPhone
            paymentMethod={selectedMethod}
            setPaymentMethod={onSelectPaymentMethod}
          />
        </Box>
      </KeyboardAwareScrollView>
      <EAppFooterPhone
        onPrimaryPress={onPress}
        primaryDisabled={isEmpty(selectedMethod)}
        primaryLabel={t('eApp:payment.proceed')}
      />
    </Box>
  );
});

export default PaymentFormPhone;
