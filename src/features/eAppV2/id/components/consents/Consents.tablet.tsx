import { useTheme } from '@emotion/react';
import { Box, Row } from 'cube-ui-components';
import { useProgressBarContext } from 'features/eAppV2/common/components/progressBar/ProgressBarContext';
import { TabletSectionsItem } from 'features/eAppV2/common/components/TabletSections';
import TabletSectionsV2 from 'features/eAppV2/common/components/TabletSectionsV2';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import React, { Fragment, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import useSyncActivePath from '../../hooks/useSyncActivePath';
import { useFormSubmissionContext } from '../FormSubmissionContext';
import ClosingAgent from './closingAgent';
import StatementOfTruth from './statementOfTruth';
import StatementPowerOfAttorneyTablet from './statementPowerOfAttorney/StatementPowerOfAttorney.tablet';
import TemporaryCoverageTablet from './temporaryCoverage/TemporaryCoverage.tablet';
import UnderwritingDecision from './underwritingDecision';

export default function ConsentsTablet() {
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();
  const { setActiveItemKey, itemKey, nextGroup, group } =
    useEAppProgressBarStore(
      state => ({
        setActiveItemKey: state.setActiveItemKey,
        itemKey: state.itemKey,
        nextGroup: state.nextGroup,
        group: state.group,
      }),
      shallow,
    );
  useSyncActivePath('consents', undefined, 'underwritingDecision');

  const { set } = useProgressBarContext();
  const { isReadyToSave } = useFormSubmissionContext();

  // Simple function to navigate to the next section
  const goToNextSection = useCallback((currentSection: string) => {
    const currentIndex = sections.findIndex(s => s.name === currentSection);
    if (currentIndex < sections.length - 1) {
      setActiveItemKey(sections[currentIndex + 1].name);
    }
  }, []);

  const { consentsState, setConsentsState } = useEAppStore(
    state => ({
      consentsState: state.consentsState,
      setConsentsState: state.setConsentsState,
    }),
    shallow,
  );

  const isGroupCompleted = Boolean(group?.completed);

  const sections = useMemo<TabletSectionsItem[]>(() => {
    return [
      {
        name: 'underwritingDecision',
        title: t('eApp:consents.underwritingDecision'),
        content: () => (
          <UnderwritingDecision
            onNext={() => {
              setConsentsState({
                isUnderwritingDecisionCompleted: true,
              });
              goToNextSection('underwritingDecision');
            }}
          />
        ),
      },
      {
        name: 'closingAgent',
        title: t('eApp:consents.closingAgent'),
        content: () => (
          <ClosingAgent
            onNext={() => {
              setConsentsState({
                isClosingAgentCompleted: true,
              });
              goToNextSection('closingAgent');
            }}
          />
        ),
        disabled: !(
          consentsState?.isUnderwritingDecisionCompleted || isGroupCompleted
        ),
      },
      {
        name: 'statementAndPowerOfAttorney',
        title: t('eApp:consents.statementAndPowerOfAttorney'),
        content: () => (
          <StatementPowerOfAttorneyTablet
            onNext={() => {
              setConsentsState({
                isStatementPowerOfAttorneyCompleted: true,
              });
              goToNextSection('statementAndPowerOfAttorney');
            }}
          />
        ),
        disabled: !(consentsState?.isClosingAgentCompleted || isGroupCompleted),
      },
      {
        name: 'statementOfTruth',
        title: t('eApp:consents.statementOfTruth'),
        content: () => (
          <StatementOfTruth
            onNext={() => {
              setConsentsState({
                isStatementOfTruthCompleted: true,
              });
              goToNextSection('statementOfTruth');
            }}
          />
        ),
        disabled: !(
          consentsState?.isStatementPowerOfAttorneyCompleted || isGroupCompleted
        ),
      },
      {
        name: 'temporaryCoverage',
        title: t('eApp:consents.temporaryCoverage'),
        content: () => (
          <TemporaryCoverageTablet
            onNext={() => {
              setConsentsState({
                isTemporaryCoverageCompleted: true,
              });
              nextGroup();
            }}
          />
        ),
        disabled: !(
          consentsState?.isStatementOfTruthCompleted || isGroupCompleted
        ),
      },
    ].filter(Boolean) as TabletSectionsItem[];
  }, [t, goToNextSection, nextGroup, consentsState, isGroupCompleted]);

  useEffect(() => {
    set(isReadyToSave, 'consents', undefined);
  }, [isReadyToSave, set]);

  return (
    <Box flex={1}>
      <Row flex={1} pl={space[8]} bgColor={colors.surface} gap={space[6]}>
        <TabletSectionsV2
          items={sections}
          activePath={itemKey}
          setActivePath={setActiveItemKey}
          isReadyToSave={isReadyToSave}
        />
        {sections.map(section => {
          const selected = section.name === itemKey;
          if (!selected) return null;
          return (
            <Fragment key={section.name}>
              {typeof section.content === 'function'
                ? React.createElement(section.content)
                : section.content}
            </Fragment>
          );
        })}
      </Row>
    </Box>
  );
}
