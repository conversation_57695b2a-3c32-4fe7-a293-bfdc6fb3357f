import { useTheme } from '@emotion/react';
import { Box, Row } from 'cube-ui-components';
import { useProgressBarContext } from 'features/eAppV2/common/components/progressBar/ProgressBarContext';
import TabletSectionsV2, {
  TabletSectionsProps,
} from 'features/eAppV2/common/components/TabletSectionsV2';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import useSyncActivePath from 'features/eAppV2/ph/hooks/useSyncActivePath';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import { useFormSubmissionContext } from '../FormSubmissionContext';
import RenewalPremiumPaymentTablet from './renewalPremiumPayment/RenewalPremiumPayment.tablet';
import WithdrawalPaymentTablet from './withdrawalPayment/WithdrawalPayment.tablet';

export default function PaymentSetupTablet() {
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();
  const { isRenewalPaymentSetupCompleted, setIsRenewalPaymentSetupCompleted } =
    useEAppStore(
      state => ({
        isRenewalPaymentSetupCompleted: state.isRenewalPaymentSetupCompleted,
        setIsRenewalPaymentSetupCompleted:
          state.setIsRenewalPaymentSetupCompleted,
      }),
      shallow,
    );

  const { setActiveItemKey, itemKey, nextGroup, group } =
    useEAppProgressBarStore(
      state => ({
        setActiveItemKey: state.setActiveItemKey,
        itemKey: state.itemKey,
        nextGroup: state.nextGroup,
        group: state.group,
      }),
      shallow,
    );
  const isFirstStepCompleted = Boolean(
    group?.items.find(item => item.routeKey === 'renewalPremiumPayment')
      ?.completed || isRenewalPaymentSetupCompleted,
  );

  const isGroupCompleted = Boolean(group?.completed);

  const sections = useMemo(
    () =>
      [
        {
          name: 'renewalPremiumPayment',
          title: t('eApp:paymentSetup.renewalPremiumPayment'),
        },
        {
          name: 'withdrawalPayment',
          title: t('eApp:paymentSetup.withdrawalPayment'),
          disabled: !(isFirstStepCompleted || isGroupCompleted),
        },
      ].filter(Boolean) as TabletSectionsProps['items'],
    [t, isFirstStepCompleted, isGroupCompleted],
  );

  const { set } = useProgressBarContext();
  const { isReadyToSave } = useFormSubmissionContext();
  useSyncActivePath('renewalPaymentSetup', undefined, 'renewalPremiumPayment');

  useEffect(() => {
    set(isReadyToSave, 'renewalPaymentSetup', undefined);
  }, [isReadyToSave, set]);
  return (
    <Box flex={1}>
      <Row flex={1} pl={space[8]} bgColor={colors.surface} gap={space[6]}>
        <TabletSectionsV2
          activePath={itemKey}
          setActivePath={setActiveItemKey}
          items={sections}
          isReadyToSave={isReadyToSave}
        />
        {itemKey === 'renewalPremiumPayment' && (
          <RenewalPremiumPaymentTablet
            onNext={() => {
              setIsRenewalPaymentSetupCompleted(true);
              setActiveItemKey('withdrawalPayment');
            }}
          />
        )}
        {itemKey === 'withdrawalPayment' && (
          <WithdrawalPaymentTablet onNext={() => nextGroup(true)} />
        )}
      </Row>
    </Box>
  );
}
