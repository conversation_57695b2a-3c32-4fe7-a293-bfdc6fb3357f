import { Icon } from 'cube-ui-components';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { updateCompletedStatus } from 'features/eAppV2/common/hooks/useGenerateEAppRoutes';
import { DecisionsType } from 'features/eAppV2/common/types/uwmeTypes';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { getScreenFlags } from 'features/eAppV2/id/utils/getScreenFlags';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PartyRole } from 'types/party';
import { shallow } from 'zustand/shallow';
import {
  ProgressGroup,
  ProgressSubgroup,
} from '../../common/types/progressBarTypes';

export const useGenerateIDRoutes = () => {
  const { t } = useTranslation(['eApp']);

  const { isTabletMode } = useLayoutAdoptionCheck();
  const { caseObj } = useGetActiveCase();
  const quotation = useSelectedQuotation();
  const channel = useGetCubeChannel();
  const { completedMap, setProgressBarState, groupKey } =
    useEAppProgressBarStore(
      state => ({
        completedMap: state.completedMap,
        setProgressBarState: state.setProgressBarState,
        groupKey: state.groupKey,
      }),
      shallow,
    );
  const isPOEqualPI = useMemo(
    () =>
      caseObj?.parties
        ?.find(p => p.roles.includes(PartyRole.INSURED))
        ?.roles.includes(PartyRole.PROPOSER),
    [caseObj?.parties],
  );
  const { decision, hasDecisionResponse } = useMemo(() => {
    const policyOwner = caseObj?.parties?.find(p =>
      p.roles.includes(PartyRole.PROPOSER),
    );
    return {
      decision: policyOwner?.uw?.result?.scenario as DecisionsType,
      hasDecisionResponse: Boolean(policyOwner?.uw?.decisionResponse),
    };
  }, [caseObj?.parties]);

  const isEntity = useCheckEntity();

  useEffect(() => {
    const { hasPayor, hasInsured, hasRenewalPayer } = getScreenFlags(
      caseObj,
      quotation,
      channel,
    );

    const groups: Array<ProgressGroup> = [
      {
        routeKey: 'appDetail',
        title: t('eApp:bar.appDetail'),
        showSubProgress: true,
        items: [
          {
            routeKey: 'policyOwner',
            title: t('eApp:bar.policyOwnerAndLifeAssuredDetails'),
            icon: <Icon.Account />,
            items: [
              {
                routeKey: 'policyOwner',
                title: '  •  ' + t('eApp:bar.policyOwner'),
                barTitle: t('eApp:bar.policyOwner'),
                icon: <Icon.Account />,
              },
              hasInsured && {
                routeKey: 'insured',
                title: '  •  ' + t('eApp:bar.insured'),
                barTitle: t('eApp:bar.insured'),
                icon: <Icon.Account />,
              },
            ].filter(Boolean),
          },
          {
            routeKey: 'others',
            title: t('eApp:bar.otherStakeholder'),
            icon: <Icon.Team />,
            items: [
              {
                routeKey: 'payor',
                title: '  •  ' + t('eApp:bar.payor'),
                barTitle: t('eApp:bar.payor'),
                icon: <Icon.Account />,
              },
              {
                routeKey: 'renewalPayor',
                title: '  •  ' + t('eApp:bar.renewalPayer'),
                barTitle: t('eApp:bar.renewalPayer'),
                icon: <Icon.Account />,
              },
              {
                routeKey: 'beneficiary',
                title: '  •  ' + t('eApp:bar.beneficiary'),
                barTitle: t('eApp:bar.beneficiary'),
                icon: <Icon.Account />,
              },
            ].filter(Boolean),
          },
          {
            routeKey: 'declaration',
            title: t('eApp:bar.declaration'),
            icon: <Icon.DocumentCopy />,
            items: [
              {
                routeKey: 'fatca',
                title: '  •  ' + t('eApp:bar.fatca'),
                icon: <Icon.DocumentCopy />,
                barTitle: t('eApp:bar.declaration'),
              },
              hasPayor && {
                routeKey: 'initialFatca',
                title:
                  '  •  ' +
                  t('eApp:bar.fatca') +
                  ' (Initial Beneficiary Owner)',
                icon: <Icon.DocumentCopy />,
                barTitle: t('eApp:bar.declaration'),
              },
              hasRenewalPayer && {
                routeKey: 'renewalFatca',
                title:
                  '  •  ' +
                  t('eApp:bar.fatca') +
                  ' (Renewal Beneficiary Owner)',
                icon: <Icon.DocumentCopy />,
                barTitle: t('eApp:bar.declaration'),
              },
            ].filter(Boolean),
          },
          {
            routeKey: 'healthQuestion',
            title: t('eApp:bar.healthQuestion'),
            icon: <Icon.Health />,
            items: [
              {
                routeKey: 'policyOwner',
                title: '  •  ' + t('eApp:bar.policyOwner'),
                icon: <Icon.Health />,
                barTitle: t('eApp:bar.policyOwner'),
              },
              hasInsured && {
                routeKey: 'insured',
                title: '  •  ' + t('eApp:bar.insured'),
                icon: <Icon.Health />,
                barTitle: t('eApp:bar.insured'),
              },
            ].filter(Boolean),
          },
        ].filter(Boolean) as ProgressGroup['items'],
      },
      {
        routeKey: 'consents',
        title: t('eApp:bar.consents'),
        items: [
          {
            routeKey: 'underwritingDecision',
            title: '  •  ' + t('eApp:consents.underwritingDecision'),
            icon: <Icon.DocumentCopy />,
            barTitle: t('eApp:bar.consents'),
          },
          {
            routeKey: 'closingAgent',
            title: '  •  ' + t('eApp:consents.closingAgent'),
            icon: <Icon.DocumentCopy />,
            barTitle: t('eApp:bar.consents'),
          },
          {
            routeKey: 'statementAndPowerOfAttorney',
            title: '  •  ' + t('eApp:consents.statementAndPowerOfAttorney'),
            icon: <Icon.DocumentCopy />,
            barTitle: t('eApp:bar.consents'),
          },
          {
            routeKey: 'statementOfTruth',
            title: '  •  ' + t('eApp:consents.statementOfTruth'),
            icon: <Icon.DocumentCopy />,
            barTitle: t('eApp:bar.consents'),
          },
          {
            routeKey: 'temporaryCoverage',
            title: '  •  ' + t('eApp:consents.temporaryCoverage'),
            icon: <Icon.DocumentCopy />,
            barTitle: t('eApp:bar.consents'),
          },
        ],
        full: true,
      },
      {
        routeKey: 'renewalPaymentSetup',
        title: t('eApp:bar.paymentSetup'),
        showSubProgress: true,
        items: [
          {
            routeKey: 'renewalPremiumPayment',
            title: t('eApp:bar.renewalPayment'),
            icon: <Icon.PremiumPayment />,
          },
          {
            routeKey: 'withdrawalPayment',
            title: t('eApp:paymentSetup.withdrawalPayment'),
            icon: <Icon.PremiumPayment />,
          },
        ],
      },
      {
        routeKey: 'documentUpload',
        title: t('eApp:bar.documentUpload'),
        showSubProgress: true,
        items: [
          {
            routeKey: 'policyOwner',
            title: t('eApp:bar.policyOwner'),
            icon: <Icon.DocumentCopy />,
            barTitle: t('eApp:bar.documentUpload'),
          },
          hasInsured && {
            routeKey: 'insured',
            title: t('eApp:bar.insured'),
            icon: <Icon.DocumentCopy />,
            barTitle: t('eApp:bar.documentUpload'),
          },
          hasPayor && {
            routeKey: 'payor',
            title: t('eApp:bar.payor'),
            icon: <Icon.DocumentCopy />,
            barTitle: t('eApp:bar.payor'),
          },
        ].filter(Boolean) as ProgressGroup['items'],
      },
      {
        routeKey: 'review',
        title: t('eApp:bar.reviewSignature'),
        items: [],
        full: true,
      },
      {
        routeKey: 'payment',
        title: t('eApp:bar.payment'),
        full: true,
        items: [],
        icon: <Icon.PremiumPayment />,
      },
    ];

    updateCompletedStatus(groups, completedMap);

    //Update disabled
    const step1 = groups[0];
    const step1_1 = groups[0].items[0] as ProgressSubgroup;
    const step1_2 = groups[0].items[1] as ProgressSubgroup;
    const step1_3 = groups[0].items[2] as ProgressSubgroup;
    const step1_4 = groups[0].items[3] as ProgressSubgroup;
    const step1_5 = groups[0].items[4] as ProgressSubgroup;
    const step1_6 = groups[0].items[5] as ProgressSubgroup;
    step1_2.disabled = !step1_1.completed;
    const nomination = step1_2.items.find(i => i.routeKey === 'beneficiary');
    if (nomination && !nomination.completed) {
      const trustee = step1_2.items.find(i => i.routeKey === 'trustee');
      if (trustee) {
        trustee.disabled = true;
      }
    }
    step1_3.disabled = !step1_2.completed;
    step1_4.disabled = !step1_3.completed;
    if (step1_5) {
      step1_5.disabled = !step1_4.completed;
    }
    if (step1_6) {
      step1_6.disabled = !step1_5.completed;
    }

    const lastStep1 = step1.items[step1.items.length - 1];
    if (
      caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER))?.uw
        ?.decisionResponse
    ) {
      lastStep1.disabled = true;
    }
    step1.completed =
      step1_1.completed &&
      step1_2.completed &&
      step1_3.completed &&
      step1_4.completed;
    if (step1_5) {
      step1.completed &&= step1_5.completed;
    }
    if (step1_6) {
      step1.completed &&= step1_6.completed;
    }

    const step2 = groups[1];
    const step3 = groups[2];
    const step4 = groups[3];
    const step5 = groups[4];
    const step6 = groups[5];
    step4.disabled = !step1.items[step1.items.length - 1].completed;
    step5.disabled = !(
      step1.completed &&
      step2.completed &&
      step3.completed &&
      step4.completed
    );
    step6.disabled = !step5.completed || step5.disabled;

    if (decision === 'Decline') {
      step1.disabled = true;
      step2.disabled = true;
      step3.disabled = true;
      step4.disabled = true;
      step6.disabled = true;
    }

    if (hasDecisionResponse) {
      step1.disabled = true;
      step2.disabled = true;
      step3.disabled = true;
      step4.disabled = true;
    }

    setProgressBarState({
      groups,
    });
  }, [
    completedMap,
    isPOEqualPI,
    isTabletMode,
    setProgressBarState,
    t,
    caseObj,
    quotation,
    channel,
    isEntity,
    groupKey,
    decision,
    hasDecisionResponse,
  ]);
};
