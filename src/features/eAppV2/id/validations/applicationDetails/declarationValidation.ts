import { requiredMessage } from 'features/eAppV2/common/constants/eAppErrorMessages';
import { normalizedSpaceString } from 'features/eAppV2/common/validations/eAppCommonSchema';
import { Application, ProposerConsent } from 'types/case';
import { PartyRole } from 'types/party';
import { InferType, array, object, string } from 'yup';
import { consentFieldMapping } from '../../constants/consent';

export enum NoTinReason {
  NOT_ISSUE = 'TNIBC',
  DOES_NOT_GET_TIN = 'TNGBC',
  NO_TIN_REQUIRED = 'TNN',
}

export enum IncomeRank {
  LOW = 'low',
  HIGH = 'high',
}

export const fatcaSchema = object({
  hasOutsideTaxResidency: string().required(requiredMessage),
  hasUsTaxResidency: string().required(requiredMessage),
  noReason: string().test({
    name: 'no-reason',
    test: (value, ctx) => {
      const { showNoReasonCondition } = ctx.options.context || {};
      const { hasOutsideTaxResidency, hasUsTaxResidency } = ctx.parent || {};
      if (
        hasOutsideTaxResidency === 'no' &&
        hasUsTaxResidency === 'no' &&
        showNoReasonCondition &&
        !value?.trim()
      ) {
        return ctx.createError({
          message: requiredMessage,
          type: 'required',
        });
      }
      return true;
    },
  }),
  taxResidencies: array().of(
    object().shape({
      country: string().required(requiredMessage),
      tinNumber: normalizedSpaceString().when(['noTinReason'], {
        is: (noTinReason: string) => !noTinReason,
        then: schema => schema.required(requiredMessage),
      }),
      noTinReason: string()
        .oneOf([
          NoTinReason.NOT_ISSUE,
          NoTinReason.DOES_NOT_GET_TIN,
          NoTinReason.NO_TIN_REQUIRED,
        ])
        .nullable(),
    }),
  ),
});

export type FatcaForm = InferType<typeof fatcaSchema>;

export const fatcaFormDefaultValue: FatcaForm = {
  hasOutsideTaxResidency: '',
  hasUsTaxResidency: '',
  noReason: '',
  taxResidencies: [],
};

export const toFatca = (form: FatcaForm, agentId: string): ProposerConsent => {
  const hasForeignTaxResidency =
    form.hasOutsideTaxResidency === 'yes' || form.hasUsTaxResidency === 'yes';
  if (hasForeignTaxResidency) {
    return {
      agentId,
      noReason: form.noReason || '',
      otherCountryTax: form.hasOutsideTaxResidency === 'yes',
      usGreenCardHolder: form.hasUsTaxResidency === 'yes',
      otherCountryTaxArray:
        form.taxResidencies?.map(i => ({
          agentId,
          country: i.country,
          tinNumber: i.tinNumber || '',
          noTinReason: i.noTinReason as string,
        })) || [],
    };
  } else {
    return {
      agentId,
      noReason: form.noReason || '',
      otherCountryTax: form.hasOutsideTaxResidency === 'yes',
      usGreenCardHolder: form.hasUsTaxResidency === 'yes',
      otherCountryTaxArray: [],
    };
  }
};

export const fromFatca = (
  partyRole: PartyRole,
  application?: Application,
): FatcaForm => {
  if (!application) {
    return fatcaFormDefaultValue;
  }

  const consentKey =
    consentFieldMapping[partyRole as keyof typeof consentFieldMapping];
  const consent = application[consentKey]?.[0];

  if (!consent) {
    return fatcaFormDefaultValue;
  }

  return {
    noReason: consent.noReason || '',
    hasOutsideTaxResidency:
      consent.otherCountryTax === undefined || consent.otherCountryTax === null
        ? ''
        : consent.otherCountryTax
        ? 'yes'
        : 'no',
    hasUsTaxResidency:
      consent.usGreenCardHolder === undefined ||
      consent.usGreenCardHolder === null
        ? ''
        : consent.usGreenCardHolder
        ? 'yes'
        : 'no',
    taxResidencies:
      consent.otherCountryTaxArray?.map(taxInfo => ({
        country: taxInfo.country,
        tinNumber: taxInfo.tinNumber || '',
        noTinReason: taxInfo.noTinReason as NoTinReason,
      })) ?? [],
  };
};

export const initialTaxResidencyFormData = {
  country: '',
  tinNumber: '',
  noTinReason: undefined,
};
