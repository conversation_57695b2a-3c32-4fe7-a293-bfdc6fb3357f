import { Case } from 'types/case';
import { PartyRole, PartyType } from 'types/party';
import { Quotation } from 'types/quotation';

export const getScreenFlags = (
  caseObj?: Case,
  quotation?: Quotation,
  channel?: string,
) => {
  const owner = caseObj?.parties?.find(p =>
    p.roles.includes(PartyRole.PROPOSER),
  );
  const insured = caseObj?.parties?.find(p =>
    p.roles.includes(PartyRole.INSURED),
  );

  const renewalPayer = caseObj?.parties?.find(p =>
    p.roles.includes(PartyRole.RENEWAL_PAYER),
  );

  const isIndividual = owner?.clientType === PartyType.INDIVIDUAL;
  const hasInsured = owner?.id !== insured?.id;
  const hasPayor = Boolean(caseObj?.havePayer);
  const hasRenewalPayer = !!renewalPayer && renewalPayer?.id !== owner?.id;

  const hasChargePremium = quotation?.plans[0].isVUL;

  return {
    hasInsured,
    hasPayor,

    hasChargePremium,
    hasR<PERSON>wal<PERSON>ayer,
  };
};
