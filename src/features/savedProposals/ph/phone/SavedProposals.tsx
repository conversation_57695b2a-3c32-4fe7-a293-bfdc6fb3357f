import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { EmptyMessage } from 'components/Message/EmptyMessage';
import { LoadingFailMessage } from 'components/Message/LoadingFailMessage';
import { LoadingMessage } from 'components/Message/LoadingMessage';
import { sub } from 'date-fns';
import { FilterButton } from 'features/savedProposals/components//header/FilterButton';
import Header from 'features/savedProposals/components/header/Header';
import { SearchButton } from 'features/savedProposals/components/header/SearchButton';
import { ListFooter } from 'features/savedProposals/components/ListFooter';
import { Icon, Typography, Row } from 'cube-ui-components';
import {
  QUERY_STATUS_HEIGHT,
  QueryStatus,
} from 'features/savedProposals/components/QueryStatus';
import {
  getStatusFilterParams,
  updateDateFilterConfig,
} from 'features/savedProposals/config';
import {
  FakeHeader,
  HEADER_HEIGHT,
} from 'features/savedProposals/proposalList/components/Header';
import ProposalList from 'features/savedProposals/proposalList/ProposalList';
import { useBottomBar } from 'hooks/useBottomBar';
import useBoundStore from 'hooks/useBoundStore';
import { useGetSavedProposals } from 'hooks/useGetSavedProposals';
import {
  useGetSavedProposalCountMap,
  useGetSavedProposalsFilters,
} from 'hooks/useGetSavedProposalsFilters';
import _ from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FlatList,
  LayoutChangeEvent,
  StyleSheet,
  TouchableOpacity,
  Text,
  Vibration,
} from 'react-native';
import Animated, {
  Extrapolate,
  interpolate,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
  runOnJS,
} from 'react-native-reanimated';
import * as ScreenOrientation from 'expo-screen-orientation';
import { RootStackParamList, ShownMainTabParamList } from 'types';
import { CaseStatus } from 'types/case';
import { SavedProposal } from 'types/proposal';
import { country } from 'utils/context';
import GATracking from 'utils/helper/gaTracking';

export type SavedProposalFilterProps = {
  order: 'newest' | 'oldest';
  interval: string;
};

const currentCountryDefaultStatusFilterParams = getStatusFilterParams();

export default function SavedProposals() {
  const { params } = useRoute<RouteProp<ShownMainTabParamList, 'Proposals'>>();
  const { sizes, colors } = useTheme();
  const { showBottomBar, hideBottomBar } = useBottomBar();
  const { t } = useTranslation(['savedProposals', 'common']);
  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();

  const caseActions = useBoundStore(state => state.caseActions);
  const quotationActions = useBoundStore(state => state.quotationActions);

  const [order, setOrder] = useState<'newest' | 'oldest'>('newest');
  const [interval, setInterval] = useState<string>(
    country === 'id' ? '90' : '60',
  );
  const [statusFilter, setStatusFilter] = useState<CaseStatus[]>([]);
  const [isLandscape, setIsLandscape] = useState(false);

  const floatingProgress = useSharedValue(1);
  const contentHeight = useSharedValue(0);
  const svHeight = useSharedValue(0);
  const { current: today } = useRef(new Date());

  const {
    isLoading,
    isError,
    isFetchingNextPage,
    isRefetching,
    refetch,
    hasNextPage,
    fetchNextPage,
    data,
  } = useGetSavedProposals({
    offset: 0,
    limit: 20,
    status:
      statusFilter.length > 0
        ? statusFilter
        : currentCountryDefaultStatusFilterParams,
    start: sub(today, { days: parseInt(interval) }).toISOString(),
    sort_by: order === 'oldest' ? '+updatedAt' : '-updatedAt',
    q: undefined,
  });

  const renderFooter = useCallback(
    (loading?: boolean) => <ListFooter loading={loading} />,
    [],
  );

  const savedProposals = useMemo(() => {
    if (!data) return [];
    return data?.pages.map(page => page.data).flat();
  }, [data]);

  const scrollClamp = useSharedValue(0);

  const scrollHandler = useAnimatedScrollHandler<{ prevY: number }>({
    onScroll: (event, ctx) => {
      const maxScroll = contentHeight.value - svHeight.value;
      const scrollY = event.contentOffset.y;
      if (scrollY > maxScroll) {
        return;
      }
      const diff = scrollY - ctx.prevY;
      const same =
        scrollY > 0 && scrollY >= maxScroll && ctx.prevY >= maxScroll;
      scrollClamp.value =
        scrollY <= 0
          ? 0
          : same
          ? scrollClamp.value
          : clamp(scrollClamp.value + diff, 0, QUERY_STATUS_HEIGHT);
      ctx.prevY = scrollY;
    },
    onBeginDrag: (event, ctx) => {
      ctx.prevY = event.contentOffset.y;
    },
  });

  const headerStyle = useAnimatedStyle(() => {
    const interpolateY = interpolate(
      scrollClamp.value,
      [0, QUERY_STATUS_HEIGHT],
      [0, -QUERY_STATUS_HEIGHT],
      Extrapolate.CLAMP,
    );

    return {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      transform: [{ translateY: interpolateY }],
    };
  });

  useFocusEffect(
    useCallback(() => {
      // Only show bottom bar initially if not in landscape
      if (!isLandscape) {
        showBottomBar();
      }

      // Listen for orientation changes
      const subscription = ScreenOrientation.addOrientationChangeListener(
        event => {
          const orientation = event.orientationInfo.orientation;
          const newIsLandscape =
            orientation === ScreenOrientation.Orientation.LANDSCAPE_LEFT ||
            orientation === ScreenOrientation.Orientation.LANDSCAPE_RIGHT;

          setIsLandscape(newIsLandscape);

          // Hide/show bottom bar based on orientation
          if (newIsLandscape) {
            hideBottomBar();
          } else {
            showBottomBar();
          }
        },
      );

      return () => {
        // Only show bottom bar when leaving if not in landscape
        if (!isLandscape) {
          showBottomBar();
        }
        // Reset to portrait when leaving the screen
        if (isLandscape) {
          ScreenOrientation.lockAsync(
            ScreenOrientation.OrientationLock.PORTRAIT_UP,
          );
          setIsLandscape(false);
        }
        // Remove orientation listener
        subscription?.remove();
      };
    }, [isLandscape, showBottomBar, hideBottomBar]),
  );

  const onContentSizeChange = useCallback((w: number, h: number) => {
    contentHeight.value = h;
  }, []);

  const onLayout = useCallback(
    (e: LayoutChangeEvent) => {
      svHeight.value = e.nativeEvent.layout.height;
    },
    [svHeight],
  );

  const listRef = useRef<FlatList<SavedProposal>>(null);

  const scrollToTop = useCallback(() => {
    listRef.current?.scrollToOffset({ animated: false, offset: 0 });
  }, []);

  const onApply = useCallback(
    (newStatusFilter: CaseStatus[]) => {
      setStatusFilter(newStatusFilter);

      const oldFilter = [...statusFilter];
      const changed =
        JSON.stringify(oldFilter.sort()) !==
        JSON.stringify(newStatusFilter.sort());
      if (changed) {
        scrollToTop();
      }
    },
    [statusFilter],
  );

  const onToggleSort = useCallback(() => {
    setOrder(order => (order === 'newest' ? 'oldest' : 'newest'));
    scrollToTop();
  }, []);

  const handleLandscapeToggle = useCallback(async () => {
    try {
      Vibration.vibrate(50); // Haptic feedback
      await ScreenOrientation.lockAsync(
        ScreenOrientation.OrientationLock.LANDSCAPE,
      );
      setIsLandscape(true);
      hideBottomBar();
    } catch (error) {
      console.error('Failed to lock to landscape:', error);
    }
  }, [hideBottomBar]);

  const handlePortraitToggle = useCallback(async () => {
    try {
      Vibration.vibrate(50); // Haptic feedback
      await ScreenOrientation.lockAsync(
        ScreenOrientation.OrientationLock.PORTRAIT_UP,
      );
      setIsLandscape(false);
      showBottomBar(); // Show bottom bar when switching to portrait
    } catch (error) {
      console.error('Failed to lock to portrait:', error);
    }
  }, [showBottomBar]);

  const onProposalItemPress = useCallback(
    (data: SavedProposal) => {
      caseActions.setActiveCase(data.caseId);
      switch (data.latestStatus) {
        case CaseStatus.QUICK_SI:
        case CaseStatus.FULL_SI:
          data.quotationId && quotationActions.setQuotationId(data.quotationId);
          navigate('SalesIllustrationForm', { from: 'saved_proposals' }); // TODO: need to check if is start RPQ
          break;
        case CaseStatus.IN_APP:
          navigate('EApp');

          GATracking.logCustomEvent('application', {
            action_type: 'eapp_open_form',
            form_source: 'saved_proposals ',
          });
          break;
        case CaseStatus.CFF:
          navigate('CustomerFactFind');
          break;
        case CaseStatus.FNA:
          navigate('Fna');
          break;
        default:
          console.warn(`Need to handle ${data.latestStatus} case status`);
      }
    },
    [navigate, quotationActions],
  );

  useEffect(() => {
    if (params?.preFilter === 'inProgress') {
      setStatusFilter([
        CaseStatus['FNA'],
        CaseStatus['FULL_SI'],
        CaseStatus['QUICK_SI'],
      ]);
    } else if (params?.preFilter === 'inSI') {
      setStatusFilter([CaseStatus['FULL_SI']]);
    } else if (params?.preFilter === 'inApplication') {
      setStatusFilter([CaseStatus['IN_APP']]);
    } else setStatusFilter([]);
    console.log('🚀 ~ SavedProposals ~ params:', params, statusFilter);
  }, [params]);

  /**
   * Saved Proposals counts
   */
  const startDateOfProposals = updateDateFilterConfig.showFilter
    ? sub(today, { days: parseInt(interval) }).toISOString()
    : undefined;

  const {
    data: proposalFilterCounts,
    // isLoading: isProposalFilterCountLoading,
  } = useGetSavedProposalsFilters({
    start: startDateOfProposals,
    clientTypes: undefined,
  });
  const totalProposalCount = useGetSavedProposalCountMap(proposalFilterCounts);

  const currentCount = useMemo(() => {
    return statusFilter.length == 0
      ? totalProposalCount.total
      : statusFilter.reduce((acc, cur) => {
          const key = cur as keyof typeof totalProposalCount;
          if (key in totalProposalCount) {
            acc += totalProposalCount?.[key] ?? 0;
          }
          return acc;
        }, 0);
  }, [statusFilter, totalProposalCount]);

  if (isLoading) {
    return (
      <>
        <Header />
        <LoadingMessage
          style={{ ...StyleSheet.absoluteFillObject, top: HEADER_HEIGHT }}
          message={t('savedProposals:loadingProposalsMessage', {
            day: interval,
          })}
        />
      </>
    );
  }

  return (
    <Container>
      {!isLandscape && (
        <Header>
          <SearchButton limit={20} offset={0} status={statusFilter} />
          <FilterButton options={statusFilter} onApply={onApply} />
        </Header>
      )}

      {!isLandscape && (
        <Animated.View>
          <QueryStatus
            count={currentCount}
            isFiltered={!_.isEmpty(statusFilter)}
            disabled={savedProposals?.length === 0}
            order={order}
            onToggleSort={onToggleSort}
            interval={interval}
            onChangeInterval={setInterval}
          />
        </Animated.View>
      )}

      <ProposalList
        ref={listRef}
        data={savedProposals}
        onPressItem={onProposalItemPress}
        renderFooter={renderFooter}
        refreshEnable
        onRefresh={refetch}
        loadMoreEnable={hasNextPage}
        loadingMore={isFetchingNextPage}
        onLoadMore={fetchNextPage}
        refreshing={isRefetching}
        ListEmptyComponent={
          isError ? (
            <LoadingFailMessage message={t('common:loadingFail')} />
          ) : (
            <EmptyMessage message={t('savedProposals:noResultsFound')} />
          )
        }
        onScroll={scrollHandler}
        headerStyle={
          savedProposals?.length > 0 || isError
            ? headerStyle
            : { width: 0, height: 0 }
        }
        scrollEventThrottle={sizes[4]}
        scrollIndicatorInsets={scrollIndicatorInsets}
        progressViewOffset={HEADER_HEIGHT}
        ListHeaderComponent={<FakeHeader />}
        headerLast
        onContentSizeChange={onContentSizeChange}
        onLayout={onLayout}
        isLandscape={isLandscape}
      />

      {/* Landscape Toggle Button - Bottom Center */}
      {!isLandscape && country === 'my' && (
        <LandscapeToggleButton onPress={handleLandscapeToggle}>
          <Row gap={sizes[2]} alignItems="center">
            <Icon.Expand fill={colors.palette.fwdAlternativeOrange[100]} />
            <Typography.H7
              fontWeight="bold"
              color={colors.palette.fwdAlternativeOrange[100]}>
              Full table
            </Typography.H7>
          </Row>
        </LandscapeToggleButton>
      )}

      {/* Portrait Toggle Button - Top Right */}
      {isLandscape && country === 'my' && (
        <PortraitToggleButton onPress={handlePortraitToggle}>
          <Icon.Close fill={colors.palette.fwdDarkGreen[100]} />
        </PortraitToggleButton>
      )}
    </Container>
  );
}

const clamp = (value: number, lowerBound: number, upperBound: number) => {
  'worklet';
  return Math.min(Math.max(lowerBound, value), upperBound);
};

const scrollIndicatorInsets = { top: HEADER_HEIGHT };

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
}));

const LandscapeToggleButton = styled.TouchableOpacity(({ theme }) => ({
  position: 'absolute',
  bottom: theme.space[30],
  alignSelf: 'center',
  backgroundColor: theme.colors.palette.white,
  width: theme.sizes[34],
  height: theme.sizes[9],
  borderRadius: theme.sizes[6],
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 1000,
  shadowColor: theme.colors.palette.black,
  shadowOffset: { width: 0, height: theme.sizes[1] },
  shadowOpacity: 0.1,
}));

const PortraitToggleButton = styled.TouchableOpacity(({ theme }) => ({
  position: 'absolute',
  top: theme.space[6],
  right: theme.space[4],
  backgroundColor: theme.colors.palette.white,
  width: theme.sizes[10],
  height: theme.sizes[10],
  borderRadius: theme.sizes[5],
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 1000,
}));
