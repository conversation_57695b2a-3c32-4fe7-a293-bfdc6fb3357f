import { memo, useCallback, useMemo, useState } from 'react';
import { IconContainer } from './IconContainer';
import { FilterIcon } from './FilterIcon';
import { ActionPanel, Box, Button, Chip, H8 } from 'cube-ui-components';
import styled from '@emotion/native';
import { Platform, StyleSheet, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CaseStatus } from 'types/case';
import {
  filterTagsConfig,
  isProposalStagesEnabled,
} from 'features/savedProposals/config';
import { FilterTagConfig } from 'features/savedProposals/types';

export const FilterButton = memo(function FilterButton({
  options: optionsProp,
  onApply: onApplyAction,
}: {
  onApply: (options: CaseStatus[]) => void;
  options: CaseStatus[];
}) {
  const { t } = useTranslation(['savedProposals']);
  const [visible, setVisible] = useState(false);
  const [options, setOptions] = useState(optionsProp);
  const { bottom } = useSafeAreaInsets();

  const onShow = useCallback(() => {
    setVisible(true);
    setOptions(optionsProp);
  }, [optionsProp]);

  const handleClose = useCallback(() => {
    setVisible(false);
  }, []);

  const optionLabels = useMemo<FilterTagConfig[]>(() => filterTagsConfig, []);

  const onClear = useCallback(() => {
    setOptions([]);
  }, []);

  const onApply = useCallback(() => {
    setVisible(false);
    onApplyAction(options);
  }, [onApplyAction, options]);

  const setOption = useCallback((key: CaseStatus) => {
    return () => {
      setOptions(options =>
        options.includes(key)
          ? options.filter(i => i !== key)
          : options.concat(key),
      );
    };
  }, []);

  return (
    <>
      <IconContainer onPress={onShow}>
        <FilterIcon active={optionsProp.length > 0} />
      </IconContainer>
      <ActionPanel
        handleClose={handleClose}
        visible={visible}
        title={t('savedProposals:filterBy')}
        containerStyle={{ paddingBottom: 0 }}
        contentContainerStyle={{ paddingTop: 12, paddingBottom: bottom }}>
        <FilterColumn>
          {optionLabels.map(({ type: key, label: value }) => {
            if (isProposalStagesEnabled(key)) {
              return (
                <Chip
                  style={{ alignSelf: 'flex-start' }}
                  key={key}
                  label={t(value)}
                  focus={options.includes(key)}
                  onPress={setOption(key)}
                />
              );
            }
          })}
        </FilterColumn>
        <PartitionLine />
        <ButtonGroup>
          <Button
            style={styles.button}
            text={t('savedProposals:clearAll')}
            disabled={options.length === 0}
            variant="secondary"
            onPress={onClear}
          />
          <Spacing />
          <Button
            style={styles.button}
            text={t('savedProposals:apply')}
            variant="primary"
            onPress={onApply}
          />
        </ButtonGroup>
      </ActionPanel>
    </>
  );
});

const FilterColumn = styled(Box)(({ theme }) => ({
  marginTop: theme.space[2],
  gap: theme.space[2],
  flexDirection: 'row',
  flexWrap: 'wrap',
}));

const PartitionLine = styled(Box)(({ theme }) => ({
  height: 1,
  backgroundColor: theme.colors.palette.fwdGrey[100],
  marginTop: theme.space[6],
  width: '200%',
  alignSelf: 'center',
}));

const ButtonGroup = styled.View(({ theme }) => ({
  marginTop: theme.space[4],
  marginBottom: Platform.OS === 'ios' ? theme.space[2] : theme.space[4],
  flexDirection: 'row',
}));

const Spacing = styled.View(({ theme }) => ({
  width: theme.space[4],
}));

const styles = StyleSheet.create({
  button: {
    flex: 1,
  },
  contentStyle: {
    padding: 0,
    flex: 1,
  },
  textStyle: {
    fontSize: 16,
    lineHeight: 20,
  },
});
