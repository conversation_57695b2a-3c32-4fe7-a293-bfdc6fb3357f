import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Body, Row, Text } from 'cube-ui-components';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { country } from 'utils/context';
import { ShowIn } from './ShowIn';

export const QUERY_STATUS_HEIGHT = 60;

export const QueryStatus = memo(function QueryStatus({
  count,
  isFiltered,
  disabled,
  order,
  onToggleSort,
  interval,
  onChangeInterval,
}: {
  count: number;
  isFiltered: boolean;
  disabled: boolean;
  order: 'newest' | 'oldest';
  onToggleSort: () => void;
  interval: string;
  onChangeInterval: (value: string) => void;
}) {
  const { t } = useTranslation(['savedProposals']);
  const { space } = useTheme();

  const isPH = country === 'ph';
  const isID = country === 'id';
  const isMY = country === 'my';

  return (
    <Container>
      <Row gap={space[4]}>
        <Group onPress={onToggleSort} disabled={disabled}>
          <Label>{t('savedProposals:sortByTime')}</Label>
          <Status fontWeight="bold" style={{ opacity: disabled ? 0.5 : 1 }}>
            {t(
              order === 'newest'
                ? 'savedProposals:newest'
                : 'savedProposals:oldest',
            )}
          </Status>
        </Group>

        {(isPH || isID) && (
          <ShowIn value={interval} onApply={onChangeInterval} />
        )}
      </Row>

      {(isPH || isMY) && (
        <Body>
          {isFiltered && !isMY
            ? t('savedProposals:filtered')
            : t('savedProposals:totalSavedProposal')}
          {` (${count})`}
        </Body>
      )}
    </Container>
  );
});

const Container = styled.View(({ theme }) => ({
  height: QUERY_STATUS_HEIGHT,
  backgroundColor: theme.colors.surface,
  paddingHorizontal: theme.space[3],
  justifyContent: 'space-between',
  display: 'flex',
  alignItems: 'center',
  flexDirection: 'row',
  gap: theme.space[1],
}));

const Group = styled.Pressable(() => ({
  flexDirection: 'row',
  alignItems: 'center',
}));

const Label = styled(Text)(({ theme }) => ({
  color: theme.colors.secondary,
  fontSize: theme.typography.label.size,
}));

const Status = styled(Text)(({ theme }) => ({
  color: theme.colors.palette.fwdAlternativeOrange[100],
  fontSize: theme.typography.label.size,
  marginLeft: theme.space[1],
}));
