import { forwardRef, memo, useMemo } from 'react';
import { Header } from './components/Header';
import styled from '@emotion/native';
import ListItem, { ListItemProps } from './components/ListItem';
import { Footer } from './components/Footer';
import {
  FlatList,
  FlatListProps,
  RefreshControl,
  ViewStyle,
} from 'react-native';
import Animated, { AnimatedStyleProp } from 'react-native-reanimated';
import { SavedProposal } from 'types/proposal';
import { LandScapeTable } from './components/LandScapeTable';

type RNGHFlatListProps<T> = Animated.AnimateProps<
  FlatListProps<T> & {
    ref: React.Ref<FlatList<T>>;
  }
>;

const AnimatedFlatList = Animated.createAnimatedComponent(
  FlatList,
) as unknown as <T>(props: RNGHFlatListProps<T>) => React.ReactElement;

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.surface,
}));
interface Props extends Partial<FlatListProps<SavedProposal>> {
  data: SavedProposal[];
  onPressItem: ListItemProps['onPress'];
  refreshing?: boolean;
  onRefresh?: () => void;
  loadingMore?: boolean;
  onLoadMore?: () => void;
  renderFooter?: (
    loading?: boolean,
  ) => FlatListProps<SavedProposal>['ListFooterComponent'];
  refreshEnable?: boolean;
  loadMoreEnable?: boolean;
  headerStyle?: AnimatedStyleProp<ViewStyle>;
  progressViewOffset?: number;
  headerLast?: boolean;
  isLandscape?: boolean;
}

const ProposalList = memo(
  forwardRef<FlatList<SavedProposal>, Props>(function ProposalList(
    {
      data,
      onPressItem,
      onLoadMore,
      loadingMore,
      renderFooter,
      refreshing,
      onRefresh,
      refreshEnable,
      loadMoreEnable,
      headerStyle,
      progressViewOffset,
      headerLast,
      isLandscape,
      ...rest
    }: Props,
    ref,
  ) {
    // If landscape, use the new table component
    if (isLandscape) {
      return (
        <LandScapeTable
          data={data}
          onPressItem={onPressItem}
          refreshing={refreshing}
          onRefresh={onRefresh}
          loadingMore={loadingMore}
          onLoadMore={onLoadMore}
          refreshEnable={refreshEnable}
          loadMoreEnable={loadMoreEnable}
          headerStyle={headerStyle}
          progressViewOffset={progressViewOffset}
        />
      );
    }

    // Original portrait list logic
    const renderItem = ({
      item,
      index,
    }: {
      item: SavedProposal;
      index: number;
    }) => (
      <ListItem onPress={() => onPressItem?.(item)} data={item} index={index} />
    );

    const footer = useMemo(
      () =>
        renderFooter ? (
          renderFooter(loadingMore)
        ) : (
          <Footer loading={loadingMore} />
        ),
      [renderFooter, loadingMore],
    );

    return (
      <Container>
        {!headerLast && <Header style={headerStyle} />}
        <AnimatedFlatList
          ref={ref}
          data={data}
          renderItem={renderItem}
          ListFooterComponent={footer}
          onEndReached={loadMoreEnable && onLoadMore ? onLoadMore : undefined}
          refreshControl={
            refreshEnable ? (
              <RefreshControl
                refreshing={!!refreshing}
                onRefresh={onRefresh}
                progressViewOffset={progressViewOffset}
              />
            ) : undefined
          }
          {...rest}
        />
        {!!headerLast && <Header style={headerStyle} />}
      </Container>
    );
  }),
);

export default ProposalList;
