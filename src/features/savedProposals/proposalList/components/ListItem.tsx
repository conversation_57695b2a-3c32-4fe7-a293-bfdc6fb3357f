import styled from '@emotion/native';
import { Icon, Text, Typography } from 'cube-ui-components';
import React, { memo, useMemo } from 'react';
import { useTheme } from '@emotion/react';
import { SavedProposal } from 'types/proposal';
import { useTranslation } from 'react-i18next';
import { PartyRole } from 'types/party';
import { isAfter, parse } from 'date-fns';
import { View } from 'react-native';
import { remoteSellingStatus } from 'features/savedProposals/utli';
import { ProposalIcon } from './SyncedLandScapeListItem';

export interface ListItemProps {
  data: SavedProposal;
  index: number;
  onPress?: (data: SavedProposal) => void;
}

const Container = styled.TouchableHighlight<{ index: number }>(
  ({ theme, index }) => ({
    backgroundColor:
      index % 2 === 1
        ? theme.colors.palette.fwdGrey[20]
        : theme.colors.background,
    padding: theme.space[3],
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomColor: theme.colors.palette.fwdGrey[100],
    borderBottomWidth: 1,
  }),
);
const FirstCell = styled.View(({ theme }) => ({
  width: `${(205 / 351) * 100}%`,
  paddingRight: theme.space[3],
  flexDirection: 'row',
  gap: theme.space[1],
}));

const Name = styled(Text)(({ theme }) => ({
  color: theme.colors.secondary,
  fontSize: theme.typography.h7.size,
}));

const Insured = styled(Text)(({ theme }) => ({
  color: theme.colors.placeholder,
  marginTop: theme.space[1],
  fontSize: theme.typography.body.size,
  lineHeight: theme.typography.body.lineHeight,
}));

const Status = styled(Text)(({ theme }) => ({
  color: theme.colors.palette.fwdBlue[100],
  fontSize: theme.typography.h8.size,
}));

const SecondCell = styled.View(({ theme }) => ({
  flex: 1,
  marginRight: theme.space[2],
}));

const StatusContainer = styled.View(({ theme }) => ({
  paddingHorizontal: theme.space[1],
  paddingVertical: 2,
  backgroundColor: theme.colors.palette.fwdLightGreen[20],
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: 2,
  alignSelf: 'flex-start',
}));

const ExpiredContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  marginTop: theme.space[1],
}));

interface Props {
  data: SavedProposal;
  index: number;
  onPress?: (data: SavedProposal) => void;
  disabled?: boolean;
}

const ListItem = memo(function ListItem({
  data,
  index,
  onPress,
  disabled,
}: Props) {
  const { t } = useTranslation(['savedProposals']);
  const { quotation, parties, latestStatus, isRemoteSelling, remoteSelling } =
    data;
  const { colors, space } = useTheme();
  const insured = useMemo(
    () => parties.find(party => party.roles.includes(PartyRole.INSURED)),
    [parties],
  );

  const isExpired = useMemo(() => {
    if (!data.quotation?.expiryDate) {
      return false;
    }

    const ed = parse(data.quotation?.expiryDate, 'yyyy-MM-dd', new Date());
    if (!ed) {
      return false;
    }

    return isAfter(Date.now(), ed);
  }, [data.quotation?.expiryDate]);

  const displayName = insured?.person?.name.firstName
    ? `${insured?.person?.name?.firstName ?? ''} ${
        insured?.person?.name?.lastName ?? ''
      }`
    : `${data?.fna?.firstName ?? ''} ${data?.fna?.lastName ?? ''}`;
  const remoteSellingStatusName = remoteSellingStatus(remoteSelling);
  return (
    <Container
      disabled={disabled}
      onPress={() => onPress?.(data)}
      index={index}
      underlayColor={colors.palette.fwdGrey[50]}>
      <>
        <FirstCell>
          <ProposalIcon data={data} />
          <View>
            <Name numberOfLines={2}>{quotation?.quotationName}</Name>
            <Insured numberOfLines={1}>{displayName}</Insured>
          </View>
        </FirstCell>
        <SecondCell>
          <StatusContainer>
            <Status numberOfLines={1}>
              {t(`savedProposals:filter.${latestStatus}` as any)}
            </Status>
          </StatusContainer>
          {isExpired && (
            <ExpiredContainer>
              <View style={{ marginRight: space[1] }}>
                <Icon.OperatingHours fill={colors.error} />
              </View>
              <Typography.Body color={colors.error}>
                {t('savedProposals:expired')}
              </Typography.Body>
            </ExpiredContainer>
          )}
          {isRemoteSelling && !!remoteSellingStatusName && (
            <Typography.H8
              style={{ marginTop: space[1] }}
              color={colors.palette.fwdGreyDarker}>
              {t(remoteSellingStatusName as any)}
            </Typography.H8>
          )}
        </SecondCell>
        {latestStatus != 'APP_SUBMITTED' && <Icon.ChevronRight />}
      </>
    </Container>
  );
});

export default ListItem;
