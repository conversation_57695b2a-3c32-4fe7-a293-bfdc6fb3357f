import styled from '@emotion/native';
import { Icon, Text, Typography } from 'cube-ui-components';
import React, { memo, useMemo } from 'react';
import { useTheme } from '@emotion/react';
import { SavedProposal } from 'types/proposal';
import { useTranslation } from 'react-i18next';
import { PartyRole, PartyType } from 'types/party';
import { isAfter, parse } from 'date-fns';
import { View, ScrollView } from 'react-native';
import { remoteSellingStatus } from 'features/savedProposals/utli';
import { checkEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { Case } from 'types/case';
import { getProductName } from 'features/eApp/utils/eAppFormat';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import {
  paymentKeyMapping,
  paymentKeyToPremiumKey,
} from 'features/savedProposals/tablet/config';
import Table from 'components/Table';
import { dateFormatUtil } from 'utils/helper/formatUtil';

const Container = styled.TouchableHighlight<{ index: number }>(
  ({ theme, index }) => ({
    backgroundColor:
      index % 2 === 1
        ? theme.colors.palette.fwdGrey[20]
        : theme.colors.background,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomColor: theme.colors.palette.fwdGrey[100],
    borderBottomWidth: 1,
  }),
);

const StickyColumn = styled.View<{ index: number }>(({ theme, index }) => ({
  backgroundColor:
    index % 2 === 1
      ? theme.colors.palette.fwdGrey[20]
      : theme.colors.background,
  width: `${(232 / 812) * 100}%`,
  padding: theme.space[3],
  borderRightColor: theme.colors.palette.fwdGrey[100],
  borderRightWidth: 1,
  zIndex: 1,
  shadowColor: theme.colors.palette.black,
  shadowOffset: {
    width: 2,
    height: 0,
  },
  shadowOpacity: 0.15,
  shadowRadius: 2,
  elevation: 3, // For Android
}));

const ScrollableContainer = styled.View(() => ({
  flex: 1,
}));

const ColumnsContainer = styled.View(() => ({
  flexDirection: 'row',
  minWidth: 600, // Minimum width to ensure proper column spacing
}));

const Cell = styled(Typography.LargeBody)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
  fontWeight: 400,
}));

const Column = styled.View(({ theme }) => ({
  minWidth: theme.space[30],
  width: theme.space[30], // Fixed width to ensure alignment
  padding: theme.space[3],
  justifyContent: 'center',
}));

const Name = styled(Text)(({ theme }) => ({
  color: theme.colors.secondary,
  fontSize: theme.typography.h7.size,
}));

const Insured = styled(Text)(({ theme }) => ({
  color: theme.colors.placeholder,
  marginTop: theme.space[1],
  fontSize: theme.typography.body.size,
  lineHeight: theme.typography.body.lineHeight,
}));

const Status = styled(Text)(({ theme }) => ({
  color: theme.colors.palette.fwdBlue[100],
  fontSize: theme.typography.h8.size,
}));

const StatusContainer = styled.View(({ theme }) => ({
  paddingHorizontal: theme.space[1],
  paddingVertical: 2,
  backgroundColor: theme.colors.palette.fwdLightGreen[20],
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: 2,
  alignSelf: 'flex-start',
}));

const ExpiredContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  marginTop: theme.space[1],
}));

interface Props {
  data: SavedProposal;
  index: number;
  onPress?: (data: SavedProposal) => void;
  disabled?: boolean;
  scrollViewRef?: React.RefObject<ScrollView>;
  onScroll?: (event: any) => void;
}

export const SyncedLandScapeListItem = memo(function SyncedLandScapeListItem({
  data,
  index,
  onPress,
  disabled,
  scrollViewRef,
  onScroll,
}: Props) {
  const { t } = useTranslation(['savedProposals']);
  const { quotation, parties, latestStatus, isRemoteSelling, remoteSelling } =
    data;

  const isEntity = useMemo(() => {
    return checkEntity(data as unknown as Case);
  }, [data]);
  const { colors, space } = useTheme();

  const insuredDisplayName = useMemo(() => {
    const insured = parties.find(party =>
      party.roles.includes(PartyRole.INSURED),
    );

    return insured?.person?.name.firstName
      ? `${insured?.person?.name?.firstName ?? ''} ${
          insured?.person?.name?.lastName ?? ''
        }`
      : `${data?.fna?.firstName ?? ''} ${data?.fna?.lastName ?? ''}`;
  }, [parties, data]);

  const proposerDisplayName = useMemo(() => {
    const proposer = parties.find(party =>
      party.roles.includes(PartyRole.PROPOSER),
    );

    if (isEntity) {
      return proposer?.entity?.name ?? '';
    }

    return proposer?.person?.name.firstName
      ? `${proposer?.person?.name?.firstName ?? ''} ${
          proposer?.person?.name?.lastName ?? ''
        }`
      : '';
  }, [parties, data, isEntity]);

  const isExpired = useMemo(() => {
    if (!data.quotation?.expiryDate) {
      return false;
    }

    const ed = parse(data.quotation?.expiryDate, 'yyyy-MM-dd', new Date());
    if (!ed) {
      return false;
    }

    return isAfter(Date.now(), ed);
  }, [data.quotation?.expiryDate]);

  const remoteSellingStatusName = remoteSellingStatus(remoteSelling);

  const handlePress = () => {
    if (!disabled) {
      onPress?.(data);
    }
  };

  return (
    <Container
      index={index}
      onPress={handlePress}
      disabled={disabled}
      underlayColor={colors.palette.fwdGrey[50]}>
      <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
        <StickyColumn index={index}>
          <View style={{ flexDirection: 'row', gap: space[1] }}>
            <ProposalIcon data={data} />
            <View>
              <Name numberOfLines={2}>{proposerDisplayName}</Name>
              <Insured numberOfLines={1}>{insuredDisplayName}</Insured>
            </View>
          </View>
        </StickyColumn>
        <ScrollableContainer>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            onScroll={onScroll}
            scrollEventThrottle={16}
            bounces={false}>
            <ColumnsContainer>
              {/* Product */}
              <Column>
                <Cell>
                  {quotation?.plans?.[0].productName
                    ? getProductName(quotation?.plans?.[0].productName)
                    : '--'}
                </Cell>
              </Column>

              {/* Proposal stage */}
              <Column>
                <StatusContainer>
                  <Status numberOfLines={1}>
                    {t(`savedProposals:filter.${latestStatus}` as any)}
                  </Status>
                </StatusContainer>
                {isExpired && (
                  <ExpiredContainer>
                    <View style={{ marginRight: space[1] }}>
                      <Icon.OperatingHours fill={colors.error} />
                    </View>
                    <Cell color={colors.error}>
                      {t('savedProposals:expired')}
                    </Cell>
                  </ExpiredContainer>
                )}
                {isRemoteSelling && !!remoteSellingStatusName && (
                  <Typography.H8
                    style={{ marginTop: space[1] }}
                    color={colors.palette.fwdGreyDarker}>
                    {t(remoteSellingStatusName as any)}
                  </Typography.H8>
                )}
              </Column>

              {/* Sum assured (RM) */}
              <Column>
                <Cell color={colors.palette.fwdGreyDarker}>
                  {data.quotation?.plans?.[0].sumAssured
                    ? numberToThousandsFormat(
                        data.quotation?.plans?.[0].sumAssured,
                      )
                    : '--'}
                </Cell>
              </Column>

              {/* Modal premium (RM) */}
              <Column>
                <Cell color={colors.palette.fwdGreyDarker}>
                  <ModalPremiumCell data={data} />
                </Cell>
              </Column>

              {/* Last update date */}
              <Column>
                <Cell color={colors.palette.fwdGreyDarker}>
                  {data?.updatedAt ? dateFormatUtil(data?.updatedAt) : '--'}
                </Cell>
              </Column>
            </ColumnsContainer>
          </ScrollView>
        </ScrollableContainer>
      </View>
    </Container>
  );
});

interface ProposalIconProps {
  data: SavedProposal;
}
export const ProposalIcon = memo(function ProposalIcon({
  data,
}: ProposalIconProps) {
  const isEntity = checkEntity(data as unknown as Case);
  const { sizes, colors } = useTheme();
  if (isEntity) {
    return (
      <Icon.Agency size={sizes[4]} fill={colors.palette.fwdDarkGreen[100]} />
    );
  }
  return (
    <Icon.Account size={sizes[4]} fill={colors.palette.fwdDarkGreen[100]} />
  );
});

const ModalPremiumCell = memo(function ModalPremiumCell({
  data,
}: {
  data: SavedProposal;
}) {
  const { t } = useTranslation('savedProposals');
  const paymentEnum =
    data.quotation?.basicInfo?.paymentMode ??
    data?.quotation?.plans?.[0].paymentMode;
  const activePremium = paymentEnum
    ? data?.quotation?.summary?.[paymentKeyToPremiumKey[paymentEnum]] ?? null
    : null;
  if (data?.quotation?.summary?.annualPrem == null) {
    return '--';
  }
  return (
    <>
      {numberToThousandsFormat(activePremium, undefined, '--')}
      {`\n/ ` + (paymentEnum ? t(paymentKeyMapping[paymentEnum]) : '--')}
    </>
  );
});
