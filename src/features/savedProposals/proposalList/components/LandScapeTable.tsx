import React, { forwardRef, memo, useRef } from 'react';
import styled from '@emotion/native';
import { Footer } from './Footer';
import {
  FlatList,
  FlatListProps,
  RefreshControl,
  ViewStyle,
  ScrollView,
} from 'react-native';
import Animated, { AnimatedStyleProp } from 'react-native-reanimated';
import { SavedProposal } from 'types/proposal';
import { SyncedLandScapeHeader } from './SyncedLandScapeHeader';
import { SyncedLandScapeListItem } from './SyncedLandScapeListItem';

type RNGHFlatListProps<T> = Animated.AnimateProps<
  FlatListProps<T> & {
    ref: React.Ref<FlatList<T>>;
  }
>;

const AnimatedFlatList = Animated.createAnimatedComponent(
  FlatList,
) as unknown as <T>(props: RNGHFlatListProps<T>) => React.ReactElement;

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.surface,
}));

const ContentContainer = styled.View(() => ({
  flex: 1,
}));

const HeaderSpacer = styled.View(() => ({
  height: 57, // HEADER_HEIGHT from SyncedLandScapeHeader
}));

interface Props extends Partial<FlatListProps<SavedProposal>> {
  data: SavedProposal[];
  onPressItem?: (data: SavedProposal) => void;
  refreshing?: boolean;
  onRefresh?: () => void;
  loadingMore?: boolean;
  onLoadMore?: () => void;
  renderFooter?: (
    loading?: boolean,
  ) => FlatListProps<SavedProposal>['ListFooterComponent'];
  refreshEnable?: boolean;
  loadMoreEnable?: boolean;
  headerStyle?: AnimatedStyleProp<ViewStyle>;
  progressViewOffset?: number;
  headerLast?: boolean;
  disabled?: boolean;
}

export const LandScapeTable = memo(
  forwardRef<FlatList<SavedProposal>, Props>(function LandScapeTable(
    {
      data,
      onPressItem,
      onLoadMore,
      loadingMore,
      renderFooter,
      refreshing,
      onRefresh,
      refreshEnable,
      loadMoreEnable,
      headerStyle,
      progressViewOffset,
      headerLast,
      disabled,
      ...rest
    }: Props,
    ref,
  ) {
    const headerScrollRef = useRef<ScrollView>(null);
    const rowScrollRefs = useRef<Array<React.RefObject<ScrollView>>>([]);

    // Initialize refs for each row
    if (rowScrollRefs.current.length !== data.length) {
      rowScrollRefs.current = data.map(() => React.createRef<ScrollView>());
    }

    const handleHeaderScroll = (event: any) => {
      const scrollX = event.nativeEvent.contentOffset.x;
      // Sync all row scrolls with header
      rowScrollRefs.current.forEach((scrollRef) => {
        if (scrollRef.current) {
          scrollRef.current.scrollTo({ x: scrollX, animated: false });
        }
      });
    };

    const handleRowScroll = (event: any) => {
      const scrollX = event.nativeEvent.contentOffset.x;
      // Sync header and all other rows
      headerScrollRef.current?.scrollTo({ x: scrollX, animated: false });
      rowScrollRefs.current.forEach((scrollRef) => {
        if (scrollRef.current) {
          scrollRef.current.scrollTo({ x: scrollX, animated: false });
        }
      });
    };

    const renderItem = ({
      item,
      index,
    }: {
      item: SavedProposal;
      index: number;
    }) => (
      <SyncedLandScapeListItem
        onPress={() => onPressItem?.(item)}
        data={item}
        index={index}
        disabled={disabled}
        scrollViewRef={rowScrollRefs.current[index]}
        onScroll={handleRowScroll}
      />
    );

    const footer = renderFooter ? renderFooter(loadingMore) : <Footer loading={loadingMore} />;

    return (
      <Container>
        {!headerLast && (
          <SyncedLandScapeHeader 
            style={headerStyle} 
            scrollViewRef={headerScrollRef}
            onScroll={handleHeaderScroll}
          />
        )}
        <ContentContainer>
          {/* Add spacer when header has absolute positioning */}
          {!headerLast && <HeaderSpacer />}
          <AnimatedFlatList
            ref={ref}
            data={data}
            renderItem={renderItem}
            ListFooterComponent={footer}
            onEndReached={loadMoreEnable && onLoadMore ? onLoadMore : undefined}
            refreshControl={
              refreshEnable ? (
                <RefreshControl
                  refreshing={!!refreshing}
                  onRefresh={onRefresh}
                  progressViewOffset={progressViewOffset}
                />
              ) : undefined
            }
            {...rest}
          />
        </ContentContainer>
        {!!headerLast && (
          <SyncedLandScapeHeader 
            style={headerStyle} 
            scrollViewRef={headerScrollRef}
            onScroll={handleHeaderScroll}
          />
        )}
      </Container>
    );
  }),
);
