import styled from '@emotion/native';
import { Text, Typography } from 'cube-ui-components';
import { memo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { View, ScrollView, ViewStyle } from 'react-native';
import Animated, { AnimatedStyleProp } from 'react-native-reanimated';

export const HEADER_HEIGHT = 57;

const Container = styled.View(({ theme }) => ({
  height: HEADER_HEIGHT,
  backgroundColor: theme.colors.secondary,
  flexDirection: 'row',
  alignItems: 'center',
}));

const StickyColumn = styled.View(({ theme }) => ({
  width: `${(232 / 812) * 100}%`,
  height: HEADER_HEIGHT,
  backgroundColor: theme.colors.secondary,
  paddingHorizontal: theme.space[3],
  justifyContent: 'center',
  borderRightColor: theme.colors.palette.fwdGrey[100],
  borderRightWidth: 1,
  zIndex: 1,
}));

const ScrollableContainer = styled.View(() => ({
  flex: 1,
}));

const ColumnsContainer = styled.View(() => ({
  flexDirection: 'row',
  minWidth: 600, // Minimum width to ensure proper column spacing
}));

const HeaderColumn = styled.View(({ theme }) => ({
  minWidth: theme.space[30],
  width: theme.space[30], // Fixed width to ensure alignment
  paddingHorizontal: theme.space[3],
  justifyContent: 'center',
  height: HEADER_HEIGHT,
}));

const SText = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.palette.white,
}));

interface Props {
  style?: AnimatedStyleProp<ViewStyle>;
  scrollViewRef?: React.RefObject<ScrollView>;
  onScroll?: (event: any) => void;
}

export const SyncedLandScapeHeader = memo(function SyncedLandScapeHeader({
  style,
  scrollViewRef,
  onScroll,
}: Props) {
  const { t } = useTranslation(['savedProposals']);

  return (
    <Animated.View style={[{ position: 'relative' }, style]}>
      <Container>
        <StickyColumn>
          <SText>
            {`${t('savedProposals:proposalName')}/`}
            {'\n'}
            {t('savedProposals:insuredLandscape')}
          </SText>
        </StickyColumn>
        <ScrollableContainer>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            onScroll={onScroll}
            scrollEventThrottle={16}
            bounces={false}>
            <ColumnsContainer>
              <HeaderColumn>
                <SText>{t('savedProposals:product')}</SText>
              </HeaderColumn>
              <HeaderColumn>
                <SText>{t('savedProposals:statusLandscape')}</SText>
              </HeaderColumn>
              <HeaderColumn>
                <SText>{t('savedProposals:sumAssuredLandscape')}</SText>
              </HeaderColumn>
              <HeaderColumn>
                <SText>{t('savedProposals:modalPremiumLandscape')}</SText>
              </HeaderColumn>
              <HeaderColumn>
                <SText>{t('savedProposals:lastUpdateLandscape')}</SText>
              </HeaderColumn>
            </ColumnsContainer>
          </ScrollView>
        </ScrollableContainer>
      </Container>
    </Animated.View>
  );
});
