import useBoundStore from 'hooks/useBoundStore';
import { country } from 'utils/context';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { moduleConfigs } from 'utils/config/module';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { CubeIdToken } from 'types';
import { jwtDecode } from 'jwt-decode';

export const useEcoachEnable = () => {
  // Checking logic from agent profile api
  const { isTabletMode } = useLayoutAdoptionCheck();
  const channel = useGetCubeChannel();
  const idToken = useBoundStore.getState().auth.authInfo?.idToken;
  const productConfig = useBoundStore(state => state.ecoach.productConfig);
  const trainerGuruAvailability = useBoundStore(state => state.ecoach.trainerGuruAvailability);
  const config = moduleConfigs[country]?.ecoachConfig[channel];

  // Checking logic for token and FE config
  if (!idToken) return false;

  // Checking logic from token (controlled by IDP)
  const { trainer_guru_enabled } = jwtDecode(String(idToken)) as CubeIdToken;
  if (!trainer_guru_enabled) return false;

  // Only show TG card if configuration data is loaded
  if (!productConfig) return false;

  // Check trainer_guru_availability based on current user channel
  if (trainerGuruAvailability) {
    // Map channel values to trainer_guru_availability keys
    let channelKey: keyof typeof trainerGuruAvailability | undefined;

    if (channel === 'AGENCY' || channel === 'TA' || channel === 'GBSN_TA') {
      channelKey = 'agency';
    } else if (channel === 'BANCA' || channel === 'BCA' || channel === 'GBSN_BCA') {
      channelKey = 'banca';
    } else if (channel === 'AFFINITY') {
      channelKey = 'affinity';
    }

    if (channelKey && trainerGuruAvailability[channelKey] === false) {
      return false;
    }
  }

  if (isTabletMode) {
    return config?.tablet;
  } else {
    return config?.mobile;
  }
};
