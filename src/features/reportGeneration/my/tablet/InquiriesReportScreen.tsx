import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  Icon,
  Label, Row,
  SmallBody,
  Typography
} from 'cube-ui-components';
import {
  MemberInfo,
  flattenTeamHierarchy,
} from 'features/reportGeneration/utils/reportUtils';
import { useGetTeamHierarchy } from 'hooks/useGetTeam';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import TabletScreenHeader from 'navigation/components/ScreenHeader/tablet';
import {
  useGetInquiriesReport,
  usePostInquiriesReport,
} from 'hooks/useReportGeneration';
import ReportDataGrid from 'features/reportGeneration/ph/components/DataGrid/ReportDataGrid';
import Animated, { LinearTransition } from 'react-native-reanimated';
import {
  BIRO_CELL_RENDER_ORDER,
  BIRO_HEADERS,
  CELL_RENDER_ORDER,
  FREEZE_HEADER,
  HEADERS,
} from '../util/inquiriesReportTableConfig';
import { TouchableOpacity } from 'react-native';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import DateSelectModal from '../components/DateSelectModal';
import SelectAgentModal from '../components/SelectAgentModal';
import PolicyInfoActionPanel from 'features/reportGeneration/components/ActionPanel/PolicyInfoActionPanel';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { View } from 'react-native';
import { BIRO_OVERVIEW_STATUS_LIST } from '../util/statusList';
import {
  CHIP_CONFIG,
  DEFAULT_POLICY_INFO,
  SEARCH_TYPE_CONFIG,
} from 'features/reportGeneration/components/ActionPanel/config/policyInfo';
import MultipleSelectionList from '../components/MultipleSelectionList';
import { DatePeriodType, PolicyInfoSearch } from 'types/report';
import { BiroReportStatus } from '../util/type';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from 'types';
import { ExportCsv } from './ApplicationNotProceedScreen';

import { FilterView } from './ApplicationNotProceedScreen';

/**
 * For both mobile and tablet
 */
export default function InquiriesReportScreen(
  props: NativeStackScreenProps<MainStackParamList, 'InquiriesReportScreen'>,
) {
  const {
    agent,
    to,
    from,
    team,
    status,
    biro,
    biroSelectedStatus,
    statusList,
  } = props.route.params;

  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const [selectedAgent, setSelectedAgent] = useState<MemberInfo | null>(agent);
  const [showSelectedAgentList, setShowSelectedAgentList] = useState(false);
  const [isTeamSelected, setIsTeamSelected] = useState(team);
  const [showDateSelectModal, setShowDateSelectModal] = useState(false);
  const [policyInfo, updatePolicyInfo] = useState<PolicyInfoSearch>({
    searchType: 'all',
    focusedChip: 'policyNumber',
    policyHolderName: '',
    policyNumber: '',
  });
  const [openPolicyHolderPanel, setOpenPolicyHolderPanel] = useState(false);

  // Handle date range
  const [dateRange, setDateRange] = useState<{
    from: string;
    to: string;
    datePeriodType: DatePeriodType;
  }>({
    from: from,
    to: to,
    datePeriodType: 'DUE',
  });

  const handleDateRangeChange = ({
    to,
    from,
    dateType,
  }: {
    to: string;
    from: string;
    dateType?: DatePeriodType;
  }) => {
    setDateRange({
      from: from,
      to: to,
      datePeriodType: dateType ?? 'DUE',
    });
  };
  const DATEPERIOD_CONFIG = [
    { title: t('last2months'), value: 'last2months' },
    { title: t('last30days'), value: 'last30days' },
    { title: t('last180days'), value: 'last180days' },
    { title: t('customise'), value: 'customise' },
  ];

  const DATETYPE_CONFIG = [
    { title: t('toolbar.dueDate'), value: 'DUE' },
    { title: t('toolbar.issueDate'), value: 'ISS' },
    { title: t('toolbar.registrationDate'), value: 'REG' },
    { title: t('toolbar.submissionDate'), value: 'SUB' },
  ];

  const ReportDateTypeButton = ({
    selectedDateTypeButton,
  }: {
    selectedDateTypeButton: DatePeriodType;
  }) => {
    const { t } = useTranslation('reportGeneration');
    switch (selectedDateTypeButton) {
      case 'ISS':
        return t('toolbar.issueDate');
      case 'EFF':
        return t('toolbar.transactionDate');
      case 'DUE':
        return t('toolbar.dueDate');
      case 'SUB':
        return t('toolbar.submissionDate');
      case 'REG':
        return t('toolbar.registrationDate');
      default:
        return t('toolbar.dueDate');
    }
  };

  // handle Selected Agent
  const { data: agentProfile } = useGetAgentProfile();
  const isLeader = agentProfile?.isLeader;
  const { data: teamData, isLoading: isTeamLoading } = useGetTeamHierarchy();
  const memberInfoList = flattenTeamHierarchy(teamData);
  const onCloseShowSelectedAgentList = () => {
    setShowSelectedAgentList(false);
  };

  const handleSelectAgent = (agent: MemberInfo) => {
    setSelectedAgent(agent);
  };

  // handle generated status
  const [selectedStatus, setSelectedStatus] =
    useState<{ code: string; meaning: string }[]>(status);

  const [showStatusSelectionList, setShowStatusSelectionList] = useState(false);

  const handleSelectedStatus = ({
    selected,
  }: {
    selected: { code: string; meaning: string }[];
  }) => {
    setSelectedStatus(selected);
  };

  const onCloseShowStatusSelectionList = () => {
    setShowStatusSelectionList(false);
  };

  // handle biro status
  const [isBiro, setIsBiro] = useState<boolean>(biro);
  const [selectedBIROStatus, setSelectedBIROStatus] = useState<
    BiroReportStatus[]
  >(biroSelectedStatus ?? []);
  const [isToggledBiro, setIsToggled] = useState(false);
  const [filteredBox, setFilteredBox] = useState({
    width: 0,
    height: 0,
    x: 0,
    y: 0,
  });

  // data
  const {
    data: inquiriesReportData,
    isLoading: isInquiriesReportLoading,
    isError,
  } = useGetInquiriesReport({
    agentId: isTeamSelected ? '' : selectedAgent?.agentCode,
    team: isTeamSelected,
    from: dateRange.from,
    to: dateRange.to,
    dateType: dateRange.datePeriodType,
    dueDateReportPolicyInfo: {
      policyHolderName: policyInfo.policyHolderName,
      policyNumber: policyInfo.policyNumber,
    },
    status: isBiro ? selectedBIROStatus : selectedStatus,
  });

  const { mutateAsync: saveCsv, isLoading: isSavingCsv } =
    usePostInquiriesReport();
  const onSaveCsv = async () => {
    try {
      await saveCsv({
        agentId: isTeamSelected ? '' : selectedAgent?.agentCode,
        team: isTeamSelected,
        from: dateRange.from,
        to: dateRange.to,
        dateType: dateRange.datePeriodType,
        dueDateReportPolicyInfo: {
          policyHolderName: policyInfo.policyHolderName,
          policyNumber: policyInfo.policyNumber,
        },
        status: isBiro ? selectedBIROStatus : status,
        biro: isBiro,
        isLeader: isLeader ?? false,
      });
    } catch (error) {
      console.error('Error saving CSV:', error);
    }
  };

  return (
    <>
      <PolicyInfoActionPanel
        visible={openPolicyHolderPanel}
        handleClose={() => setOpenPolicyHolderPanel(false)}
        contextValue={policyInfo}
        updateContextValue={updatePolicyInfo}
        //
        defaultPolicyInfo={DEFAULT_POLICY_INFO}
        searchTypeConfig={SEARCH_TYPE_CONFIG}
        chipConfig={CHIP_CONFIG}
        //
        title={t('actionPanel.title.searchForPolicyInfo')}
        searchTip={t('actionPanel.subtitle.searchTip')}
        policyHolderNameError={'Invalid input'}
        policyNumberHint={t('actionPanel.hint.policyNumber')}
        policyNumberError={'Invalid input'}
        //
        primaryLabel={t('actionPanel.search')}
        secondaryLabel={t('actionPanel.reset')}
      />
      <SelectAgentModal
        visible={showSelectedAgentList}
        onClose={onCloseShowSelectedAgentList}
        data={memberInfoList}
        handleSelectAgent={handleSelectAgent}
        selectedAgent={selectedAgent}
        setSelectedAgent={setSelectedAgent}
      />
      <DateSelectModal
        visible={showDateSelectModal}
        handleClose={() => setShowDateSelectModal(false)}
        datePeriodConfig={DATEPERIOD_CONFIG}
        title={t('datePeriod')}
        defaultDateRange={{
          datePeriodType: dateRange.datePeriodType,
          from: dateRange.from,
          to: dateRange.to,
        }}
        handleDateRangeChange={handleDateRangeChange}
        dateTypeConfig={DATETYPE_CONFIG}
      />
      <TabletScreenHeader
        route={'InquiriesReportScreen'}
        customTitle="Certificate inquiries report"
        isLeftArrowBackShown
        showBottomSeparator={false}
        rightChildren={
          (inquiriesReportData?.summary?.caseCount ?? 0) > 0 ? (
            <ExportCsv onSaveCsv={onSaveCsv} isSavingCsv={isSavingCsv} />
          ) : undefined
        }
      />
      <Box padding={space[4]} gap={space[2]} flex={1}>
        <Row gap={space[1]}>
          {isLeader && (
            <>
              <TouchableOpacity
                disabled={isTeamSelected}
                onPress={() => {
                  setShowSelectedAgentList(true);
                }}>
                <FilterView>
                  <SmallBody
                    color={
                      isTeamSelected
                        ? colors.palette.fwdGrey[100]
                        : colors.onBackground
                    }>
                    {t('toolbar.selectedAgent')}
                  </SmallBody>
                  <Row gap={space[1]} alignItems="center">
                    <Label
                      fontWeight="medium"
                      color={
                        isTeamSelected
                          ? colors.palette.fwdGrey[100]
                          : colors.onBackground
                      }>
                      {selectedAgent?.agentName ?? '--'}
                    </Label>
                    <Icon.ChevronDown
                      size={sizes[4]}
                      fill={
                        isTeamSelected
                          ? colors.palette.fwdGrey[100]
                          : colors.onBackground
                      }
                    />
                  </Row>
                </FilterView>
              </TouchableOpacity>
              <FilterView customTeamView={isTeamSelected}>
                <Checkbox
                  value={isTeamSelected}
                  onChange={() => {
                    setIsTeamSelected(!isTeamSelected);
                    // If team is selected, set the login member as selected agent
                    setSelectedAgent(memberInfoList[0]);
                  }}
                />
                <Label>{t('toolbar.team')}</Label>
              </FilterView>
            </>
          )}
          <TouchableOpacity>
            <FilterView>
              <Checkbox value={isBiro} onChange={() => setIsBiro(!isBiro)} />
              <Label>{t('BiroName')}</Label>
            </FilterView>
          </TouchableOpacity>
          {isBiro && (
            <TouchableOpacity onPress={() => setIsToggled(!isToggledBiro)}>
              <View
                onLayout={e => {
                  e.target.measure((x, y, width, height, pageX, pageY) => {
                    setFilteredBox({ width, height, x: pageX, y: pageY });
                  });
                }}>
                <FilterView
                  style={{
                    backgroundColor:
                      isBiro && isToggledBiro
                        ? colors.palette.fwdOrange[5]
                        : colors.background,
                    borderColor:
                      isBiro && isToggledBiro
                        ? colors.palette.fwdOrange[100]
                        : colors.palette.fwdGrey[100],
                    borderWidth: isBiro && isToggledBiro ? 2 : 1,
                  }}>
                  <Label>{t('biroStatus.dropdown.biroTitle')}</Label>
                  <Label fontWeight="medium">
                    {selectedBIROStatus.length ===
                    BIRO_OVERVIEW_STATUS_LIST.length
                      ? t('biroStatus.dropdown.biroAll', {
                          biroStatusNum: BIRO_OVERVIEW_STATUS_LIST.length,
                        })
                      : selectedBIROStatus
                          .map(status => status.dropdownLabel)
                          .join(', ')}
                  </Label>
                  <Icon.ChevronDown
                    size={sizes[4]}
                    fill={
                      isBiro && isToggledBiro
                        ? colors.palette.fwdOrange[100]
                        : colors.onBackground
                    }
                  />
                </FilterView>
              </View>
            </TouchableOpacity>
          )}
          {!isBiro && (
            <TouchableOpacity
              onPress={() => {
                setShowStatusSelectionList(true);
              }}>
              <FilterView
                style={{
                  backgroundColor:
                    policyInfo.policyHolderName || policyInfo.policyNumber
                      ? colors.palette.fwdOrange[5]
                      : colors.background,
                  borderColor:
                    policyInfo.policyHolderName || policyInfo.policyNumber
                      ? colors.palette.fwdOrange[100]
                      : colors.palette.fwdGrey[100],
                  borderWidth:
                    policyInfo.policyHolderName || policyInfo.policyNumber
                      ? 2
                      : 1,
                }}>
                <SmallBody>{t('toolbar.status')}</SmallBody>
                <Row gap={space[1]} alignItems="center">
                  <Label fontWeight="medium">
                    {selectedStatus.length == statusList?.length
                      ? t('actionPanel.status.all', {
                          statusNum: statusList.length,
                        })
                      : t('selected', { statusNum: selectedStatus.length })}
                  </Label>
                  <Icon.ChevronDown
                    size={sizes[4]}
                    fill={colors.onBackground}
                  />
                </Row>
              </FilterView>
            </TouchableOpacity>
          )}
          <MultipleSelectionList
            title={t('generalStatus')}
            visible={showStatusSelectionList}
            onClose={onCloseShowStatusSelectionList}
            data={statusList || []}
            handleSelected={handleSelectedStatus}
          />
          <TouchableOpacity
            onPress={() => {
              setOpenPolicyHolderPanel(true);
            }}>
            <FilterView
              style={{
                backgroundColor:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? colors.palette.fwdOrange[5]
                    : colors.background,
                borderColor:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? colors.palette.fwdOrange[100]
                    : colors.palette.fwdGrey[100],
                borderWidth:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? 2
                    : 1,
              }}>
              <SmallBody>Certificate owner/number</SmallBody>
              <Row gap={space[1]} alignItems="center">
                <Label fontWeight="medium">
                  {policyInfo.policyHolderName ||
                    policyInfo.policyNumber ||
                    'All'}
                </Label>
                <Icon.ChevronDown size={sizes[4]} fill={colors.onBackground} />
              </Row>
            </FilterView>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setShowDateSelectModal(true);
            }}>
            <FilterView>
              <SmallBody>
                <ReportDateTypeButton
                  selectedDateTypeButton={dateRange.datePeriodType}
                />
              </SmallBody>
              <Row gap={space[1]} alignItems="center">
                <Label fontWeight="medium">{`${dateFormatUtil(
                  dateRange.from,
                )} - ${dateFormatUtil(dateRange.to)}`}</Label>
                <Icon.ChevronDown size={sizes[4]} fill={colors.onBackground} />
              </Row>
            </FilterView>
          </TouchableOpacity>
        </Row>
        <Row>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            {t('totalResultNum', {
              totalResultNum: inquiriesReportData?.summary.caseCount ?? 0,
            })}
          </Typography.Body>
        </Row>
      </Box>
      {isBiro && isToggledBiro && (
        <DropdownView
          filterBoxHeight={filteredBox.height}
          xAxis={filteredBox.x}
          yAxis={filteredBox.y}
          selectedBIROStatus={selectedBIROStatus}
          setSelectedBIROStatus={setSelectedBIROStatus}
        />
      )}

      <Animated.View layout={LinearTransition.delay(50)} style={{ flex: 10 }}>
        <ReportDataGrid
          darkMode={false}
          isLoading={isInquiriesReportLoading}
          isError={isError}
          data={inquiriesReportData?.data}
          freezeHeader={FREEZE_HEADER}
          headers={isBiro ? BIRO_HEADERS : HEADERS}
          cellRenderOrder={isBiro ? BIRO_CELL_RENDER_ORDER : CELL_RENDER_ORDER}
        />
      </Animated.View>
    </>
  );
}

const DropdownView = ({
  filterBoxHeight,
  xAxis,
  yAxis,
  selectedBIROStatus,
  setSelectedBIROStatus,
}: {
  filterBoxHeight: number;
  xAxis: number;
  yAxis: number;
  selectedBIROStatus: {
    code: string;
    meaning: string;
    dropdownLabel: string;
  }[];
  setSelectedBIROStatus: React.Dispatch<
    React.SetStateAction<
      { code: string; meaning: string; dropdownLabel: string }[]
    >
  >;
}) => {
  const { colors, space } = useTheme();
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: colors.background,
        width: space[55],
        position: 'absolute',
        left: xAxis,
        top: yAxis + filterBoxHeight + space[1],
        shadowColor: 'rgba(0, 0, 0, 0.25)',
        zIndex: 1000,
      }}>
      {BIRO_OVERVIEW_STATUS_LIST.map((status, index) => {
        const isSelected = selectedBIROStatus.some(
          selected => selected.code === status.code,
        );
        return (
          <TouchableOpacity
            onPress={() => {
              const updatedStatus = isSelected
                ? selectedBIROStatus.filter(
                    selected => selected.code !== status.code,
                  )
                : [...selectedBIROStatus, status];
              setSelectedBIROStatus(updatedStatus);
            }}>
            <Row
              height={space[10]}
              key={index}
              alignItems="center"
              py={space[2]}
              px={space[4]}
              style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Label>{status.dropdownLabel}</Label>

              <Icon.Tick
                size={space[4]}
                fill={
                  isSelected ? colors.palette.fwdOrange[100] : colors.background
                }
              />
            </Row>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};
