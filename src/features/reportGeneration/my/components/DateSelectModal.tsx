import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Portal } from '@gorhom/portal';
import {
  Button,
  Chip,
  Column,
  H6,
  Icon,
  LargeBody,
  Row,
  SmallLabel,
} from 'cube-ui-components';
import { format, subDays, subMonths } from 'date-fns';
import { formatDate } from 'features/eRecruit/ib/phone/components/utils/FormatDate';
import { Dispatch, SetStateAction, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, TouchableOpacity, View } from 'react-native';
import { DatePeriodSearch, DatePeriodType } from 'types/report';
import DatePeriodActionPanelMY from './DatePeriodActionPanel';

const today = format(new Date(), 'yyyy-MM-dd');
const lastTwoMonths = format(subMonths(new Date(), 2), 'yyyy-MM-dd');
const lastThirtyDays = format(subDays(new Date(), 29), 'yyyy-MM-dd'); // 29 as today is included
const last180Days = format(subDays(new Date(), 179), 'yyyy-MM-dd'); // 179 as today is included

export default function DateSelectModal({
  visible,
  handleClose,
  datePeriodConfig,
  title,
  defaultDateRange,
  handleDateRangeChange,
  dateTypeConfig,
}: {
  visible: boolean;
  handleClose: () => void;
  datePeriodConfig?: { title: string; value: string }[];
  title: string;
  defaultDateRange: DatePeriodSearch;
  handleDateRangeChange?: ({
    to,
    from,
    dateType,
  }: {
    to: string;
    from: string;
    dateType: DatePeriodType;
  }) => void;
  dateTypeConfig?: { title: string; value: string }[];
}) {
  const handlePeriodBtn = (defaultDateRange: DatePeriodSearch) => {
    if (
      defaultDateRange.from == lastTwoMonths &&
      defaultDateRange.to == today
    ) {
      return 'last2months';
    }
    if (
      defaultDateRange.from == lastThirtyDays &&
      defaultDateRange.to == today
    ) {
      return 'last30days';
    }
    if (defaultDateRange.from == last180Days && defaultDateRange.to == today) {
      return 'last180days';
    }
    return 'customise';
  };
  const { colors, sizes, space } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const [dateRange, setDateRange] =
    useState<DatePeriodSearch>(defaultDateRange);
  const [showDatePanel, setShowDatePanel] = useState(false);
  const [selectedDatePeriodButton, setSelectedDatePeriodButton] = useState(
    handlePeriodBtn(defaultDateRange),
  );
  useMemo(() => {
    setDateRange(defaultDateRange);
  }, [defaultDateRange]);

  const isDateTypeVisible = dateTypeConfig && dateTypeConfig.length > 1;

  return (
    <Portal>
      <DatePeriodActionPanelMY
        visible={showDatePanel}
        handleClose={() => setShowDatePanel(false)}
        contextValue={dateRange ?? defaultDateRange}
        updateContextValue={setDateRange}
        defaultDatePeriod={defaultDateRange}
        disableTypeChip={true}
      />
      <Modal visible={visible} animationType="fade" transparent>
        <ModalContainer>
          <View
            style={{
              borderRadius: sizes[4],
              padding: space[6],
              paddingHorizontal: space[12],
              paddingBottom: space[12],
              width: '50%',
              height: isDateTypeVisible ? '50%' : '40%',
              backgroundColor: colors.palette.white,
            }}>
            <Row justifyContent="flex-end">
              <TouchableOpacity onPress={handleClose}>
                <Icon.Close size={sizes[6]} fill={colors.onBackground} />
              </TouchableOpacity>
            </Row>
            <H6 fontWeight="bold" children={title} />
            <Column gap={space[2]} paddingY={space[4]}>
              {/* use for transaction report only */}
              {isDateTypeVisible && (
                <>
                  <SmallLabel color={colors.palette.fwdGreyDarkest}>
                    {t('dateType')}
                  </SmallLabel>
                  <Row gap={space[2]}>
                    {dateTypeConfig &&
                      dateTypeConfig.map((item, index) => {
                        return (
                          <Chip
                            key={index}
                            label={item.title}
                            focus={dateRange.datePeriodType === item?.value}
                            onPress={() => {
                              setDateRange({
                                ...dateRange,
                                datePeriodType: item.value as DatePeriodType,
                                from: defaultDateRange.from,
                                to: defaultDateRange.to,
                              });
                            }}
                          />
                        );
                      })}
                  </Row>
                </>
              )}
              <SmallLabel color={colors.palette.fwdGreyDarkest}>
                {t('datePeriod')}
              </SmallLabel>
              <Row gap={space[2]}>
                {datePeriodConfig &&
                  datePeriodConfig.map((item, index) => {
                    return (
                      <Chip
                        key={index}
                        label={item.title}
                        focus={selectedDatePeriodButton === item.value}
                        onPress={() => {
                          setSelectedDatePeriodButton(item.value);
                          setDateRange({
                            ...dateRange,
                            from:
                              item.value === 'last2months'
                                ? lastTwoMonths
                                : item.value === 'last30days'
                                ? lastThirtyDays
                                : item.value === 'last180days'
                                ? last180Days
                                : dateRange.from, // use previous value
                            to: today,
                            datePeriodType: isDateTypeVisible
                              ? dateRange?.datePeriodType
                              : ('DUE' as DatePeriodType), // Ensure datePeriodType is set
                          });
                          if (item.value === 'customise') {
                            setShowDatePanel(true);
                          }
                          console.log('pressed', item.value);
                        }}
                      />
                    );
                  })}
              </Row>
            </Column>
            <Column gap={space[2]} paddingBottom={space[6]}>
              <SmallLabel color={colors.palette.fwdGreyDarkest}>
                {t('dateRange')}
              </SmallLabel>
              <LargeBody>{`${formatDate(dateRange?.from)} - ${formatDate(
                dateRange?.to,
              )}`}</LargeBody>
            </Column>
            <Row flex={1} justifyContent="center" gap={space[4]}>
              <Button
                text={t('reset')}
                size="default"
                variant="secondary"
                style={{ width: '40%' }}
                onPress={() => {
                  console.log('selected date range', dateRange);
                  setDateRange({
                    ...defaultDateRange,
                    from: lastTwoMonths,
                  });
                  setSelectedDatePeriodButton('customise');
                }}
              />
              <Button
                text={t('confirm')}
                size="default"
                variant="primary"
                style={{ width: '40%' }}
                onPress={() => {
                  console.log('selected date range', dateRange);
                  handleDateRangeChange?.({
                    to: dateRange?.to ?? defaultDateRange.to,
                    from: dateRange?.from ?? defaultDateRange.from,
                    dateType: dateRange?.datePeriodType ?? 'DUE',
                  });
                  handleClose();
                }}
              />
            </Row>
          </View>
        </ModalContainer>
      </Modal>
    </Portal>
  );
}

const ModalContainer = styled.View(({ theme: { colors, sizes, space } }) => ({
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
}));
