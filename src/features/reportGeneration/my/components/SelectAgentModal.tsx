import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Portal } from '@gorhom/portal';
import {
  Box,
  Button,
  H6,
  Icon,
  RadioButton,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import { MemberInfo } from 'features/reportGeneration/utils/reportUtils';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ScrollView, TouchableOpacity, View } from 'react-native';

export default function SelectAgentModal({
  visible,
  onClose,
  data,
  handleSelectAgent,
  selectedAgent,
  setSelectedAgent,
}: {
  visible: boolean;
  onClose: () => void;
  data: MemberInfo[];
  handleSelectAgent?: (agent: MemberInfo) => void;
  selectedAgent?: MemberInfo | null | undefined;
  setSelectedAgent?: (agent: MemberInfo) => void;
}) {
  const { colors, sizes, space } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const [searchText, setSearchText] = useState('');

  return (
    <Portal>
      <Modal visible={visible} animationType="fade" transparent={true}>
        <ModalContainer>
          <View
            style={{
              borderRadius: sizes[4],
              padding: space[6],
              paddingBottom: space[12],
              width: '80%',
              height: '80%',
              backgroundColor: colors.palette.white,
            }}>
            <Row justifyContent="flex-end">
              <TouchableOpacity onPress={onClose}>
                <Icon.Close size={sizes[6]} fill={colors.onBackground} />
              </TouchableOpacity>
            </Row>
            <View style={{ paddingHorizontal: space[6] }}>
              <H6
                fontWeight="bold"
                color={colors.onBackground}
                style={{ paddingBottom: space[4] }}>
                {t('selectAgent')}
              </H6>
              <View style={{ gap: space[6] }}>
                <ScrollView
                  contentContainerStyle={{ gap: space[6], height: '75%' }}>
                  <TextField
                    left={<Icon.Search fill={colors.palette.fwdGreyDarker} />}
                    value={searchText}
                    placeholder={t('search')}
                    onChangeText={text => setSearchText(text)}
                  />
                  <View>
                    <Row>
                      <Box flex={1}>
                        <Typography.Body
                          color={colors.palette.fwdGreyDarker}
                          style={{ paddingLeft: space[11] }}>
                          {t('agentName')}
                        </Typography.Body>
                      </Box>
                      <Box flex={1}>
                        <Typography.Body color={colors.palette.fwdGreyDarker}>
                          {t('agentRoleAndCode')}
                        </Typography.Body>
                      </Box>
                    </Row>
                    {data.map((item, index) => {
                      return (
                        <Row
                          borderBottom={1}
                          borderBottomColor={colors.palette.fwdGrey[100]}
                          paddingY={space[3]}>
                          <Row flex={1}>
                            <RadioButton
                              value={item}
                              selected={
                                selectedAgent?.agentCode === item.agentCode
                              }
                              onSelect={() => {
                                setSelectedAgent?.(item ?? undefined);
                                handleSelectAgent?.(item);
                              }}
                              style={{ marginRight: space[5] }}
                            />
                            <Typography.LargeBody>
                              {item?.agentName}
                            </Typography.LargeBody>
                          </Row>
                          <Row flex={1}>
                            <Typography.LargeBody>
                              {item?.designation + ' - ' + item?.agentCode}
                            </Typography.LargeBody>
                          </Row>
                        </Row>
                      );
                    })}
                  </View>
                </ScrollView>
                <Row
                  gap={space[4]}
                  justifyContent="center"
                  alignItems="baseline">
                  <Button
                    text={t('reset')}
                    variant="secondary"
                    style={{ width: sizes[50] }}
                    onPress={() => {
                      setSearchText('');
                      setSelectedAgent?.(data[0] ?? undefined);
                      handleSelectAgent?.(data[0]);
                    }}
                  />
                  <Button
                    text={t('confirm')}
                    variant="primary"
                    style={{ width: sizes[50] }}
                    onPress={() => {
                      onClose();
                    }}
                  />
                </Row>
              </View>
            </View>
          </View>
        </ModalContainer>
      </Modal>
    </Portal>
  );
}

const ModalContainer = styled.View(({ theme: { colors, sizes, space } }) => ({
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
}));
