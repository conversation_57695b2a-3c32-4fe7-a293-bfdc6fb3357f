import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Portal } from '@gorhom/portal';
import {
  Box,
  Button,
  H6,
  Icon,
  RadioButton,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import { MemberInfo } from 'features/reportGeneration/utils/reportUtils';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ScrollView, TouchableOpacity, View } from 'react-native';

export default function MultipleSelectionList({
  title,
  visible,
  onClose,
  data,
  handleSelected,
}: {
  title: string;
  visible: boolean;
  onClose: () => void;
  data: { code: string; meaning: string }[];
  handleSelected?: ({
    selected,
  }: {
    selected: { code: string; meaning: string }[];
  }) => void;
}) {
  const { colors, sizes, space } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const [selectedValue, setSelectedValue] = useState(data);
  const [searchText, setSearchText] = useState('');
  const [selectionState, setSelectionState] = useState(
    data.map(item => ({ ...item, selected: true })),
  );

  const dataShownOnList =
    useMemo(() => {
      const updatedData = data.map(item => ({
        ...item,
        selected: true,
      }));
      if (!searchText) {
        return [
          { code: 'SELECTALL', meaning: 'Select all', selected: true },
          ...updatedData,
        ];
      }
      if (searchText) {
        return updatedData.filter(item =>
          item.meaning.toLowerCase().includes(searchText.toLowerCase()),
        );
      }
    }, [data, searchText]) || [];

  const handleItemPress = (code: string) => {
    setSelectionState(prevState =>
      prevState.map(item =>
        item.code === code ? { ...item, selected: !item.selected } : item,
      ),
    );
  };

  const handleSelectAllPress = () => {
    const isSelectAllSelected = selectionState.every(item => item.selected);
    setSelectionState(prevState =>
      prevState.map(item => ({ ...item, selected: !isSelectAllSelected })),
    );
  };

  const selectedItems = selectionState.filter(item => item.selected);
  // const isSelectAllSelected = dataShownOnList.find(
  //   item => item.code === 'SELECTALL',
  // )?.selected;

  const isSelectAllSelected = selectionState.every(item => item.selected);

  return (
    <Portal>
      <Modal visible={visible} animationType="fade" transparent={true}>
        <ModalContainer>
          <View
            style={{
              borderRadius: sizes[4],
              padding: space[6],
              paddingBottom: space[12],
              width: '80%',
              height: '90%',
              backgroundColor: colors.palette.white,
            }}>
            <Row justifyContent="flex-end">
              <TouchableOpacity onPress={onClose}>
                <Icon.Close size={sizes[6]} fill={colors.onBackground} />
              </TouchableOpacity>
            </Row>
            <View style={{ paddingHorizontal: space[6], flex: 1 }}>
              <H6
                fontWeight="bold"
                color={colors.onBackground}
                style={{ paddingBottom: space[4] }}>
                {title}
              </H6>
              <TextField
                inputContainerStyle={{
                  borderRadius: sizes[12],
                  height: sizes[11],
                }}
                value={searchText}
                placeholder={t('searchForStatus')}
                onChangeText={text => setSearchText(text)}
              />
              <ScrollView
                horizontal
                style={{ paddingTop: space[3], height: 88 }}>
                {isSelectAllSelected ? (
                  <TouchableOpacity onPress={handleSelectAllPress}>
                    <Row
                      backgroundColor={colors.palette.fwdOrange[5]}
                      alignItems="center"
                      paddingX={space[3]}
                      paddingY={space[2]}
                      gap={space[1]}
                      borderRadius={space[11]}
                      borderWidth={2}
                      borderColor={colors.primary}>
                      <Typography.H8 fontWeight="medium">
                        Select All
                      </Typography.H8>
                      <Icon.Close size={sizes[5]} />
                    </Row>
                  </TouchableOpacity>
                ) : (
                  selectedItems.map(item => (
                    <TouchableOpacity
                      key={item.code}
                      onPress={() => handleItemPress(item.code)}>
                      <Row
                        backgroundColor={colors.palette.fwdOrange[5]}
                        alignItems="center"
                        marginRight={space[1]}
                        paddingX={space[3]}
                        paddingY={space[2]}
                        gap={space[1]}
                        borderRadius={space[11]}
                        borderWidth={2}
                        borderColor={colors.primary}>
                        <Typography.H8 fontWeight="medium">
                          {item.meaning}
                        </Typography.H8>
                        <Icon.Close size={sizes[5]} />
                      </Row>
                    </TouchableOpacity>
                  ))
                )}
              </ScrollView>
              <Box paddingTop={space[6]}>
                <Typography.Body color={colors.palette.fwdGreyDarker}>
                  {t('pleaseSelect')}
                </Typography.Body>
              </Box>
              <ScrollView contentContainerStyle={{}}>
                <View>
                  {dataShownOnList.map(item => {
                    return (
                      <TouchableOpacity
                        key={item.code}
                        onPress={() =>
                          item.code === 'SELECTALL'
                            ? handleSelectAllPress()
                            : handleItemPress(item.code)
                        }>
                        <Row
                          key={item.code}
                          justifyContent="space-between"
                          paddingY={space[3]}
                          borderBottom={1}
                          borderBottomColor={colors.palette.fwdGrey[100]}>
                          <Typography.LargeLabel>
                            {item.meaning}
                          </Typography.LargeLabel>
                          {item.code === 'SELECTALL' &&
                            selectionState.every(item => item.selected) && (
                              <Icon.Tick size={sizes[4]} />
                            )}
                          {item.code !== 'SELECTALL' &&
                            selectionState.find(
                              selectItem => selectItem.code == item.code,
                            )?.selected && <Icon.Tick size={sizes[4]} />}
                        </Row>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </ScrollView>
              <Row
                gap={space[4]}
                justifyContent="center"
                alignItems="baseline"
                paddingTop={space[6]}>
                <Button
                  text={t('reset')}
                  variant="secondary"
                  style={{ width: sizes[50] }}
                  onPress={() => {
                    setSearchText('');
                    setSelectionState(
                      data.map(item => ({ ...item, selected: true })),
                    );
                  }}
                />
                <Button
                  text={t('confirm')}
                  variant="primary"
                  style={{ width: sizes[50] }}
                  onPress={() => {
                    const selectedState = selectionState.filter(
                      item => item.selected,
                    );
                    const selectedItems = selectedState.map(item => ({
                      code: item.code,
                      meaning: item.meaning,
                    }));
                    handleSelected?.({ selected: selectedItems });
                    onClose();
                  }}
                />
              </Row>
            </View>
          </View>
        </ModalContainer>
      </Modal>
    </Portal>
  );
}

const ModalContainer = styled.View(({ theme: { colors, sizes, space } }) => ({
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
}));
