import { useTheme } from '@emotion/react';
import * as React from 'react';
import Svg, { G, Circle, Path } from 'react-native-svg';

function FacebookLogoCircleSvg() {
  const { sizes } = useTheme();

  return (
    <Svg width={sizes[20]} height={sizes[20]} viewBox="0 0 256 256">
      <G
        transform="matrix(2.81 0 0 2.81 1.407 1.407)"
        stroke="none"
        strokeWidth={0}
        strokeDasharray="none"
        strokeLinecap="butt"
        strokeLinejoin="miter"
        strokeMiterlimit={10}
        fill="none"
        fillRule="nonzero"
        opacity={1}>
        <Circle
          cx={45}
          cy={45}
          r={45}
          stroke="none"
          strokeWidth={1}
          strokeDasharray="none"
          strokeLinecap="butt"
          strokeLinejoin="miter"
          strokeMiterlimit={10}
          fill="#1877F2"
          fillRule="nonzero"
          opacity={1}
        />
        <Path
          d="M38.633 37.184v9.136h-10.64v12.388h10.64v30.836c2.081.294 4.205.456 6.367.456 2.159 0 4.28-.162 6.359-.456V58.708h10.613l1.589-12.388H51.359v-7.909c0-3.587.991-6.031 6.107-6.031l6.525-.003v-11.08c-1.128-.151-5.002-.488-9.508-.488-9.409.001-15.85 5.773-15.85 16.375z"
          stroke="none"
          strokeWidth={1}
          strokeDasharray="none"
          strokeLinecap="butt"
          strokeLinejoin="miter"
          strokeMiterlimit={10}
          fill="#fff"
          fillRule="nonzero"
          opacity={1}
        />
      </G>
    </Svg>
  );
}

export default FacebookLogoCircleSvg;
