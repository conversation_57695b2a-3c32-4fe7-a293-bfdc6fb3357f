import { useCallback, useEffect } from 'react';

import { usePromptContext } from 'components/prompt/PromptContext';
import {
  getAlertActions,
  getColumnActionButtons,
} from 'components/prompt/PromptDialog';
import { useTranslation } from 'react-i18next';
import { SocialMarketingAlertAction, SocialMarketingAlertType } from '../types';
import useConnectionStatus from 'features/ecoach/hooks/useConnectionStatus';

export function useSocialMarketingAlert() {
  const { t } = useTranslation('socialMarketing');
  const { isConnected } = useConnectionStatus();
  const { prompt } = usePromptContext();

  const showSocialMarketingAlert = useCallback(
    async (
      type: SocialMarketingAlertType,
      actions?: {
        onPrimaryAction?: SocialMarketingAlertAction['onPress'];
        onSecondaryAction?: SocialMarketingAlertAction['onPress'];
      },
    ) => {
      let title = '',
        description = '',
        primaryAction = null,
        secondaryAction = null;

      switch (type) {
        case SocialMarketingAlertType.Risk: {
          title = t('createPost.error.risk.title');
          description = t('createPost.error.risk.description');
          primaryAction = {
            label: t('createPost.error.cta.rewrite'),
            onPress: actions?.onPrimaryAction,
          };
          break;
        }
        case SocialMarketingAlertType.NoNetwork: {
          title = t('createPost.error.lostNetwork.title');
          description = t('createPost.error.lostNetwork.description');
          primaryAction = {
            label: t('createPost.error.cta.okGotIt'),
            onPress: actions?.onPrimaryAction,
          };
          break;
        }
        case SocialMarketingAlertType.Internal: {
          title = t('createPost.error.internal.title');
          description = t('createPost.error.internal.description');
          primaryAction = {
            label: t('createPost.error.cta.rewrite'),
            onPress: actions?.onPrimaryAction,
          };
          break;
        }
        case SocialMarketingAlertType.Timeout: {
          title = t('createPost.error.timeout.title');
          description = t('createPost.error.timeout.description');
          primaryAction = {
            label: t('createPost.error.cta.okGotIt'),
            onPress: actions?.onPrimaryAction,
          };
          break;
        }
        case SocialMarketingAlertType.LeaveEdit: {
          title = t('createPost.error.leaveEdit.title');
          description = t('createPost.error.leaveEdit.description');
          primaryAction = {
            label: t('createPost.error.cta.continueEditing'),
            onPress: actions?.onPrimaryAction,
          };
          secondaryAction = {
            label: t('createPost.error.cta.leaveEdit'),
            onPress: actions?.onSecondaryAction,
          };
          break;
        }
        case SocialMarketingAlertType.LeaveSave: {
          title = t('createPost.error.leaveSave.title');
          description = t('createPost.error.leaveSave.description');
          primaryAction = {
            label: t('createPost.error.cta.save'),
            onPress: actions?.onPrimaryAction,
          };
          secondaryAction = {
            label: t('createPost.error.cta.discard'),
            onPress: actions?.onSecondaryAction,
          };
          break;
        }
        case SocialMarketingAlertType.AvatarRecordTermsAgreement: {
          title = t('createPost.avatarRecordTermsAgreement.title');
          description = t('createPost.avatarRecordTermsAgreement.description');
          primaryAction = {
            label: t('createPost.avatarRecordTermsAgreement.cta.agree'),
            onPress: actions?.onPrimaryAction,
          };
          break;
        }
      }

      const isAccepted = await prompt({
        title,
        description,
        actions: !!secondaryAction ? getColumnActionButtons : getAlertActions,
        closable: true,
        useModal: type !== SocialMarketingAlertType.AvatarRecordTermsAgreement,
        config: {
          accept: primaryAction?.label || '',
          dismiss: secondaryAction?.label || '',
        },
      });

      if (isAccepted) {
        actions?.onPrimaryAction?.();
      } else {
        actions?.onSecondaryAction?.();
      }
    },
    [prompt],
  );

  useEffect(() => {
    // Check network state
    if (!isConnected) {
      showSocialMarketingAlert(SocialMarketingAlertType.NoNetwork);
    }
  }, [isConnected]);

  return showSocialMarketingAlert;
}
