import { useRef } from 'react';
import { baseUrl, linkedInAuthUrl, linkedInClientId } from 'utils/context';
import * as WebBrowser from 'expo-web-browser';
import {
  checkLinkedInLongPollingStatus,
  initiateLinkedInPoll,
} from 'api/socialMedialApi';
import { AxiosError } from 'axios';

const POLL_INTERVAL_MS = 60000; // 60s for long polling
const MAX_POLL_DURATION_MS = 3 * POLL_INTERVAL_MS; // re-poll 3 times

export const useLinkedInConnection = ({
  onCancel,
}: {
  onCancel?: () => void;
}) => {
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const openLinkedInAuth = async (sessionId: string) => {
    const params = new URLSearchParams({
      state: sessionId,
      response_type: 'code',
      client_id: linkedInClientId,
      redirect_uri: `${baseUrl}/api-gateway/proc/agent/social-media/auth/linkedin/callback`,
      scope: 'r_liteprofile,r_emailaddress,w_member_social',
    });

    const oauthUrl = `${linkedInAuthUrl}?${params.toString()}`;

    const authSessionResult = await WebBrowser.openAuthSessionAsync(oauthUrl);
    WebBrowser.dismissAuthSession();

    return authSessionResult;
  };

  const pollLinkedInAuthToken = async (
    sessionId: string,
  ): Promise<string | undefined> => {
    const startTime = Date.now();

    const executePolling = async () => {
      const elapsed = Date.now() - startTime;

      if (elapsed > MAX_POLL_DURATION_MS) {
        throw new AxiosError('Authentication timeout', '408');
      }

      try {
        const result = await checkLinkedInLongPollingStatus(sessionId);

        // If result is a string, check if it's "pending"
        if (typeof result === 'string') {
          if (result !== 'pending') {
            return result; // Stop if it's anything but "pending"
          }
        } else if (result.success && result.accessToken) {
          return result.accessToken; // Stop if accessToken received
        }

        // Repeat after POLL_INTERVAL
        pollingIntervalRef.current = setTimeout(
          executePolling,
          POLL_INTERVAL_MS,
        );
      } catch (err) {
        throw err;
      }
    };

    return executePolling();
  };

  const stopPolling = () => {
    if (pollingIntervalRef.current) {
      clearTimeout(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  };

  const loginWithLinkedIn = async () => {
    try {
      const sessionId = await initiateLinkedInPoll();
      const authSessionResult = await openLinkedInAuth(sessionId);

      if (authSessionResult?.type === 'cancel') {
        stopPolling();
        onCancel?.();
      }

      const authResult = await pollLinkedInAuthToken(sessionId);
      return authResult;
    } catch (error) {
      stopPolling();
      throw error;
    }
  };

  return { loginWithLinkedIn };
};
