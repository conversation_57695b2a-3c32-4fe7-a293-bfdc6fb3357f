import { useQueryClient } from '@tanstack/react-query';
import socialMarketingApi from 'api/socialMarketingApi';
import { AxiosError } from 'axios';
import { AGENT_POSTS_QUERY_KEY } from 'hooks/useGetAgentPosts';
import { useCallback, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { countryModuleProfileConfig } from 'utils/config/module';
import { create } from 'zustand';
import {
  GetMarketingActivityStatictics,
  SocialMarketingCreatePostPayload,
  SocialMarketingGeneratePostCaptionPayload,
  SocialMarketingPost,
  SocialMarketingRegeneratePostPayload,
  SocialMarketingSessionStatus,
  SocialMarketingUpdatePostCaptionPayload,
} from '../types';

// Zustand store for managing post creation state
interface PostCreationState {
  isEditing: boolean;
  isEditMode: boolean;
  post: SocialMarketingPost | null;
  /**
   * Cached post media url to avoid re-loading the same media file
   * and useful to preload the media file
   */
  cachedPostMediaUrl?: string;
  status?: SocialMarketingSessionStatus;
  isRegenerating: boolean;
  sessionId?: string;
  statistics?: GetMarketingActivityStatictics;
  createPost: (payload: SocialMarketingCreatePostPayload) => Promise<void>;
  regeneratePost: (
    postId: string,
    payload: SocialMarketingRegeneratePostPayload,
  ) => Promise<void>;
  checkPostSessionStatus: () => Promise<{
    isPostRegenerated: boolean;
  }>;
  generatePostCaption: (
    postId: string,
    payload?: SocialMarketingGeneratePostCaptionPayload,
  ) => Promise<void>;
  updatePostCaption: (
    postId: string,
    payload: SocialMarketingUpdatePostCaptionPayload,
  ) => Promise<void>;
  getPostById: (postId: string) => Promise<void>;
  setEditing: (isEditing: boolean) => void;
  setEditMode: (isEditMode: boolean) => void;
  fetchMarketingActivityStatistics: () => Promise<void>;
  setCachedPostMediaUrl: (mediaUrl: string) => void;
}

export const useSocialMarketingPostStore = create<PostCreationState>(
  (set, get) => ({
    isRegenerating: false,
    isEditing: false,
    isEditMode: false,
    post: null,
    status: SocialMarketingSessionStatus.Generating,
    createPost: async payload => {
      set({ status: SocialMarketingSessionStatus.Generating });
      const response = await socialMarketingApi.createPost(payload);
      set({
        post: response.post,
        status: response.status,
        sessionId: response.sessionId,
        cachedPostMediaUrl: response.post.mediaUrl || undefined,
      });
    },
    regeneratePost: async (postId, payload) => {
      set({
        isRegenerating: true,
        status: SocialMarketingSessionStatus.Generating,
      });
      const response = await socialMarketingApi.regeneratePost(postId, payload);
      set({
        post: response.post,
        status: response.status,
        sessionId: response.sessionId,
        isEditing: false,
      });
    },
    checkPostSessionStatus: async () => {
      const { post, sessionId, isRegenerating } = get();
      let isPostRegenerated = false;

      // Exit early if required data is missing
      if (!post || !sessionId) {
        return { isPostRegenerated };
      }

      const response = await socialMarketingApi.getPostSessionStatus(
        post.id,
        sessionId,
      );

      if (response?.status === SocialMarketingSessionStatus.Ready) {
        isPostRegenerated = isRegenerating;

        // Update state with the new session status and post data
        set({
          status: response.status,
          sessionId: response.sessionId,
          post: response.post,
          isRegenerating: false,
        });
      }

      return { isPostRegenerated };
    },
    generatePostCaption: async (
      postId,
      payload = { language: countryModuleProfileConfig.defaultLanguage },
    ) => {
      const generatedPostCaption = await socialMarketingApi.generatePostCaption(
        postId,
        payload,
      );
      set({
        post: {
          ...(get().post || {}),
          ...generatedPostCaption,
        },
      });
    },
    updatePostCaption: async (postId, payload) => {
      const updatedPostCaption = await socialMarketingApi.updatePostCaption(
        postId,
        payload,
      );
      set({
        post: {
          ...(get().post || {}),
          ...updatedPostCaption,
        },
        isEditing: false,
      });
    },
    getPostById: async postId => {
      const post = await socialMarketingApi.getPostById(postId);
      set({ post, isRegenerating: false, isEditing: false, isEditMode: false });
    },
    fetchMarketingActivityStatistics: async () => {
      const statistics =
        await socialMarketingApi.getMarketingActivityStatistics();
      set({
        statistics,
      });
    },
    setEditing: (isEditing: boolean) => set({ isEditing }),
    setEditMode: (isEditMode: boolean) => set({ isEditMode }),
    setCachedPostMediaUrl: (mediaUrl: string) =>
      set({ cachedPostMediaUrl: mediaUrl }),
  }),
);

export const useCreatePost = () => {
  const createPost = useSocialMarketingPostStore(state => state.createPost);
  const queryClient = useQueryClient();

  const handleCreatePost = useCallback(
    async (payload: SocialMarketingCreatePostPayload) => {
      await createPost(payload);
      queryClient.invalidateQueries(AGENT_POSTS_QUERY_KEY);
    },
    [createPost, queryClient],
  );

  return handleCreatePost;
};

export const useRegeneratePost = () => {
  const regeneratePost = useSocialMarketingPostStore(
    state => state.regeneratePost,
  );
  const queryClient = useQueryClient();

  const handleRegeneratePost = useCallback(
    async (postId: string, payload: SocialMarketingRegeneratePostPayload) => {
      await regeneratePost(postId, payload);
      queryClient.invalidateQueries(AGENT_POSTS_QUERY_KEY);
    },
    [regeneratePost, queryClient],
  );

  return handleRegeneratePost;
};

export const useGeneratePostCaption = () => {
  const generatePostCaption = useSocialMarketingPostStore(
    state => state.generatePostCaption,
  );
  const queryClient = useQueryClient();

  const handleGeneratePostCaption = useCallback(
    async (
      postId: string,
      payload?: SocialMarketingGeneratePostCaptionPayload,
    ) => {
      await generatePostCaption(postId, payload);
      queryClient.invalidateQueries(AGENT_POSTS_QUERY_KEY);
    },
    [generatePostCaption, queryClient],
  );

  return handleGeneratePostCaption;
};

export const useUpdatePostCaption = () => {
  const updatePostCaption = useSocialMarketingPostStore(
    state => state.updatePostCaption,
  );
  const queryClient = useQueryClient();
  const { t } = useTranslation('socialMarketing');

  const handleUpdatePostCaption = useCallback(
    async (
      postId: string,
      payload: SocialMarketingUpdatePostCaptionPayload,
    ) => {
      try {
        await updatePostCaption(postId, payload);
        queryClient.invalidateQueries(AGENT_POSTS_QUERY_KEY);
      } catch (err) {
        const prefixErrorMessage = /^Compliance not met:\s*/;
        if (err instanceof AxiosError) {
          const errorMessage = err.response?.data?.messageList?.[0]?.content;
          throw new Error(
            errorMessage?.match(prefixErrorMessage)
              ? errorMessage.replace(prefixErrorMessage, '')
              : t('prompt.error.common'),
          );
        }
        throw new Error(t('prompt.error.common'));
      }
    },
    [updatePostCaption, queryClient],
  );

  return handleUpdatePostCaption;
};

export const useSocialMarketingPost = (postId?: string) => {
  const getPostById = useSocialMarketingPostStore(state => state.getPostById);
  const post = useSocialMarketingPostStore(state => state.post);
  const isRegenerating = useSocialMarketingPostStore(
    state => state.isRegenerating,
  );

  useEffect(() => {
    if (!postId) return;
    getPostById(postId);
  }, [postId, getPostById]);

  return {
    post,
    isRegenerating,
  };
};

export const useSocialMarketingPostStatus = () => {
  const status = useSocialMarketingPostStore(state => state.status);

  return status;
};

export const useSocialMarketingPostEditState = () => {
  return useSocialMarketingPostStore(state => ({
    isEditing: state.isEditing,
    isEditMode: state.isEditMode,
    setEditing: state.setEditing,
    setEditMode: state.setEditMode,
  }));
};

export const usePostSessionStatusCheck = (options?: {
  onPostRegenerated?: () => void;
}) => {
  const status = useSocialMarketingPostStore(state => state.status);

  const checkPostSessionStatus = useSocialMarketingPostStore(
    state => state.checkPostSessionStatus,
  );
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Clear previous interval if any
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Set new interval only if status is not Ready
    // This will check the post session status every 3 seconds
    if (status !== SocialMarketingSessionStatus.Ready) {
      intervalRef.current = setInterval(() => {
        checkPostSessionStatus().then(({ isPostRegenerated }) => {
          if (isPostRegenerated) {
            options?.onPostRegenerated?.();
          }
        });
      }, 3000);
    }

    // Always clear interval on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [status]);
};

export const useMarketingActivityStatistics = () => {
  const fetchMarketingActivityStatistics = useSocialMarketingPostStore(
    state => state.fetchMarketingActivityStatistics,
  );
  const statistics = useSocialMarketingPostStore(state => state.statistics);

  useEffect(() => {
    fetchMarketingActivityStatistics();
  }, []);

  return statistics;
};

export const useCachedPostMediaUrl = () => {
  const setCachedPostMediaUrl = useSocialMarketingPostStore(
    state => state.setCachedPostMediaUrl,
  );
  const cachedPostMediaUrl = useSocialMarketingPostStore(
    state => state.cachedPostMediaUrl,
  );

  return {
    setCachedPostMediaUrl,
    cachedPostMediaUrl,
  };
};
