import { createNativeStackNavigator } from '@react-navigation/native-stack';
import AiAvatarCreationProcedureScreen from 'screens/AiAvatarCreationProcedureScreen';
import AiAvatarLibraryScreen from 'screens/AiAvatarLibraryScreen';
import { SocialMarketingCreateNewScreen } from 'screens/SocialMarketingCreateNewScreen';
import { SocialMarketingLoadingScreen } from 'screens/SocialMarketingLoadingScreen';
import { SocialMarketingPostCaptionScreen } from 'screens/SocialMarketingPostCaptionScreen';
import SocialMarketingPostCreationReviewScreen from 'screens/SocialMarketingPostCreationReviewScreen';
import { RootStackParamListMap } from 'types';
import SocialMarketingMediaFlowHeader from '../components/SocialMarketingMediaFlowHeader';
import { useIgniteFeatureEnable } from './useIgniteFeatureFlag';
import { SocialMarketingRepostScreen } from 'screens/SocialMarketingRepost';

type Region = keyof RootStackParamListMap;
type StackNavigator<T extends Region> = ReturnType<
  typeof createNativeStackNavigator<RootStackParamListMap[T]>
>;

function useIgniteStackScreens<T extends Region>(Stack: StackNavigator<T>) {
  const isIgniteFeatureEnabled = useIgniteFeatureEnable();

  if (!isIgniteFeatureEnabled) {
    return null;
  }

  return (
    <Stack.Group
      screenOptions={{
        gestureEnabled: true,
        // Default header is false, most of screen has its own header
        // We will show the header on the screen that needs it
        headerShown: false,
      }}>
      <Stack.Screen
        name="SocialMarketingCreateNew"
        component={SocialMarketingCreateNewScreen}
        options={{
          // Android can not show the stack modal, such as iOS, so we need to use transparentModal
          // this is a workaround to show the modal screen on Android
          presentation: 'transparentModal',
          animation: 'slide_from_bottom',
        }}
      />
      <Stack.Screen
        name="SocialMarketingPostCaption"
        component={SocialMarketingPostCaptionScreen}
        options={{
          headerShown: true,
          header: () => <SocialMarketingMediaFlowHeader />,
        }}
      />
      <Stack.Screen
        name="SocialMarketingPostCreationReview"
        component={SocialMarketingPostCreationReviewScreen}
        options={{
          headerShown: true,
          header: () => <SocialMarketingMediaFlowHeader />,
        }}
      />
      <Stack.Screen name="AiAvatarLibrary" component={AiAvatarLibraryScreen} />
      <Stack.Screen
        name="AiAvatarCreationProcedure"
        component={AiAvatarCreationProcedureScreen}
      />
      <Stack.Screen
        name="SocialMarketingLoading"
        component={SocialMarketingLoadingScreen}
        options={{
          headerShown: true,
          header: () => <SocialMarketingMediaFlowHeader />,
          animation: 'none',
        }}
      />
      <Stack.Screen
        name="SocialMarketingRepost"
        component={SocialMarketingRepostScreen}
        options={{
          headerShown: true,
          header: () => <SocialMarketingMediaFlowHeader />,
        }}
      />
    </Stack.Group>
  );
}

export default useIgniteStackScreens;
