import dayjs from 'dayjs';
import * as socialMedia<PERSON>pi from 'api/socialMedialApi';
import { SocialMarketingPlatform } from 'features/socialMarketing/types';

export const useSocialMediaToken = () => {
  const verifySocialMediaToken = async (
    platform?: SocialMarketingPlatform | null,
  ) => {

    try {
      let verifyToken:
        | (() => Promise<socialMediaApi.VerifySocialMediaTokenResponse>)
        | undefined;

      switch (platform) {
        case SocialMarketingPlatform.Facebook:
          verifyToken = socialMediaApi.verifyFacebookToken;
          break;
        case SocialMarketingPlatform.Linkedin:
          verifyToken = socialMediaApi.verifyLinkedInToken;
          break;
        default:
          return false;
      }

      const res = await verifyToken();

      const expiredAt = dayjs(res.expiredAt * 1000);
      const today = dayjs();
      const isValidToken = expiredAt.isAfter(today);
      return isValidToken;
    } catch (err) {
      return false;
    }
  };
  return { verifySocialMediaToken };
};
