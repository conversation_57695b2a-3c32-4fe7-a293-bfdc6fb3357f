import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { RouteProp, StackActions, useRoute } from '@react-navigation/native';
import { Button } from 'cube-ui-components';
import {
  useRegeneratePost,
  useSocialMarketingPost,
  useSocialMarketingPostEditState,
} from 'features/socialMarketing/hooks/usePosts';
import React, { useCallback, useEffect, useState } from 'react';
import { Keyboard } from 'react-native';

import { useRootStackNavigation } from 'hooks/useRootStack';
import { useTranslation } from 'react-i18next';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import GATracking from 'utils/helper/gaTracking';
import SocialMarketingMediaReview from './SocialMarketingMediaReview';
import SocialMarketingPromptInputReview from './SocialMarketingPromptInputReview';
import { RootStackParamList } from 'types';

const Container = styled(SafeAreaView)(({ theme: { colors } }) => ({
  flex: 1,
  backgroundColor: colors.palette.fwdGrey[20],
}));
const FooterWrapper = styled.View(({ theme: { space } }) => ({
  flexGrow: 1,
  justifyContent: 'flex-end',
  padding: space[4],
}));

type SocialMarketingPostCreationReviewRouteProp = RouteProp<
  RootStackParamList,
  'SocialMarketingPostCreationReview'
>;

export default function SocialMarketingPostCreationReviewPhone() {
  const { name: screenName, params } =
    useRoute<SocialMarketingPostCreationReviewRouteProp>();
  const { bottom } = useSafeAreaInsets();
  const navigation = useRootStackNavigation();
  const { colors, space } = useTheme();
  const { t } = useTranslation('socialMarketing');
  const { post } = useSocialMarketingPost(params?.postId);
  const { setEditing } = useSocialMarketingPostEditState();
  const regeneratePost = useRegeneratePost();
  const [isPromptFocused, setIsPromptFocused] = useState(false);
  const [prompt, setPrompt] = useState(post?.prompt || '');

  const handleRegeneratePost = useCallback(async () => {
    if (!prompt || !post) {
      return;
    }

    navigation.dispatch(StackActions.replace('SocialMarketingLoading'));

    await regeneratePost(post.id || '', {
      prompt,
    });
  }, [post, prompt, regeneratePost]);

  const onTrackingButtonClicked = useCallback(
    async (buttonName: string) => {
      await GATracking.logButtonPress({
        screenName,
        screenClass: 'Social Marketing',
        actionType: 'non_cta_button',
        buttonName,
      });
    },
    [screenName],
  );

  const handleFocusPromptInput = useCallback(
    (focused: boolean) => {
      onTrackingButtonClicked(
        `Prompt input: ${focused ? 'onFocus' : 'onBlur'}`,
      );
      setIsPromptFocused(focused);
    },
    [onTrackingButtonClicked],
  );

  const handleContinueButtonPress = useCallback(() => {
    if (isPromptFocused) {
      Keyboard.dismiss();
      setIsPromptFocused(false);
      onTrackingButtonClicked('Regenerate button');
      return handleRegeneratePost();
    }

    navigation.navigate('SocialMarketingPostCaption');
  }, [
    isPromptFocused,
    navigation,
    onTrackingButtonClicked,
    handleRegeneratePost,
  ]);

  useEffect(() => {
    if (!post) {
      return;
    }

    const isEdited = prompt !== post?.prompt;
    setEditing(isEdited);
  }, [prompt, post?.prompt]);

  return (
    <Container edges={['bottom']}>
      <KeyboardAwareScrollView
        // Use bottomOffset to ensure the input is not covered by the keyboard
        // Adjust bottomOffset based on the input height, input height is fixed at space[30]
        // space[5] is the bottom space for the footer button
        bottomOffset={space[35]}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{
          flexGrow: 1,
          padding: space[4],
        }}>
        <SocialMarketingMediaReview blur={isPromptFocused} />

        <SocialMarketingPromptInputReview
          isFocused={isPromptFocused}
          onFocus={handleFocusPromptInput}
          prompt={prompt}
          onChangePrompt={setPrompt}
        />
      </KeyboardAwareScrollView>
      <FooterWrapper>
        <KeyboardStickyView
          offset={{
            opened: bottom,
          }}
          style={{
            backgroundColor: colors.background,
          }}>
          <Button
            disabled={!prompt?.length} // Disable button if prompt is empty
            onPress={handleContinueButtonPress}
            text={t(
              isPromptFocused
                ? 'createPost.cta.regenerate'
                : 'createPost.cta.postCaption',
            )}
          />
        </KeyboardStickyView>
      </FooterWrapper>
    </Container>
  );
}
