import { useTheme } from '@emotion/react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Button } from 'cube-ui-components';
import { useSocialMarketingPostStatus } from 'features/socialMarketing/hooks/usePosts';
import { SocialMarketingSessionStatus } from 'features/socialMarketing/types';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import LoadingSocial from '../LoadingSocial';
import { Platform } from 'react-native';

type LottiePostStatusProgressProps = {
  description: string;
  onCompleted: () => void;
};

enum LottieState {
  Generating = 'Generating',
  ComplianceChecking = 'ComplianceChecking',
  Succeed = 'Succeed',
}

export default function LottiePostStatusProgress({
  description,
  onCompleted,
}: LottiePostStatusProgressProps) {
  const { space } = useTheme();
  const status = useSocialMarketingPostStatus();
  const { t } = useTranslation('socialMarketing');
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const [lottieState, setLottieState] = useState(LottieState.Generating);

  const { title, lottieSource, showButton } = useMemo(() => {
    let title = '',
      lottieSource: string | null = null,
      showButton = true;

    if (lottieState === LottieState.Generating) {
      title = t('createPost.generating');
      lottieSource = require('assets/lottie/customize.json');
    } else if (lottieState === LottieState.ComplianceChecking) {
      title = t('loading.checking');
      lottieSource = require('assets/lottie/checking-with-compliance.json');
    } else if (lottieState === LottieState.Succeed) {
      title = t('createPost.generating.done');
      description = '';
      lottieSource = require('assets/lottie/check-successful.json');
      showButton = false;
    }

    return {
      title,
      lottieSource,
      showButton,
    };
  }, [lottieState]);

  const handleGoToPostPress = () => {
    navigation.navigate('SocialMarketingMyPosts');
  };

  const handleAnimationFinish = useCallback(() => {
    if (lottieState === LottieState.ComplianceChecking) {
      setTimeout(() => {
        setLottieState(LottieState.Succeed);
      }, 3000);
    } else if (lottieState === LottieState.Succeed) {
      setTimeout(() => {
        onCompleted();
      }, 1200);
    }
  }, [lottieState, onCompleted]);

  useEffect(() => {
    if (status === SocialMarketingSessionStatus.Generating) {
      setLottieState(LottieState.Generating);
    } else if (status === SocialMarketingSessionStatus.Ready) {
      setLottieState(LottieState.ComplianceChecking);
    }
  }, [status]);

  if (!lottieSource) {
    return null;
  }

  return (
    <LoadingSocial
      title={title}
      lottie={lottieSource}
      description={description}
      loop={lottieState === LottieState.Generating}
      onAnimationFinish={handleAnimationFinish}>
      {showButton && (
        <Button
          onPress={handleGoToPostPress}
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: space[8],
          }}
          textStyle={{
            paddingHorizontal: space[12],
          }}
          text={t('createPost.generating.goToMyPosts')}
        />
      )}
    </LoadingSocial>
  );
}
