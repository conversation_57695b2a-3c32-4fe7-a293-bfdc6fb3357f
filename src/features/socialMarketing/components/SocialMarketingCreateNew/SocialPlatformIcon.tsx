import React, { useMemo } from 'react';

import styled from '@emotion/native';
import { SocialMarketingPlatform } from 'features/socialMarketing/types';
import { Icon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

const PlatformContainer = styled.TouchableOpacity<{ isSelected?: boolean }>(
  ({ theme: { space, colors }, isSelected }) => ({
    flexShrink: 1,
    alignItems: 'center',
    paddingVertical: space[2],
    paddingHorizontal: space[3],
    borderWidth: 2,
    borderRadius: space[10],
    borderColor: isSelected
      ? colors.palette.fwdOrange[100]
      : colors.palette.fwdGrey[100],
    backgroundColor: isSelected
      ? colors.palette.fwdOrange[20]
      : colors.background,
    marginLeft: space[3],
  }),
);

type SocialPlatformIconProps = {
  isSelected?: boolean;
  platform: SocialMarketingPlatform;
  onPress?: (platform: SocialMarketingPlatform) => void;
};

export default function SocialPlatformIcon(props: SocialPlatformIconProps) {
  const { sizes } = useTheme();

  const IconComponent = useMemo(() => {
    switch (props.platform) {
      case SocialMarketingPlatform.Instagram:
        return Icon.SocialInstagramColour;
      case SocialMarketingPlatform.Facebook:
        return Icon.SocialFacebookColour;
      case SocialMarketingPlatform.TikTok:
        return Icon.SocialTikTokColour;
      default:
        return null; // Default icon if none matches
    }
  }, [props.platform]);

  if (!IconComponent) {
    return null;
  }

  return (
    <PlatformContainer
      isSelected={props.isSelected}
      onPress={() => props.onPress?.(props.platform)}>
      <IconComponent size={sizes[6]} />
    </PlatformContainer>
  );
}
