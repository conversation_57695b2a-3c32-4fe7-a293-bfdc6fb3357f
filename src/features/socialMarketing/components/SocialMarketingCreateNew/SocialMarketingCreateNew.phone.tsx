import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { FlatList, useWindowDimensions, View } from 'react-native';

import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import { BottomSheetDefaultBackdropProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop/types';
import { useRoute } from '@react-navigation/native';
import socialMarketingApi from 'api/socialMarketingApi';
import { isAxiosError } from 'axios';
import Skeleton from 'components/Skeleton';
import {
  Button,
  Card,
  Icon,
  Row,
  Switch,
  TextField,
  Typography,
} from 'cube-ui-components';
import {
  mediaTypeAvatarVideoMP4,
  mediaTypeImagePNG,
  mediaTypeShortVideoMP4,
} from 'features/socialMarketing/assets/mediaTypes';
import { useCreatePost } from 'features/socialMarketing/hooks/usePosts';
import { useSocialMarketingAlert } from 'features/socialMarketing/hooks/useSocialMarketingAlert';
import {
  SocialMarketingAlertType,
  SocialMarketingPlatform,
  SocialMarketingPostType,
  SocialMarketingRatio,
  SocialMarketingTopic,
  SocialMarketingVideoDuration,
} from 'features/socialMarketing/types';
import { useRootStackNavigation } from 'hooks/useRootStack';
import { useTranslation } from 'react-i18next';
import { LayoutChangeEvent } from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import GATracking from 'utils/helper/gaTracking';
import MediaTypeCardPhone from './MediaTypeCard.phone';
import RatioSelection from './RatioSelection';
import SocialPlatformIcon from './SocialPlatformIcon';

const SheetIndicator = styled.View(({ theme: { colors, space } }) => ({
  alignSelf: 'center',
  width: space[10],
  height: space[1],
  borderRadius: space[1],
  backgroundColor: colors.palette.fwdGrey[100],
  marginTop: space[2],
  marginBottom: space[8],
}));
const Label = styled(Typography.H7)(({ theme: { space } }) => ({
  marginVertical: space[2],
  flex: 1,
}));
const AiTextBoxWrapper = styled.View(({ theme: { colors, space } }) => ({
  flex: 1,
  backgroundColor: colors.background,
  borderRadius: space[4],
}));
const TopicItemContainer = styled.Pressable(({ theme: { colors, space } }) => ({
  marginVertical: space[2],
  paddingVertical: space[1],
  paddingHorizontal: space[2],
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdOrange[100],
  borderRadius: space[6],
}));
const TopicItemSeprator = styled.View(({ theme: { space } }) => ({
  width: space[2],
}));
const FormRow = styled(Row)(() => ({
  flex: 1,
  alignItems: 'center',
  justifyContent: 'flex-end',
}));
const OptionContainer = styled(Row)(({ theme: { space } }) => ({
  flex: 1,
  alignItems: 'center',
  justifyContent: 'space-between',
  marginVertical: space[2],
  paddingVertical: space[2],
}));
const DurationSelector = styled.Pressable<{ isSelected?: boolean }>(
  ({ theme: { colors, space }, isSelected }) => ({
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: isSelected ? colors.palette.fwdOrange[50] : 'transparent',
    borderWidth: 1,
    borderColor: isSelected
      ? colors.palette.fwdOrange[100]
      : colors.palette.fwdGrey[100],
    borderRadius: space[10],
    paddingVertical: space[2],
    paddingHorizontal: space[4],
    marginLeft: space[4],
  }),
);

const mockTopics: SocialMarketingTopic[] = [
  {
    id: '1',
    title: 'Medical benefit',
  },
  {
    id: '2',
    title: 'Family protection',
  },
  {
    id: '3',
    title: 'Accident benefit',
  },
  {
    id: '4',
    title: 'Accidents',
  },
];

export default function SocialMarketingCreateNewPhone() {
  const navigation = useRootStackNavigation();
  const { height: screenHeight } = useWindowDimensions();
  const { name: screenName } = useRoute();
  const { colors, space } = useTheme();
  const { bottom, top } = useSafeAreaInsets();
  const { t } = useTranslation('socialMarketing');
  const createPost = useCreatePost();
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(
    () => [screenHeight - top - space[6]],
    [screenHeight, top, space],
  );
  const showAlert = useSocialMarketingAlert();

  const [promptInputHeight, setPromptInputHeight] = useState(0);
  const [isPromptGenerating, setIsPromptGenerating] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [selectedTopic, setSelectedTopic] = useState('');
  const [selectedMediaType, setSelectedMediaType] =
    useState<SocialMarketingPostType>(SocialMarketingPostType.Image);
  const [selectedPlatform, setSelectedPlatform] =
    useState<SocialMarketingPlatform>();
  const [selectedDuration, setSelectedDuration] = useState<
    SocialMarketingVideoDuration | undefined
  >();
  const [isFixedCameraLens, setIsFixedCameraLens] = useState(false);
  const [selectedRatio, setSelectedRatio] = useState<SocialMarketingRatio>(
    SocialMarketingRatio.Square,
  );

  const isAvatarVideo = useMemo(
    () => selectedMediaType === SocialMarketingPostType.AvatarVideo,
    [selectedMediaType],
  );

  const isFormComplete = useMemo(() => {
    // Avatar video is always complete
    if (isAvatarVideo) {
      return true;
    }

    const basicFieldsFilled =
      prompt.trim() !== '' &&
      selectedMediaType?.trim() !== '' &&
      !!selectedPlatform &&
      !!selectedRatio;

    const isDurationValid =
      selectedMediaType === SocialMarketingPostType.Image || !!selectedDuration;

    return basicFieldsFilled && isDurationValid;
  }, [
    prompt,
    selectedMediaType,
    selectedPlatform,
    selectedRatio,
    selectedDuration,
    isAvatarVideo,
  ]);

  const isVideoMediaTypeSelected =
    selectedMediaType === SocialMarketingPostType.ShortVideo ||
    selectedMediaType === SocialMarketingPostType.AvatarVideo;
  const promptField = useMemo<{ label: string; placeholder: string }>(() => {
    if (isVideoMediaTypeSelected) {
      return {
        label: t('createPost.describeVideo'),
        placeholder: t('createPost.describeVideoPlaceholder'),
      };
    }

    return {
      label: t('createPost.describeImage'),
      placeholder: t('createPost.describeImagePlaceholder'),
    };
  }, [isVideoMediaTypeSelected]);
  const ratioLabel = isVideoMediaTypeSelected
    ? t('createPost.videoRatio')
    : t('createPost.imageRatio');

  const handlePromptInputHeightChange = useCallback(
    (event: LayoutChangeEvent) => {
      const { height } = event.nativeEvent.layout;
      setPromptInputHeight(height);
    },
    [],
  );

  const handleRenderTopicItem = useCallback(
    ({ item }: { item: SocialMarketingTopic }) => {
      return (
        <TopicItemContainer
          onPress={async () => {
            try {
              onTrackingButtonClicked(`Topic button: ${item.title}`);
              setSelectedTopic(item.title);
              setIsPromptGenerating(true);
              const generatedPrompt =
                await socialMarketingApi.generatePromptFromTopic(
                  selectedMediaType,
                  item.title,
                );
              setPrompt(generatedPrompt);
            } finally {
              // Apply 0.5s timeout to avoid flashing when the API returns quickly
              setTimeout(() => {
                setIsPromptGenerating(false);
              }, 500);
            }
          }}>
          <Typography.SmallLabel
            fontWeight="medium"
            color={colors.palette.fwdOrange[100]}>
            {item.title}
          </Typography.SmallLabel>
        </TopicItemContainer>
      );
    },
    [selectedMediaType],
  );

  const handleCreatePost = useCallback(async () => {
    if (isAvatarVideo) {
      // Close the current modal (presented as a stack screen)
      // This ensures that the screen appears on top of the base stack
      navigation.goBack();
      return navigation.navigate('AiAvatarCreationProcedure');
    }

    // Check if the form is complete before proceeding
    const isReady = selectedMediaType && isFormComplete && selectedPlatform;

    if (!isReady) {
      return;
    }

    try {
      await createPost({
        mediaType: selectedMediaType,
        prompt,
        topic: selectedTopic,
        ratio: selectedRatio,
        platform: selectedPlatform!,
        durationInSeconds: selectedDuration,
        isFixedCameraLens,
      });
      // Close the current modal (presented as a stack screen)
      // This ensures that the screen appears on top of the base stack
      navigation.goBack();

      navigation.navigate('SocialMarketingLoading');
    } catch (error) {
      if (isAxiosError(error) && error.response?.status === 500) {
        showAlert(SocialMarketingAlertType.Internal);
      }
    }
  }, [
    selectedMediaType,
    prompt,
    selectedPlatform,
    selectedRatio,
    selectedTopic,
    selectedDuration,
    isFixedCameraLens,
    navigation,
    isAvatarVideo,
    showAlert,
  ]);

  const handleSelectMediaType = (type: SocialMarketingPostType) => {
    setSelectedMediaType(type);
    // Reset selected duration to avoid confusion when switching between media types
    if (type === SocialMarketingPostType.Image) {
      setSelectedDuration(undefined);
    }

    if (type === SocialMarketingPostType.AvatarVideo) {
      setPrompt('');
    }
  };

  const onTrackingButtonClicked = useCallback(
    async (buttonName: string) => {
      await GATracking.logButtonPress({
        screenName,
        screenClass: 'Social Marketing',
        actionType: 'non_cta_button',
        buttonName,
      });
    },
    [screenName],
  );

  const handleSelectPlatform = useCallback(
    (platform: SocialMarketingPlatform) => {
      setSelectedPlatform(platform);
      onTrackingButtonClicked(`Platform button: ${platform}`);
    },
    [onTrackingButtonClicked],
  );

  const handleSelectDuration = useCallback(
    (duration: SocialMarketingVideoDuration) => {
      setSelectedDuration(duration);
      onTrackingButtonClicked(`Duration button: ${duration}`);
    },
    [onTrackingButtonClicked],
  );

  const handleBottomSheetDismiss = () => {
    bottomSheetModalRef.current?.close();
    navigation.goBack();
  };
  const renderBackdrop = useCallback(
    (backdropProps: BottomSheetDefaultBackdropProps) => (
      <BottomSheetBackdrop
        {...backdropProps}
        onPress={handleBottomSheetDismiss}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        pressBehavior="close"
      />
    ),
    [handleBottomSheetDismiss],
  );

  // Default value exists, so listen to value instead of event
  useEffect(() => {
    const buttonName = `Media type button: ${selectedMediaType}`;
    onTrackingButtonClicked(buttonName);
  }, [selectedMediaType, onTrackingButtonClicked]);
  // Would change by platform, so listen to value instead of event
  useEffect(() => {
    const buttonName = `Ratio button: ${selectedRatio}`;
    onTrackingButtonClicked(buttonName);
  }, [selectedRatio, onTrackingButtonClicked]);

  useEffect(() => {
    bottomSheetModalRef.current?.present();
  }, []);

  return (
    <BottomSheetModalProvider>
      <BottomSheetModal
        index={0}
        ref={bottomSheetModalRef}
        snapPoints={snapPoints}
        enableContentPanningGesture={false}
        enablePanDownToClose={true}
        handleComponent={() => <SheetIndicator />}
        backdropComponent={renderBackdrop}
        keyboardBehavior="extend"
        android_keyboardInputMode="adjustResize"
        keyboardBlurBehavior="restore"
        onDismiss={handleBottomSheetDismiss}>
        <KeyboardAwareScrollView
          nestedScrollEnabled
          bottomOffset={promptInputHeight}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: space[4] }}
          keyboardShouldPersistTaps="handled"
          overScrollMode="never"
          keyboardDismissMode="on-drag">
          <Typography.H6 fontWeight="bold">
            {t('createPost.title')}
          </Typography.H6>
          <View
            style={{
              marginVertical: space[3],
            }}>
            <Label fontWeight="medium">{t('createPost.chooseMediaType')}</Label>
            <FormRow alignItems="center" justifyContent="space-between">
              <MediaTypeCardPhone
                type={SocialMarketingPostType.Image}
                title={t('mediaType.image')}
                thumbnail={mediaTypeImagePNG}
                onSelect={handleSelectMediaType}
                isSelected={selectedMediaType === SocialMarketingPostType.Image}
              />
              <MediaTypeCardPhone
                type={SocialMarketingPostType.ShortVideo}
                title={t('mediaType.shortVideo')}
                thumbnail={mediaTypeShortVideoMP4}
                onSelect={handleSelectMediaType}
                isSelected={
                  selectedMediaType === SocialMarketingPostType.ShortVideo
                }
              />
              <MediaTypeCardPhone
                type={SocialMarketingPostType.AvatarVideo}
                title={t('mediaType.avatarVideo')}
                thumbnail={mediaTypeAvatarVideoMP4}
                onSelect={handleSelectMediaType}
                isSelected={
                  selectedMediaType === SocialMarketingPostType.AvatarVideo
                }
              />
            </FormRow>
          </View>
          {isAvatarVideo ? null : (
            <>
              <Label fontWeight="medium">{promptField.label}</Label>
              <Card
                variant="gradient"
                gradientBorder
                strokeWidth={space[2]}
                blur={space[1]}
                opacity={0.4}
                style={{
                  marginBottom: space[4],
                }}>
                <AiTextBoxWrapper onLayout={handlePromptInputHeightChange}>
                  <View
                    style={
                      // Apply opacity to keep the height of the text input box to avoid flickering
                      isPromptGenerating && {
                        opacity: 0,
                      }
                    }>
                    <TextField
                      textarea
                      value={prompt}
                      onChangeText={setPrompt}
                      onFocus={() =>
                        onTrackingButtonClicked(`Prompt input: onFocus`)
                      }
                      onBlur={() =>
                        onTrackingButtonClicked(`Prompt input: onBlur`)
                      }
                      // Needs both prop and style to override the vertical alignment
                      textAlignVertical="top"
                      inputStyle={{
                        textAlignVertical: 'top',
                      }}
                      placeholder={promptField.placeholder}
                      inputContainerStyle={{
                        borderRadius: space[4],
                        height: space[30],
                        borderWidth: 0,
                        paddingVertical: space[2],
                        paddingRight: space[1],
                      }}
                    />
                    <Typography.SmallLabel
                      fontWeight="medium"
                      style={{
                        marginHorizontal: space[4],
                        marginTop: space[2],
                      }}>
                      {t('createPost.tryTheseTopics')}
                    </Typography.SmallLabel>
                    <FlatList
                      horizontal
                      data={mockTopics}
                      showsHorizontalScrollIndicator={false}
                      contentContainerStyle={{
                        paddingHorizontal: space[4],
                        paddingBottom: space[2],
                      }}
                      keyboardShouldPersistTaps="handled"
                      keyExtractor={item => `topic-${item.id}`}
                      renderItem={handleRenderTopicItem}
                      ItemSeparatorComponent={() => <TopicItemSeprator />}
                    />
                  </View>
                </AiTextBoxWrapper>
                {isPromptGenerating && (
                  <View
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      zIndex: 1,
                      paddingVertical: space[6],
                      paddingHorizontal: space[4],
                      gap: space[1],
                    }}>
                    <Skeleton width="100%" height={16} radius={2} />
                    <Skeleton width="100%" height={16} radius={2} />
                    <Skeleton width="100%" height={16} radius={2} />
                    <Skeleton width="50%" height={16} radius={2} />
                  </View>
                )}
              </Card>
              <OptionContainer>
                <Label
                  fontWeight="medium"
                  style={{
                    flex: 0.3,
                    flexShrink: 1,
                  }}>
                  {t('createPost.platforms')}
                </Label>
                <FormRow alignItems="center" justifyContent="center">
                  {Object.values(SocialMarketingPlatform).map(platform => (
                    <SocialPlatformIcon
                      key={platform}
                      onPress={handleSelectPlatform}
                      platform={platform}
                      isSelected={selectedPlatform === platform}
                    />
                  ))}
                </FormRow>
              </OptionContainer>
              {isVideoMediaTypeSelected && (
                <OptionContainer>
                  <Label fontWeight="medium">{t('createPost.duration')}</Label>
                  <FormRow
                    style={{
                      flex: 1,
                    }}
                    alignItems="center"
                    justifyContent="center">
                    {Object.values(SocialMarketingVideoDuration).map(
                      duration => (
                        <DurationSelector
                          key={duration}
                          onPress={() => {
                            handleSelectDuration(duration);
                          }}
                          isSelected={selectedDuration === duration}>
                          <Typography.Body
                            fontWeight={
                              selectedDuration === duration ? 'bold' : 'normal'
                            }>
                            {t('createPost.duration.inSeconds', {
                              duration,
                            })}
                          </Typography.Body>
                        </DurationSelector>
                      ),
                    )}
                  </FormRow>
                </OptionContainer>
              )}
              <OptionContainer>
                <Label fontWeight="medium">{ratioLabel}</Label>
                <RatioSelection
                  onSelect={setSelectedRatio}
                  platform={selectedPlatform}
                  selectedRatio={selectedRatio}
                  isVideoMediaTypeSelected={isVideoMediaTypeSelected}
                />
              </OptionContainer>
              {isVideoMediaTypeSelected && (
                <OptionContainer>
                  <View style={{ flex: 0.6 }}>
                    <Label fontWeight="medium">
                      {t('createPost.fixedCameraLens.label')}
                    </Label>
                    <Typography.Body color={colors.palette.fwdGreyDarkest}>
                      {t('createPost.fixedCameraLens.description')}
                    </Typography.Body>
                  </View>
                  <View
                    style={{
                      flex: 0.4,
                      justifyContent: 'center',
                      alignItems: 'flex-end',
                    }}>
                    <Switch
                      value={isFixedCameraLens}
                      onChange={setIsFixedCameraLens}
                    />
                  </View>
                </OptionContainer>
              )}
            </>
          )}
        </KeyboardAwareScrollView>
        <KeyboardStickyView
          offset={{
            opened: bottom,
          }}
          style={{
            backgroundColor: colors.background,
            marginHorizontal: space[4],
          }}>
          <Button
            variant="primary"
            size="medium"
            disabled={!isFormComplete}
            icon={<Icon.AIStar />}
            onPress={handleCreatePost}
            style={{
              marginBottom: bottom + space[4],
            }}
            text={t('createPost.cta.generate')}
          />
        </KeyboardStickyView>
      </BottomSheetModal>
    </BottomSheetModalProvider>
  );
}
