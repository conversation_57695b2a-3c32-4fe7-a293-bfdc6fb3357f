import React, { useCallback, useEffect, useState } from 'react';
import { AiAvatarCreationProcedureStep } from './types';
import AiAvatarCreationIntroduction from './steps/AiAvatarCreationIntroduction';
import AiAvatarCreationRecord from './steps/AiAvatarCreationRecord';
import AiAvatarCreationPreview from './steps/AiAvatarCreationPreview';
import { useSocialMarketingAlert } from 'features/socialMarketing/hooks/useSocialMarketingAlert';
import { SocialMarketingAlertType } from 'features/socialMarketing/types';
import { useCameraPermission } from 'react-native-vision-camera';

export default function AiAvatarCreationProcedurePhone() {
  const showSocialMarketingAlert = useSocialMarketingAlert();
  const { hasPermission, requestPermission } = useCameraPermission();

  const [videoUrl, setVideoUrl] = useState<string>('');
  const [step, setStep] = useState(AiAvatarCreationProcedureStep.Introduction);

  const handleNextStep = () => {
    // Do not allow to go to next step if already at the last step
    if (step === AiAvatarCreationProcedureStep.Preview) {
      return;
    }
    setStep(prev => prev + 1);
  };

  const handleBackStep = () => {
    // Do not allow to go to previous step if already at the first step
    if (step === AiAvatarCreationProcedureStep.Introduction) {
      return;
    }

    setStep(prev => prev - 1);
  };

  const handleShowAgreementTerms = useCallback(
    (hasCameraPermission?: boolean) => {
      if (step !== AiAvatarCreationProcedureStep.Introduction) {
        return;
      }

      if (hasCameraPermission) {
        showSocialMarketingAlert(
          SocialMarketingAlertType.AvatarRecordTermsAgreement,
        );
      }
    },
    [step],
  );

  const requestCameraPermissionAndShowAgreementTerms = useCallback(() => {
    if (hasPermission) {
      handleShowAgreementTerms(true);
    } else {
      requestPermission().then(handleShowAgreementTerms);
    }
  }, [hasPermission, handleShowAgreementTerms]);

  useEffect(() => {
    requestCameraPermissionAndShowAgreementTerms();
  }, []);

  switch (step) {
    case AiAvatarCreationProcedureStep.Introduction:
      return <AiAvatarCreationIntroduction onNextPress={handleNextStep} />;
    case AiAvatarCreationProcedureStep.Record:
      return (
        <AiAvatarCreationRecord
          onBackPress={handleBackStep}
          onSubmit={url => {
            setVideoUrl(url);
            handleNextStep();
          }}
        />
      );
    case AiAvatarCreationProcedureStep.Preview:
      return (
        <AiAvatarCreationPreview
          videoUrl={videoUrl}
          onRecordAgain={handleBackStep}
        />
      );
    default:
      return null;
  }
}
