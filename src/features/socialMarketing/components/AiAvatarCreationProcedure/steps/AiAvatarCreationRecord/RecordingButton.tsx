import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, Typography } from 'cube-ui-components';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import Animated, {
  FadeIn,
  FadeOut,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = {
  isRecording: boolean;
  onPress: () => void;
};

const RecordingButton = ({ isRecording, onPress }: Props) => {
  const { bottom } = useSafeAreaInsets();
  const { space, colors, sizes, borderRadius } = useTheme();
  const { t } = useTranslation('socialMarketing');
  const [width, setWidth] = useState(0);

  const outerBorderColor = useSharedValue<string | undefined>(undefined);
  useEffect(() => {
    outerBorderColor.value = isRecording
      ? colors.palette.fwdOrange[100]
      : colors.palette.white;
  }, [
    colors.palette.fwdOrange,
    colors.palette.white,
    isRecording,
    outerBorderColor,
  ]);
  const outerAnimatedStyle = useAnimatedStyle(() => ({
    borderColor: outerBorderColor.value,
  }));

  const innerSize = useSharedValue<number | undefined>(undefined);
  const innerRadius = useSharedValue<number | undefined>(undefined);
  useEffect(() => {
    innerSize.value = withTiming(isRecording ? sizes[6] : sizes[14], {
      duration: 200,
    });
    innerRadius.value = withTiming(
      isRecording ? borderRadius['x-small'] : borderRadius.full,
      { duration: 120 },
    );
  }, [
    borderRadius,
    innerRadius,
    innerSize,
    isRecording,
    outerBorderColor,
    sizes,
  ]);
  const innerAnimatedStyle = useAnimatedStyle(() => ({
    width: innerSize.value,
    height: innerSize.value,
    borderRadius: innerRadius.value,
  }));

  return (
    <Column
      gap={space[3]}
      marginBottom={bottom}
      alignItems="center"
      onLayout={e => setWidth(e.nativeEvent.layout.width)}>
      <AnimatedButtonOuter
        style={[
          {
            width: sizes[18],
            height: sizes[18],
            borderWidth: sizes[1],
            borderRadius: borderRadius.full,
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
          },
          outerAnimatedStyle,
        ]}
        onPress={onPress}>
        <BlackBackdrop />
        <AnimatedButtonInner
          style={[
            { backgroundColor: colors.palette.fwdOrange[100] },
            innerAnimatedStyle,
          ]}
        />
      </AnimatedButtonOuter>
      <AnimatedTextContainer
        entering={FadeIn}
        exiting={FadeOut}
        key={isRecording ? 'recording' : 'notRecording'}>
        <Typography.Label
          style={{
            minWidth: sizes[25],
            textAlign: 'center',
          }}
          color={colors.palette.white}>
          {isRecording
            ? t('avatar.recording.submit')
            : t('avatar.recording.tapToRecord')}
        </Typography.Label>
      </AnimatedTextContainer>
    </Column>
  );
};

const BlackBackdrop = styled.View(({ theme: { colors } }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: colors.palette.black,
  opacity: 0.4,
}));
const AnimatedButtonOuter = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedButtonInner = Animated.createAnimatedComponent(View);
const AnimatedTextContainer = Animated.createAnimatedComponent(View);

export default RecordingButton;
