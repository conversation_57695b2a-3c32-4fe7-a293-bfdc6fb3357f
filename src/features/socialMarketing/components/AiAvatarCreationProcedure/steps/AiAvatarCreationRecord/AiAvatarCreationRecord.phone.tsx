import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon, Row, Typography, addErrorToast } from 'cube-ui-components';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import Animated, { FadeInUp, FadeOutUp } from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Camera,
  CameraCaptureError,
  VideoFile,
} from 'react-native-vision-camera';
import {
  CubeVisionCamera,
  CubeVisionCameraProps,
} from 'components/CubeVisionCamera';
import { useCountdown } from 'hooks/useCountdown';
import CounterProgressBar from './CounterProgressBar';
import RecordingButton from './RecordingButton';

const RECORDING_TRANSCRIPT_TEXT =
  'Hey there! I’m excited to use my AI-powered avatar for engaging videos. I am recording a short clip for <PERSON><PERSON> to train a hyper realistic avatar of me. \nWith an avatar that looks and sounds like me, I can easily create talking videos just by typing a script.';
const COUNT_DOWN_VALUE = 30;
const RECORDING_FILE_TYPE = 'mp4';
const RECORDING_VIDEO_CODEC = 'h265'; // fallback to h264 if not support by device

export type AiAvatarCreationRecordPhoneProps = {
  onBackPress?: () => void;
  onSubmit?: (videoUrl: string) => void;
};

export default function AiAvatarCreationRecordPhone({
  onBackPress,
  onSubmit,
}: AiAvatarCreationRecordPhoneProps) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('socialMarketing');
  const { count, startCountdown, stopCountdown, resetCountdown } = useCountdown(
    {
      countStart: 0,
      countStop: COUNT_DOWN_VALUE,
      isIncrement: true,
    },
  );

  const cameraRef = useRef<Camera>(null);
  const cameraProps: CubeVisionCameraProps = useMemo(
    () => ({
      cameraRef,
      cameraDevice: {
        cameraPosition: 'front',
      },
      cameraProps: {
        video: true,
        resizeMode: 'cover',
      },
    }),
    [],
  );
  const onRecordingFinished = useCallback(
    async (video: VideoFile) => {
      onSubmit?.(video.path);
    },
    [onSubmit],
  );
  const onRecordingError = (error: CameraCaptureError) => {
    addErrorToast([{ message: `Recording error` }]);
    console.error('Recording error:', error);
  };
  const startRecording = useCallback(() => {
    if (!cameraRef.current) return;

    try {
      cameraRef.current.startRecording({
        fileType: RECORDING_FILE_TYPE,
        videoCodec: RECORDING_VIDEO_CODEC,
        onRecordingFinished,
        onRecordingError,
      });
      console.log('Start recording...');
    } catch (error) {
      addErrorToast([{ message: `Failed to start recording` }]);
      console.error('Failed to start recording:', error);
    }
  }, [onRecordingFinished]);
  const stopRecording = useCallback(async (isCancel = false) => {
    if (!cameraRef.current) return;

    try {
      isCancel
        ? await cameraRef.current.cancelRecording()
        : await cameraRef.current.stopRecording();
      console.log('Stop recording...');
    } catch (error) {
      console.error('Failed to stop recording:', error);
    }
  }, []);

  const [isRecording, setIsRecording] = useState(false);
  const onPressRecordingButton = useCallback(async () => {
    if (!isRecording) {
      setIsRecording(true);
      startCountdown();
      scrollViewRef.current?.scrollTo({ y: 0 });
      startRecording();
    } else {
      setIsRecording(false);
      stopCountdown();
      await stopRecording();
    }
  }, [
    isRecording,
    startCountdown,
    startRecording,
    stopCountdown,
    stopRecording,
  ]);

  // const reset = useCallback(() => {
  //   resetCountdown();
  //   setIsRecording(false);
  //   scrollViewRef.current?.scrollTo({ y: 0 });
  // }, [resetCountdown]);

  const transcriptFontsize = 22;
  const scrollViewPaddingTop = space[32];
  const scrollViewPaddingBottom = space[48];
  const scrollViewRef = useRef<ScrollView>(null);
  const [scrollViewContentHeight, setScrollViewContentHeight] = useState(0);
  const [scrollViewHeight, setScrollViewHeight] = useState<number | undefined>(
    undefined,
  );
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  const isShowIntruction = count === 0 && !isRecording;

  useEffect(() => {
    // Counting behavior
    if (count === 0 || !scrollViewRef.current || isUserScrolling) return;

    scrollViewRef.current.scrollTo({
      y:
        (scrollViewContentHeight - scrollViewPaddingBottom) *
        (count / COUNT_DOWN_VALUE),
    });
  }, [
    count,
    isUserScrolling,
    scrollViewContentHeight,
    scrollViewPaddingBottom,
  ]);
  useEffect(() => {
    // Count down finished
    if (count === COUNT_DOWN_VALUE) {
      stopRecording();
      setIsRecording(false);
    }
  }, [count, stopRecording]);

  return (
    <StyledSafeAreaView>
      <StatusBar style="light" />
      <Row height={space[11]} alignItems={'center'} gap={space[3]}>
        <BackButtonWrapper onPress={onBackPress}>
          <Icon.ArrowLeft size={space[6]} fill={colors.palette.white} />
        </BackButtonWrapper>
        <View style={{ flex: 1 }}>
          <CounterProgressBar count={count} maxCount={COUNT_DOWN_VALUE} />
        </View>
        <Typography.Label
          style={{ marginRight: space[4], width: sizes[10] }}
          color="white">
          0:{(COUNT_DOWN_VALUE - count).toString().padStart(2, '0')}
        </Typography.Label>
      </Row>
      <View
        style={{
          flex: 1,
          marginTop: space[4],
          marginBottom: space[7],
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            width: '100%',
            alignItems: 'center',
          }}>
          <ScrollView
            ref={scrollViewRef}
            showsVerticalScrollIndicator={false}
            onContentSizeChange={(_, contentHeight) => {
              setScrollViewContentHeight(contentHeight);
            }}
            onScrollBeginDrag={() => setIsUserScrolling(true)}
            onScrollEndDrag={() => setIsUserScrolling(false)}
            onLayout={e => setScrollViewHeight(e.nativeEvent.layout.height)}
            style={{
              paddingHorizontal: space[6],
            }}>
            <Typography.ExtraLargeBody
              style={{
                textAlign: 'center',
                paddingTop: scrollViewPaddingTop,
                paddingBottom: scrollViewPaddingBottom,
                fontSize: transcriptFontsize, // Adjust font size for better readability based on new design
                lineHeight: transcriptFontsize * 2.1, // 210% of font size
              }}
              color={colors.palette.white}>
              {RECORDING_TRANSCRIPT_TEXT}
            </Typography.ExtraLargeBody>
          </ScrollView>
          <LinearGradient
            colors={[
              colors.palette.fwdDarkGreenTransparent[50],
              colors.palette.fwdDarkGreen[100],
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              height: scrollViewHeight ? scrollViewHeight * 0.15 : undefined,
            }}
            pointerEvents="none"
          />
          {isShowIntruction && (
            <Animated.View
              style={{
                position: 'absolute',
              }}
              entering={FadeInUp}
              exiting={FadeOutUp}>
              <InstructionContainer>
                <Typography.Body
                  color={colors.palette.fwdGrey[100]}
                  style={{ textAlign: 'center' }}>
                  {t('avatar.recording.instruction')}
                </Typography.Body>
              </InstructionContainer>
            </Animated.View>
          )}
        </View>

        <CameraContainer>
          <CubeVisionCamera {...cameraProps} />
        </CameraContainer>
      </View>
      <RecordingButton
        isRecording={isRecording}
        onPress={onPressRecordingButton}
      />
    </StyledSafeAreaView>
  );
}

const StyledSafeAreaView = styled(SafeAreaView)(({ theme: { colors } }) => ({
  position: 'absolute',
  top: 0,
  right: 0,
  bottom: 0,
  left: 0,
  backgroundColor: colors.palette.fwdDarkGreen[100],
}));
const CameraContainer = styled.View(({ theme: { sizes, colors } }) => ({
  width: '33%',
  aspectRatio: 9 / 16,
  overflow: 'hidden',
  borderRadius: sizes[4],
  backgroundColor: colors.palette.fwdGreyDarkest,
}));

const InstructionContainer = styled.View(
  ({ theme: { space, borderRadius } }) => ({
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: space[2],
    paddingVertical: space[1],
    borderRadius: borderRadius.small,
    alignItems: 'center',
    gap: space[2],
  }),
);

const BackButtonWrapper = styled(TouchableOpacity)(() => ({
  padding: 10,
  paddingLeft: 13,
}));
