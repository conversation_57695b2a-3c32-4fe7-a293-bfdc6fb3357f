import React, { useMemo, useRef } from 'react';
import { TouchableOpacity, View } from 'react-native';

import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import AiBackground from 'components/AiBackground';
import {
  CubeVisionCamera,
  CubeVisionCameraProps,
} from 'components/CubeVisionCamera';
import Header from 'components/Header';
import { Button, Icon, Typography } from 'cube-ui-components';
import { useRootStackNavigation } from 'hooks/useRootStack';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Camera } from 'react-native-vision-camera';

const Container = styled(SafeAreaView)(({ theme: { space } }) => ({
  flex: 1,
  paddingHorizontal: space[4],
}));
const CameraVisionContainer = styled.View(({ theme: { space, colors } }) => ({
  flex: 1,
  aspectRatio: 9 / 16, // 16:9 aspect ratio for camera preview
  marginBottom: space[4],
  marginTop: space[11],
  borderRadius: space[4],
  overflow: 'hidden',
  alignSelf: 'center',
  backgroundColor: colors.palette.fwdGreyDarkest,
}));
const ContentContainer = styled.View(({ theme: { space } }) => ({
  flex: 1,
  paddingVertical: space[4],
}));
const LineItemContainer = styled.View(({ theme: { space } }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  flexShrink: 1,
  gap: space[3],
  paddingVertical: space[3],
}));
const FooterWrapper = styled.View(({ theme: { colors, space } }) => ({
  marginVertical: space[4],
  backgroundColor: colors.palette.fwdOrange[20],
  borderTopLeftRadius: space[1],
  borderTopRightRadius: space[1],
}));
const BannerWrapper = styled.View(({ theme: { colors, space } }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  paddingVertical: space[2],
  gap: space[2],
}));

const LineItemIcon = (props: { label: string }) => {
  const { colors, space } = useTheme();

  return (
    <View
      style={{
        width: space[8],
        height: space[8],
        borderRadius: space[4],
        backgroundColor: colors.palette.fwdOrange[100],
        alignItems: 'center',
        justifyContent: 'center',
      }}>
      <Typography.LargeBody color={colors.palette.white} fontWeight="bold">
        {props.label}
      </Typography.LargeBody>
    </View>
  );
};

export type AiAvatarCreationIntroductionPhoneProps = {
  onNextPress: () => void;
};

export default function AiAvatarCreationIntroductionPhone({
  onNextPress,
}: AiAvatarCreationIntroductionPhoneProps) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('socialMarketing');
  const navigation = useRootStackNavigation();
  const cameraRef = useRef<Camera>(null);

  const cameraProps: CubeVisionCameraProps = useMemo(
    () => ({
      cameraRef,
      cameraDevice: {
        cameraPosition: 'front',
      },
    }),
    [],
  );

  const handleGoBackPress = () => {
    navigation.goBack();
  };

  const handleRecordVideoPress = () => {
    onNextPress();
  };

  return (
    <AiBackground>
      <Container edges={['bottom']}>
        <Header
          headerTitleAlign="center"
          headerTitle={({ children }) => (
            <Typography.LargeLabel
              fontWeight="bold"
              color={colors.onBackground}>
              {children}
            </Typography.LargeLabel>
          )}
          headerTitleStyle={{
            justifyContent: 'center',
            alignItems: 'center',
          }}
          title={t('createAiAvatar.header.title')}
          headerLeft={() => (
            <TouchableOpacity onPress={handleGoBackPress}>
              <Icon.ArrowLeft size={22} fill={colors.palette.black} />
            </TouchableOpacity>
          )}
          headerLeftContainerStyle={{
            justifyContent: 'center',
          }}
          headerStyle={{
            backgroundColor: colors.palette.whiteTransparent,
            borderBottomWidth: 0,
          }}
        />

        <CameraVisionContainer>
          <CubeVisionCamera {...cameraProps} />
        </CameraVisionContainer>

        <ContentContainer>
          <Typography.H5
            fontWeight="bold"
            style={{
              textAlign: 'center',
              marginBottom: space[4],
              paddingHorizontal: space[4],
            }}>
            {t('createAiAvatar.content.title')}
          </Typography.H5>

          <LineItemContainer>
            <LineItemIcon label="1" />
            <Typography.LargeBody
              color={colors.palette.fwdGreyDarkest}
              style={{
                flexShrink: 1,
              }}>
              {t('createAiAvatar.lineItem.findQuiteSpot')}
            </Typography.LargeBody>
          </LineItemContainer>

          <LineItemContainer>
            <LineItemIcon label="2" />
            <Typography.LargeBody
              color={colors.palette.fwdGreyDarkest}
              style={{
                flexShrink: 1,
              }}>
              {t('createAiAvatar.lineItem.placeYourself')}
            </Typography.LargeBody>
          </LineItemContainer>

          <LineItemContainer>
            <LineItemIcon label="3" />
            <Typography.LargeBody
              color={colors.palette.fwdGreyDarkest}
              style={{
                flexShrink: 1,
              }}>
              {t('createAiAvatar.lineItem.keepSmiling')}
            </Typography.LargeBody>
          </LineItemContainer>
        </ContentContainer>

        <FooterWrapper>
          <BannerWrapper>
            <Icon.Coin size={sizes[5]} />
            <Typography.Body>
              {t('createPost.avatarRecord.banner.limitationNote')}
            </Typography.Body>
          </BannerWrapper>
          <Button
            variant="primary"
            size="medium"
            onPress={handleRecordVideoPress}
            text={t('createAiAvatar.cta.recordVideo')}
          />
        </FooterWrapper>
      </Container>
    </AiBackground>
  );
}
