import React, { useCallback, useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import AiBackground from 'components/AiBackground';
import CircleGradientSpinner from 'components/CircleSpinnerGradient';
import { Button, Column, Icon, Row, Typography } from 'cube-ui-components';
import { useEvent } from 'expo';
import { VideoView, useVideoPlayer } from 'expo-video';
import { useRootStackNavigation } from 'hooks/useRootStack';
import LottieView from 'lottie-react-native';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';
import StatusHeader from './StatusHeader.phone';
import { PauseButton } from './styled';
import { AiAvatarCreationPreviewStatus } from './types';

const Container = styled(SafeAreaView)(({ theme: { space } }) => ({
  flex: 1,
  paddingHorizontal: space[4],
  paddingTop: space[12],
  paddingBottom: space[4],
}));

export type AiAvatarCreationPreviewPhoneProps = {
  videoUrl: string;
  onRecordAgain: () => void;
};

export default function AiAvatarCreationPreviewPhone({
  videoUrl,
  onRecordAgain,
}: AiAvatarCreationPreviewPhoneProps) {
  const navigation = useRootStackNavigation();
  const { colors, space, sizes, borderRadius } = useTheme();
  const { t } = useTranslation('socialMarketing');

  const player = useVideoPlayer(videoUrl, player => {
    player.loop = true;
    player.volume = 1.0;
    player.muted = false;
    player.play();
  });

  const { isPlaying } = useEvent(player, 'playingChange', {
    isPlaying: player.playing,
  });

  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<AiAvatarCreationPreviewStatus>(
    AiAvatarCreationPreviewStatus.Preview,
  );

  // Simulate progress animation and auto-transition to finished
  useEffect(() => {
    if (progress < 100 && status === AiAvatarCreationPreviewStatus.Processing) {
      const timer = setTimeout(
        () =>
          setProgress(prev => {
            const newProgress = Math.min(prev + 1, 100);
            // Auto-transition to finished when progress reaches 100
            if (newProgress >= 100) {
              setStatus(AiAvatarCreationPreviewStatus.Finished);
            }
            return newProgress;
          }),
        50,
      );
      return () => clearTimeout(timer);
    }
  }, [progress, status]);

  // Update player settings based on status
  useEffect(() => {
    if (status === AiAvatarCreationPreviewStatus.Preview) {
      player.muted = false;
      player.volume = 1.0;
      player.play();
    } else {
      player.muted = true;
      player.volume = 0;
      player.pause();
    }
  }, [status, player]);

  const togglePause = useCallback(() => {
    if (isPlaying) {
      player.pause();
    } else {
      player.play();
    }
  }, [isPlaying, player]);

  const handleUseVideo = useCallback(() => {
    setStatus(AiAvatarCreationPreviewStatus.Processing);
    setProgress(0); // Reset progress when starting
  }, []);

  if (status === AiAvatarCreationPreviewStatus.Preview) {
    return (
      <AiBackground>
        <Container edges={['top', 'bottom']}>
          <StatusHeader status={status} progress={progress} />

          <Column
            flex={1}
            paddingTop={space[2]}
            paddingX={space[14]}
            gap={space[6]}
            paddingBottom={space[4]}>
            <View style={{ flex: 1, position: 'relative' }}>
              <VideoView
                style={{
                  flex: 1,
                  width: '100%',
                  overflow: 'hidden',
                  borderRadius: borderRadius.large,
                }}
                player={player}
                nativeControls={false}
                contentFit="cover"
                allowsFullscreen={false}
                allowsPictureInPicture={true}
                allowsVideoFrameAnalysis={false}
              />
              <PauseButton onPress={togglePause}>
                {isPlaying ? <Icon.Pause /> : <Icon.Play />}
              </PauseButton>
            </View>

            <TouchableOpacity onPress={onRecordAgain}>
              <Row
                gap={space[1]}
                paddingTop={space[6]}
                alignItems="center"
                justifyContent="center">
                <Icon.Refresh />
                <Typography.H7
                  color={colors.palette.fwdOrange[100]}
                  fontWeight="bold">
                  {t('videoEditing.recordAgain')}
                </Typography.H7>
              </Row>
            </TouchableOpacity>
          </Column>

          <Column paddingX={space[4]} mt={space[4]}>
            <Button
              style={{ width: '100%' }}
              text={t('videoEditing.useVideo')}
              onPress={handleUseVideo}
            />
          </Column>
        </Container>
      </AiBackground>
    );
  }

  return (
    <AiBackground>
      <Container edges={['top', 'bottom']}>
        {/* Show confetti only when finished */}
        {status === AiAvatarCreationPreviewStatus.Finished && (
          <LottieView
            source={require('assets/lottie/confetti.json')}
            autoPlay
            loop
            style={StyleSheet.absoluteFill}
          />
        )}

        <Column
          flex={1}
          justifyContent="center"
          alignItems="center"
          paddingX={space[3]}
          gap={space[6]}
          paddingBottom={space[4]}>
          <Column
            justifyContent="center"
            alignItems="center"
            gap={space[7]}
            paddingX={space[4]}>
            <View style={{ position: 'relative' }}>
              <VideoView
                style={{
                  position: 'absolute',
                  alignSelf: 'center',
                  width: sizes[45],
                  height: sizes[45],
                  top: '13%', // To center it vertically of the spinner
                  borderRadius: borderRadius.full,
                }}
                player={player}
                nativeControls={false}
                contentFit="cover"
                allowsFullscreen={false}
                allowsPictureInPicture={true}
                allowsVideoFrameAnalysis={false}
              />
              <CircleGradientSpinner
                backgroundColor={colors.palette.fwdOrange[20]}
                gradientColors={[
                  colors.palette.fwdOrange[100],
                  colors.palette.fwdYellow[100],
                  colors.palette.fwdLightGreen[100],
                  colors.palette.fwdOrange[100],
                ]}
                size={sizes[55]}
                strokeWidth={sizes[3]}
                duration={2500}
                arcSize={0.7}
              />
            </View>

            <StatusHeader status={status} progress={progress} />
          </Column>
          <Column paddingTop={space[6]} paddingX={space[6]} gap={space[2]}>
            <Button
              text={t(
                status === AiAvatarCreationPreviewStatus.Processing
                  ? 'videoEditing.gotoMyPost'
                  : 'videoEditing.seeThePost',
              )}
              variant="primary"
              contentStyle={{
                width: '100%',
              }}
              onPress={() => navigation.navigate('SocialMarketingMyPosts')}
            />
          </Column>
        </Column>
      </Container>
    </AiBackground>
  );
}
