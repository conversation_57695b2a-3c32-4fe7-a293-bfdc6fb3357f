import { useTheme } from '@emotion/react';
import MarkdownText, { useMarkdownStyle } from 'components/MarkdownText';
import { Column, Typography } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { AiAvatarCreationPreviewStatus } from './types';

interface StatusHeaderProps {
  status: AiAvatarCreationPreviewStatus;
  progress: number;
}

export default function StatusHeader({ status, progress }: StatusHeaderProps) {
  const { colors, space, typography } = useTheme();
  const mdStyle = useMarkdownStyle();
  const { t } = useTranslation('socialMarketing');

  const getHeaderContent = () => {
    switch (status) {
      case AiAvatarCreationPreviewStatus.Finished:
        return {
          title: t('videoEditing.avatarIsReady'),
          description: t('videoEditing.tapBelow'),
        };
      case AiAvatarCreationPreviewStatus.Processing:
        return {
          title: t('videoEditing.processAvatar', { percent: progress }),
          description: t('videoEditing.processAvatarDescription'),
        };
      case AiAvatarCreationPreviewStatus.Preview:
      default:
        return {
          title: t('videoEditing.previewYourVideo'),
          description: t('videoEditing.previewYourVideoDescription'),
        };
    }
  };

  const { title, description } = getHeaderContent();

  return (
    <Column alignItems="center" justifyContent="center" gap={space[1]}>
      <Typography.H5
        fontWeight="bold"
        color={colors.palette.fwdDarkGreen[100]}
        style={{ textAlign: 'center' }}>
        {title}
      </Typography.H5>

      <MarkdownText
        style={{
          body: {
            ...mdStyle.body,
            textAlign: 'center',
            fontSize: typography.largeBody.size,
            lineHeight: typography.largeBody.lineHeight,
          },
          strong: {
            ...mdStyle.strong,
            fontSize: typography.h7.size,
            lineHeight: typography.h7.lineHeight,
          },
        }}>
        {description}
      </MarkdownText>
    </Column>
  );
}
