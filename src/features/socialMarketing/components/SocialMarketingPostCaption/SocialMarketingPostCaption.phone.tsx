import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { postToFacebook } from 'api/socialMedialApi';
import CheckmarkedBottomSheetList from 'components/CheckmarkedBottomSheetList';
import { CheckmarkedBottomSheetListOption } from 'components/CheckmarkedBottomSheetList/CheckmarkedBottomSheetList';
import Skeleton from 'components/Skeleton';
import {
  addToast,
  Button,
  Card,
  Column,
  Icon,
  Row,
  Text,
  Toast,
  Typography,
} from 'cube-ui-components';
import CubeFonts from 'cube-ui-components/dist/cjs/fonts';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import SocialEditPostCaptionSVG from 'features/socialMarketing/assets/SocialEditPostCaptionSVG';
import SocialMarketingPostCaptionContent from 'features/socialMarketing/components/SocialMarketingPostCaptionContent';
import SocialMarketingUniqueLink from 'features/socialMarketing/components/SocialMarketingPostCaptionContent/SocialMarketingUniqueLink';
import {
  useGeneratePostCaption,
  useSocialMarketingPost,
  useSocialMarketingPostEditState,
  useUpdatePostCaption,
} from 'features/socialMarketing/hooks/usePosts';
import { useSocialMediaPlatform } from 'features/socialMarketing/hooks/useSocialMedialPlatform';
import { useSocialMediaToken } from 'features/socialMarketing/hooks/useSocialMediaToken';
import {
  SocialConnectActionType,
  SocialMarketingPlatform,
  SocialMarketingPostType,
} from 'features/socialMarketing/types';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ScrollView,
  StyleProp,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { SocialMarketingParamList } from 'types';
import { countryModuleProfileConfig } from 'utils/config/module';
import GATracking from 'utils/helper/gaTracking';
import { CommonKey, SocialMarketingKey } from 'utils/translation/i18next';
import SocialConnectionModal from '../SocialConnection/SocialConnectionModal';

const SNAP_POINTS = ['60%'];

import MediaPostCaption, { MEDIA_HEIGHT } from '../MediaPostCaption';

export default function SocialMarketingPostCaptionPhone() {
  const { name: screenName } = useRoute();
  const { t } = useTranslation(['socialMarketing', 'common']);
  const { verifySocialMediaToken } = useSocialMediaToken();
  const navigation = useNavigation<NavigationProp<SocialMarketingParamList>>();

  const { platforms } = useSocialMediaPlatform();
  const { colors, space, borderRadius, sizes } = useTheme();

  const [isSocialConnectionModalOpen, setIsSocialConnectionModalOpen] =
    useState(false);
  const [isFocus, setIsFocus] = useState<boolean>(false);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const { post } = useSocialMarketingPost();
  const { isEditMode, setEditing, setEditMode } =
    useSocialMarketingPostEditState();
  const updatePostCaption = useUpdatePostCaption();
  const generatePostCaption = useGeneratePostCaption();
  const [tempText, setTempText] = useState<string>(post?.caption || '');
  const [showSkeleton, setShowSkeleton] = useState<boolean>(false);
  const [language, setLanguage] = useState<string>(
    countryModuleProfileConfig.defaultLanguage,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isPosting, setIsPosting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const inputRef = useRef<TextInput>(null);
  const { bottom } = useSafeAreaInsets();

  const selectedPlatformIcon = useMemo(
    () => platforms.find(item => item.key === post?.platform)?.icon,
    [post],
  );

  const closeBottomSheetModal = () => {
    setIsSocialConnectionModalOpen(false);
  };

  useEffect(() => {
    if (!post) return;
    const isEdited = tempText !== post?.caption;
    setEditing(isEdited);
  }, [tempText, post?.caption]);

  useEffect(() => {
    if (!isEditMode) {
      setTempText(post?.caption || '');
      setErrorMsg(null);
    }
  }, [isEditMode, post]);

  const textComp = useMemo(() => {
    const tokens = tempText.split(/(\s+)/);

    return tokens.map((token, index) => {
      const color = token.startsWith('#')
        ? colors.palette.fwdBlue[100]
        : colors.palette.fwdDarkGreen[100];

      return (
        <Text key={index} color={color}>
          {token}
        </Text>
      );
    });
  }, [tempText, colors]);

  useEffect(() => {
    !isLoading && setTempText(post?.caption || '');
  }, [post, isLoading]);

  useEffect(() => {
    if (isLoading) {
      setShowSkeleton(true);
    } else {
      const timer = setTimeout(() => {
        setShowSkeleton(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [isLoading]);

  const languageOptions: CheckmarkedBottomSheetListOption[] = useMemo(
    () =>
      countryModuleProfileConfig.profileLanguages.map(option => ({
        value: option,
        label: t(`socialMarketing:postCaption.language.${option}`),
      })),
    [t, countryModuleProfileConfig.profileLanguages],
  );

  const handleSave = async () => {
    setIsSaving(true);
    setErrorMsg(null);
    updatePostCaption(post?.id || '', { caption: tempText })
      .then(() => {
        setEditMode(false);
        setEditing(false);
        Toast.show(
          [
            {
              message: t('socialMarketing:postCaption.update.success'),
              IconLeft: <Icon.Tick />,
            },
          ],
          {
            type: 'success',
            duration: Toast.durations.SHORT,
          },
        );
      })
      .catch(err => setErrorMsg(err.message))
      .finally(() => setIsSaving(false));
  };

  const handleGeneratePostCaption = async () => {
    onTrackingButtonClicked(`Refresh button`);
    setIsLoading(true);
    setErrorMsg(null);
    await generatePostCaption(post?.id || '', {
      language,
    });
    setIsLoading(false);
  };

  const handleSocialAuthenticationSuccess = async () => {
    if (post?.platform === SocialMarketingPlatform.Facebook) {
      await postToFacebook(post?.id as string);
    } else if (post?.platform === SocialMarketingPlatform.Linkedin) {
      // await postToLinkedIn(post?.id as string);
    }

    navigation.navigate('SocialMarketingTemplates');
    addToast([
      {
        message: 'Post Successfully',
        IconLeft: <Icon.Tick />,
      },
    ]);
  };

  const handlePost = async () => {
    setIsPosting(true);
    try {
      const isValidToken = await verifySocialMediaToken(post?.platform);
      if (isValidToken) {
        await handleSocialAuthenticationSuccess();
        return;
      }
      setIsSocialConnectionModalOpen(true);
    } catch (err) {
      console.warn(err);
    } finally {
      setIsPosting(false);
    }
  };

  useEffect(() => {
    handleGeneratePostCaption();
  }, [post?.id]);

  const onTrackingButtonClicked = useCallback(
    async (buttonName: string) => {
      await GATracking.logButtonPress({
        screenName,
        screenClass: 'Social Marketing',
        actionType: 'non_cta_button',
        buttonName,
      });
    },
    [screenName],
  );

  // Default value exists, so listen to value instead of event
  useEffect(() => {
    onTrackingButtonClicked(`Language button: ${language}`);
  }, [language, onTrackingButtonClicked]);

  return (
    <>
      <SafeAreaView
        edges={['bottom']}
        style={{ flex: 1, backgroundColor: colors.background }}>
        {isEditMode ? (
          <KeyboardAwareScrollView
            bottomOffset={space[35]}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{
              flexGrow: 1,
            }}>
            <EditCardContainer>
              <Typography.SmallLabel
                fontWeight="medium"
                color={colors.palette.fwdOrange[100]}
                style={{ marginBottom: space[2], marginLeft: space[1] }}>
                {t('socialMarketing:create.cta.postCaption.title')}
              </Typography.SmallLabel>
              <Card
                variant={errorMsg ? 'gradient' : undefined}
                {...(errorMsg
                  ? {
                      colors: [colors.palette.alertRed],
                      gradientBorder: true,
                      opacity: 0.4,
                    }
                  : {})}>
                <InnerEditContainer
                  style={errorMsg && { borderColor: colors.palette.alertRed }}>
                  {showSkeleton ? (
                    <SkeletonInput rows={4} style={{ paddingHorizontal: 0 }} />
                  ) : (
                    <>
                      <View>
                        <TextContentInput
                          ref={inputRef}
                          multiline
                          value={tempText}
                          style={{ color: colors.palette.whiteTransparent }}
                          onBlur={() => setIsFocus(false)}
                          onFocus={() => setIsFocus(true)}
                          onChangeText={text => {
                            if (text.length <= MAX_CHAR_COUNT) {
                              setTempText(text);
                            }
                          }}
                        />
                        <View
                          pointerEvents="none"
                          style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                          }}>
                          <TextContentInput multiline editable={false}>
                            {textComp}
                          </TextContentInput>
                        </View>
                      </View>
                      <SocialMarketingUniqueLink
                        onPress={() => {}}
                        opacity={0.5}
                      />
                      <Row
                        flexDirection="row"
                        alignItems="center"
                        justifyContent="space-between"
                        paddingBottom={space[4]}>
                        <LinearGradient
                          colors={['#E87722', '#E87722', '#FED141', '#6ECEB2']}
                          start={{ x: 0.5, y: 0 }}
                          end={{ x: 0.5, y: 1 }}
                          style={{
                            borderRadius: borderRadius.full,
                            padding: 2,
                          }}>
                          <Button
                            text={t('socialMarketing:postCaption.refresh')}
                            variant="text"
                            size="small"
                            icon={<Icon.AIStar />}
                            rounded
                            iconAfter
                            onPress={handleGeneratePostCaption}
                            style={{
                              borderRadius: borderRadius.full,
                              backgroundColor: colors.palette.white,
                            }}
                            contentStyle={{
                              display: 'flex',
                              flexDirection: 'row-reverse',
                              paddingHorizontal: space[2],
                            }}
                          />
                        </LinearGradient>
                        <CheckmarkedBottomSheetList
                          title={t(
                            'socialMarketing:postCaption.chooseYourLanguage',
                          )}
                          onSelect={setLanguage}
                          onPress={() => {
                            setIsFocus(false);
                            inputRef.current?.blur();
                          }}
                          selectedValue={language}
                          options={languageOptions}
                          snapPoints={['40%']}
                          renderSelectedOption={key => (
                            <Row alignItems="center" gap={2}>
                              <Icon.Language size={space[4]} />
                              <Typography.Label
                                fontWeight="bold"
                                color={colors.palette.fwdOrange[100]}>
                                {t(
                                  `socialMarketing:postCaption.language.short.${key}` as SocialMarketingKey &
                                    CommonKey,
                                )}
                              </Typography.Label>
                            </Row>
                          )}
                        />
                      </Row>
                    </>
                  )}
                </InnerEditContainer>
              </Card>
              <Typography.SmallLabel
                style={{
                  textAlign: 'right',
                  marginTop: space[2],
                  color: colors.palette.fwdGreyDarkest,
                }}>
                {`${tempText.length}/${MAX_CHAR_COUNT}`}
              </Typography.SmallLabel>
            </EditCardContainer>
          </KeyboardAwareScrollView>
        ) : (
          <View style={{ flex: 1, backgroundColor: colors.background }}>
            <MediaPostCaption
              type={post?.mediaType || SocialMarketingPostType.Image}
              thumbnail={post?.localUrl || post?.mediaUrl || ''}
            />
            <ScrollView
              bounces={false}
              showsVerticalScrollIndicator={false}
              style={{ flex: 1 }}>
              <BlurViewContainer intensity={17}>
                <PostContainer>
                  <Typography.H6
                    fontWeight="bold"
                    style={{ marginBottom: space[1] }}>
                    {t('socialMarketing:create.cta.postCaption.title')}
                  </Typography.H6>
                  {showSkeleton ? (
                    <SkeletonInput rows={4} />
                  ) : (
                    <>
                      <SocialMarketingPostCaptionContent
                        content={post?.caption || ''}
                      />
                      <Divider />
                      <TouchableOpacity
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          gap: space[1],
                        }}
                        onPress={() => {
                          setEditMode(true);
                          onTrackingButtonClicked(`Edit button`);
                          setTimeout(() => {
                            inputRef.current?.focus();
                            setIsFocus(true);
                          }, 100);
                        }}>
                        <SocialEditPostCaptionSVG />
                        <Typography.Label
                          fontWeight="bold"
                          color={colors.palette.fwdOrange[100]}>
                          {t('socialMarketing:postCaption.editCaption')}
                        </Typography.Label>
                      </TouchableOpacity>
                    </>
                  )}
                </PostContainer>
              </BlurViewContainer>
            </ScrollView>
            <View
              style={{
                backgroundColor: colors.background,
                paddingBottom: space[4],
              }}>
              <Button
                loading={isPosting}
                text={`${t('socialMarketing:createPost.cta.postNow')} `}
                icon={selectedPlatformIcon}
                disabled={showSkeleton}
                iconAfter
                style={{
                  width: '100%',
                  height: space[11],
                  alignSelf: 'center',
                  paddingHorizontal: space[4],
                }}
                contentStyle={{
                  display: 'flex',
                  flexDirection: 'row-reverse',
                  gap: space[1],
                }}
                onPress={handlePost}
              />
            </View>
          </View>
        )}
        <KeyboardStickyView
          offset={{ closed: bottom, opened: bottom }}
          style={{ backgroundColor: colors.background }}>
          {isEditMode && (
            <Column
              margin={space[4]}
              style={{ ...(!isFocus && { marginBottom: space[12] }) }}>
              {errorMsg && (
                <Row
                  alignItems="flex-start"
                  alignSelf="stretch"
                  gap={space[1]}
                  paddingBottom={space[3]}>
                  <Icon.WarningFill
                    size={sizes[4]}
                    fill={colors.palette.alertRed}
                  />
                  <Typography.Label
                    style={{ flex: 1 }}
                    color={colors.palette.alertRed}>
                    {errorMsg}
                  </Typography.Label>
                </Row>
              )}
              <Button
                text={t('common:save')}
                loading={isSaving}
                style={{ width: '100%', height: space[11] }}
                disabled={!tempText || tempText.length > MAX_CHAR_COUNT}
                onPress={handleSave}
              />
            </Column>
          )}
        </KeyboardStickyView>
      </SafeAreaView>

      <SocialConnectionModal
        open={isSocialConnectionModalOpen}
        platform={post?.platform}
        onClose={closeBottomSheetModal}
        onAuthSuccess={handleSocialAuthenticationSuccess}
        type={SocialConnectActionType.post}
      />
    </>
  );
}

const MAX_CHAR_COUNT = 1000;

const BlurViewContainer = styled(BlurView)(() => ({
  backgroundColor: 'rgba(255, 255, 255, 0.8)',
  marginTop: MEDIA_HEIGHT,
}));

const PostContainer = styled(View)(({ theme }) => ({
  paddingVertical: theme.space[8],
  paddingHorizontal: theme.space[4],
  display: 'flex',
  flexDirection: 'column',
}));

const TextContentInput = styled(TextInput)(({ theme }) => ({
  fontSize: theme.typography.largeBody.size,
  lineHeight: theme.typography.largeBody.lineHeight,
  color: theme.colors.palette.fwdDarkGreen[100],
  borderRadius: theme.space[4],
  fontFamily: CubeFonts.FWDCircularTT.Book,
}));

const InnerEditContainer = styled(Card)(({ theme }) => ({
  padding: theme.space[4],
  borderRadius: theme.borderRadius.large,
  borderWidth: 1,
  borderStyle: 'solid',
  borderColor: theme.colors.palette.fwdOrange[100],
  display: 'flex',
  flexDirection: 'column',
  gap: theme.space[6],
}));

const EditCardContainer = styled(Card)(({ theme }) => ({
  padding: theme.space[4],
}));

const Divider = styled(View)(({ theme }) => ({
  height: 1,
  width: '100%',
  backgroundColor: theme.colors.palette.fwdGrey[100],
  marginVertical: theme.space[4],
}));

const SkeletonInput = ({
  rows,
  style,
}: {
  rows: number;
  style?: StyleProp<ViewStyle>;
}) => {
  const { space, colors, sizes } = useTheme();
  return (
    <View
      style={[
        {
          display: 'flex',
          flexDirection: 'column',
          padding: space[4],
        },
        style,
      ]}>
      {[...Array(rows)].map((_, index) => (
        <Skeleton
          width={index === rows - 1 ? '50%' : '100%'}
          height={sizes[5]}
          backgroundColor={colors.palette.fwdGrey[100]}
          radius={space[1]}
          containerStyle={{
            marginTop: index === 0 ? 0 : space[4],
          }}
        />
      ))}
    </View>
  );
};
