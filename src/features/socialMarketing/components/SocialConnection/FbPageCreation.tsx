import React from 'react';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { Button, Icon, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { Linking, TouchableOpacity } from 'react-native';
import FacebookCreatePageSVG from 'features/agentProfile/assets/FacebookCreatePageSVG';

type FbPageCreationProps = {
  onClose: () => void;
  selectPlatformButtonText: string;
  selectPlatformButtonHandler: () => void;
};

export default function FbPageCreation({
  onClose,
  selectPlatformButtonHandler,
  selectPlatformButtonText,
}: FbPageCreationProps) {
  const { t } = useTranslation('socialMarketing');
  const { colors, typography } = useTheme();

  const handleCreatePage = async () => {
    const url = 'https://facebook.com/pages';
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      Linking.openURL(url);
      onClose();
    } else {
      console.warn("Can't open url", url);
    }
  };

  return (
    <ViewContainer>
      <ContentContainer>
        <TouchableOpacity onPress={onClose}>
          <Icon.Close fill={colors.secondary} />
        </TouchableOpacity>
        <TitleContainer>
          <FacebookCreatePageSVG />
          <TextTitle fontWeight="bold">
            {t('socialMedia.dontHaveFacebookPage')}
          </TextTitle>
          <TextDescription>
            {t('socialMedia.setUpPageNotification')}
          </TextDescription>
        </TitleContainer>
      </ContentContainer>

      <ButtonsContainer>
        <Button
          variant="primary"
          text={t('socialMedia.createFacebookPage')}
          onPress={handleCreatePage}
        />
        <Button
          variant="text"
          text={selectPlatformButtonText}
          size="small"
          textStyle={{ fontSize: typography.h7.size }}
          onPress={selectPlatformButtonHandler}
        />
      </ButtonsContainer>
    </ViewContainer>
  );
}

const ViewContainer = styled.View(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  padding: theme.space[4],
  paddingBottom: theme.space[10],
  height: '100%',
}));

const ContentContainer = styled.View(() => ({
  display: 'flex',
}));

const TitleContainer = styled.View(() => ({
  alignItems: 'center',
}));

const TextTitle = styled(Typography.H6)(({ theme }) => ({
  color: theme.colors.secondary,
  textAlign: 'center',
  marginBottom: theme.space[1],
  marginTop: theme.space[3],
}));

const TextDescription = styled(Typography.H7)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  textAlign: 'center',
}));

const ButtonsContainer = styled.View(({ theme }) => ({
  gap: theme.space[4],
}));
