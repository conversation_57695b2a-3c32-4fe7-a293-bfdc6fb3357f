import React, { useState } from 'react';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Icon,
  RadioButton,
  RadioButtonGroup,
  Typography,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { TouchableOpacity, View } from 'react-native';
import { useSocialMediaStore } from 'features/socialMarketing/store/socialMediaStore';
import { connectFacebookPage } from 'api/socialMedialApi';
import { FacebookPage } from 'features/socialMarketing/types';

type FbPageSelectionProps = {
  pages: FacebookPage[];
  onClose: () => void;
  onPageSelected?: () => Promise<void>;
};

export default function FbPageSelection({
  pages,
  onClose,
  onPageSelected,
}: FbPageSelectionProps) {
  const { t } = useTranslation('socialMarketing');
  const { colors } = useTheme();
  const [selectedPage, setSelectedPage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const setIsFacebookLinked = useSocialMediaStore(
    state => state.setIsFacebookLinked,
  );
  const handleSelectPage = (pageId: string) => {
    setSelectedPage(pageId);
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    await connectFacebookPage(selectedPage);
    setIsFacebookLinked(true);
    await onPageSelected?.();
    setIsLoading(false);
    onClose();
  };

  return (
    <ViewContainer>
      <ContentContainer>
        <TitleContainer>
          <TouchableOpacity onPress={onClose}>
            <Icon.Close fill={colors.secondary} />
          </TouchableOpacity>
          <TextTitle fontWeight="bold">
            {t('socialMedia.createFacebookPage')}
          </TextTitle>
        </TitleContainer>
        <RadioButtonGroup value={selectedPage} onChange={handleSelectPage}>
          {pages.map((page, index) => (
            <View key={page.id}>
              <PageItem onPress={() => handleSelectPage(page.id)}>
                <RadioButton value={page.id} />
                <PageContent>
                  <PageImage src={page.pictureUrl} alt={page.name} />
                  <PageName>{page.name}</PageName>
                </PageContent>
              </PageItem>
              {index < pages.length - 1 && <Divider />}
            </View>
          ))}
        </RadioButtonGroup>
      </ContentContainer>

      <Button
        loading={isLoading}
        variant="primary"
        text={t('socialMedia.publishToThisPage')}
        onPress={handleSubmit}
      />
    </ViewContainer>
  );
}

const ViewContainer = styled.View(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  padding: theme.space[4],
  paddingBottom: theme.space[10],
  height: '100%',
}));

const ContentContainer = styled.View(() => ({
  display: 'flex',
}));

const TitleContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  marginBottom: theme.space[3],
}));

const TextTitle = styled(Typography.H6)(({ theme }) => ({
  marginLeft: theme.space[3],
  color: theme.colors.secondary,
}));

const PageItem = styled.TouchableOpacity(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  paddingRight: theme.space[2],
  paddingVertical: theme.space[3],
}));

const PageContent = styled.View(({ theme }) => ({
  gap: theme.space[3],
  flexDirection: 'row',
  alignItems: 'center',
}));

const PageImage = styled.Image(({ theme }) => ({
  width: theme.sizes[12],
  height: theme.sizes[12],
  borderRadius: theme.sizes[6],
}));

const PageName = styled(Typography.H7)(({ theme }) => ({
  color: theme.colors.secondary,
  flex: 1,
}));

const Divider = styled(View)(({ theme }) => ({
  height: 1,
  width: '100%',
  backgroundColor: theme.colors.palette.fwdGrey[100],
  marginVertical: theme.space[2],
}));
