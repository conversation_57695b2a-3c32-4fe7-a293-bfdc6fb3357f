import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { BottomSheetBackdrop, BottomSheetModal } from '@gorhom/bottom-sheet';
import { BottomSheetDefaultBackdropProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop/types';
import * as socialMedia<PERSON>pi from 'api/socialMedialApi';
import { Button, Icon, Typography } from 'cube-ui-components';
import { useFacebookLogin } from 'features/socialMarketing/hooks/useFacebookLogin';
import { useLinkedInConnection } from 'features/socialMarketing/hooks/useLinkedInConnection';
import { useSocialMediaStore } from 'features/socialMarketing/store/socialMediaStore';
import {
  FacebookPage,
  SocialConnectActionType,
  SocialConnectionStep,
  SocialMarketingPlatform,
} from 'features/socialMarketing/types';
import { isEmpty } from 'lodash';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable } from 'react-native';
import Animated, { SlideInRight, SlideOutLeft } from 'react-native-reanimated';
import FbPageCreation from './FbPageCreation';
import FbPageSelection from './FbPageSelection';
import PostingChannelSelection from './PostingChannelSelection';
import SocialConnectionIndicator from './SocialConnectionIndicator';

type SocialMediaModalProps = {
  platform?: SocialMarketingPlatform | null;
  onClose: () => void;
  onAuthSuccess?: () => Promise<void>;
  type: SocialConnectActionType;
  open: boolean;
};

export default function SocialConnectionModal({
  platform,
  onClose,
  onAuthSuccess,
  type,
  open,
}: SocialMediaModalProps) {
  const { t } = useTranslation('socialMarketing');
  const { colors } = useTheme();
  const setIsFacebookLinked = useSocialMediaStore(
    state => state.setIsFacebookLinked,
  );
  const [step, setStep] = useState<SocialConnectionStep>(
    SocialConnectionStep.Login,
  );
  const [pages, setPages] = useState<FacebookPage[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { loginWithFacebook } = useFacebookLogin({
    onCancel: () => {
      setIsLoading(false);
    },
  });
  const { loginWithLinkedIn } = useLinkedInConnection({
    onCancel: () => {
      setIsLoading(false);
    },
  });
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const { title, description, ctaLabel } = useMemo(() => {
    let platformLabel = '';

    switch (platform) {
      case SocialMarketingPlatform.Facebook:
        platformLabel = t('socialMedia.platforms.facebook');
        break;
      case SocialMarketingPlatform.Linkedin:
        platformLabel = t('socialMedia.platforms.linkedin');
        break;
      case SocialMarketingPlatform.Instagram:
        platformLabel = t('socialMedia.platforms.instagram');
        break;
    }

    return {
      title: t('socialMedia.connect.title', {
        platform: platformLabel,
      }),
      description: t('socialMedia.connect.description', {
        platform: platformLabel,
      }),
      ctaLabel: t('socialMedia.login', {
        platform: platformLabel,
      }),
    };
  }, [platform, t]);

  const closeBottomSheetModal = () => {
    bottomSheetModalRef.current?.close();
  };

  const renderBackdrop = useCallback(
    (props: BottomSheetDefaultBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
        pressBehavior={'close'}
        onPress={closeBottomSheetModal}
      />
    ),
    [],
  );

  const handleSocialAuthenticationSuccess = async () => {
    setIsLoading(true);
    if (platform === SocialMarketingPlatform.Facebook) {
      const pages = await socialMediaApi.getFacebookPage();
      setPages(pages);
      if (pages.length === 1) {
        await socialMediaApi.connectFacebookPage(pages[0].id);
        setIsFacebookLinked(true);
        await onAuthSuccess?.();
        handleClose();
      } else {
        setStep(
          isEmpty(pages)
            ? SocialConnectionStep.CreatePage
            : SocialConnectionStep.SelectPage,
        );
      }
    } else {
      await onAuthSuccess?.();
      handleClose();
    }
    setIsLoading(false);
  };

  const handleSocialAuthentication = async () => {
    setIsLoading(true);

    switch (platform) {
      case SocialMarketingPlatform.Facebook:
        await loginWithFacebook();
        break;
      case SocialMarketingPlatform.Linkedin:
        await loginWithLinkedIn();
        break;
      default:
        break;
    }
    handleSocialAuthenticationSuccess();
  };

  const handleClose = () => {
    setStep(SocialConnectionStep.Login);
    bottomSheetModalRef.current?.close();
  };
  const handleSelectAnotherPlatform = () => {
    if (type === SocialConnectActionType.link) {
      handleClose();
    } else if (type === SocialConnectActionType.post) {
      setStep(SocialConnectionStep.PostToAnotherChannel);
    }
  };

  useEffect(() => {
    if (open) {
      bottomSheetModalRef.current?.present();
    } else {
      bottomSheetModalRef.current?.close();
    }
  }, [open, bottomSheetModalRef]);

  if (!platform) {
    return null;
  }

  return (
    <BottomSheetModal
      ref={bottomSheetModalRef}
      index={0}
      snapPoints={['50%']}
      backdropComponent={renderBackdrop}
      enablePanDownToClose
      onDismiss={onClose}
      enableOverDrag={false}>
      <Animated.View
        key={step}
        exiting={
          step === SocialConnectionStep.Login
            ? undefined
            : SlideOutLeft.duration(200)
        }
        entering={
          step === SocialConnectionStep.Login
            ? undefined
            : SlideInRight.duration(300)
        }>
        {step === SocialConnectionStep.Login && (
          <ViewContainer>
            <ContentContainer>
              <Pressable
                onPress={closeBottomSheetModal}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                }}>
                <Icon.Close fill={colors.palette.fwdDarkGreen[100]} size={24} />
              </Pressable>
              <TextTitle fontWeight="bold">{title}</TextTitle>
              <TextDescription>{description}</TextDescription>

              <SocialConnectionIndicator platform={platform} />
            </ContentContainer>

            <Button
              loading={isLoading}
              variant="primary"
              text={ctaLabel}
              onPress={handleSocialAuthentication}
            />
          </ViewContainer>
        )}
        {step === SocialConnectionStep.SelectPage && pages && (
          <FbPageSelection
            pages={pages}
            onClose={handleClose}
            onPageSelected={onAuthSuccess}
          />
        )}
        {step === SocialConnectionStep.CreatePage && (
          <FbPageCreation
            onClose={handleClose}
            selectPlatformButtonText={t(
              type === SocialConnectActionType.link
                ? 'socialMedia.linkAnotherPlatform'
                : 'socialMedia.postToAnotherChannel',
            )}
            selectPlatformButtonHandler={handleSelectAnotherPlatform}
          />
        )}
        {step === SocialConnectionStep.PostToAnotherChannel && (
          <PostingChannelSelection onClose={handleClose} />
        )}
      </Animated.View>
    </BottomSheetModal>
  );
}

const ViewContainer = styled.View(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  padding: theme.space[4],
  paddingBottom: theme.space[10],
  height: '100%',
}));

const ContentContainer = styled.View(() => ({
  display: 'flex',
  alignItems: 'center',
}));

const TextTitle = styled(Typography.H6)(({ theme }) => ({
  marginBottom: theme.space[2],
  color: theme.colors.secondary,
}));

const TextDescription = styled(Typography.H7)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  marginBottom: theme.space[6],
  textAlign: 'center',
}));
