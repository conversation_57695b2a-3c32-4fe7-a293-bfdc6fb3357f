import styled from '@emotion/native';
import GradientCircularSpinner from 'components/GradientCircularSpinner';
import FwdCubeSquaredLogoOnOrangeSVG from 'features/home/<USER>/FwdCubeSquaredLogoOnOrangeSVG';
import FacebookLogoCircleSvg from 'features/socialMarketing/assets/FacebookLogoCircleSvg';
import LinkedInLogoSvg from 'features/socialMarketing/assets/LinkedInLogoSvg';
import { SocialMarketingPlatform } from 'features/socialMarketing/types';
import React, { useMemo } from 'react';

const Container = styled.View(({ theme: { space, sizes } }) => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  height: sizes[33],
  marginBottom: space[6],
}));

const CubeIconWrapper = styled.View(({ theme: { space } }) => ({
  alignSelf: 'flex-start',
  paddingRight: space[1],
}));

const SocialLogoWrapper = styled.View(({ theme: { space } }) => ({
  alignSelf: 'flex-end',
  paddingLeft: space[1],
}));

const FWDCubeSquaredIconWrapper = styled.View(
  ({ theme: { sizes, colors } }) => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: sizes[20],
    height: sizes[20],
    borderRadius: sizes[4],
    backgroundColor: colors.palette.fwdOrange[100],
  }),
);

const FWDCubeSquaredIcon = () => (
  <FWDCubeSquaredIconWrapper>
    <FwdCubeSquaredLogoOnOrangeSVG />
  </FWDCubeSquaredIconWrapper>
);

type SocialConnectionIndicatorProps = {
  platform: SocialMarketingPlatform;
};

export default function SocialConnectionIndicator({
  platform,
}: SocialConnectionIndicatorProps) {
  const socialLogo = useMemo(() => {
    switch (platform) {
      case SocialMarketingPlatform.Facebook:
        return <FacebookLogoCircleSvg />;
      case SocialMarketingPlatform.Linkedin:
        return <LinkedInLogoSvg />;
      default:
        return null;
    }
  }, [platform]);

  return (
    <Container>
      <GradientCircularSpinner
        style={{
          position: 'absolute',
          alignSelf: 'center',
        }}
      />
      <CubeIconWrapper>
        <FWDCubeSquaredIcon />
      </CubeIconWrapper>
      <SocialLogoWrapper>
        {React.isValidElement(socialLogo) && socialLogo}
      </SocialLogoWrapper>
    </Container>
  );
}
