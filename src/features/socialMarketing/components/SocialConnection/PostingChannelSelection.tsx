import React, { useState } from 'react';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { Icon, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { View } from 'react-native';
import { useSocialMediaPlatform } from 'features/socialMarketing/hooks/useSocialMedialPlatform';
import {
  SocialMarketingCreatePostPayload,
  SocialMarketingPlatform,
  SocialMarketingPostType,
} from 'features/socialMarketing/types';
import {
  useCreatePost,
  useSocialMarketingPost,
} from 'features/socialMarketing/hooks/usePosts';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from 'types';

type PostingChannelSelectionProps = {
  onClose: () => void;
};

export default function PostingChannelSelection({
  onClose,
}: PostingChannelSelectionProps) {
  const { t } = useTranslation('socialMarketing');
  const { colors } = useTheme();
  const [selectedPlatform, setSelectedPlatform] =
    useState<SocialMarketingPlatform>(SocialMarketingPlatform.Facebook);
  const { platforms } = useSocialMediaPlatform();
  const createPost = useCreatePost();
  const { post } = useSocialMarketingPost();
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const handleSelectPlatform = async (platform: SocialMarketingPlatform) => {
    setSelectedPlatform(platform);
    if (!post) return;

    if (post.mediaType === SocialMarketingPostType.AvatarVideo) {
      return navigation.navigate('AiAvatarCreationProcedure');
    } else {
      navigation.navigate('SocialMarketingLoading');
    }

    await createPost({
      platform,
      mediaType: post.mediaType,
      prompt: post.prompt,
      topic: post.topic,
      ratio: post.ratio,
      durationInSeconds: post.durationInSeconds,
      isFixedCameraLens: post.isFixedCameraLens,
    } as SocialMarketingCreatePostPayload);

    onClose();
  };

  return (
    <ViewContainer>
      <TitleContainer>
        <CloseIconButton onPress={onClose}>
          <Icon.Close fill={colors.secondary} />
        </CloseIconButton>
        <TextTitle fontWeight="bold">
          {t('socialMedia.selectPostingChannel')}
        </TextTitle>
      </TitleContainer>

      {platforms.map(({ key, label, icon }) => (
        <View key={key}>
          <PlatformItem onPress={() => handleSelectPlatform(key)}>
            <PlatformContent>
              {icon}
              <PlatformName>{label}</PlatformName>
            </PlatformContent>
            {selectedPlatform === key && <Icon.Tick />}
          </PlatformItem>
          <Divider />
        </View>
      ))}
    </ViewContainer>
  );
}

const ViewContainer = styled.View(({ theme }) => ({
  display: 'flex',
  padding: theme.space[4],
  paddingBottom: theme.space[10],
  height: '100%',
}));

const TitleContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.space[3],
  position: 'relative',
}));

const CloseIconButton = styled.TouchableOpacity(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
}));

const TextTitle = styled(Typography.H6)(({ theme }) => ({
  marginLeft: theme.space[3],
  color: theme.colors.secondary,
}));

const PlatformItem = styled.TouchableOpacity(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingRight: theme.space[2],
  paddingVertical: theme.space[3],
}));

const PlatformContent = styled.View(({ theme }) => ({
  gap: theme.space[3],
  flexDirection: 'row',
  alignItems: 'center',
}));

const PlatformName = styled(Typography.H7)(({ theme }) => ({
  color: theme.colors.secondary,
}));

const Divider = styled(View)(({ theme }) => ({
  height: 1,
  width: '100%',
  backgroundColor: theme.colors.palette.fwdGrey[100],
  marginVertical: theme.space[3],
}));
