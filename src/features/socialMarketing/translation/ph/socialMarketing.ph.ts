export default {
  header: 'Social Marketing',
  'templates.title': 'Create your post',
  'templates.description': 'Chose a template:',
  'myPosts.title': 'My posts',
  'menus.templates': 'Templates',
  'menus.myPosts': 'My posts',
  'menus.createNew': 'Create new',
  'tutorial.createPost': 'To create a post,',
  'tutorial.chooseATemplate': 'Choose a template',
  'tutorial.customizeYourImage': 'Customize your image',
  'tutorial.customizeYourVideo': 'Customize your video',
  'tutorial.feelingAdventurous': 'Feeling adventurous?',
  'tutorial.makeYourOwn': 'Make your own',
  'tutorial.createFromScratch': '...or create an image or video from scratch',
  'tutorial.cta.createNew': 'Create new',
  'tutorial.madeSimple': 'Social marketing made simple',
  'tutorial.yourJourney': 'Your journey begins',
  'tutorial.here': 'here',
  'createPost.title': 'Create new',
  'createPost.chooseMediaType': 'Choose media type',
  'mediaType.image': 'Image',
  'mediaType.shortVideo': 'Short video',
  'mediaType.avatarVideo': 'Avatar video',
  'createPost.describeImage': 'Describe your image',
  'createPost.describeImagePlaceholder':
    'Eg. Share a heartfelt story about a family saved by insurance during a crisis. Create an image of young parents holding a toddler at a warm home.',
  'createPost.describeVideo': 'Describe your video',
  'createPost.describeVideoPlaceholder':
    'Eg. Create a video with a mom cooking, blend her expertise in insurance in cooking',
  'createPost.platforms': 'Platforms',
  'createPost.tryTheseTopics': 'Try these topics:',
  'createPost.imageRatio': 'Image ratio',
  'createPost.imageRatio.square': '1:1 Square',
  'createPost.imageRatio.socialPost': '4:5 Social post',
  'createPost.imageRatio.socialStory': '9:16 Social story',
  'createPost.imageDescriptionPlaceholder':
    'Create an image of a young family, kids playing in park',
  'createPost.videoRatio': 'Video ratio',
  'createPost.ratioRecommended': 'Most optimal',
  'createPost.videoDescription': 'Video description',
  'createPost.videoDescriptionPlaceholder':
    'Create a video with a mom cooking lasagna, blend her expertise in insurance in cooking',
  'createPost.duration': 'Duration',
  'createPost.duration.inSeconds': '{{duration}}s',
  'createPost.fixedCameraLens.label': 'Fixed camera lens',
  'createPost.fixedCameraLens.description':
    'Camera angle remains fixed if this option is enabled.',
  'createPost.generating': 'Customizing your masterpiece...',
  'createPost.generating.image.description': `It'll take about 2-3 minutes, so feel free to collapse the window while you wait. We'll let you know when it's all set!`,
  'createPost.generating.video.description': `It may take 3-5 minutes to create your video.\nWe’ll notify you when it’s done...`,
  'createPost.generating.goToMyPosts': 'Go to my post',
  'createPost.generating.done': 'Done!',
  'createPost.cta.postCaption': 'Next: add post caption',
  'createPost.cta.generate': 'Generate',
  'createPost.cta.regenerate': 'Regenerate',
  'createPost.cta.postNow': 'Post now',
  'createPost.cta.finalizing': 'Finalizing...',
  'createPost.updated': 'Updated! 🎉',
  'loading.yourVideo': 'Your video is {{percent}}% ready',
  'loading.description':
    'This may take 3-5 minutes. Come back later and we’ll notify you when it’s ready.',
  'loading.checking': 'Checking with compliance...',
  'loading.descriptionChecking': 'You’re one step away from the magic!',
  'create.cta.postCaption.title': 'Post caption',
  'create.cta.postCaption.askMeHow': 'Ask me how',
  'create.cta.postCaption.uniqueLink': `{{username}}'s unique link`,
  'create.cta.postCaption.language.ta': 'TA',
  'create.cta.postCaption.language.en': 'EN',
  'postCaption.markdownShowMore': 'more',
  'postCaption.markdownShowLess': 'Less',
  'postCaption.refresh': 'Refresh',
  'postCaption.editCaption': 'Edit caption',
  'postCaption.update.success': 'Updated! 🎉',
  'postCaption.chooseYourLanguage': 'Choose your language',
  'postCaption.language.english': 'English',
  'postCaption.language.taglish': 'Taglish',
  'postCaption.language.tagalog': 'Tagalog',
  'postCaption.language.melayu': 'Melayu',
  'postCaption.language.short.english': 'EN',
  'postCaption.language.short.tagalog': 'TL',
  'postCaption.language.short.taglish': 'MIS',
  'postCaption.language.short.melayu': 'MS',
  'avatarLibrary.title': 'AI avatar library',
  'avatarLibrary.createNew': 'Create new',
  'avatarLibrary.reachedLimit':
    'You’ve reached your monthly avatar limit. \nCreate a new one next month!',
  'avatarLibrary.useAvatar': 'Apply avatar to video',
  'createAiAvatar.header.title': 'Create new avatar',
  'createAiAvatar.content.title': 'Get ready to create your new avatar!',
  'createAiAvatar.lineItem.findQuiteSpot':
    'Find a quiet spot with good lighting',
  'createAiAvatar.lineItem.placeYourself':
    'Place yourself at the center of the camera',
  'createAiAvatar.lineItem.keepSmiling':
    'Small mistakes are fine - Remember to keep smiling!',
  'createAiAvatar.cta.recordVideo': 'Record video',
  'videoEditing.previewYourVideo': 'Preview your video',
  'videoEditing.previewYourVideoDescription':
    'We’ll use this recording to create your digital twin! Remember, you have just **1 avatar quota** each month!',
  'videoEditing.recordAgain': 'Record again',
  'videoEditing.useVideo': 'Use video',
  'videoEditing.processAvatar': 'Your avatar is {{percent}}% ready',
  'videoEditing.processAvatarDescription':
    'It may take **3-5 minutes** to train your model. We’ll notify you when it’s done.',
  'videoEditing.gotoMyPost': 'Go to my posts',
  'videoEditing.seeThePost': 'See the post',
  'videoEditing.avatarIsReady': 'Your avatar is ready!',
  'videoEditing.tapBelow': 'Tab below to take a look.',
  'videoEditing.applyAvatar': 'Apply avatar to video',
  'avatar.recording.tapToRecord': 'Tap to record',
  'avatar.recording.submit': 'Submit',
  'avatar.recording.instruction':
    'When you’re ready, tap to record \nthe sample script',
  'myPosts.blank.title': 'You haven’t created anything yet',
  'myPosts.black.subTitle':
    'Hit that fancy + button down there to get started.',
  'myPosts.post.title.updating': 'Updating... it may take a few minutes',
  'myPosts.post.title.updated': 'Update successful',
  'myPosts.post.title.draft': 'Edited {{time}} ago',
  'myPosts.post.title.published': 'Posted on {{time}}',
  'myPosts.post.button.edit': 'Edit',
  'myPosts.post.button.repost': 'Repost',
  'myPosts.stats.allTimeLeads': 'All-time leads',
  'myPosts.stats.postViews': 'Post views',
  'myPosts.stats.mySiteVisits': 'My site visits',
  'myPosts.stats.monthlyTrend.title': 'This month',
  'myPosts.stats.monthlyTrend.content.trendUp': '{{num}} 🎉',
  'myPosts.stats.monthlyTrend.content.trendDown': '{{num}} 🥺',
  'socialMedia.facebook': 'Facebook',
  'socialMedia.instagram': 'Instagram',
  'socialMedia.linkedin': 'Linkedin',
  'socialMedia.tiktok': 'Tiktok',
  'createPost.leaving.editing.title': 'Wait! Your changes haven’t been saved.',
  'createPost.leaving.editing.subTitle':
    'If you leave editing mode now, all unsaved changes will be lost. Are you sure you want to leave?',
  'createPost.leaving.button.continue': 'Continue editing',
  'createPost.leaving.button.leave': 'Leave anyway',
  'createPost.leaving.saving.title':
    'Would you like to save your draft before leaving?',
  'createPost.error.cta.rewrite': 'Rewrite',
  'createPost.error.cta.okGotIt': 'Ok, got it',
  'createPost.error.cta.leaveEdit': 'Leave anyway',
  'createPost.error.cta.continueEditing': 'Continue editing',
  'createPost.error.cta.save': 'Save',
  'createPost.error.cta.discard': 'Discard',
  'createPost.error.risk.title':
    'Oops! It looks like we cannot process your image request',
  'createPost.error.risk.description': `It doesn’t quite fit our professional guidelines. Let's give it another shot with a different idea!`,
  'createPost.error.lostNetwork.title':
    'Your internet connection has decided to take a coffee break!',
  'createPost.error.lostNetwork.description':
    'If it doesn’t return soon, we might lose that brilliant AI-generated masterpiece you were working on. Check your network and bring it back to life so we can keep the creativity flowing! ☕️💻',
  'createPost.error.internal.title': 'Service temporarily unavailable',
  'createPost.error.internal.description':
    'The AI service is temporarily down due to maintenance or high demand. Please try again in a few minutes.',
  'createPost.error.timeout.title': 'Content generation timeout',
  'createPost.error.timeout.description':
    'The AI took too long to generate content. Consider simplifying your input or trying again.',
  'createPost.error.leaveEdit.title': 'Wait! Your changes haven’t been saved.',
  'createPost.error.leaveEdit.description':
    'If you leave editing mode now, all unsaved changes will be lost. Are you sure you want to leave? ',
  'createPost.error.leaveSave.title':
    'Would you like to save your draft before leaving?',
  'createPost.error.leaveSave.description':
    'The draft will be saved to “My posts”.',
  'myPosts.repost.postTo': 'Post to',
  'myPosts.repost.channel.title': 'Select posting channel',
  'prompt.error.common':
    'Your description may include content that does not align with our professional guidelines. ',
  // Social media
  'socialMedia.platforms.facebook': 'Facebook',
  'socialMedia.platforms.linkedin': 'Linkedin',
  'socialMedia.platforms.instagram': 'Instagram',
  'socialMedia.login': 'Log in {{platform}}',
  'socialMedia.connect.title': 'Connect to {{platform}}',
  'socialMedia.connect.description':
    "Quick heads up! Next, we'll take you to {{platform}}. Just hit 'Allow' so we can share your awesome post on social. Thanks a bunch! 🙌",
  'socialMedia.selectYourFbPage': 'Select your Facebook page',
  'socialMedia.publishToThisPage': 'Publish to this page',
  'socialMedia.dontHaveFacebookPage':
    "Oops! Looks like you don't have any Facebook public page",
  'socialMedia.setUpPageNotification':
    'Can you set up a public profile? That way, we can publish posts for you there!',
  'socialMedia.createFacebookPage': 'Create a public Facebook page',
  'socialMedia.selectPostingChannel': 'Select posting channel',
  'socialMedia.postToAnotherChannel': 'Post to another channel',
  'socialMedia.linkAnotherPlatform': 'Link another social platform',
  'createPost.avatarRecordTermsAgreement.title':
    'Please agree to the following before creating your avatar.',
  'createPost.avatarRecordTermsAgreement.description':
    'By proceeding, you allow FWD to use your facial data for avatar and video generation. Make sure that you are the only individual in the video. Create a unique avatar and have fun!',
  'createPost.avatarRecordTermsAgreement.cta.agree': 'Agree, let’s start!',
  'createPost.avatarRecord.banner.limitationNote':
    'For now, you can create 1 avatar only',
};
