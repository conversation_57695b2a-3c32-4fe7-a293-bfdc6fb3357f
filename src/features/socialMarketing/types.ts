export type MediaContentType =
  | 'video/mp4'
  | 'video/quicktime'
  | 'image/png'
  | 'image/jpeg';

export enum SocialMarketingPostType {
  Image = 'image',
  ShortVideo = 'video', // Following API convention, 'video' is used for short videos
  AvatarVideo = 'avatar', // Following API convention, 'avatar' is used for avatar videos
}

export interface SocialMarketingMedia {
  uid: string;
  title: string;
  url: string;
  content_type: MediaContentType;
  created_at?: string;
  updated_at?: string;
}

export interface SocialMarketingTemplate {
  template_name: string;
  _metadata: unknown;
  post_caption?: string;
  video?: SocialMarketingMedia;
  image?: SocialMarketingMedia;
  search_tags?: string;
}

export interface SocialMarketingCategory {
  id: string;
  title: string;
}

export interface SocialMarketingTopic {
  id: string;
  title: string;
}

export enum SocialMarketingPlatform {
  Instagram = 'instagram',
  Facebook = 'facebook',
  TikTok = 'tiktok',
  Linkedin = 'linkedin',
}

export enum SocialMarketingRatio {
  Square = '1:1',
  // 4:5 for social post
  SocialPost = '4:5',
  // 9:16 for social story
  SocialStory = '9:16',
}

export enum SocialMarketingVideoDuration {
  FiveSeconds = '5', // 5 seconds
  TenSeconds = '10', // 10 seconds
}

export interface SocialMarketingCreatePostPayload {
  mediaType: SocialMarketingPostType;
  prompt: string;
  topic: string;
  ratio: SocialMarketingRatio;
  platform?: SocialMarketingPlatform;
  durationInSeconds?: SocialMarketingVideoDuration; // Duration in seconds for video posts
  isFixedCameraLens?: boolean; // Whether to use a fixed camera lens for video posts
}

export interface SocialMarketingRegeneratePostPayload {
  prompt: string;
  topic?: string | null;
}

export enum SocialMarketingPostStatus {
  Generating = 'MEDIA_GENERATING',
  Generated = 'MEDIA_GENERATED',
  CaptionUpdated = 'CAPTION_UPDATED',
  Published = 'PUBLISHED',
}

export enum SocialMarketingSessionStatus {
  Generating = 'GENERATING',
  Ready = 'SUCCEED',
}

export interface SocialMarketingPost {
  id: string;
  status?: SocialMarketingPostStatus;
  agentId: string;
  caption?: string | null;
  facebookPostId?: string | null;
  mediaType?: SocialMarketingPostType | null;
  mediaId: string;
  mediaUrl?: string | null;
  localUrl?: string | null;
  prompt?: string | null;
  platform?: SocialMarketingPlatform | null;
  durationInSeconds?: SocialMarketingVideoDuration | null; // Duration in seconds for video posts
  isFixedCameraLens?: boolean | null; // Whether to use a fixed camera lens for video
  ratio?: SocialMarketingRatio | null;
  topic?: string | null;
  createdAt: number;
  updatedAt?: number;
}
export interface SocialMarketingUpdatePostCaptionPayload {
  caption: string;
}

export interface SocialMarketingGeneratePostCaptionPayload {
  language: string;
}

export interface SocialMarketingGeneratePostResponse {
  sessionId: string;
  status: SocialMarketingSessionStatus;
  post: SocialMarketingPost;
}

export interface AiAvatar {
  id: string;
  agentId: string;
  avatarUrl: string;
  localUrl?: string;
  createdAt: string;
}

export interface GetAiAvatarsResponse {
  content: AiAvatar[];
  hasNext: boolean;
  hasPrevious: boolean;
  maxItems: number;
  page: number;
  size: number;
  totalItems: number;
  totalPages: number;
  monthlyLimit: number;
  reachMonthlyLimit: boolean;
}

export interface GetMarketingActivityStatictics {
  total_posts_last_week: number;
  total_views_last_week: number;
}

export interface FacebookPage {
  id: string;
  name: string;
  pictureUrl: string;
}

export enum SocialConnectionStep {
  Login = 'login',
  SelectPage = 'selectPage',
  CreatePage = 'createPage',
  PostToAnotherChannel = 'postToAnotherChannel',
}

export enum SocialConnectActionType {
  link = 'LINK',
  post = 'POST',
}

export type Platform = {
  key: SocialMarketingPlatform;
  label: string;
  icon: JSX.Element;
};
export interface GetAgentPostsListParams {
  page?: number;
}

export interface PostPaginationResponse {
  content: SocialMarketingPost[];
  page: number;
  size: number;
  totalItems: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
  maxItems: number;
  monthlyLimit: number;
  reachMonthlyLimit: boolean;
}

export enum SocialMarketingAlertType {
  Risk = 'Risk',
  NoNetwork = 'No network',
  Internal = 'Internal',
  Timeout = 'Timeout',
  LeaveEdit = 'Leave edit',
  LeaveSave = 'Leave save',
  AvatarRecordTermsAgreement = 'Avatar record terms agreement',
}

export type SocialMarketingAlertAction = {
  label: string;
  onPress?: () => void;
};

export interface SocialMarketingAlertContent {
  title?: string | null;
  description?: string | null;
  primaryAction?: SocialMarketingAlertAction | null;
  secondaryAction?: SocialMarketingAlertAction | null;
}
