import { StateCreator, StoreApi, create } from 'zustand';
import { PersistOptions, createJSONStorage, persist } from 'zustand/middleware';
import { customPersistSecureStorage } from '../../aiBot/store/customPersistStorage';

export interface SocialMediaStoreState {
  isFacebookLinked: boolean;
  isLinkedInLinked: boolean;
  _hasHydrated: boolean;
}

export interface SocialMediaStoreEvent {
  setIsFacebookLinked: (isFacebookLinked: boolean) => void;
  setIsLinkedInLinked: (isLinkedInLinked: boolean) => void;
}

export type SocialMediaStore = SocialMediaStoreState & SocialMediaStoreEvent;

const initialState: SocialMediaStoreState = {
  isFacebookLinked: false,
  isLinkedInLinked: false,
  _hasHydrated: false,
};

const store: StateCreator<SocialMediaStore> = (
  set: StoreApi<SocialMediaStore>['setState'],
) => ({
  ...initialState,
  setIsFacebookLinked: isFacebookLinked => set({ isFacebookLinked }),
  setIsLinkedInLinked: isLinkedInLinked => set({ isLinkedInLinked }),
});

const createSocialMediaStore = (
  withPersist = false,
  persistOptions?: Omit<
    PersistOptions<SocialMediaStore, Partial<SocialMediaStore>>,
    'name'
  >,
) =>
  create<
    SocialMediaStore,
    [['zustand/persist', Partial<SocialMediaStore>]] | []
  >(
    withPersist
      ? persist(store, {
          name: 'socialMedia',
          ...persistOptions,
        })
      : store,
  );

export const useSocialMediaStore = createSocialMediaStore(false, {
  storage: createJSONStorage(() => customPersistSecureStorage),
  onRehydrateStorage: () => () => {
    useSocialMediaStore.setState({ _hasHydrated: true });
  },
});
