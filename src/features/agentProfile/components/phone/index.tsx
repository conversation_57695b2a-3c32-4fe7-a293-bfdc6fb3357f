import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import ResponsiveView from 'components/ResponsiveView';
import Skeleton from 'components/Skeleton';
import { HIT_SLOP_SPACE } from 'constants/hitSlop';
import {
  ActionPanel,
  Box,
  Column,
  FontWeight,
  H6,
  H7,
  Icon,
  Label,
  Row,
  Typography,
  XView,
} from 'cube-ui-components';
import { Image } from 'expo-image';
import AwardSection from 'features/agentProfile/ph/phone/AwardSection';
import HeaderSettingButton from 'features/agentProfile/ph/phone/HeaderSettingButton';
import MobileAgentNameCard from 'features/agentProfile/ph/phone/MobileAgentNameCard';
import ShareLink from 'features/agentProfile/ph/components/ShareLink';
import TabletAgentNameCard from 'features/agentProfile/ph/phone/TabletAgentNameCard';
import AvatarPlaceholderSVG from 'features/home/<USER>/AvatarPlaceholderSVG';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import HeaderBackButton from 'navigation/components/HeaderBackButton';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React, { memo, useCallback, useMemo } from 'react';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { LicenseInfo, RootStackParamList } from 'types';
import { country } from 'utils/context';
import GATracking from 'utils/helper/gaTracking';
import { useTranslation } from 'react-i18next';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { useTermsConditionsUrl } from 'features/home/<USER>/useTermsConditionsUrl';
import useToggle from 'hooks/useToggle';
import PdfViewer from 'features/pdfViewer/components/PdfViewer';
import { PdfProps } from 'react-native-pdf';
import { useMyAgreementUrl } from 'features/home/<USER>/useMyAgreementUrl';
import { AgreementDocumentType } from 'hooks/useDocuments';
import { T } from 'ramda';
// import MdrtSection from 'features/agentProfileV2/ph/phone/MdrtSection';
// import EliteAgentSection from 'features/agentProfileV2/ph/phone/EliteAgentSection';

const HIT_SLOP_SPACE_TWO = HIT_SLOP_SPACE(2);

export default function AgentProfileScreen() {
  const { space, colors, borderRadius } = useTheme();
  const { isWideScreen } = useWindowAdaptationHelpers();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { name: screenName } = useRoute();
  const { isLoading, data: agentProfile } = useGetAgentProfile();
  const agentProfilePicture = agentProfile?.agentPhotoUrl || '';

  const licenses = agentProfile?.licenses ?? [];

  const onEditPress = () => {
    GATracking.logButtonPress({
      screenName,
      screenClass: 'Agent Profile',
      actionType: 'non_cta_button',
      buttonName: 'Edit button to Personal details',
    });

    navigation.navigate('PersonalDetails');
  };

  return (
    <>
      <ScreenHeader
        route={'AgentProfile'}
        leftChildren={<HeaderBackButton />}
        rightChildren={<HeaderSettingButton />}
      />

      <ScrollView
        bounces={false}
        showsVerticalScrollIndicator={false}
        style={{ backgroundColor: colors.surface }}>
        <ProfileContainer
          narrowStyle={{
            borderBottomLeftRadius: 36,
            paddingBottom: space[7],
            marginBottom: space[5],
            paddingHorizontal: space[3],
          }}>
          <InfoContainer>
            {agentProfilePicture ? (
              <Image
                style={{
                  height: 62,
                  width: 62,
                  borderRadius: borderRadius.full,
                }}
                source={agentProfilePicture}
              />
            ) : (
              <AvatarPlaceholderSVG size={62} />
            )}

            <Row flex={1}>
              <View style={{ flex: 1, gap: space[1] }}>
                <NameAndEditContainer>
                  <View style={{}}>
                    {isLoading ? (
                      <Skeleton width={'80%'} height={16} radius={2} />
                    ) : (
                      <Typography.H6
                        fontWeight="bold"
                        color={colors.primary}
                        children={agentProfile?.person?.fullName ?? '--'}
                      />
                    )}
                  </View>
                </NameAndEditContainer>

                <Position>
                  {agentProfile?.companyTitle ??
                    `${agentProfile?.designation} `}
                  {agentProfile?.designation &&
                    `(${agentProfile?.designation})`}
                </Position>
              </View>
              <TouchableOpacity
                hitSlop={HIT_SLOP_SPACE_TWO}
                onPress={onEditPress}>
                <Icon.Edit fill={colors.palette.fwdAlternativeOrange[100]} />
              </TouchableOpacity>
            </Row>
          </InfoContainer>
          {country === 'id' && <Divider />}

          <View style={{ alignItems: 'center' }}>
            {isWideScreen ? <TabletAgentNameCard /> : <MobileAgentNameCard />}
          </View>

          {country === 'ph' && <ShareLink />}
          {country === 'id' && <IDNLicenseSection licenses={licenses} />}
          {country === 'id' && (
            <Box>
              <MyDocuments />
              <TernsAndConditions />
            </Box>
          )}
        </ProfileContainer>

        {country === 'ph' && (
          <View style={{ gap: space[6] }}>
            <AwardSection />
            {/* <MdrtSection /> */}
            {/* <EliteAgentSection /> */}
          </View>
        )}

        <EmptyBottomView />
      </ScrollView>
    </>
  );
}

function IDNLicenseSection({
  licenses,
}: {
  licenses: Array<LicenseInfo> | Array<string>;
}) {
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation(['common', 'agentProfile']);

  const isLicensesExist = Array.isArray(licenses) && licenses.length > 0;

  return (
    <Box gap={space[2]}>
      <Box h={sizes[10]} justifyContent="center">
        <H7 fontWeight="bold">{t('agentProfile:activeLicense.title')}</H7>
      </Box>
      {isLicensesExist ? (
        <Box gap={space[3]}>
          <LicenseRow
            type={t('agentProfile:activeLicense.licenseType')}
            period={t('agentProfile:activeLicense.period')}
            code={t('agentProfile:activeLicense.licenseCode')}
            fontWeight="bold"
          />
          {licenses?.map((license, idx) => {
            if (typeof license === 'object') {
              return (
                <LicenseRow
                  key={license.desc ?? idx}
                  type={license.desc}
                  period={t('agentProfile:activeLicense.period.content', {
                    start: license.effectiveFrom
                      ? dateFormatUtil(license.effectiveFrom)
                      : '--',
                    end: license.effectiveTo
                      ? dateFormatUtil(license.effectiveTo)
                      : '--',
                  })}
                  code={license.number}
                />
              );
            }
          })}
        </Box>
      ) : (
        <Label color={colors.palette.fwdGreyDarkest}>
          {t('agentProfile:activeLicense.noRecords')}
        </Label>
      )}
    </Box>
  );
}

function LicenseRow({
  type,
  period,
  code,
  fontWeight = 'normal',
}: {
  type?: string;
  period: string;
  code?: string;
  fontWeight?: FontWeight;
}) {
  const { space, colors } = useTheme();

  return (
    <Row gap={space[3]}>
      <Column flex={1}>
        <Label
          fontWeight={fontWeight}
          color={
            fontWeight === 'normal' ? colors.palette.fwdGreyDarkest : undefined
          }>
          {type ?? '--'}
        </Label>
      </Column>

      <Column flex={1}>
        <Label fontWeight={fontWeight}>{period}</Label>
      </Column>

      <Column flex={1}>
        <Label fontWeight={fontWeight}>{code ?? '--'}</Label>
      </Column>
    </Row>
  );
}

const TernsAndConditions = memo(() => {
  const { t } = useTranslation('agentProfile');
  const { space, colors } = useTheme();
  const [pdfUrl, pdfGenerator] = useTermsConditionsUrl();
  const [pdfVisible, showPdf, hidePdf] = useToggle(false);

  const pdfOption = useMemo<Partial<PdfProps>>(() => {
    return {
      scale: 1,
      minScale: 1,
    };
  }, []);

  if (!pdfUrl) {
    return null;
  }
  return (
    <>
      <TouchableOpacity onPress={showPdf}>
        <Row alignItems="center" paddingY={space[3]}>
          <Icon.DocumentCopy fill={colors.primary} />
          <H7
            fontWeight="bold"
            style={{ flex: 1, paddingLeft: space[1], paddingRight: space[3] }}>
            {t('agentProfile.tablet.tabs.termsAndConditions')}
          </H7>
          <Icon.ChevronRight fill={colors.palette.fwdAlternativeOrange[100]} />
        </Row>
      </TouchableOpacity>
      <PdfViewer
        title={''}
        onClose={hidePdf}
        visible={pdfVisible}
        pdfGenerator={pdfGenerator}
        pdfOption={pdfOption}
      />
    </>
  );
});

const MyDocuments = memo(() => {
  const { t } = useTranslation('agentProfile');
  const { data: agentProfile } = useGetAgentProfile();
  const isLeader =
    agentProfile?.designation !== 'FWP' && agentProfile?.designation !== 'BA';
  const [visible, showModal, hideModal] = useToggle(false);
  const { space, colors } = useTheme();
  return (
    <>
      <TouchableOpacity onPress={showModal}>
        <Row alignItems="center" paddingY={space[3]}>
          <Icon.Document fill={colors.primary} />
          <H7
            fontWeight="bold"
            style={{ flex: 1, paddingLeft: space[1], paddingRight: space[3] }}>
            {t('agentProfile.tablet.tabs.myDocuments')}
          </H7>
          <Icon.ChevronRight fill={colors.palette.fwdAlternativeOrange[100]} />
        </Row>
      </TouchableOpacity>
      <ActionPanel
        visible={visible}
        handleClose={hideModal}
        titleStyle={{ paddingVertical: space[4] }}
        contentContainerStyle={{ paddingTop: 0, gap: space[3] }}
        title={t('agentProfile.tablet.tabs.myDocuments')}>
        <Agreement
          onClose={hideModal}
          type={AgreementDocumentType.AGENT_CONTRACT}
        />
        {isLeader && <Box h={1} bgColor={colors.palette.fwdGrey[100]} />}
        {isLeader && (
          <Agreement
            onClose={hideModal}
            type={AgreementDocumentType.LEADER_CONTRACT}
          />
        )}
      </ActionPanel>
    </>
  );
});

const Agreement = memo(
  ({ type, onClose }: { type: AgreementDocumentType; onClose: () => void }) => {
    const { t } = useTranslation('agentProfile');
    const { space, colors } = useTheme();
    const [pdfUrl, pdfGenerator] = useMyAgreementUrl(type);
    const [pdfVisible, showPdf, hidePdf] = useToggle(false);

    const pdfOption = useMemo<Partial<PdfProps>>(() => {
      return {
        scale: 1,
        minScale: 1,
      };
    }, []);

    const title =
      type === AgreementDocumentType.LEADER_CONTRACT
        ? t('agentProfile.tablet.tabs.leaderAgreement')
        : t('agentProfile.tablet.tabs.myAgreement');

    const onPress = useCallback(() => {
      onClose();
      showPdf();
    }, [showPdf, onClose]);

    if (!pdfUrl) {
      return null;
    }
    return (
      <>
        <TouchableOpacity onPress={onPress}>
          <Row alignItems="center" paddingY={space[3]}>
            <Icon.Document fill={colors.primary} />
            <Typography.LargeBody
              style={{
                flex: 1,
                paddingLeft: space[2],
                paddingRight: space[3],
              }}>
              {title}
            </Typography.LargeBody>
          </Row>
        </TouchableOpacity>
        <PdfViewer
          title={title}
          onClose={hidePdf}
          visible={pdfVisible}
          pdfGenerator={pdfGenerator}
          pdfOption={pdfOption}
          downloadable={true}
        />
      </>
    );
  },
);

const ProfileContainer = styled(ResponsiveView)(({ theme }) => ({
  backgroundColor: theme.colors.background,
  paddingTop: theme.space[6],
  paddingBottom: theme.space[8],
  paddingHorizontal: theme.space[4],
  borderBottomLeftRadius: 40, // Border radius from Figma
  marginBottom: theme.space[6],
  gap: theme.space[5],
}));

const InfoContainer = styled(XView)(({ theme }) => ({
  alignItems: 'center',
  gap: theme.space[3],
  marginLeft: 2,
}));

const NameAndEditContainer = styled(XView)(({ theme }) => ({
  // alignItems: 'center',
  // justifyContent: 'space-between',
  gap: theme.space[1],
}));

const Position = styled(Label)(({ theme }) => ({
  width: '75%',
  color: theme.colors.palette.fwdGreyDarkest,
}));

const EmptyBottomView = styled.View(({ theme }) => ({
  backgroundColor: 'transparent',
  paddingVertical: theme.space[4],
}));

const Divider = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.palette.fwdGrey[100],
  height: 1,
  width: '100%',
}));
