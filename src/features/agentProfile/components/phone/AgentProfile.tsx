import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { useQueryClient } from '@tanstack/react-query';
import ResponsiveView from 'components/ResponsiveView';
import Skeleton from 'components/Skeleton';
import {
  addToast,
  Button,
  Icon,
  Label,
  Row,
  Toast,
  Typography,
  XView,
} from 'cube-ui-components';
import { Image } from 'expo-image';
import EditProfileIcon from 'features/agentProfile/assets/EditProfilePictureSVG';
import HeaderSettingButton from 'features/agentProfile/ph/phone/HeaderSettingButton';
import { getDisplayAddressArray } from 'features/agentProfile/utils/agentNameCardUtils';
import AvatarPlaceholderSVG from 'features/home/<USER>/AvatarPlaceholderSVG';
import SocialConnectionModal from 'features/socialMarketing/components/SocialConnection/SocialConnectionModal';
import {
  SocialConnectActionType,
  SocialMarketingPlatform,
} from 'features/socialMarketing/types';
import {
  getQueryKey as getAgentProfileQueryKey,
  useGetAgentProfile,
} from 'hooks/useGetAgentProfile';
import { useUploadAgentStorefrontAvatar } from 'hooks/useUploadAgentStorefrontAvatar';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import HeaderBackButton from 'navigation/components/HeaderBackButton';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CubeResponse } from 'types';
import { country } from 'utils/context';
import UploadProfileImagePanel from '../UploadProfileImagePanel';
import AboutMeCard from './AboutMeCard';
import AwardSection from './AwardSection';
import ContactCard, { ContactInfoItem } from './ContactCard';
import ShareLink from './ShareLink';
import SocialMediaLinkCard from './SocialMediaLinkCard';
import ViewAsPublic from './ViewAsPublic';

export default function AgentProfileScreen() {
  const { space, colors, borderRadius } = useTheme();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { isLoading, data: agentProfile } = useGetAgentProfile();
  const agentProfilePicture = agentProfile?.agentPhotoUrl || '';
  const { t } = useTranslation(['common', 'agentProfile']);

  const [isSocialConnectionModalOpen, setIsSocialConnectionModalOpen] =
    useState(false);
  const [panelVisible, setPanelVisible] = useState(false);
  const [imgToUpload, setImgToUpload] = useState<string>('');
  const [selectedPlatform, setSelectedPlatform] =
    useState<SocialMarketingPlatform>();
  const insets = useSafeAreaInsets();
  const {
    mutateAsync: uploadAgentPicure,
    isLoading: isLoadingUploadAgentPicture,
  } = useUploadAgentStorefrontAvatar();
  const queryClient = useQueryClient();

  const closeBottomSheetModal = () => {
    setIsSocialConnectionModalOpen(false);
  };

  const handleFacebookLoginSuccess = async () => {
    addToast([
      {
        message: t('agentProfile:agentProfile.linkFacebookSuccessfully'),
        IconLeft: <Icon.Tick />,
      },
    ]);
  };

  const contactInfo = useMemo<ContactInfoItem[]>(() => {
    const contactList = [
      {
        icon: <Icon.Call fill={colors.palette.fwdDarkGreen[50]} />,
        text: agentProfile?.contact?.mobilePhone || '',
      },
      {
        icon: <Icon.Email fill={colors.palette.fwdDarkGreen[50]} />,
        text: agentProfile?.contact?.email || '',
      },
    ];

    const adrressArr = getDisplayAddressArray(agentProfile?.address);

    if (adrressArr.length) {
      contactList.unshift({
        icon: <Icon.Location fill={colors.palette.fwdDarkGreen[50]} />,
        text: adrressArr.join('\n'),
      });
    }

    return contactList;
  }, [agentProfile, country]);

  const handlePress = async () => {
    const res = await uploadAgentPicure(imgToUpload);
    if (res.status === 200) {
      const {
        success,
      }: CubeResponse<{
        success: boolean;
      }> = JSON.parse(res.body);
      success &&
        Toast.show(
          [
            {
              message: t(
                'agentProfile:agentProfile.profilePicture.update.success',
              ),
              IconLeft: <Icon.Tick />,
            },
          ],
          {
            type: 'success',
            position: 'top',
            duration: Toast.durations.SHORT,
          },
        );
      await queryClient.invalidateQueries({
        queryKey: getAgentProfileQueryKey(agentProfile?.agentId),
      });
      setImgToUpload('');
    }
  };

  return (
    <>
      <ScreenHeader
        route={'AgentProfile'}
        leftChildren={<HeaderBackButton />}
        rightChildren={<HeaderSettingButton />}
        showBottomSeparator={false}
      />

      <ScrollView
        bounces={false}
        showsVerticalScrollIndicator={false}
        style={{ backgroundColor: colors.surface }}>
        <ProfileContainer
          narrowStyle={{
            borderBottomLeftRadius: space[9],
            paddingHorizontal: space[3],
          }}>
          <InfoContainer>
            <TouchableOpacity
              style={{ zIndex: 1 }}
              onPress={() => setPanelVisible(true)}>
              {imgToUpload || agentProfilePicture ? (
                <Image
                  style={{
                    height: space[16],
                    width: space[16],
                    borderRadius: borderRadius.full,
                  }}
                  source={imgToUpload || agentProfilePicture}
                />
              ) : (
                <AvatarPlaceholderSVG size={space[16]} />
              )}
              <CameraButtonContainer>
                <EditProfileIcon />
              </CameraButtonContainer>
            </TouchableOpacity>

            <Row flex={1}>
              <View style={{ flex: 1, gap: space[1] }}>
                <NameAndEditContainer>
                  <View>
                    {isLoading ? (
                      <Skeleton width={'80%'} height={16} radius={2} />
                    ) : (
                      <Typography.H5
                        fontWeight="bold"
                        color={colors.palette.fwdDarkGreen[100]}
                        children={agentProfile?.person?.fullName ?? '--'}
                      />
                    )}
                  </View>
                </NameAndEditContainer>

                <Position>
                  {agentProfile?.companyTitle ??
                    `${agentProfile?.designation} `}
                  {agentProfile?.designation &&
                    `(${agentProfile?.designation})`}
                </Position>
                <Position>
                  {t('agentProfile:agentProfile.agentId', {
                    agentId: agentProfile?.agentId ?? '--',
                  })}
                </Position>
                <ViewAsPublic agentProfile={agentProfile} />
              </View>
            </Row>
          </InfoContainer>
          {(country === 'ph' || country === 'ib') && <ShareLink />}
          <AboutMeCard />
          {country === 'ph' && (
            <ViewCardContainer>
              <AwardSection />
            </ViewCardContainer>
          )}
          <ContactCard contactInfo={contactInfo} />
          <SocialMediaLinkCard
            onLinkPress={(platform: SocialMarketingPlatform) => {
              setSelectedPlatform(platform);
              setIsSocialConnectionModalOpen(true);
            }}
          />
        </ProfileContainer>
        <EmptyBottomView />
      </ScrollView>

      {imgToUpload && (
        <View
          style={[
            {
              backgroundColor: colors.background,
              paddingTop: space[4],
              paddingBottom: space[4] + insets.bottom,
            },
            isNarrowScreen && {
              paddingTop: space[3],
              paddingBottom: space[3] + insets.bottom,
            },
          ]}>
          <Button
            text={t('common:save')}
            loading={isLoadingUploadAgentPicture}
            style={[
              {
                width: '100%',
                alignSelf: 'center',
                paddingHorizontal: space[4],
              },
              isNarrowScreen && {
                paddingHorizontal: space[3],
              },
            ]}
            onPress={handlePress}
          />
        </View>
      )}

      <UploadProfileImagePanel
        visible={panelVisible}
        onClose={() => setPanelVisible(false)}
        setImgToUpload={imgPath => setImgToUpload(imgPath)}
      />

      <SocialConnectionModal
        open={isSocialConnectionModalOpen}
        platform={selectedPlatform}
        onClose={closeBottomSheetModal}
        onAuthSuccess={handleFacebookLoginSuccess}
        type={SocialConnectActionType.link}
      />
    </>
  );
}

const ProfileContainer = styled(ResponsiveView)(({ theme }) => ({
  backgroundColor: theme.colors.background,
  paddingTop: theme.space[6],
  paddingBottom: theme.space[4],
  paddingHorizontal: theme.space[4],
  gap: theme.space[5],
  height: '100%',
}));

const InfoContainer = styled(XView)(({ theme }) => ({
  display: 'flex',
  alignItems: 'flex-start',
  gap: theme.space[3],
  marginLeft: 2,
}));

const NameAndEditContainer = styled(XView)(({ theme }) => ({
  gap: theme.space[1],
}));

const Position = styled(Label)(({ theme }) => ({
  width: '100%',
  color: theme.colors.palette.fwdGreyDarkest,
}));

const EmptyBottomView = styled(View)(({ theme }) => ({
  backgroundColor: 'transparent',
  paddingVertical: theme.space[4],
}));

const ViewCardContainer = styled(View)(({ theme }) => ({
  ...theme.getElevation(3),
  backgroundColor: theme.colors.palette.white,
  width: '100%',
  paddingVertical: theme.space[4],
  borderRadius: theme.space[4],
}));

const CameraButtonContainer = styled(View)(({ theme }) => ({
  ...theme.getElevation(7),
  position: 'absolute',
  left: theme.space[8],
  top: theme.space[10],
  width: theme.space[8],
  height: theme.space[8],
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.full,
  shadowColor: theme.colors.palette.black,
}));
