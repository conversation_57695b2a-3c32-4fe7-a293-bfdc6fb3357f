import { Icon, Button, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { useSocialMediaStore } from 'features/socialMarketing/store/socialMediaStore';
import { useSocialMediaToken } from 'features/socialMarketing/hooks/useSocialMediaToken';
import { useSocialMediaPlatform } from 'features/socialMarketing/hooks/useSocialMedialPlatform';
import { useCallback, useEffect, useMemo } from 'react';
import { SocialMarketingPlatform } from 'features/socialMarketing/types';

interface Props {
  onLinkPress?: (platform: SocialMarketingPlatform) => void;
}

export default function SocialMediaLinkCard({ onLinkPress }: Props) {
  const { space } = useTheme();
  const { t } = useTranslation('agentProfile');
  const { verifySocialMediaToken } = useSocialMediaToken();
  const {
    isFacebookLinked,
    isLinkedInLinked,
    setIsFacebookLinked,
    setIsLinkedInLinked,
  } = useSocialMediaStore(state => ({
    setIsFacebookLinked: state.setIsFacebookLinked,
    setIsLinkedInLinked: state.setIsLinkedInLinked,
    isFacebookLinked: state.isFacebookLinked,
    isLinkedInLinked: state.isLinkedInLinked,
  }));
  const { platforms } = useSocialMediaPlatform();

  const socialMediaStatusMapping: { [key: string]: boolean } = {
    [SocialMarketingPlatform.Facebook]: isFacebookLinked,
    [SocialMarketingPlatform.Linkedin]: isLinkedInLinked,
  };

  const setSocialMediaStatus = useCallback(
    (platform: SocialMarketingPlatform) => (status: boolean) => {
      if (platform === SocialMarketingPlatform.Facebook) {
        setIsFacebookLinked(status);
      } else if (platform === SocialMarketingPlatform.Linkedin) {
        setIsLinkedInLinked(status);
      }
    },
    [setIsFacebookLinked, setIsLinkedInLinked],
  );

  useEffect(() => {
    Object.values(SocialMarketingPlatform).forEach(platform => {
      verifySocialMediaToken(platform).then(setSocialMediaStatus(platform));
    });
  }, []);

  return (
    <CardContainer>
      <TextTitle fontWeight="bold">
        {t('agentProfile.linkToSocialMedia.title')}
      </TextTitle>
      <TextContent>
        {t('agentProfile.linkToSocialMedia.description')}
      </TextContent>

      {platforms.map(({ key, label, icon }, idx) => (
        <SocialContainer
          key={key}
          style={[idx < platforms.length - 1 && { marginBottom: space[4] }]}>
          <SocialItem>
            {icon}
            <TextSocialItem>{label}</TextSocialItem>
          </SocialItem>

          {socialMediaStatusMapping[key] ? (
            <LinkedBadge>
              <Icon.TickCircleFill size={15} fill="#03824F" />
              <Typography.Label>Linked</Typography.Label>
            </LinkedBadge>
          ) : (
            <Button
              variant="secondary"
              text="Link"
              size="small"
              onPress={() => onLinkPress?.(key)}
            />
          )}
        </SocialContainer>
      ))}
    </CardContainer>
  );
}

const TextTitle = styled(Typography.H7_2)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
  marginBottom: theme.space[2],
}));

const TextContent = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  marginBottom: theme.space[4],
}));

const SocialContainer = styled(View)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const SocialItem = styled(View)(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  gap: theme.space[2],
}));

const TextSocialItem = styled(Typography.LargeLabel)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
}));

const CardContainer = styled(View)(({ theme }) => ({
  ...theme.getElevation(3),
  backgroundColor: theme.colors.palette.white,
  width: '100%',
  padding: theme.space[4],
  borderRadius: theme.space[4],
}));

const LinkedBadge = styled.View(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  gap: 6,
}));
