import { Box, H7, Icon, Label, Row } from 'cube-ui-components';
import { ScrollDownIcon } from 'features/pdfViewer/icons/ScrollDownIcon';
import Animated, {
  interpolate,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import React, { useEffect, useState } from 'react';
import { Pressable, useWindowDimensions } from 'react-native';
import { useTheme } from '@emotion/react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useTranslation } from 'react-i18next';

type ScrollProgress = 'start' | 'inProgress' | 'completed';

interface Props {
  width?: number;
  pageData: {
    currentPage: number;
    totalPages: number;
  };
  onScrollToEnd: () => void;
  bottom?: number;
}

export const AnimatedScrollDownPdfV2 = ({
  pageData,
  width,
  bottom,
  onScrollToEnd,
}: Props) => {
  const [scrollProgress, setScrollProgress] = useState<ScrollProgress>('start');

  const { t } = useTranslation('pdfViewer');

  const { width: windowWidth } = useWindowDimensions();

  const { space, colors, sizes } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();

  const progressValue = useSharedValue(0);
  const isFirstPage = useSharedValue(1);
  const containerBouncingValue = useSharedValue(0);
  const contentBouncingValue = useSharedValue(0);

  const calculatedWidth = width || windowWidth;

  useEffect(() => {
    if (pageData.totalPages === 0) {
      return;
    }
    progressValue.value = withTiming(
      Math.round(
        ((pageData.currentPage - 1) / (pageData.totalPages - 1)) * 100,
      ),
    );
    if (pageData.currentPage === 1) {
      setScrollProgress('start');
      contentBouncingValue.value = withTiming(0);
      isFirstPage.value = withTiming(1);
      containerBouncingValue.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 600 }),
          withDelay(200, withTiming(0, { duration: 600 })),
        ),
        -1,
      );
    }

    if (pageData.currentPage !== 1 && scrollProgress !== 'inProgress') {
      setScrollProgress('inProgress');
      containerBouncingValue.value = withTiming(0);
      isFirstPage.value = withTiming(0);
      contentBouncingValue.value = withRepeat(
        withTiming(1, { duration: 1000 }),
        -1,
        true,
      );
    }

    if (pageData.currentPage === pageData.totalPages) {
      contentBouncingValue.value = withTiming(0);
      setScrollProgress('completed');
    }
  }, [pageData, scrollProgress]);

  const floatingContainerStyle = useAnimatedStyle(() => {
    return {
      position: 'absolute',
      overflow: 'hidden',
      bottom: interpolate(isFirstPage.value, [1, 0], [bottom || space[5], 0]),
      alignSelf: 'center',
      width: interpolate(
        isFirstPage.value,
        [1, 0],
        [224, calculatedWidth - 32],
      ),
      backgroundColor: interpolateColor(
        isFirstPage.value,
        [1, 0],
        ['#0097A9', '#7FCBD4'],
      ),
      borderRadius: isTabletMode ? space[4] : space[3],
      borderTopRightRadius: interpolate(
        isFirstPage.value,
        [1, 0],
        [isTabletMode ? space[4] : space[3], 0],
      ),
      borderTopLeftRadius: interpolate(
        isFirstPage.value,
        [1, 0],
        [isTabletMode ? space[4] : space[3], 0],
      ),
    };
  });

  const contentFloatingStyle = useAnimatedStyle(() => ({
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: space[2],
    columnGap: space[1],
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => {
    return {
      position: 'absolute',
      width: `${progressValue.value}%`,
      backgroundColor: '#0097A9',
      height: '100%',
    };
  });

  return (
    <Animated.View style={floatingContainerStyle}>
      {scrollProgress === 'start' ? (
        <Pressable onPress={onScrollToEnd} style={{ opacity: 0.9 }}>
          <Row
            paddingY={space[2]}
            justifyContent={'center'}
            alignItems={'center'}
            gap={space[2]}>
            <ScrollDownIcon width={24} height={24} />
            <H7 fontWeight={'medium'} color={colors.palette.white}>
              {t('scrollToBottom')}
            </H7>
          </Row>
        </Pressable>
      ) : (
        <>
          <Animated.View style={progressAnimatedStyle} />
          <Animated.View style={contentFloatingStyle}>
            {scrollProgress === 'inProgress' ? (
              <Icon.GoDown size={sizes[4]} fill={colors.palette.white} />
            ) : (
              <Icon.TickCircleFill
                size={sizes[4]}
                fill={colors.palette.white}
              />
            )}
            <Label fontWeight={'medium'} color={colors.palette.white}>
              {scrollProgress === 'inProgress'
                ? t('scrollToEnd')
                : t('readingCompleted')}
            </Label>
          </Animated.View>
        </>
      )}
    </Animated.View>
  );
};
