export default {
  reviewProposal: 'Review proposal',
  reviewProductBrochure: 'Review product brochure',
  reviewProductTipsBrochure: 'Review TIPS Brochure',
  reviewProductHighlights: 'Product Highlights',
  reviewRIPLAY: 'RIPLAY',
  yourMessage: 'Your message',
  attachment: 'Attachment:',
  send: 'Send',
  cancel: 'Cancel',
  subject: 'Subject',
  attachmentNotFound: 'Attachment not found',
  unableToSendEmail: 'Unable to send email. Please retry.',
  failedToSendEmail: 'Failed to send email',
  emailHasBeenSent: 'Email has been sent.',
  to: 'To',
  cc: 'Cc (optional)',
  productHighlightSheet: 'Product Highlight Sheet',
  emailTitle: 'Send {{document}}',
  RPQ: 'RPQ document',
  sendPdf: 'Send PDF',
  pdsAcknowledged: 'Read & Understand SI PDS',
  language: 'Language',
  done: 'Done',
  invalidEmail: 'Invalid recipient. Please enter a valid email address.',
  recipientNotFound: 'Recipient email not found',
  emptyError: 'Please enter at least one valid email address.',
  scrollDown: 'Scroll down',
  scrollToEnd: 'Scroll to the end to proceed',
  readingCompleted: 'Reading completed',
  scrollToBottom: 'Scroll/Tap to bottom',
};
