import { Icon } from 'cube-ui-components';
import { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import {
  IdnRouteSubgroupKey,
  ProgressGroupWithItems,
  ProgressItem,
  ProgressSubgroup,
  RouteGroupKey,
  RouteItemKey,
  RouteSubgroupKey,
  IBRouteGroupKey,
  ReviewInfoKey,
} from 'features/eRecruit/types/progressBarTypes';
import { useERecruitProgressBarStore } from 'features/eRecruit/util/store/ERecruitProgressBarStore';
import { useERecruitStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamListMap } from 'types';
import { useGetApplicationData } from '../useGetERecruitApplicationForm';
import { applicationStageList } from 'types/eRecruit';
import useGetERecruitOptionListForAppForm from '../useGetERecruitOptionListForAppForm';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { useGetERecruitConfig } from '../useGetERecruitConfig';
import { ERecruitProgressBarState } from 'features/eRecruit/util/store/id/ERecruitProgressBarStore';

export const useGenerateIDNRoutes = (
  isRerouted: boolean,
  setIsRerouted: React.Dispatch<React.SetStateAction<boolean>>,
) => {
  const { t } = useTranslation('eRecruit');
  const { data: eRecruitOnlyOptionList, isLoading: isERCLoading } =
    useGetERecruitConfig();

  const cityList = eRecruitOnlyOptionList?.cityList ?? [];

  const generateRouteKey = (
    keys: [
      RouteGroupKey | undefined,
      RouteSubgroupKey | undefined,
      RouteItemKey | undefined,
    ],
  ) => {
    return keys.join('-');
  };

  const updateCompletedStatus = useCallback(
    (
      groups: ProgressGroupWithItems<IdnRouteSubgroupKey>[],
      completedMap: Record<string, boolean>,
    ) => {
      groups.forEach(group => {
        if (!group.items || group.items.length === 0) {
          group.completed =
            completedMap[
              generateRouteKey([group.routeKey, undefined, undefined])
            ];
        } else {
          group.items.forEach(item => {
            if ((item as ProgressSubgroup).items) {
              const subgroup = item as ProgressSubgroup;
              subgroup.items.forEach(item => {
                item.completed =
                  completedMap[
                    generateRouteKey([
                      group.routeKey,
                      subgroup.routeKey,
                      item.routeKey,
                    ])
                  ];
              });
              subgroup.completed =
                subgroup.items.filter(i => i.completed).length ===
                subgroup.items.length;
            } else {
              item.completed =
                completedMap[
                  generateRouteKey([
                    group.routeKey,
                    undefined,
                    item.routeKey as RouteItemKey,
                  ])
                ];
            }
            group.completed =
              group.items.filter(i => i.completed).length ===
              group.items.length;
          });
        }
      });
    },
    [],
  );

  const route =
    useRoute<RouteProp<RootStackParamListMap['ib'], 'ERecruitApplication'>>();

  const { completedMap, setProgressBarState, groupKey, groupsFromStore } =
    useERecruitProgressBarStore(
      state => ({
        groupsFromStore: state.groups,
        groupKey: state.groupKey,
        completedMap: state.completedMap,
        setProgressBarState: state.setProgressBarState,
      }),
      shallow,
    );
  const {
    updatedRegistrationStagingId,
    // data
    essentialInfo,
    backgroundDetails,
    addressInfo,
    candidatePosition,
    emergencyContact,
    bankAccountInfo,
    insuranceExperience,
    healthCondition,
    financialCondition,
    complianceAndReputation,
    amlAndOther,
    declarationOfInterest,
    remarkOptional,

    // setter
    setEssentialInfo,
    setBackgroundDetails,
    setAddressInfo,
    setCandidatePosition,
    setEmergencyContact,
    setBankAccountInformation,
    setInsuranceExperience,
    setHealthCondition,
    setFinancialCondition,
    setComplianceAndReputation,
    setAmlAndOther,
    setDeclarationOfInterest,

    setRegistrationStagingId,
  } = useERecruitStore(
    state => ({
      updatedRegistrationStagingId: state.registrationStagingId,

      // data
      essentialInfo: state.essentialInfo,
      backgroundDetails: state.backgroundDetails,
      addressInfo: state.addressInfo,
      candidatePosition: state.candidatePosition,
      emergencyContact: state.emergencyContact,
      bankAccountInfo: state.bankAccountInfo,

      insuranceExperience: state.insuranceExperience,
      healthCondition: state.healthCondition,
      financialCondition: state.financialCondition,
      complianceAndReputation: state.complianceAndReputation,
      amlAndOther: state.amlAndOther,
      declarationOfInterest: state.declarationOfInterest,
      remarkOptional: state.remarkOptional,
      // setters
      setEssentialInfo: state.setEssentialInfo,
      setBackgroundDetails: state.setBackgroundDetails,
      setAddressInfo: state.setAddressInfo,
      setCandidatePosition: state.setCandidatePosition,
      setRegistrationStagingId: state.setRegistrationStagingId,
      setEmergencyContact: state.setEmergencyContact,
      setBankAccountInformation: state.setBankAccountInformation,

      setInsuranceExperience: state.setInsuranceExperience,
      setHealthCondition: state.setHealthCondition,
      setFinancialCondition: state.setFinancialCondition,
      setComplianceAndReputation: state.setComplianceAndReputation,
      setAmlAndOther: state.setAmlAndOther,
      setDeclarationOfInterest: state.setDeclarationOfInterest,
    }),
    shallow,
  );
  const { regulatoryList } = useGetERecruitOptionListForAppForm();
  const healthInfoObj = regulatoryList?.find(i => i.section == 'S-4');
  const financialInfoObj = regulatoryList?.find(i => i.section == 'S-1');
  const compliAndRepuObj = regulatoryList?.find(i => i.section == 'S-2');
  const amlInfoObj = regulatoryList?.find(i => i.section == 'S-5');

  const registrationStagingId = route.params?.registrationStagingId ?? '';
  const registrationStagingIdParam = registrationStagingId;

  const { data: recruitmentCache, isInitialLoading } = useGetApplicationData(
    registrationStagingIdParam ||
      updatedRegistrationStagingId?.toString() ||
      '',
  );

  type KeyOfStageToPageMap =
    | 'PERSONAL_DETAILS'
    | 'OCCUPATION_DETAILS'
    | 'OTHER_DETAILS'
    | 'DOCUMENT'
    | 'CONSENT';

  useEffect(() => {
    // populating data from Backend to frontend store
    // ----------------------------------
    if (recruitmentCache) {
      const applicationMainStage =
        recruitmentCache?.stage as KeyOfStageToPageMap;
      const pageMapResult =
        (stageToPageMap[
          applicationMainStage as KeyOfStageToPageMap
        ] as IBRouteGroupKey) || stageToPageMap['PERSONAL_DETAILS'];
      if (
        isRerouted == false ||
        // current page is not equal to targeted page
        (groupKey != pageMapResult &&
          // target to pg2, current on pg1
          ((pageMapResult == 'otherDetails' && groupKey == 'personalDetails') ||
            // pg 3 Review Section TBC as one seperate page, or bottomSheet modal
            // target to pg4, current on pg2
            (pageMapResult == 'documentUpload' &&
              groupKey == 'otherDetails'))) ||
        // target to pg5, current on pg4
        (pageMapResult == 'consents' && groupKey == 'documentUpload')
      ) {
        console.log('=== rerounting in useGenerateIDNRoutes');
        const subgroupHandler = (): IdnRouteSubgroupKey | IBRouteGroupKey => {
          switch (applicationMainStage) {
            case 'PERSONAL_DETAILS':
              return pageMapResult;
            case 'OTHER_DETAILS':
              if (
                recruitmentCache.workingExperiences?.[0]
                  ?.isHaveExpGeneInsurance == null ||
                recruitmentCache.workingExperiences?.[0]
                  ?.isHaveExpLifeInsurance == null
              ) {
                return 'insuranceExperience';
              }
              // if("")
              return 'insuranceExperience';
            default:
              return pageMapResult;
          }
        };
        const primaryItem = groupsFromStore?.find(
          gp => gp.routeKey == pageMapResult,
        );
        const barStateToBeSet = primaryItem
          ? ({
              groupKey: pageMapResult,
              subgroupKey: subgroupHandler(),
              itemKey: subgroupHandler(),
              item: {
                routeKey: primaryItem?.routeKey,
                title: primaryItem?.title,
                disabled: primaryItem?.disabled,
                completed: primaryItem?.completed,
                withDot: primaryItem?.withDot,
                icon: primaryItem?.icon,
              },
            } satisfies ERecruitProgressBarState)
          : ({
              groupKey: pageMapResult,
              subgroupKey: subgroupHandler(),
              itemKey: subgroupHandler(),
            } satisfies ERecruitProgressBarState);

        setProgressBarState(barStateToBeSet);
        setIsRerouted(true);
      } else {
        console.log('==xxx== NO rerounting triggered in useGenerateIDNRoutes');
      } // groupKey subgroupKey itemKey
    }
  }, [isRerouted, recruitmentCache, setIsRerouted, setProgressBarState]);

  useEffect(() => {
    const uploadItems: ProgressItem<ReviewInfoKey>[] = [
      {
        routeKey: 'documentUpload',
        title: '  •  ' + t('eRecruit.progressBar.documents'),
        icon: <Icon.DocumentCopy />,
        barTitle: 'Documents',
      },
    ];

    const groups: Array<ProgressGroupWithItems<IdnRouteSubgroupKey>> = [
      {
        routeKey: 'personalDetails',
        title: t('eRecruit.progressBar.coreProfileAndIdentity'),
        icon: <Icon.Account />,
        items: [
          {
            routeKey: 'essentialInformation',
            title:
              '  •  ' +
              t('eRecruit.application.personalDetails.essentialInformation'),
            barTitle: t('eRecruit.progressBar.coreProfileAndIdentity'),
            icon: <Icon.Account />,
          },
          {
            routeKey: 'backgroundDetails',
            title:
              '  •  ' +
              t('eRecruit.application.personalDetails.backgroundDetails'),
            barTitle: t('eRecruit.progressBar.coreProfileAndIdentity'),
            icon: <Icon.Account />,
          },
          {
            routeKey: 'addressDetails',
            title:
              '  •  ' +
              t('eRecruit.application.otherDetails.addressInformation'),
            barTitle: t('eRecruit.progressBar.coreProfileAndIdentity'),
            icon: <Icon.Account />,
          },
          {
            routeKey: 'candidatePosition',
            title:
              '  •  ' +
              t('eRecruit.application.otherDetails.candidateInformation'),
            barTitle: t('eRecruit.progressBar.coreProfileAndIdentity'),
            icon: <Icon.Account />,
          },
          {
            routeKey: 'emergencyContact',
            title:
              '  •  ' + t('eRecruit.application.otherDetails.emergencyContact'),
            barTitle: t('eRecruit.progressBar.coreProfileAndIdentity'),
            icon: <Icon.Account />,
          },
          {
            routeKey: 'bankAccInfo',
            title:
              '  •  ' +
              t('eRecruit.application.otherDetails.bankAccountInformation'),
            barTitle: t('eRecruit.progressBar.coreProfileAndIdentity'),
            icon: <Icon.Account />,
          },
        ],
      },
      {
        routeKey: 'otherDetails',
        title: t('eRecruit.progressBar.declaration'),
        icon: <Icon.LoanApplication />,

        items: [
          {
            routeKey: 'insuranceExperience',
            title:
              '  •  ' +
              t('eRecruit.application.personalDetails.insuranceExperience'),
            barTitle: t('eRecruit.progressBar.declaration'),
            icon: <Icon.LoanApplication />,
          },
          {
            routeKey: 'healthCondition',
            title: '  •  ' + renderLabelByLanguage(healthInfoObj?.longDesc),
            barTitle: t('eRecruit.progressBar.declaration'),
            icon: <Icon.LoanApplication />,
          },

          {
            routeKey: 'financialCondition',
            title: '  •  ' + renderLabelByLanguage(financialInfoObj?.longDesc),
            barTitle: t('eRecruit.progressBar.declaration'),
            icon: <Icon.LoanApplication />,
          },
          {
            routeKey: 'complianceAndReputationRecords',
            title: '  •  ' + renderLabelByLanguage(compliAndRepuObj?.longDesc),
            barTitle: t('eRecruit.progressBar.declaration'),
            icon: <Icon.LoanApplication />,
          },
          {
            routeKey: 'amlAndOthers',
            title: '  •  ' + renderLabelByLanguage(amlInfoObj?.longDesc),
            barTitle: t('eRecruit.progressBar.declaration'),
            icon: <Icon.LoanApplication />,
          },
          {
            routeKey: 'declarationOfCOI',
            title:
              '  •  ' + t('eRecruit.application.otherDetails.declarationOfCOI'),
            barTitle: t('eRecruit.progressBar.declaration'),
            icon: <Icon.LoanApplication />,
          },
          {
            routeKey: 'remarkOptional',
            title: '  •  ' + t('application.otherDetails.remark.optional'),
            barTitle: t('eRecruit.progressBar.declaration'),
            icon: <Icon.LoanApplication />,
          },
        ],
      },
      {
        routeKey: 'reviewInfo',
        title: t('eRecruit.progressBar.reviewInfo'),
        items: [
          {
            routeKey: 'otherDetails',
            title: '  •  ' + t('eRecruit.progressBar.reviewInfo'),
            barTitle: t('eRecruit.progressBar.otherDetails'),
            icon: <Icon.LoanApplication />,
          },
        ],
      },
      {
        routeKey: 'documentUpload',
        title: t('eRecruit.progressBar.documents'),
        items: uploadItems,
        full: true,
      },
      {
        routeKey: 'consents',
        title: t('eRecruit.progressBar.consent'),
        items: [
          {
            routeKey: 'consents',
            title: '  •  ' + t('eRecruit.progressBar.consent'),
            barTitle: t('eRecruit.progressBar.consent'),
            icon: <Icon.DocumentCopy />,
          },
        ],
      },
    ];

    updateCompletedStatus(groups, completedMap);

    const step1 = groups[0];

    step1.items[0].completed =
      applicationStageList.indexOf(
        recruitmentCache?.stage ?? 'NEW_APPLICATION',
      ) > 1;

    step1.items.forEach(section => {
      switch (section.routeKey) {
        case 'essentialInformation': {
          return (section.completed = essentialInfo.done);
        }
        case 'backgroundDetails': {
          return (section.completed = backgroundDetails.done);
        }
        case 'addressDetails': {
          return (section.completed = addressInfo.done);
        }
        case 'candidatePosition': {
          return (section.completed = candidatePosition.done);
        }
        case 'emergencyContact': {
          return (section.completed = emergencyContact.done);
        }
        case 'bankAccInfo': {
          return (section.completed = bankAccountInfo.done);
        }
        default:
          return false;
      }
    });

    // ----------step 1---------------
    step1.completed = step1.items.every(section => {
      if (section.routeKey == 'emergencyContact') {
        return candidatePosition.candidatePosition.isHaveFinancingProgram
          ? section.completed
          : true;
      }
      return section.completed;
    });
    // ----------step 2---------------

    const step2 = groups[1];
    step2.items.forEach(section => {
      switch (section.routeKey) {
        case 'insuranceExperience': {
          return (section.completed = insuranceExperience.done);
        }
        case 'healthCondition': {
          return (section.completed = healthCondition.done);
        }
        case 'financialCondition': {
          return (section.completed = financialCondition.done);
        }
        case 'complianceAndReputationRecords': {
          return (section.completed = complianceAndReputation.done);
        }
        case 'amlAndOthers': {
          return (section.completed = amlAndOther.done);
        }
        case 'declarationOfCOI': {
          return (section.completed = declarationOfInterest.done);
        }
        case 'remarkOptional': {
          return (section.completed = remarkOptional.done);
        }
        default:
          return false;
      }
    });
    step2.completed =
      applicationStageList.indexOf(
        recruitmentCache?.stage ?? 'NEW_APPLICATION',
      ) > 2;
    // ----------step 3---------------

    const step3 = groups[2];
    step3.items[0].completed =
      applicationStageList.indexOf(
        recruitmentCache?.stage ?? 'NEW_APPLICATION',
      ) > 3;
    step3.completed = step3.items[0].completed;
    // ----------step 4---------------

    const step4 = groups[3];
    step4.items[0].completed =
      applicationStageList.indexOf(
        recruitmentCache?.stage ?? 'NEW_APPLICATION',
      ) > 4;
    step4.completed = step4.items[0].completed;

    // ----------step 5---------------
    const step5 = groups[4];
    step5.items[0].completed =
      applicationStageList.indexOf(
        recruitmentCache?.stage ?? 'NEW_APPLICATION',
      ) > 5;
    step5.completed = step5.items[0].completed;

    step1.disabled = step3.completed;
    step2.disabled = !step1.completed || step3.completed;
    step3.disabled = !step2.completed || step3.completed;
    step4.disabled = !step3.completed || step4.completed;
    step5.disabled = !step4.completed;

    setProgressBarState({
      groups,
    });
  }, [
    recruitmentCache,
    t,
    essentialInfo.done,
    backgroundDetails.done,
    addressInfo.done,
    candidatePosition.done,
    emergencyContact.done,
    bankAccountInfo.done,
    healthInfoObj?.longDesc,
    financialInfoObj?.longDesc,
    compliAndRepuObj?.longDesc,
    amlInfoObj?.longDesc,
    updateCompletedStatus,
    completedMap,
    setProgressBarState,
    candidatePosition.candidatePosition.isHaveFinancingProgram,
    insuranceExperience.done,
    healthCondition.done,
    financialCondition.done,
    complianceAndReputation.done,
    amlAndOther.done,
    declarationOfInterest.done,
    remarkOptional.done,
  ]);
};

const stageToPageMap = {
  PERSONAL_DETAILS: 'personalDetails',
  OCCUPATION_DETAILS: 'occupationDetails',
  OTHER_DETAILS: 'otherDetails',
  DOCUMENT: 'documentUpload',
  CONSENT: 'consents',
};
