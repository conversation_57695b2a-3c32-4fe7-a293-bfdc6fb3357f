import { useFocusEffect } from '@react-navigation/native';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useCallback, useMemo, useState } from 'react';
import { SceneMap } from 'react-native-tab-view';
import { BuildCountry, UIMode } from 'types';
import { country } from 'utils/context';
import GATracking from 'utils/helper/gaTracking/gaTracking';
import { shallow } from 'zustand/shallow';
import { IBTabs } from 'features/eRecruit//ib/phone/components/ProgressBar/tabs';
import { IDNTabs } from 'features/eRecruit/id/phone/ApplicationForm/components/ProgressBar/tabs';
import { useERecruitProgressBarStore } from '../util/store/ERecruitProgressBarStore';
import { CombinedRouteKey } from '../types/progressBarTypes';

export type ERecruitTab<RouteKey = CombinedRouteKey> = {
  route: RouteKey;
  component: React.ComponentType;
  trackingId?: string;
};

export type ERecruitTabs<RouteKey = CombinedRouteKey> = Record<
  UIMode,
  ERecruitTab<RouteKey>[]
>;

const TABS_BY_COUNTRY: Record<BuildCountry, ERecruitTabs | null> = {
  ph: null,
  my: IBTabs,
  ib: IBTabs,
  id: IDNTabs,
};

export const useGetERecruitTab = () => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { routes, renderScene } = useMemo(() => {
    const tabs: ERecruitTab<CombinedRouteKey>[] =
      (isTabletMode
        ? TABS_BY_COUNTRY[country]?.tablet
        : TABS_BY_COUNTRY[country]?.phone) || [];
    const renderScene = SceneMap(
      tabs.reduce<Record<string, React.ComponentType>>((map, tab) => {
        map[tab.route] = tab.component;
        return map;
      }, {}),
    );
    const routes = tabs.map(tab => ({ key: tab.route }));
    return { routes, renderScene };
  }, [isTabletMode]);

  const [index, setIndex] = useState(0);

  const navigationState = useMemo(() => {
    return {
      index,
      routes,
    };
  }, [index, routes]);

  const { groupKey, subGroupKey, itemKey } = useERecruitProgressBarStore(
    state => ({
      groupKey: state.groupKey,
      subGroupKey: state.subgroupKey,
      itemKey: state.itemKey,
    }),
    shallow,
  );

  useFocusEffect(
    useCallback(() => {
      const tabs =
        (isTabletMode
          ? TABS_BY_COUNTRY[country]?.tablet
          : TABS_BY_COUNTRY[country]?.phone) || [];
      for (let i = 0; i < tabs.length; i++) {
        const trackingId = tabs[i].trackingId;
        const currentRouteKey = tabs[i].route;
        const [currentGroupKey, currentSubgroupKey, currentItemKey] =
          currentRouteKey.split('-');

        if (
          (currentGroupKey === '' || currentGroupKey === groupKey) &&
          (currentSubgroupKey === '' || currentSubgroupKey === subGroupKey) &&
          (currentItemKey === '' || currentItemKey === itemKey)
        ) {
          setIndex(i);
          if (trackingId) {
            GATracking.trackScreen(trackingId);
          }
        }
      }
    }, [groupKey, isTabletMode, itemKey, subGroupKey]),
  );

  return { index, setIndex, renderScene, navigationState };
};
