import { BuildCountry } from 'types';
import { country } from 'utils/context';
import { useGenerateIBRoutes } from './ib/useGenerateIBRoutes';
import { useGenerateIDNRoutes } from './id/useGenerateIDNRoutes';
import { useMemo } from 'react';
type Fn = (
  isRerouted: boolean,
  setIsRerouted: React.Dispatch<React.SetStateAction<boolean>>,
) => void;
type VoidFn = () => void;
type RouteFunctionMap = {
  ib: VoidFn;
  id: Fn;
  ph: VoidFn;
  my: VoidFn;
};

const generateRoutesByCountry: RouteFunctionMap = {
  ib: useGenerateIBRoutes,
  id: useGenerateIDNRoutes,
  ph: () => null,
  my: useGenerateIBRoutes,
};

export const useGenerateERecruitRoutes = <
  C extends BuildCountry,
>(): RouteFunctionMap[C] => {
  const routeGenerator = useMemo(
    () => generateRoutesByCountry[country as C],
    [],
  );

  return routeGenerator;
};
