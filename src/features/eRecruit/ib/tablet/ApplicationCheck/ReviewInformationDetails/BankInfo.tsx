import { useTheme } from '@emotion/react';
import { Row, PictogramIcon, H6, Column, XView } from 'cube-ui-components';
import { View } from 'react-native';
import InfoField from 'features/eRecruit/components/InfoField';
import {
  ApplicationFormResponds,
  GetERecruitConfigResponse,
} from 'types/eRecruit';

type ContactDetailsProps = ApplicationFormResponds['contact'] | undefined;

export function BankInfo({
  contact,
  configList,
}: {
  contact: ContactDetailsProps;
  configList: GetERecruitConfigResponse | undefined;
}) {
  const { space } = useTheme();
  return (
    <View>
      <Row
        gap={space[2]}
        style={{ alignItems: 'center', paddingBottom: space[5] }}>
        <PictogramIcon.Bank2 size={space[10]} />
        <H6 fontWeight="bold">{'Bank account information'}</H6>
      </Row>
      <Column gap={space[2]}>
        <XView>
          <InfoField
            label={'Name of company'}
            data={
              (contact?.bankInformation.bankName &&
                configList?.bankList.find(
                  item => item.itemCode === contact?.bankInformation.bankName,
                )?.longDesc.en) ||
              'N/A'
            }
          />
          <InfoField
            label={'Account number'}
            data={
              contact?.bankInformation.accountNumber &&
              contact.bankInformation.accountNumber !== ''
                ? contact?.bankInformation.accountNumber
                : 'N/A'
            }
          />
        </XView>
        <XView>
          <InfoField
            label={`NRIC number as per bank's record`}
            data={
              contact?.bankInformation.icNumber &&
              contact.bankInformation.icNumber !== ''
                ? contact?.bankInformation.icNumber
                : 'N/A'
            }
          />
          <InfoField label={''} data={''} />
        </XView>
      </Column>
    </View>
  );
}
