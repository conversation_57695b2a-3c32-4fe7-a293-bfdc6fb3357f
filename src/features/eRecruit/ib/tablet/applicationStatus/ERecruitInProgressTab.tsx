import { View, TouchableOpacity, RefreshControl } from 'react-native';
import { useTheme } from '@emotion/react';
import {
  Row,
  Icon,
  Typography,
  Box,
  Column,
  Button,
  Chip,
} from 'cube-ui-components';
import { SetStateAction, useMemo, useState } from 'react';
import {
  CubeStatusKeys,
  ERTableInProgressContent,
  filterStatusList,
  positionList,
} from 'types/eRecruit';
import { ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useGetERApplicationList } from 'features/eRecruit/hooks/useGetERApplicationList';
import SlidingSideModal from 'components/SlidingSideModal';
import SearchingCandidatesListSection from 'features/eRecruit/ib/tablet/SearchingCandidatesList';
import styled from '@emotion/native';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import ERTable, {
  lastUpdateHandler,
} from 'features/eRecruit/ib/tablet/applicationStatus/ERInProgressTable';
import { country } from 'utils/context';
import { RecruitKey } from 'utils/translation/i18next';

export default function ERInProgressScreen({
  filterStatus,
}: {
  filterStatus: CubeStatusKeys | undefined;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  const [positionFilter, setPositionFilter] = useState<(string | undefined)[]>(
    [],
  );
  const [filterBy, setFilterBy] = useState<(CubeStatusKeys | undefined)[]>(
    filterStatus == undefined ? [] : [filterStatus],
  );

  const [isSortDate, setIsSortDate] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  const {
    isLoading: isAppStatusDataLoading,
    data: newData,
    isRefetching,
    refetch,
  } = useGetERApplicationList({
    cubeStatusList:
      country === 'id'
        ? [
            'PENDING_LEADER_APPROVAL',
            'REMOTE_SIGNATURE',
            'RESUME_APPLICATION',
            'POTENTIAL_CANDIDATE',
          ]
        : [
            'PENDING_PAYMENT',
            'PENDING_LEADER_APPROVAL',
            'REMOTE_CHECKING',
            'REMOTE_SIGNATURE',
            'RESUME_APPLICATION',
            'POTENTIAL_CANDIDATE',
          ],
    limit: 99999,
  });

  const processedData = useMemo(() => {
    const mappedData =
      newData?.data?.map(item => ({
        displayName: item.name,
        position: item.candidatePositionCode ?? '--',
        status: item.cubeStatus,
        lastUpdated: lastUpdateHandler(item),
        recruitId: item.registrationId ?? item.registrationStagingId,
        stage: item.stage,
      })) ?? [];

    mappedData.sort((a, b) =>
      isSortDate
        ? new Date(a?.lastUpdated).getTime() -
          new Date(b?.lastUpdated).getTime()
        : new Date(b?.lastUpdated).getTime() -
          new Date(a?.lastUpdated).getTime(),
    );

    const dataOnPositionFilter =
      positionFilter.length > 0
        ? mappedData.filter(item => positionFilter.includes(item.position))
        : mappedData;

    return filterBy.length > 0
      ? dataOnPositionFilter.filter(item => filterBy.includes(item.status))
      : dataOnPositionFilter;
  }, [filterBy, positionFilter, isSortDate, newData]);

  const tagOnPressForPosition = (tabFilter: string) => {
    positionFilter.includes(tabFilter)
      ? setPositionFilter(positionFilter.filter(item => item !== tabFilter))
      : setPositionFilter([...positionFilter, tabFilter]);
  };

  const tagOnPress = (tabFilter: CubeStatusKeys) => {
    filterBy.includes(tabFilter)
      ? setFilterBy(filterBy.filter(item => item !== tabFilter))
      : setFilterBy([...filterBy, tabFilter]);
  };

  if (isSearching) {
    return (
      <CommonAnimatedViewWrapper>
        <SearchingCandidatesListSection setIsSearching={setIsSearching} />
      </CommonAnimatedViewWrapper>
    );
  }

  return (
    <Box flex={1} bgColor={colors.palette.fwdGrey[50]}>
      <Row justifyContent="space-between" alignItems="center">
        <Typography.H6 fontWeight="bold">
          {t('applicationStatus.inProgress')}
        </Typography.H6>
        {/* need to put in the search later on  */}
        <TouchableOpacity onPress={() => setIsSearching(true)}>
          <Row
            justifyContent="center"
            gap={space[2]}
            alignItems="center"
            borderRadius={space[10]}
            paddingX={space[5]}
            paddingY={space[2]}
            borderColor={colors.palette.fwdOrange[50]}
            borderWidth={2}
            backgroundColor={colors.background}
            style={{
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 4,
              },
              shadowOpacity: 0.12,
              shadowRadius: 10,
              elevation: 5, // This is for Android
            }}>
            <Icon.Search size={18} />
            <Typography.LargeLabel color={colors.primary} fontWeight="medium">
              {t('eRecruit.searchCandidate')}
            </Typography.LargeLabel>
          </Row>
        </TouchableOpacity>
      </Row>
      <Row py={space[3]} gap={space[2]} alignItems="center">
        <Typography.Body
          fontWeight="normal"
          color={colors.palette.fwdGreyDarkest}>
          {t('applicationStatus.filterBy')}
        </Typography.Body>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: space[1] }}>
          {filterStatusList.map(item => (
            <Chip
              key={item.status}
              focus={filterBy.includes(item.status)}
              label={t(`candidate.status.${item.label}` as RecruitKey)}
              onPress={() => tagOnPress(item.status)}
            />
          ))}
        </ScrollView>

        <FilterButtonWithModal
          filterBy={filterBy}
          tagOnPress={tagOnPress}
          positionFilter={positionFilter}
          tagOnPressForPosition={tagOnPressForPosition}
          setFilterBy={setFilterBy}
          setPositionFilter={setPositionFilter}
        />
      </Row>
      <View style={{ paddingBottom: space[3] }}>
        <Typography.Body color={colors.palette.fwdGreyDarkest}>
          {t('applicationStatus.totalCaseShownFromLast90Days', {
            count: processedData?.length,
          })}
        </Typography.Body>
      </View>
      <ERTable
        type="refetchable"
        isRefetching={isRefetching}
        refetch={refetch}
        data={processedData}
        isListProcessing={isAppStatusDataLoading}
        isSortDate={isSortDate}
        setIsSortDate={setIsSortDate}
      />
    </Box>
  );
}

function FilterButtonWithModal({
  filterBy,
  tagOnPress,
  positionFilter,
  tagOnPressForPosition,
  setFilterBy,
  setPositionFilter,
}: {
  filterBy: (CubeStatusKeys | undefined)[];
  tagOnPress: (tabFilter: CubeStatusKeys) => void;
  positionFilter: (string | undefined)[];
  tagOnPressForPosition: (tabFilter: string) => void;
  setFilterBy: React.Dispatch<SetStateAction<(CubeStatusKeys | undefined)[]>>;
  setPositionFilter: React.Dispatch<SetStateAction<(string | undefined)[]>>;
}) {
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');

  return (
    <>
      <SlidingSideModal
        hasHeaderBottomBar={false}
        title={'Filter'}
        visible={isOpenFilter}
        onClose={() => setIsOpenFilter(false)}>
        <Column justifyContent="space-between" flex={1}>
          <Box>
            <Box paddingX={space[4]} paddingY={0} gap={space[3]}>
              <Typography.H7 fontWeight="bold">
                {t('filterPanel.title')}
              </Typography.H7>
              <Row columnGap={space[1]} rowGap={space[3]} flexWrap="wrap">
                {filterStatusList.map(item => (
                  <Chip
                    key={item.status}
                    focus={filterBy.includes(item.status)}
                    label={t(`candidate.status.${item.label}` as RecruitKey)}
                    onPress={() => tagOnPress(item.status)}
                  />
                ))}
              </Row>
            </Box>
            <Box paddingY={space[6]} marginX={space[4]}>
              <Box height={1} backgroundColor={colors.palette.fwdGrey[100]} />
            </Box>
            <Box paddingX={space[4]} paddingY={0} gap={space[3]}>
              <Typography.H7 fontWeight="bold">Position</Typography.H7>
              <Row columnGap={space[1]} rowGap={space[3]} flexWrap="wrap">
                {positionList.map(position => (
                  <Chip
                    key={position}
                    focus={positionFilter.includes(position)}
                    label={position}
                    onPress={() => tagOnPressForPosition(position)}
                  />
                ))}
              </Row>
            </Box>
          </Box>
          <Box>
            <Box paddingY={space[6]}>
              <Box height={1} backgroundColor={colors.palette.fwdGrey[100]} />
            </Box>
            <Row
              paddingBottom={space[6]}
              gap={space[5]}
              paddingX={space[4]}
              justifyContent="space-between">
              <Button
                text="Reset"
                variant="secondary"
                style={{ flex: 1 }}
                onPress={() => {
                  setFilterBy([]);
                  setPositionFilter([]);
                }}
              />
              <Button
                text="Apply"
                style={{ flex: 1 }}
                onPress={() => {
                  setIsOpenFilter(false);
                }}
              />
            </Row>
          </Box>
        </Column>
      </SlidingSideModal>
      <TouchableOpacity
        onPress={() => {
          setIsOpenFilter(true);
        }}>
        {filterBy.length > 0 || positionFilter.length > 0 ? (
          <Icon.Filtered2 />
        ) : (
          <Icon.Filter fill={colors.onBackground} />
        )}
      </TouchableOpacity>
    </>
  );
}

const CommonAnimatedViewWrapper = styled(AnimatedViewWrapper)(({ theme }) => ({
  flex: 1000,
  backgroundColor: theme.colors.surface,
}));
