import React from 'react';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { RootStackParamListMap } from 'types';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import SittingOnCouchSVG from 'features/eRecruit/assets/tablet/SittingOnCouchSVG';
import NewEmailIcon from '../asset/NewEmailIcon';
import NewPhoneCallIcon from '../asset/NewPhoneCallIcon';
import { useState } from 'react';
import { contactCandidate } from 'features/eRecruit/util/contactUtils';

export default function CandidateSideBarContent({
  candidateProfileDetails,
  cubeStatus,
}: {
  candidateProfileDetails?: any;
  cubeStatus?: string;
}) {
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();
  const { navigate, goBack } =
    useNavigation<NavigationProp<RootStackParamListMap['ib']>>();

  const [activeButton, setActiveButton] = useState('applicationStatus');

  const Icon_CONFIG: Icon_CONFIG[] = [
    {
      label: 'Email',
      type: 'email',
      icon: <NewEmailIcon />,
      onPress: () =>
        contactCandidate({
          method: 'email',
          info: candidateProfileDetails?.email ?? null,
          errorMsg: t(
            'eRecruit.candidateProfile.phoneNumberAndEmailAreNotProvided',
          ),
        }),
    },
    {
      label: 'Contact',
      type: 'call',
      icon: <NewPhoneCallIcon />,
      onPress: () =>
        contactCandidate({
          method: 'call',
          info: candidateProfileDetails?.mobilePhone ?? null,
          errorMsg: t(
            'eRecruit.candidateProfile.phoneNumberAndEmailAreNotProvided',
          ),
        }),
    },
  ];

  return (
    <>
      <Row gap={space[5]} paddingBottom={space[4]}>
        <TouchableOpacity
          onPress={() => {
            goBack();
          }}>
          <Icon.ArrowLeft fill={colors.secondary} />
        </TouchableOpacity>
        <Typography.H6 fontWeight="bold">
          {t('eRecruit.candidateProfile')}
        </Typography.H6>
      </Row>
      <Box paddingX={space[9]} paddingY={space[6]} gap={space[2]}>
        <Typography.H5 fontWeight="bold">
          {candidateProfileDetails?.name}
        </Typography.H5>
        <Box>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            {cubeStatus === 'APPROVED'
              ? 'Approved agent'
              : cubeStatus === 'REJECTED'
              ? 'Rejected'
              : 'In application progress'}
          </Typography.Body>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            Created on{' '}
            {dateFormatUtil(candidateProfileDetails?.createdDate ?? '')}
          </Typography.Body>
        </Box>
      </Box>
      <Box paddingX={space[9]} paddingTop={space[4]} gap={space[8]}>
        <TouchableOpacity onPress={() => setActiveButton('applicationStatus')}>
          <Row gap={space[3]}>
            <Icon.LoanApplication
              size={24}
              fill={
                activeButton == 'applicationStatus'
                  ? colors.primary
                  : colors.onBackground
              }
            />
            <Typography.LargeBody
              color={
                activeButton == 'applicationStatus'
                  ? colors.primary
                  : colors.onBackground
              }
              fontWeight={
                activeButton == 'applicationStatus' ? 'bold' : 'normal'
              }>
              Application status
            </Typography.LargeBody>
          </Row>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => setActiveButton('personalInfo')}>
          <Row gap={space[3]}>
            <Icon.Account
              size={24}
              fill={
                activeButton == 'personalInfo'
                  ? colors.primary
                  : colors.onBackground
              }
            />
            <Typography.LargeBody
              color={
                activeButton == 'personalInfo'
                  ? colors.primary
                  : colors.onBackground
              }
              fontWeight={activeButton == 'personalInfo' ? 'bold' : 'normal'}>
              Personal info
            </Typography.LargeBody>
          </Row>
        </TouchableOpacity>
      </Box>
      <Box
        style={{
          position: 'absolute',
          bottom: 0,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Row gap={space[8]}>
          {Icon_CONFIG.map(({ icon, onPress, type, label }) => (
            <Column justifyContent="center" alignItems="center" key={type}>
              <TouchableOpacity
                onPress={onPress}
                key={type}
                style={{
                  backgroundColor: colors.palette.fwdOrange[5],
                  borderRadius: 100,
                  borderWidth: 2,
                  borderColor: colors.primary,
                  width: 56,
                  height: 56,
                  justifyContent: 'center',
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 0,
                  },
                  shadowOpacity: 0.16,
                  shadowRadius: 10,
                  elevation: 5, // This is for Android
                }}>
                <Box>{icon}</Box>
              </TouchableOpacity>
              <Typography.Body>{label}</Typography.Body>
            </Column>
          ))}
        </Row>
        <SittingOnCouchSVG />
      </Box>
    </>
  );
}

type Icon_CONFIG = {
  type: string;
  icon: React.JSX.Element;
  onPress: () => void;
  label: string;
};
