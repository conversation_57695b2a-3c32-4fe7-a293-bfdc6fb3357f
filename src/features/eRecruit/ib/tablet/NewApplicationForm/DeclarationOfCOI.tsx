import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  Column,
  H6,
  LargeBody,
  Row,
  H7,
  Icon,
  TextField,
  Typography,
  Picker,
} from 'cube-ui-components';
import NewDocForFormIcon from 'features/eRecruit/ib/tablet/asset/NewDocForFormIcon';
import React, { useEffect, useMemo, useState } from 'react';
import { Fragment } from 'react';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import {
  useFieldArray,
  UseFormReturn,
  FieldArrayMethodProps,
  Control,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  TextProps,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
} from 'react-native';
import Input from 'components/Input';
import DatePickerCalendar from 'components/DatePickerCalendar';
import { declarationCOIDefaultValue } from 'features/eRecruit/ib/validations/otherDetailsSchema';
import { questionsMap as IbQuestionsMap } from 'features/eRecruit/ib/tablet/config';
import Tooltip from 'components/Tooltip';
import { NewApplicationFormValues } from 'types/eRecruit';
import { country } from 'utils/context';
import useBoundStore from 'hooks/useBoundStore';
import { IdnQuestionsMap } from 'features/eRecruit/id/config';
import { BuildCountry } from 'types';
import RecruitYesNoInput from 'features/eRecruit/components/RecruitYesNoInput';

const appendListFocusOption: FieldArrayMethodProps = { shouldFocus: false };

export type COIQuestionMapReturnType = ReturnType<typeof getQuestionsMap>;

export function getQuestionsMap(
  country: BuildCountry,
  currentLanguage: string,
) {
  switch (country) {
    case 'id':
      return currentLanguage === 'id' ? IdnQuestionsMap : IbQuestionsMap;
    default:
      return IbQuestionsMap;
  }
}

type ReturnTypeOfQuestionMap = ReturnType<typeof getQuestionsMap>;

type SharedProps = {
  hookForm: UseFormReturn<NewApplicationFormValues>;
  isAffirmed: boolean;
  setIsAffirmed: React.Dispatch<React.SetStateAction<boolean>>;
  maxRecords?: number;
  isRelationshipMerged?: boolean;
};

export default function DeclarationOfCOI({
  type,
  hookForm,
  isAffirmed,
  setIsAffirmed,
  maxRecords,
  isRelationshipMerged = false,
}:
  | ({
      type: 'my';
    } & SharedProps)
  | ({
      type: 'default';
    } & SharedProps)) {
  const { t } = useTranslation(['eRecruit']);
  const { space } = useTheme();
  const currentLanguage = useBoundStore(state => state.language);

  const control = hookForm.control;

  // pre-filling the form with default values

  const questionsMap = useMemo(
    () => getQuestionsMap(country, currentLanguage),
    [currentLanguage],
  );

  return (
    <Container>
      <LabelStyle>
        <NewDocForFormIcon />
        {/* <PictogramIcon.Bank2 size={space[10]} /> */}
        <H6 fontWeight="bold">
          {t('eRecruit:eRecruit.application.otherDetails.declarationOfCOI')}
        </H6>
      </LabelStyle>

      {/* Q1 */}
      <COISection questionsMap={questionsMap} questionKey={'ownershipInterest'}>
        <RecruitYesNoInput
          control={control}
          name={'conflictOfInterest.ownershipInterest'}
          shouldHighlightOnUntouched={Input.defaultHighlightCheckForBoolean}
          initialHighlight={false}
        />
        <OwnershipInterestForm
          hookForm={hookForm as UseFormReturn<NewApplicationFormValues>}
          maxRecords={maxRecords}
          isRelationshipMerged={isRelationshipMerged}
        />
      </COISection>

      {/* Q2 */}
      <COISection
        questionsMap={questionsMap}
        questionKey={'externalEmployment'}>
        <RecruitYesNoInput
          control={control}
          name={'conflictOfInterest.externalEmployment'}
        />
        <ExternalDirectorshipEmploymentForm
          maxRecords={maxRecords}
          hookForm={hookForm as UseFormReturn<NewApplicationFormValues>}
        />
      </COISection>

      {/* Q3 */}
      <COISection
        questionsMap={questionsMap}
        questionKey={'businessAffiliationInterest'}>
        <RecruitYesNoInput
          control={control}
          name={'conflictOfInterest.businessAffiliationInterest'}
        />
        <BusinessAffiliationsInterestsForm
          maxRecords={maxRecords}
          isRelationshipMerged={isRelationshipMerged}
          hookForm={hookForm as UseFormReturn<NewApplicationFormValues>}
        />
      </COISection>

      {/* Q4 */}
      <COISection
        questionsMap={questionsMap}
        questionKey={'relationshipGovernmentOfficial'}>
        <RecruitYesNoInput
          control={control}
          name={'conflictOfInterest.relationshipGovernmentOfficial'}
        />
        <RelationshipsWithGovernmentOfficialsForm
          maxRecords={maxRecords}
          hookForm={hookForm as UseFormReturn<NewApplicationFormValues>}
        />
      </COISection>

      {/* Q5 */}
      <COISection questionsMap={questionsMap} questionKey={'otherInterest'}>
        <RecruitYesNoInput
          control={control}
          name={'conflictOfInterest.otherInterest'}
        />
        <OtherInterestForm
          hookForm={hookForm as UseFormReturn<NewApplicationFormValues>}
        />
      </COISection>
      <Box gap={space[4]}>
        <H6 fontWeight="bold">
          {t('eRecruit:application.COI.declarationTitle')}
        </H6>
        <Row gap={space[2]}>
          <Column py={space[1]} pl={space[1]}>
            {type == 'my' ? (
              <Input
                highlight
                control={
                  control as unknown as Control<
                    NewApplicationFormValues,
                    unknown
                  >
                }
                as={Checkbox}
                name="conflictOfInterest.isCOIAffirmed"
                onChange={() => setIsAffirmed && setIsAffirmed(v => !v)}
                // style={{ flex: 1 }}
                // label={}
              />
            ) : (
              <Checkbox
                highlight
                value={isAffirmed}
                onChange={() => setIsAffirmed(!isAffirmed)}
              />
            )}
          </Column>
          <Box flex={1}>
            <LargeBody>
              {t('eRecruit:application.COI.declarationMsg')}
            </LargeBody>
          </Box>
        </Row>
      </Box>
    </Container>
  );
}

function COISection({
  questionsMap,
  questionKey,
  tooltipPlacement,
  children,
}: {
  questionsMap: ReturnTypeOfQuestionMap;
  questionKey: keyof ReturnTypeOfQuestionMap;
  tooltipPlacement?: 'top' | 'bottom';
  children?: React.ReactNode;
}) {
  const { space } = useTheme();

  const keysOfQuestionsMap = Object.keys(questionsMap);
  const idxOfQuestions = keysOfQuestionsMap?.indexOf(questionKey);

  return (
    <Box gap={space[4]}>
      <COISectionTitle>
        {`Q${idxOfQuestions >= 0 ? idxOfQuestions + 1 : '--'} -` +
          questionsMap?.[questionKey]?.title}
      </COISectionTitle>
      <COISectionContent
        style={{
          flexDirection: 'row',
        }}>
        {questionsMap?.[questionKey]?.qBody.length > 1 &&
        questionsMap?.[questionKey] &&
        questionsMap?.[questionKey]?.tooltipContentList?.length > 0 ? (
          questionsMap?.[questionKey]?.qBody.map((qBody, idx) => {
            return (
              <Fragment
                key={
                  questionsMap?.[questionKey].key +
                  questionsMap?.[questionKey]?.tooltipContentList?.[idx]
                }>
                <COISectionContent>{qBody}</COISectionContent>
                {idx != questionsMap?.[questionKey]?.qBody?.length - 1 && (
                  <Tooltip
                    placement={tooltipPlacement ?? 'top'}
                    content={
                      questionsMap?.[questionKey]?.tooltipContentList?.[idx]
                    }
                  />
                )}
              </Fragment>
            );
          })
        ) : (
          <COISectionContent>
            {questionsMap?.[questionKey]?.qBody}
          </COISectionContent>
        )}
      </COISectionContent>
      {children}
      <COISectionSeparator />
    </Box>
  );
}

function OwnershipInterestForm({
  hookForm,
  maxRecords = 5,
  isRelationshipMerged = false,
}: {
  hookForm: UseFormReturn<NewApplicationFormValues>;
  maxRecords?: number;
  isRelationshipMerged?: boolean;
}) {
  const { space, colors } = useTheme();
  const { t } = useTranslation('eRecruit');

  const { watch, control } = hookForm;
  const { fields, append, remove } = useFieldArray({
    name: 'conflictOfInterest.ownershipInterests',
    control,
  });

  const isOwnershipInterestYes = watch('conflictOfInterest.ownershipInterest');
  useEffect(() => {
    if (isOwnershipInterestYes) {
      fields.length === 0 &&
        append(
          {
            ...declarationCOIDefaultValue.conflictOfInterest.ownershipInterests,
          },
          appendListFocusOption,
        );
    } else {
      fields.length > 0 && remove();
      // remove all item in the list when no index is provided;
    }
  }, [fields, isOwnershipInterestYes, append, remove]);

  if (!isOwnershipInterestYes) {
    return null;
  }
  return (
    <Box minHeight={40} gap={space[4]}>
      {fields.map((field, idx) => (
        <React.Fragment key={field.id}>
          <Column gap={space[5]}>
            <Row justifyContent="space-between">
              <Row alignItems="center" gap={space[2]}>
                <H7 fontWeight="bold" key={idx}>
                  {t('application.COI.record', {
                    number: (idx ?? 0) + 1,
                  })}
                </H7>
              </Row>
              {fields?.length === 1 && idx === 0 ? (
                <TouchableOpacity disabled>
                  <Icon.Delete fill={colors.palette.fwdGreyDark} />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    if (fields?.length === 1) {
                      console.log('last item should not be deleted');
                      return;
                    }
                    remove(idx);
                  }}>
                  <Icon.Delete fill={colors.palette.fwdDarkGreen[100]} />
                </TouchableOpacity>
              )}
            </Row>

            <Row gap={space[5]}>
              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.ownershipInterests.${idx}.nameOfBusiness`}
                label={t('application.COI.nameOfBusinessEnterpriseOrEntity')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />

              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.ownershipInterests.${idx}.natureOfBusiness`}
                label={t('application.COI.natureOfBusiness')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.ownershipInterests.${idx}.nameOfOwner`}
                label={t(
                  isRelationshipMerged
                    ? 'application.COI.nameOfOwnerAndRelationship'
                    : 'application.COI.nameOfOwner',
                )}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
            </Row>
            <Row gap={space[5]}>
              {isRelationshipMerged ? null : (
                <Input
                  control={control}
                  multiline
                  autoExpand
                  as={TextField}
                  name={`conflictOfInterest.ownershipInterests.${idx}.relationship`}
                  label={t('application.COI.relationship')}
                  style={{ flex: 1 }}
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />
              )}
              <DebouncedPercentageInput idx={idx} hookForm={hookForm} />
              <Input
                control={control}
                as={DatePickerCalendar}
                name={`conflictOfInterest.ownershipInterests.${idx}.dateAcquired`}
                maxDate={new Date()}
                style={{
                  flex: 1,
                }}
                label={t('application.COI.dateAcquired')}
                hint={t('eRecruit.application.dateHint')}
                formatDate={value => (value ? dateFormatUtil(value) : '')}
                value={
                  watch(
                    `conflictOfInterest.ownershipInterests.${idx}.dateAcquired`,
                  )
                    ? new Date(
                        watch(
                          `conflictOfInterest.ownershipInterests.${idx}.dateAcquired`,
                        ),
                      )
                    : undefined
                }
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
              {isRelationshipMerged ? <Box flex={1} /> : null}
            </Row>
          </Column>
          {fields?.length > 1 && idx != fields?.length - 1 && (
            <COISectionSeparator />
          )}
        </React.Fragment>
      ))}
      <AddButton
        customText={t('application.COI.addRecord')}
        isDisabled={fields.length >= maxRecords}
        isHidden={
          (country == 'id' && fields.length >= maxRecords) || maxRecords == 1
        }
        onPress={() => {
          fields.length < maxRecords &&
            append(
              {
                ...declarationCOIDefaultValue.conflictOfInterest
                  .ownershipInterests?.[0],
              },
              appendListFocusOption,
            );
        }}
      />
    </Box>
  );
}

function DebouncedPercentageInput({
  idx,
  hookForm,
}: {
  idx: number;
  hookForm: UseFormReturn<NewApplicationFormValues>;
}) {
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');

  const [localValue, setLocalValue] = useState('');
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = hookForm;

  const percentageValidator = (value: string) => {
    const numberOnlyStr = value.replace(/[^0-9.]/g, '');
    const demicalPlacesShown = 2;
    const parsedNumber = Number.isNaN(parseFloat(numberOnlyStr))
      ? ''
      : parseFloat(numberOnlyStr);

    const numberInput =
      parsedNumber == ''
        ? ''
        : parsedNumber >= 5 && parsedNumber <= 100
        ? Number.isInteger(parsedNumber)
          ? parsedNumber.toString()
          : parsedNumber.toFixed(demicalPlacesShown).slice(-1) === '0'
          ? parsedNumber.toFixed(demicalPlacesShown).slice(0, -1)
          : parsedNumber.toFixed(demicalPlacesShown)
        : '';

    return numberInput;
  };

  const formValue = watch(
    `conflictOfInterest.ownershipInterests.${idx}.percentageOfOwnership`,
  );

  useEffect(() => {
    console.log('formValue: ', formValue);
    formValue && setLocalValue(formValue?.toString());
  }, [formValue]);

  useEffect(() => {
    const text = percentageValidator(localValue);

    const timer = setTimeout(() => {
      const key =
        `conflictOfInterest.ownershipInterests.${idx}.percentageOfOwnership` as const;
      setValue(key, text as any);
    }, 500);
    return () => clearTimeout(timer);
  }, [localValue]);

  return (
    <Input
      control={control}
      multiline
      autoExpand
      as={TextField}
      name={`conflictOfInterest.ownershipInterests.${idx}.percentageOfOwnership`}
      // value={field.value.replace(/\D/gi, '')}
      value={formValue}
      label={t('application.COI.percentageOfOwnership')}
      onChangeText={value => setLocalValue(value)}
      style={{ flex: 1 }}
      right={
        <Typography.LargeBody color={colors.palette.fwdDarkGreen[50]}>
          %
        </Typography.LargeBody>
      }
      shouldHighlightOnUntouched={Input.defaultHighlightCheck}
      initialHighlight={false}
    />
  );
}

function ExternalDirectorshipEmploymentForm({
  hookForm,
  maxRecords = 5,
}: {
  hookForm: UseFormReturn<NewApplicationFormValues>;
  maxRecords?: number;
}) {
  const {
    control,
    setValue,
    watch,
    formState: { errors },
  } = hookForm;
  const { fields, append, remove } = useFieldArray({
    name: 'conflictOfInterest.externalEmployments',
    control,
  });
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');

  const isExternalEmploymentsYes = watch(
    'conflictOfInterest.externalEmployment',
  );

  useEffect(() => {
    if (isExternalEmploymentsYes) {
      fields.length === 0 &&
        append(
          {
            ...declarationCOIDefaultValue.conflictOfInterest
              .externalEmployments,
          },
          appendListFocusOption,
        );
    } else {
      fields.length > 0 && remove();
      // remove all item in the list when no index is provided;
    }
  }, [fields, isExternalEmploymentsYes, append, remove]);

  if (!isExternalEmploymentsYes) {
    return null;
  }

  return (
    <Box minHeight={40} gap={space[4]}>
      {fields.map((field, idx) => (
        <React.Fragment key={field.id}>
          <Column gap={space[5]}>
            <Row justifyContent="space-between">
              <Row alignItems="center" gap={space[2]}>
                <H7 fontWeight="bold" key={idx}>
                  {t('application.COI.record', {
                    number: (idx ?? 0) + 1,
                  })}
                </H7>
              </Row>
              {fields?.length === 1 && idx === 0 ? (
                <TouchableOpacity disabled>
                  <Icon.Delete fill={colors.palette.fwdGreyDark} />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    if (fields?.length === 1) {
                      console.log('last item should not be deleted');
                      return;
                    }
                    remove(idx);
                  }}>
                  <Icon.Delete fill={colors.palette.fwdDarkGreen[100]} />
                </TouchableOpacity>
              )}
            </Row>

            <Row gap={space[5]}>
              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.externalEmployments.${idx}.nameOfBusiness`}
                label={t('application.COI.nameOfBusinessEnterpriseOrEntity')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />

              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.externalEmployments.${idx}.natureOfBusiness`}
                label={t('application.COI.natureOfBusiness')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.externalEmployments.${idx}.position`}
                label={t('application.COI.position')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
            </Row>
            <Row gap={space[5]}>
              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.externalEmployments.${idx}.details`}
                label={t('application.COI.details')}
                style={{ flex: 2 }}
                hint={t('application.COI.detailsDescription')}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
              <Input
                control={control}
                as={Picker}
                name={`conflictOfInterest.externalEmployments.${idx}.compensationReceived`}
                label={t('application.COI.compensationReceived')}
                type="chip"
                items={[
                  {
                    label: t('application.COI.yes'),
                    value: true,
                  },
                  {
                    label: t('application.COI.no'),
                    value: false,
                  },
                ]}
                size="large"
                labelStyle={{
                  marginLeft: space[3],
                }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
            </Row>
          </Column>
          {fields?.length > 1 && idx != fields?.length - 1 && (
            <COISectionSeparator />
          )}
        </React.Fragment>
      ))}
      <AddButton
        customText={t('application.COI.addRecord')}
        isDisabled={fields.length >= maxRecords}
        isHidden={
          (country == 'id' && fields.length >= maxRecords) || maxRecords == 1
        }
        onPress={() => {
          maxRecords < 5 &&
            append(
              {
                ...declarationCOIDefaultValue.conflictOfInterest
                  .externalEmployments?.[0],
              },
              appendListFocusOption,
            );
        }}
      />
    </Box>
  );
}
function BusinessAffiliationsInterestsForm({
  hookForm,
  maxRecords = 5,
  isRelationshipMerged = false,
}: {
  hookForm: UseFormReturn<NewApplicationFormValues>;
  maxRecords?: number;
  isRelationshipMerged?: boolean;
}) {
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');
  const {
    watch,
    control,
    formState: { errors },
  } = hookForm;
  const { fields, append, remove } = useFieldArray({
    name: 'conflictOfInterest.businessAffiliationInterests',
    control,
  });
  const isBusinessAffiliationInterestYes = watch(
    'conflictOfInterest.businessAffiliationInterest',
  );

  useEffect(() => {
    if (isBusinessAffiliationInterestYes) {
      fields.length === 0 &&
        append(
          {
            ...declarationCOIDefaultValue.conflictOfInterest
              .businessAffiliationInterests,
          },
          appendListFocusOption,
        );
    } else {
      fields.length > 0 && remove();
      // remove all item in the list when no index is provided;
    }
  }, [fields, isBusinessAffiliationInterestYes, append, remove]);

  if (!isBusinessAffiliationInterestYes) {
    return null;
  }

  return (
    <Box minHeight={40} gap={space[4]}>
      {fields.map((field, idx) => (
        <React.Fragment key={field.id}>
          <Column gap={space[5]}>
            <Row justifyContent="space-between">
              <Row alignItems="center" gap={space[2]}>
                <H7 fontWeight="bold" key={idx}>
                  {t('application.COI.record', {
                    number: (idx ?? 0) + 1,
                  })}
                </H7>
              </Row>
              {fields?.length === 1 && idx === 0 ? (
                <TouchableOpacity disabled>
                  <Icon.Delete fill={colors.palette.fwdGreyDark} />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    if (fields?.length === 1) {
                      console.log('last item should not be deleted');
                      return;
                    }
                    remove(idx);
                  }}>
                  <Icon.Delete fill={colors.palette.fwdDarkGreen[100]} />
                </TouchableOpacity>
              )}
            </Row>

            <Row gap={space[5]}>
              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.businessAffiliationInterests.${idx}.nameOfBusiness`}
                label={t('application.COI.nameOfBusinessEnterpriseOrEntity')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />

              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.businessAffiliationInterests.${idx}.natureOfBusiness`}
                label={t('application.COI.natureOfBusiness')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.businessAffiliationInterests.${idx}.nameOfFamilyMember`}
                label={t(
                  isRelationshipMerged
                    ? 'application.COI.nameOfFamilyMemberAndRelationship'
                    : 'application.COI.nameOfFamilyMember',
                )}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
            </Row>
            <Row gap={space[5]}>
              {isRelationshipMerged ? null : (
                <Input
                  control={control}
                  multiline
                  autoExpand
                  as={TextField}
                  name={`conflictOfInterest.businessAffiliationInterests.${idx}.relationship`}
                  label={t('application.COI.relationship')}
                  style={{ flex: 1 }}
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />
              )}

              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.businessAffiliationInterests.${idx}.positionDepartment`}
                // value={field.value.replace(/\D/gi, '')}
                value={watch(
                  `conflictOfInterest.businessAffiliationInterests.${idx}.positionDepartment`,
                )}
                label={t('application.COI.positionDepartment')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />

              <Input
                control={control}
                as={DatePickerCalendar}
                name={`conflictOfInterest.businessAffiliationInterests.${idx}.dateCommencementEmployment`}
                maxDate={new Date()}
                style={{
                  flex: 1,
                }}
                label={t('application.COI.dateCommencementEmployment')}
                hint={t('eRecruit.application.dateHint')}
                formatDate={value => (value ? dateFormatUtil(value) : '')}
                value={
                  watch(
                    `conflictOfInterest.businessAffiliationInterests.${idx}.dateCommencementEmployment`,
                  )
                    ? new Date(
                        watch(
                          `conflictOfInterest.businessAffiliationInterests.${idx}.dateCommencementEmployment`,
                        ),
                      )
                    : undefined
                }
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
              {isRelationshipMerged ? <Box flex={1} /> : null}
            </Row>
          </Column>
          {fields?.length > 1 && idx != fields?.length - 1 && (
            <COISectionSeparator />
          )}
        </React.Fragment>
      ))}
      <AddButton
        customText={t('application.COI.addRecord')}
        isDisabled={fields.length >= maxRecords}
        isHidden={
          (country == 'id' && fields.length >= maxRecords) || maxRecords == 1
        }
        onPress={() => {
          maxRecords < 5 &&
            append(
              {
                ...declarationCOIDefaultValue.conflictOfInterest
                  .businessAffiliationInterests?.[0],
              },
              appendListFocusOption,
            );
        }}
      />
    </Box>
  );
}
function RelationshipsWithGovernmentOfficialsForm({
  hookForm,
  maxRecords = 5,
}: {
  hookForm: UseFormReturn<NewApplicationFormValues>;
  maxRecords?: number;
}) {
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');

  const {
    watch,
    control,
    formState: { errors },
  } = hookForm;
  const { fields, append, remove } = useFieldArray({
    name: 'conflictOfInterest.relationshipGovernmentOfficials',
    control,
  });

  const isRelateGovernmentOfficialYes = watch(
    'conflictOfInterest.relationshipGovernmentOfficial',
  );

  useEffect(() => {
    if (isRelateGovernmentOfficialYes) {
      fields.length === 0 &&
        append(
          {
            ...declarationCOIDefaultValue.conflictOfInterest
              .relationshipGovernmentOfficials,
          },
          appendListFocusOption,
        );
    } else {
      fields.length > 0 && remove();
      // remove all item in the list when no index is provided;
    }
  }, [fields, isRelateGovernmentOfficialYes, append, remove]);

  if (!isRelateGovernmentOfficialYes) {
    return null;
  }

  return (
    <Box minHeight={40} gap={space[4]}>
      {fields.map((field, idx) => (
        <React.Fragment key={field.id}>
          <Column gap={space[5]}>
            <Row justifyContent="space-between">
              <Row alignItems="center" gap={space[2]}>
                <H7 fontWeight="bold" key={idx}>
                  {t('application.COI.record', {
                    number: (idx ?? 0) + 1,
                  })}
                </H7>
              </Row>
              {fields?.length === 1 && idx === 0 ? (
                <TouchableOpacity disabled>
                  <Icon.Delete fill={colors.palette.fwdGreyDark} />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    if (fields?.length === 1) {
                      console.log('last item should not be deleted');
                      return;
                    }
                    remove(idx);
                  }}>
                  <Icon.Delete fill={colors.palette.fwdDarkGreen[100]} />
                </TouchableOpacity>
              )}
            </Row>

            <Row gap={space[5]}>
              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.relationshipGovernmentOfficials.${idx}.nameOfGovernment`}
                label={t('application.COI.nameOfGovernment')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />

              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.relationshipGovernmentOfficials.${idx}.positionDepartment`}
                label={t('application.COI.positionDepartment')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
              <Input
                control={control}
                multiline
                autoExpand
                as={TextField}
                name={`conflictOfInterest.relationshipGovernmentOfficials.${idx}.relationship`}
                label={t('application.COI.relationshipWithGovOfficials')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
            </Row>
          </Column>
          {fields?.length > 1 && idx != fields?.length - 1 && (
            <COISectionSeparator />
          )}
        </React.Fragment>
      ))}
      <AddButton
        customText={t('application.COI.addRecord')}
        isDisabled={fields.length >= maxRecords}
        isHidden={
          (country == 'id' && fields.length >= maxRecords) || maxRecords == 1
        }
        onPress={() => {
          maxRecords < 5 &&
            append(
              {
                ...declarationCOIDefaultValue.conflictOfInterest
                  .relationshipGovernmentOfficials?.[0],
              },
              appendListFocusOption,
            );
        }}
      />
    </Box>
  );
}
function OtherInterestForm({
  hookForm,
}: {
  hookForm: UseFormReturn<NewApplicationFormValues>;
}) {
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');
  const {
    watch,
    control,
    setValue,
    formState: { errors },
  } = hookForm;
  const { fields, append, remove } = useFieldArray({
    name: 'conflictOfInterest.otherInterests',
    control,
  });
  const isOtherInterestsYes = watch('conflictOfInterest.otherInterest');

  useEffect(() => {
    if (isOtherInterestsYes) {
      fields.length === 0 &&
        append(
          {
            ...declarationCOIDefaultValue.conflictOfInterest.otherInterests,
          },
          appendListFocusOption,
        );
    } else {
      fields.length > 0 && remove();
      // remove all item in the list when no index is provided;
    }
  }, [fields, isOtherInterestsYes, append, remove]);

  if (!isOtherInterestsYes) {
    return null;
  }

  return (
    <Box minHeight={40} gap={space[4]}>
      {fields.map((field, idx) => (
        <React.Fragment key={field.id}>
          <Column gap={space[5]}>
            <Row gap={space[5]} mt={space[4]}>
              <Input
                multiline
                autoExpand
                control={control}
                as={TextField}
                name={`conflictOfInterest.otherInterests.${idx}.details`}
                label={t('application.COI.otherDetails')}
                style={{ flex: 1 }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
            </Row>
          </Column>
          {fields?.length > 1 && idx != fields?.length - 1 && (
            <COISectionSeparator />
          )}
        </React.Fragment>
      ))}
      {/* Only One record of others details is allowed */}
      {/* <AddButton
        customText={t("application.COI.addRecord")}
        isDisabled={false}
        onPress={() =>
          append(
            {
              ...declarationCOIDefaultValue.conflictOfInterest.otherInterests,
            },
            appendListFocusOption,
          )
        }
      /> */}
    </Box>
  );
}

const Container = styled(View)(
  ({ theme: { colors, space, borderRadius } }) => ({
    backgroundColor: colors.background,
    padding: space[6],
    borderRadius: borderRadius.medium,
    gap: space[5],
    flex: 1,
  }),
);

const LabelStyle = styled(Row)(({ theme: { space } }) => ({
  justifyContent: 'flex-start',
  alignItems: 'center',
  gap: space[2],
}));

const COISectionTitle = (props: TextProps) => (
  <H6 fontWeight="bold">{props.children}</H6>
);

const COISectionContent = styled(LargeBody)(({ theme: { space } }) => ({}));

const COISectionSeparator = styled(View)(({ theme: { colors, space } }) => ({
  borderTopWidth: 1,
  borderTopColor: colors.palette.fwdGrey[50],
}));

const AddButton = ({
  onPress,
  isDisabled = false,
  customText,
  isHidden = false,
}: {
  onPress: TouchableOpacityProps['onPress'];
  isDisabled: boolean;
  customText?: string;
  isHidden?: boolean;
}) => {
  const { t } = useTranslation('eRecruit');
  const { colors, space, borderRadius } = useTheme();
  if (isHidden) {
    return null;
  }
  return (
    <TouchableOpacity
      disabled={isDisabled}
      onPress={onPress}
      style={{
        paddingHorizontal: space[3],
        borderWidth: 2,
        borderColor: isDisabled ? colors.primaryVariant : colors.primary,
        borderRadius: borderRadius['x-small'],
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'flex-start',
        gap: space[1],
        // width: 78,
        height: 36,
      }}>
      <Icon.Plus fill={isDisabled ? colors.primaryVariant : colors.primary} />
      <H7
        color={isDisabled ? colors.primaryVariant : colors.primary}
        fontWeight="bold">
        {customText
          ? customText
          : t(`eRecruit.application.occupationDetails.add`)}
      </H7>
    </TouchableOpacity>
  );
};
