import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { Box, Button, Column, Row, Typography } from 'cube-ui-components';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { View } from 'react-native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { NewApplicationFormParmaList } from 'types';
import { Portal } from '@gorhom/portal';
import { BottomSheetDefaultBackdropProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop/types';
import InfoField, {
  LabelStyle as InfoFieldLabelStyle,
  DataStyle as InfoFieldDataStyle,
  ColumnField,
  InfoFieldQualification,
  OtherInsuranceQualifications,
} from 'features/eRecruit/components/InfoField';
import TabletFooter from 'components/Footer/TabletFooter';
import { tabletStyles } from 'features/coverageDetails/utils/common/customStyles';
import {
  AgentInfoResponse,
  ApplicationFormResponds,
  CertificationType,
  WorkingExperienceType,
} from 'types/eRecruit';
import { useSaveERecruitApplicationForm } from 'features/eRecruit/hooks/useSaveERecruitApplicationForm';
import { useTranslation } from 'react-i18next';
import { calculateAgeAlternative } from 'features/eRecruit/util/calculateAgeAlternative';

import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import NewIdentitySectionIcon from 'features/eRecruit/ib/tablet/asset/ApplicationDetailsListIcon';
import NewContactDetailsIcon from 'features/eRecruit/ib/tablet/asset/NewContactDetailsIcon';
import NewQualificationIcon from 'features/eRecruit/ib/tablet/asset/NewQualificationIcon';
import NewSpouseInfoIcon from 'features/eRecruit/ib/tablet/asset/NewSpouseInfoIcon';
import NewBankIcon from 'features/eRecruit/ib/tablet/asset/NewBankIcon';
import NewCandidatePosIcon from 'features/eRecruit/ib/tablet/asset/NewCandidatePosIcon';
import NewLeaderIcon from 'features/eRecruit/ib/tablet/asset/NewLeaderInfoIcon';
import NewAddrIcon from 'features/eRecruit/ib/tablet/asset/NewAddrIcon';
import NewDocForFormIcon from 'features/eRecruit/ib/tablet/asset/NewDocForFormIcon';
import NewSuitcaseIcon from 'features/eRecruit/ib/tablet/asset/NewSuitcaseIcon';
import { useEffect } from 'react';
import { getAgentInfo } from 'api/eRecruitApi';
import { questionsMap } from '../config';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { country } from 'utils/context';
import { getCandidateBranchNameByListAndAgentTypeAndCode } from '../../utils/otherDetailsFuntions';
import { AgencyType } from '../../type';
import { ScrollView } from 'react-native-gesture-handler';

type ReviewSectionProps = {
  data: ApplicationFormResponds | null;
};

export function ReviewInformationButtonSheet({
  showReviewSheet,
  setShowReviewSheet,
  data,
}: {
  showReviewSheet: boolean;
  setShowReviewSheet: React.Dispatch<React.SetStateAction<boolean>>;
} & ReviewSectionProps) {
  const { space, colors, borderRadius } = useTheme();
  const navigation =
    useNavigation<NavigationProp<NewApplicationFormParmaList>>();

  const { t } = useTranslation('eRecruit');
  const snapPoint = country === 'ib' ? '90%' : '96%';
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => [snapPoint], []);

  const backToOtherDetails = () => {
    setShowReviewSheet(false);
  };
  const { mutateAsync, isLoading } = useSaveERecruitApplicationForm();
  const confirmInformation = () => {
    if (data == null) {
      console.log(
        '🚀 ~ ReviewInformationButtonSheet.tsx ~ confirmInformation ~ data:',
        data,
      );
      return;
    }
    mutateAsync(data, {
      onSuccess: (result: { registrationStagingId: number }) => {
        if (result?.registrationStagingId) {
          setShowReviewSheet(false);
          navigation.navigate('newApplicationDocuments', {
            registrationStagingId: String(result.registrationStagingId),
          });
        } else {
          return;
        }
      },
      onError: e => {
        console.log(e);
      },
    });
  };

  const renderBackdrop = useCallback(
    (props: BottomSheetDefaultBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
      />
    ),
    [],
  );

  if (showReviewSheet === false) {
    return null;
  }

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetModalRef}
        handleComponent={() => <></>}
        index={0}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        enableOverDrag={false}
        style={{
          borderTopWidth: space[3],
          borderTopRightRadius: borderRadius.medium,
          borderTopLeftRadius: borderRadius.medium,
          borderTopColor: colors.primary,
          backgroundColor: colors.primary,
        }}>
        <Typography.H5
          fontWeight="bold"
          style={{
            paddingHorizontal: space[10],
            paddingTop: space[6],
            paddingBottom: space[6],
          }}>
          {t(`eRecruit.application.review.reviewInformation`)}
        </Typography.H5>

        <ScrollView
          style={{
            paddingHorizontal: space[10],
            paddingTop: space[6],
          }}
          contentContainerStyle={{
            gap: space[4],
            paddingBottom: space[12],
          }}>
          <PersonalDetailsReview data={data} />
          <OccupationDetailsReview data={data} />
          <OtherDetailsReview data={data} />
        </ScrollView>

        <TabletFooter>
          <Row justifyContent="flex-end" gap={space[5]}>
            <Button
              variant={'secondary'}
              text={t(`eRecruit.application.review.back`)}
              onPress={() => backToOtherDetails()}
              // disabled={!isMandatoryFieldsFilled}
              // loading={isSubmitting}
              style={{ width: space[22], alignSelf: 'flex-end' }}
              textStyle={{ ...tabletStyles.mainButtonText }}
              contentStyle={{ minHeight: space[13] }}
            />
            <Button
              variant={'primary'}
              text={t(`eRecruit.application.review.confirm`)}
              // subtext={'Next ' + 'Review'}
              onPress={() => confirmInformation()}
              // disabled={!isMandatoryFieldsFilled}
              loading={isLoading}
              style={{ width: space[40], alignSelf: 'flex-end' }}
              textStyle={{ ...tabletStyles.mainButtonText }}
              contentStyle={{ minHeight: space[13] }}
            />
          </Row>
        </TabletFooter>
      </BottomSheet>
    </Portal>
  );
}

function PersonalDetailsReview({ data }: ReviewSectionProps) {
  const { space, colors, borderRadius } = useTheme();
  const navigation =
    useNavigation<NavigationProp<NewApplicationFormParmaList>>();

  const {
    ethnicityConfigToOption: ethnicityConfig,
    genderConfigToOption: genderConfig,
    maritalConfigToOption: maritalConfig,
    religionConfigToOption: religionConfig,
    citizenshipConfigToOption: citizenshipConfig,
    educationConfigToOption: educationConfig,
  } = useGetERecruitOptionListForAppForm();

  const { t } = useTranslation('eRecruit');

  const identity = data?.identity;
  const contact = data?.contact;
  const spouse = data?.spouseInformation;
  const qualifications = data?.qualifications;

  const dateOfBirth = data?.identity?.dateOfBirth ?? 'N/A';
  const formattedDate = formatDate(dateOfBirth, 'D/M/YYYY');
  const age = calculateAgeAlternative(new Date(dateOfBirth));

  const firstInsuranceCertConfig = [
    {
      type: CertificationType.TAKAFUL_TBE_FAMILY,
      name: t(`eRecruit.application.personalDetails.tbeFamily`),
    },
    {
      type: CertificationType.TAKAFUL_TBE_GENERAL,
      name: t(`eRecruit.application.personalDetails.tbeGeneral`),
    },
  ];
  const secondInsuranceCertConfig = [
    {
      type: CertificationType.INSURANCE_PCE,
      name: t(`eRecruit.application.personalDetails.pce`),
    },
    {
      type: CertificationType.INSURANCE_CEILLI,
      name: t(`eRecruit.application.personalDetails.ceilli`),
    },
    {
      type: CertificationType.INSURANCE_GENERAL,
      name: t(`eRecruit.application.personalDetails.general`),
    },
  ];

  const otherInsuranceQualificationConfig = [
    {
      type: CertificationType.ISLAMIC_RFP_CFP_CHFC,
      name: t(`eRecruit.application.personalDetails.islamic`),
    },
    {
      type: CertificationType.MFPC,
      name: t(`eRecruit.application.personalDetails.module2MFPC`),
    },
    {
      type: CertificationType.FPAM,
      name: t(`eRecruit.application.personalDetails.module2FPAM`),
    },
    {
      type: CertificationType.OTHER,
      name: t(`eRecruit.application.personalDetails.otherQualifications`),
    },
  ];

  return (
    <View>
      <Column gap={space[6]} />
      <TitleContainer>
        <Typography.H7
          fontWeight="bold"
          color={colors.palette.white}
          style={{
            paddingHorizontal: space[6],
            paddingVertical: space[3],
          }}>
          {t(`eRecruit.application.review.personalDetails`)}
        </Typography.H7>
      </TitleContainer>

      <Container>
        <Column gap={space[8]}>
          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewIdentitySectionIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.personalDetails.identityDetails`)}
              </Typography.H6>
            </Row>
            <Column gap={space[2]}>
              <Row>
                <InfoField
                  label={t(`eRecruit.application.personalDetails.name`)}
                  data={`${identity?.title ?? ''} ${
                    identity?.firstName ?? ''
                  } ${identity?.lastName ?? ''}`}
                />
                <InfoField
                  label={t(`eRecruit.application.personalDetails.dateOfBirth`)}
                  data={
                    `${formattedDate}` +
                    t(`eRecruit.application.personalDetails.yearsOld`, {
                      age: age,
                    })
                  }
                />
              </Row>

              <Row>
                <InfoField
                  label={t(`eRecruit.application.personalDetails.icNumber`)}
                  data={
                    identity?.registration.find(reg => reg.type === 'NRIC')
                      ?.number ?? 'N/A'
                  }
                />
                <InfoField
                  label={t(`eRecruit.application.personalDetails.citizen`)}
                  data={
                    getRenderLabel(citizenshipConfig, identity?.citizen) ??
                    'N/A'
                  }
                />
              </Row>

              <Row>
                <InfoField
                  label={t(`eRecruit.application.personalDetails.gender`)}
                  data={getRenderLabel(genderConfig, identity?.gender) ?? 'N/A'}
                />
                <InfoField
                  label={t(`eRecruit.application.personalDetails.title`)}
                  data={identity?.title ?? 'N/A'}
                />
              </Row>

              <Row>
                <InfoField
                  label={t(`eRecruit.application.personalDetails.ethnicity`)}
                  data={
                    getRenderLabel(ethnicityConfig, identity?.ethnicity) ??
                    'N/A'
                  }
                />
                <InfoField
                  label={t(`eRecruit.application.personalDetails.religion`)}
                  data={
                    getRenderLabel(religionConfig, identity?.religion) ?? 'N/A'
                  }
                />
              </Row>
              <Row>
                <InfoField
                  label={t(
                    `eRecruit.application.personalDetails.maritalStatus`,
                  )}
                  data={
                    getRenderLabel(maritalConfig, identity?.maritalStatus) ??
                    'N/A'
                  }
                />
                <InfoField
                  label={t(
                    `eRecruit.application.personalDetails.passportNumber`,
                  )}
                  data={
                    identity?.registration.find(reg => reg.type === 'PASSPORT')
                      ?.number ?? 'N/A'
                  }
                />
              </Row>

              <Row>
                <InfoField
                  label={t(
                    `eRecruit.application.personalDetails.oldIc/Police/Army`,
                  )}
                  data={
                    identity?.registration.find(reg => reg.type === 'OLD_IC')
                      ?.number ?? 'N/A'
                  }
                />
                <InfoField
                  label={t(
                    `eRecruit.application.personalDetails.incomeTaxFileNumber`,
                  )}
                  data={
                    identity?.registration.find(
                      reg => reg.type === 'INCOME_TAX',
                    )?.number ?? 'N/A'
                  }
                />
              </Row>
            </Column>
          </View>

          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewContactDetailsIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.personalDetails.contactDetails`)}
              </Typography.H6>
            </Row>
            <Column gap={space[2]}>
              <Row>
                <InfoField
                  label={t(`eRecruit.application.personalDetails.mobileNumber`)}
                  data={
                    contact?.phones?.find(tel => tel.type === 'MOBILE')
                      ? `${
                          contact.phones.find(tel => tel.type === 'MOBILE')
                            ?.countryCode
                        } ${
                          contact.phones.find(tel => tel.type === 'MOBILE')
                            ?.number
                        }`
                      : 'N/A'
                  }
                />
                <InfoField label={'Email'} data={contact?.email ?? 'N/A'} />
              </Row>
              <Row>
                <InfoField
                  label={t(`eRecruit.application.personalDetails.officePhone`)}
                  data={
                    contact?.phones?.find(tel => tel.type === 'WORK')
                      ? `${
                          contact.phones.find(tel => tel.type === 'WORK')
                            ?.countryCode
                        } ${
                          contact.phones.find(tel => tel.type === 'WORK')
                            ?.number
                        }`
                      : 'N/A'
                  }
                />
                <InfoField label={''} data={''} />
              </Row>
            </Column>
          </View>

          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewQualificationIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.personalDetails.qualification`)}
              </Typography.H6>
            </Row>

            <InfoFieldQualification
              label={t(
                `eRecruit.application.personalDetails.academicQualification`,
              )}
              data={[
                getRenderLabel(educationConfig, qualifications?.academic.name),
              ]}
            />

            {/* <InfoFieldQualification */}
            {/*   label={t( */}
            {/*     `eRecruit.application.personalDetails.insuranceCertificate`, */}
            {/*   )} */}
            {/*   data={ */}
            {/*     firstInsuranceCertConfig */}
            {/*       .filter(config => */}
            {/*         qualifications?.certifications?.some( */}
            {/*           cert => cert?.type === config?.type, */}
            {/*         ), */}
            {/*       ) */}
            {/*       .map(config => config?.name) || [] */}
            {/*   } */}
            {/* /> */}

            <InfoFieldQualification
              label={t(
                `eRecruit.application.personalDetails.insuranceCertificate`,
              )}
              data={
                secondInsuranceCertConfig
                  .filter(config =>
                    qualifications?.certifications?.some(
                      cert => cert?.type === config?.type,
                    ),
                  )
                  .map(config => config?.name) || []
              }
            />
            <OtherInsuranceQualifications
              label={t(
                `eRecruit.application.personalDetails.otherInsuranceQualifications`,
              )}
              data={
                otherInsuranceQualificationConfig
                  .filter(config =>
                    qualifications?.certifications?.some(
                      ({ type }) => type === config?.type,
                    ),
                  )
                  .map(matchedConfig => {
                    const matchedData = qualifications?.certifications?.find(
                      item => item?.type === matchedConfig.type,
                    );

                    if (matchedConfig.type === CertificationType.OTHER) {
                      return {
                        name: matchedConfig.name,
                        description: matchedData?.otherDesc,
                      };
                    }
                    return {
                      name: matchedConfig.name,
                      description: null,
                    };
                  }) || []
              }
            />
          </View>

          {identity?.maritalStatus === 'M' && (
            <View>
              <Row
                gap={space[2]}
                style={{ alignItems: 'center', paddingBottom: space[5] }}>
                <NewSpouseInfoIcon />
                <Typography.H6 fontWeight="bold">
                  {' '}
                  {t(`eRecruit.application.personalDetails.spouseInformation`)}
                </Typography.H6>
              </Row>
              <Column gap={space[2]}>
                <Row>
                  <InfoField
                    label={t(`eRecruit.application.personalDetails.name`)}
                    data={`${spouse?.firstName ?? ''} ${
                      spouse?.lastName ?? ''
                    }`}
                  />
                  <InfoField
                    label={t(
                      `eRecruit.application.personalDetails.numOfDependents`,
                    )}
                    data={
                      spouse?.numberOfDependence
                        ? spouse?.numberOfDependence?.toString()
                        : 'N/A'
                    }
                  />
                </Row>
                <Row>
                  <InfoField
                    label={t(`eRecruit.application.personalDetails.occupation`)}
                    data={spouse?.occupation ?? 'N/A'}
                  />
                  <InfoField
                    label={t(`eRecruit.application.personalDetails.icNumber`)}
                    data={spouse?.icNumber ?? 'N/A'}
                  />
                </Row>
                <Row>
                  <InfoField
                    label={t(
                      `eRecruit.application.personalDetails.passportNumber`,
                    )}
                    data={spouse?.passportNo ?? 'N/A'}
                  />
                  <InfoField
                    label={t(
                      `eRecruit.application.personalDetails.oldIc/Police/Army`,
                    )}
                    data={spouse?.oldIcNumber ?? 'N/A'}
                  />
                </Row>
              </Column>
            </View>
          )}
        </Column>
      </Container>
    </View>
  );
}
function OccupationDetailsReview({ data }: ReviewSectionProps) {
  const { space, colors, borderRadius } = useTheme();

  const { t } = useTranslation('eRecruit');

  const work = data?.workingExperiences;
  const spouse = data?.spouseInformation;

  const checkingSpouseDataExists =
    spouse?.type !== null &&
    spouse?.companyName !== null &&
    spouse?.rank !== null &&
    spouse?.salary !== null &&
    spouse?.dateApplied !== null &&
    spouse?.dateTermination !== null;
  const {
    intermediateTypeOptions,
    familyGeneralTakafulCompaniesList,
    lifeGeneralTakafulCompaniesList,
  } = useGetERecruitOptionListForAppForm();

  return (
    <View>
      <TitleContainer>
        <Typography.H7
          fontWeight="bold"
          color={colors.palette.white}
          style={{
            paddingHorizontal: space[4],
            paddingVertical: space[3],
          }}>
          {t(`eRecruit.application.review.occupationDetails`)}
        </Typography.H7>
      </TitleContainer>

      <Container>
        <Column gap={space[8]}>
          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewSuitcaseIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.occupationDetails.currentOccupation`)}
              </Typography.H6>
            </Row>
            {work
              ?.filter(work => work.type === WorkingExperienceType.PRESENT)
              .map((item, index) => (
                <React.Fragment key={'PRESENT' + index}>
                  <Column gap={space[2]}>
                    <Row>
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.position`,
                        )}
                        data={item.position ?? 'N/A'}
                      />
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.companyName`,
                        )}
                        data={item.companyName ?? 'N/A'}
                      />
                    </Row>
                    <Row>
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.basicSalary`,
                        )}
                        data={
                          item?.basicSalary
                            ? t(
                                `eRecruit.application.review.salaryWithCurrency`,
                                {
                                  salary: item.basicSalary?.toString(),
                                },
                              )
                            : 'N/A'
                        }
                      />
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.dateAppoint`,
                        )}
                        data={formatDate(item.dateApplied) ?? 'N/A'}
                      />
                    </Row>

                    <Row>
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.dateTermination`,
                        )}
                        data={formatDate(item.dateTermination) ?? 'N/A'}
                      />
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.contactNo`,
                        )}
                        data={`${
                          (item.companyPhoneCountryCode
                            ? item.companyPhoneCountryCode + ' '
                            : '') + (item.companyPhone ?? 'N/A')
                        }`}
                      />
                    </Row>
                    <Row>
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.companyEmail`,
                        )}
                        data={item.companyEmail ?? 'N/A'}
                      />
                      <InfoField label={''} data={''} />
                    </Row>
                    <Row>
                      <ColumnField
                        label={t(
                          `eRecruit.application.occupationDetails.companyAddress`,
                        )}
                        data={item.companyAddress ?? 'N/A'}
                      />
                    </Row>
                  </Column>
                </React.Fragment>
              ))}
          </View>

          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewSuitcaseIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.occupationDetails.previousOccupation`)}
              </Typography.H6>
            </Row>
            {work
              ?.filter(work => work.type === WorkingExperienceType.PREVIOUS)
              .map((item, index) => (
                <Column marginBottom={space[5]} key={'PREVIOUS' + index}>
                  <Row
                    style={{
                      paddingBottom: space[3],
                      alignItems: 'center',
                      gap: space[2],
                    }}>
                    <Typography.H7 fontWeight="bold">
                      {index + 1}. {item.companyName ?? 'N/A'}
                    </Typography.H7>
                  </Row>
                  <Column gap={space[2]}>
                    <Row>
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.position`,
                        )}
                        data={item.position ?? 'N/A'}
                      />
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.companyName`,
                        )}
                        data={item.companyName ?? 'N/A'}
                      />
                    </Row>
                    <Row>
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.basicSalary`,
                        )}
                        data={
                          item?.basicSalary
                            ? t(
                                `eRecruit.application.review.salaryWithCurrency`,
                                {
                                  salary: item.basicSalary?.toString(),
                                },
                              )
                            : 'N/A'
                        }
                      />
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.dateAppoint`,
                        )}
                        data={formatDate(item.dateApplied) ?? 'N/A'}
                      />
                    </Row>

                    <Row>
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.dateTermination`,
                        )}
                        data={formatDate(item.dateTermination) ?? 'N/A'}
                      />
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.contactNo`,
                        )}
                        data={
                          `${item.companyPhoneCountryCode} ${item.companyPhone}` ??
                          'N/A'
                        }
                      />
                    </Row>
                    <Row>
                      <InfoField
                        label={t(
                          `eRecruit.application.occupationDetails.companyEmail`,
                        )}
                        data={item.companyEmail ?? 'N/A'}
                      />
                      <InfoField label={''} data={''} />
                    </Row>
                    <Row>
                      <ColumnField
                        label={t(
                          `eRecruit.application.occupationDetails.companyAddress`,
                        )}
                        data={item.companyAddress ?? 'N/A'}
                      />
                    </Row>
                  </Column>
                </Column>
              ))}
          </View>

          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewSuitcaseIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.occupationDetails.family`)}
              </Typography.H6>
            </Row>
            <Column gap={space[5]}>
              {work
                ?.filter(work => work.type === WorkingExperienceType.TAKAFUL)
                .map((item, index) => (
                  <InsuranceExperience
                    key={'TAKAFUL' + index}
                    index={index + 1}
                    intermediaryType={getRenderLabel(
                      intermediateTypeOptions,
                      item?.intermediaryType,
                    )}
                    companyName={
                      familyGeneralTakafulCompaniesList.find(
                        company => company.value === item?.companyName,
                      )
                        ? familyGeneralTakafulCompaniesList.find(
                            company => company.value === item?.companyName,
                          )?.label
                        : item?.companyName
                    }
                    rank={item?.rank}
                    salary={item?.basicSalary}
                    dateAppointed={formatDate(item?.dateApplied)}
                    dateTerminaion={formatDate(item?.dateTermination)}
                  />
                ))}
            </Column>
          </View>

          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewSuitcaseIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.occupationDetails.lifeInsurance`)}
              </Typography.H6>
            </Row>
            <Column gap={space[5]}>
              {work
                ?.filter(work => work.type === WorkingExperienceType.INSURANCE)
                .map((item, index) => (
                  <InsuranceExperience
                    key={'INSURANCE' + index}
                    index={index}
                    intermediaryType={getRenderLabel(
                      intermediateTypeOptions,
                      item?.intermediaryType,
                    )}
                    companyName={
                      lifeGeneralTakafulCompaniesList.find(
                        company => company.value === item?.companyName,
                      )
                        ? lifeGeneralTakafulCompaniesList.find(
                            company => company.value === item?.companyName,
                          )?.label
                        : item?.companyName
                    }
                    rank={item?.rank}
                    salary={item?.basicSalary}
                    dateAppointed={formatDate(item?.dateApplied)}
                    dateTerminaion={formatDate(item?.dateTermination)}
                  />
                ))}
            </Column>
          </View>
          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewSuitcaseIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.occupationDetails.spouseInsurance`)}
              </Typography.H6>
            </Row>
            <LabelStyle>
              {t(`eRecruit.application.occupationDetails.spouseTakafulState`)}
            </LabelStyle>
            <DataStyle style={{ marginBottom: space[3] }}>
              {spouse?.spouseInsuranceRepresent
                ? t(`eRecruit.application.occupationDetails.yes`)
                : t(`eRecruit.application.occupationDetails.no`)}
            </DataStyle>
            {checkingSpouseDataExists && (
              <>
                <InsuranceExperience
                  intermediaryType={getRenderLabel(
                    intermediateTypeOptions,
                    spouse?.type,
                  )}
                  rank={spouse?.rank}
                  salary={spouse?.salary}
                  companyName={spouse?.companyName}
                  dateAppointed={formatDate(spouse?.dateApplied)}
                  dateTerminaion={formatDate(spouse?.dateTermination)}
                  isMultiple={false}
                />
              </>
            )}
          </View>
        </Column>
      </Container>
    </View>
  );
}

function OtherDetailsReview({ data }: ReviewSectionProps) {
  const { space, colors, borderRadius } = useTheme();

  const {
    jobTypeOptions,
    agencyTypeOptions,
    positionList,
    allBranchOptions,
    cityOptions,
    stateOptions,
    bankOptions,
  } = useGetERecruitOptionListForAppForm();

  const { t } = useTranslation('eRecruit');

  const { data: agentProfile } = useGetAgentProfile();

  const [agencyManagerInfo, setAgencyManagerInfo] = useState<
    Pick<AgentInfoResponse, 'agencyManagerCode' | 'agencyManagerName'>
  >({
    agencyManagerCode: null,
    agencyManagerName: null,
  });

  const contact = data?.contact;
  const leader = data?.leaderInformation;
  const leaderCode = leader?.agentCode;
  const position = data?.position;
  console.log('position: ', position);
  useEffect(() => {
    const setAgentName = async (agentCode: string) => {
      if (agentCode) {
        try {
          const agentInfo = await getAgentInfo(agentCode);
          console.log('agentInfo', agentInfo);
          if (agentInfo) {
            setAgencyManagerInfo({
              agencyManagerCode: agentInfo?.agencyManagerCode,
              agencyManagerName: agentInfo?.agencyManagerName,
            });
          }
        } catch (e) {
          console.log('-- useEffect -- setAgentName -- err: ', e);
        }
      }
    };
    leaderCode ? setAgentName(leaderCode) : console.log('leaderCode is null');
  }, [leaderCode]);

  return (
    <View>
      <TitleContainer>
        <Typography.H7
          fontWeight="bold"
          color={colors.palette.white}
          style={{
            paddingHorizontal: space[6],
            paddingVertical: space[3],
          }}>
          {t(`eRecruit.application.otherDetails.otherDetails`)}
        </Typography.H7>
      </TitleContainer>

      <Container>
        <Column gap={space[8]}>
          {/* Address */}
          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewAddrIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.otherDetails.addressInformation`)}
              </Typography.H6>
            </Row>
            <Column gap={space[2]}>
              <Box gap={space[1]}>
                <LabelStyle>
                  {t(`eRecruit.application.otherDetails.residentialAddress`)}
                </LabelStyle>
                <DataStyle>
                  {contact?.address.line1}
                  {','}
                  {contact?.address.line2}
                  {','}
                  {getRenderLabel(cityOptions, contact?.address?.city)}
                  {','}
                  {getRenderLabel(stateOptions, contact?.address?.state)}
                  {','}
                  {t(`eRecruit.application.otherDetails.malaysia`)}
                </DataStyle>
              </Box>
              <Box gap={space[1]}>
                <LabelStyle>
                  {t(`eRecruit.application.otherDetails.businessAddress`)}
                </LabelStyle>
                <DataStyle>
                  {contact?.businessAddress.line1}
                  {','}
                  {contact?.businessAddress.line2}
                  {','}
                  {getRenderLabel(cityOptions, contact?.businessAddress?.city)}
                  {','}
                  {getRenderLabel(
                    stateOptions,
                    contact?.businessAddress?.state,
                  )}
                  {','}
                  {t(`eRecruit.application.otherDetails.malaysia`)}
                </DataStyle>
              </Box>
            </Column>
          </View>
          {/* Agency Type */}
          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewIdentitySectionIcon />

              <Typography.H6 fontWeight="bold">
                {t('eRecruit.application.otherDetails.agencyType')}
              </Typography.H6>
            </Row>
            <Column gap={space[2]}>
              <Row>
                <InfoField
                  label={t(
                    `eRecruit.application.otherDetails.supervisorCandidateInformation.agencyType`,
                  )}
                  data={
                    getRenderLabel(agencyTypeOptions, position?.agencyType) ??
                    'N/A'
                  }
                />
                <Box flex={1} />
              </Row>
            </Column>
          </View>
          {/* Bank */}
          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewBankIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.otherDetails.bankAccountInformation`)}
              </Typography.H6>
            </Row>
            <Column gap={space[2]}>
              <Row>
                <InfoField
                  label={t(
                    `eRecruit.application.occupationDetails.companyName`,
                  )}
                  data={
                    getRenderLabel(
                      bankOptions,
                      contact?.bankInformation?.bankName,
                    ) ?? 'N/A'
                  }
                />
                <InfoField
                  label={t(`eRecruit.application.otherDetails.accountNumber`)}
                  data={contact?.bankInformation.accountNumber ?? 'N/A'}
                />
              </Row>
              <Row>
                <InfoField
                  label={t(`eRecruit.application.otherDetails.NRIC`)}
                  data={contact?.bankInformation.icNumber ?? 'N/A'}
                />
                <InfoField label={''} data={''} />
              </Row>
            </Column>
          </View>
          {/* Candidate Position */}
          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewCandidatePosIcon />

              <Typography.H6 fontWeight="bold">
                {t(
                  'eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition',
                )}
              </Typography.H6>
            </Row>
            <Column gap={space[2]}>
              <Row>
                <InfoField
                  label={t(
                    `eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition`,
                  )}
                  data={
                    getRenderLabel(positionList, position?.position) ?? 'N/A'
                  }
                />
                <InfoField
                  label={t(
                    `eRecruit.application.otherDetails.supervisorCandidateInformation.jobType`,
                  )}
                  data={
                    getRenderLabel(jobTypeOptions, position?.jobType) ?? 'N/A'
                  }
                />
              </Row>
              <Row>
                <InfoField
                  label={t(
                    `eRecruit.application.otherDetails.leaderInformation.ALCFWDName`,
                  )}
                  data={leader?.alcFwdName ?? 'N/A'}
                />
                <InfoField
                  label={t(
                    `eRecruit.application.otherDetails.supervisorCandidateInformation.reportingBranch`,
                  )}
                  data={getCandidateBranchNameByListAndAgentTypeAndCode(
                    allBranchOptions,
                    position?.agencyType as AgencyType,
                    position?.branchCode,
                  )}
                />
              </Row>
              <Row>
                <InfoField
                  label={t(
                    `eRecruit.application.otherDetails.supervisorCandidateInformation.introducerName`,
                  )}
                  data={data?.introducerName ?? 'N/A'}
                />

                <InfoField
                  label={t(
                    `eRecruit.application.otherDetails.supervisorCandidateInformation.introducerCode`,
                  )}
                  data={data?.introducerCode ?? 'N/A'}
                />
              </Row>
            </Column>
          </View>
          {/* Leader Info */}
          <View>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[5] }}>
              <NewLeaderIcon />

              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.otherDetails.leaderInformation`)}
              </Typography.H6>
            </Row>
            <Column gap={space[2]}>
              <Row>
                <InfoField
                  label={t(
                    `eRecruit.application.otherDetails.leaderInformation.immediateLeaderName`,
                  )}
                  data={leader?.name ? leader?.name : 'N/A'}
                />
                <InfoField
                  label={t(
                    `eRecruit.application.otherDetails.leaderInformation.immediateLeaderCode`,
                  )}
                  data={leader?.agentCode ? leader?.agentCode : 'N/A'}
                />
              </Row>
              <Row>
                <InfoField
                  label={t('application.otherDetails.agencyManagerCode')}
                  data={agencyManagerInfo?.agencyManagerCode ?? 'N/A'}
                />
                <InfoField
                  label={t('application.otherDetails.agencyManagerName')}
                  data={agencyManagerInfo?.agencyManagerName ?? 'N/A'}
                />
              </Row>
              {/* <Row>
                <InfoField
                  label={t(
                    `eRecruit.application.otherDetails.leaderInformation.immediateLeaderName`,
                  )}
                  data={leader?.name ?? 'N/A'}
                />
                <Box flex={1} />
              </Row> */}
            </Column>
          </View>
          {/* Declaration of Conflict of Interest */}
          <Box gap={space[5]}>
            <Row gap={space[2]} style={{ alignItems: 'center' }}>
              <NewDocForFormIcon />
              {/* <PictogramIcon.Bank2 size={space[10]} /> */}
              <Typography.H6 fontWeight="bold">
                {t('eRecruit.application.otherDetails.declarationOfCOI')}
              </Typography.H6>
            </Row>
            {/* Q1 */}

            <COIReviewSection data={data} questionKey="ownershipInterest">
              {data?.conflictOfInterest?.ownershipInterest
                ? data?.conflictOfInterest?.ownershipInterests?.map(
                    (item, idx) => (
                      <Box gap={space[3]}>
                        <Typography.H7 fontWeight="bold">
                          {`Record ${idx + 1}`}
                        </Typography.H7>
                        <Box gap={space[2]}>
                          <Row>
                            <InfoField
                              label={'Name of business enterprise / entity'}
                              data={item.nameOfBusiness ?? 'N/A'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField
                              label={'Nature of business'}
                              data={item.natureOfBusiness ?? 'N/A'}
                            />
                          </Row>
                          <Row>
                            <InfoField
                              label={'Name of owner'}
                              data={item.nameOfOwner ?? 'N/A'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField
                              label={'Relationship'}
                              data={item.relationship ?? 'N/A'}
                            />
                          </Row>
                          <Row>
                            <InfoField
                              label={'Percentage of ownership'}
                              data={
                                item.percentageOfOwnership == null
                                  ? 'N/A'
                                  : item.percentageOfOwnership + '%'
                              }
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField
                              label={'Date acquired'}
                              data={
                                dateFormatUtil(new Date(item.dateAcquired)) ??
                                'N/A'
                              }
                            />
                          </Row>
                        </Box>
                      </Box>
                    ),
                  )
                : null}
            </COIReviewSection>
            {/* Q2 */}
            <COIReviewSection data={data} questionKey="externalEmployment">
              {data?.conflictOfInterest?.externalEmployment
                ? data?.conflictOfInterest?.externalEmployments?.map(
                    (item, idx) => (
                      <Box gap={space[3]}>
                        <Typography.H7 fontWeight="bold">
                          {`Record ${idx + 1}`}
                        </Typography.H7>
                        <Box gap={space[2]}>
                          <Row>
                            <InfoField
                              label={'Name of business enterprise / entity'}
                              data={item.nameOfBusiness ?? 'N/A'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField
                              label={'Nature of business'}
                              data={item.natureOfBusiness ?? 'N/A'}
                            />
                          </Row>
                          <Row>
                            <InfoField
                              label={'Position'}
                              data={item.position ?? 'N/A'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField
                              label={'Details'}
                              data={item.details ?? 'N/A'}
                            />
                          </Row>
                          <Row>
                            <InfoField
                              label={'Compensation received'}
                              data={item.compensationReceived ? 'Yes' : 'No'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField label={''} data={''} />
                          </Row>
                        </Box>
                      </Box>
                    ),
                  )
                : null}
            </COIReviewSection>
            {/* Q3 */}
            <COIReviewSection
              data={data}
              questionKey="businessAffiliationInterest">
              {data?.conflictOfInterest?.businessAffiliationInterest
                ? data?.conflictOfInterest?.businessAffiliationInterests?.map(
                    (item, idx) => (
                      <Box gap={space[3]}>
                        <Typography.H7 fontWeight="bold">
                          {`Record ${idx + 1}`}
                        </Typography.H7>
                        <Box gap={space[2]}>
                          <Row>
                            <InfoField
                              label={'Name of business enterprise / entity'}
                              data={item.nameOfBusiness ?? 'N/A'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField
                              label={'Nature of business'}
                              data={item.natureOfBusiness ?? 'N/A'}
                            />
                          </Row>
                          <Row>
                            <InfoField
                              label={'Name of family member'}
                              data={item.nameOfFamilyMember ?? 'N/A'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField
                              label={'Relationship'}
                              data={item.relationship ?? 'N/A'}
                            />
                          </Row>
                          <Row>
                            <InfoField
                              label={'Position and department'}
                              data={item.positionDepartment ?? 'N/A'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField
                              label={'Date of commencement of employment'}
                              data={
                                dateFormatUtil(
                                  new Date(item.dateCommencementEmployment),
                                ) ?? 'N/A'
                              }
                              labelStyle={{ maxWidth: 172 }}
                            />
                          </Row>
                        </Box>
                      </Box>
                    ),
                  )
                : null}
            </COIReviewSection>
            {/* Q4 */}
            <COIReviewSection
              data={data}
              questionKey="relationshipGovernmentOfficial">
              {data?.conflictOfInterest?.relationshipGovernmentOfficial
                ? data?.conflictOfInterest?.relationshipGovernmentOfficials?.map(
                    (item, idx) => (
                      <Box gap={space[3]}>
                        <Typography.H7 fontWeight="bold">
                          {`Record ${idx + 1}`}
                        </Typography.H7>
                        <Box gap={space[2]}>
                          <Row>
                            <InfoField
                              label={
                                'Name of the Government Entity/ Organisation'
                              }
                              data={item.nameOfGovernment ?? 'N/A'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField
                              label={'Position and Department'}
                              data={item.positionDepartment ?? 'N/A'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                          </Row>
                          <Row>
                            <InfoField
                              label={
                                'Relationship with Immediate Family Member'
                              }
                              data={item.relationship ?? 'N/A'}
                              labelStyle={{ maxWidth: 172 }}
                            />
                            <InfoField label={''} data={''} />
                          </Row>
                        </Box>
                      </Box>
                    ),
                  )
                : null}
            </COIReviewSection>
            {/* Q5 */}
            <COIReviewSection data={data} questionKey="otherInterest">
              {data?.conflictOfInterest?.otherInterest
                ? data?.conflictOfInterest?.otherInterests?.map((item, idx) => (
                    <Box gap={space[3]}>
                      <Typography.H7 fontWeight="bold">
                        {`Record ${idx + 1}`}
                      </Typography.H7>
                      <Box gap={space[5]}>
                        <Row>
                          <InfoFieldLabelStyle style={{ flex: 22 }}>
                            Other details
                          </InfoFieldLabelStyle>
                          <InfoFieldDataStyle style={{ flex: 78 }}>
                            {item?.details ?? 'N/A'}
                          </InfoFieldDataStyle>
                        </Row>
                      </Box>
                    </Box>
                  ))
                : null}
            </COIReviewSection>
          </Box>
        </Column>
      </Container>
    </View>
  );
}

function InsuranceExperience({
  index,
  intermediaryType,
  companyName,
  rank,
  salary,
  dateAppointed,
  dateTerminaion,
  isMultiple = true,
}: {
  index?: number;
  intermediaryType: string | null;
  companyName?: string | null;
  rank?: string | null;
  salary?: number | null;
  dateAppointed: string | null;
  dateTerminaion: string | null;
  isMultiple?: boolean;
}) {
  const { space, colors, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');

  return (
    <Column>
      {isMultiple && (
        <Row pb={space[3]} gap={space[2]} alignItems="center">
          <Typography.H7 fontWeight="bold">{`${index}.`}</Typography.H7>
          <Typography.H7 fontWeight="bold">
            {companyName ?? 'N/A'}
          </Typography.H7>
        </Row>
      )}

      <Column gap={space[2]}>
        <ExperienceRow>
          <InfoField
            label={t(
              `eRecruit.application.occupationDetails.typeOfIntermediary`,
            )}
            data={intermediaryType ?? 'N/A'}
          />
          <InfoField
            label={t(`eRecruit.application.occupationDetails.companyName`)}
            data={companyName ?? 'N/A'}
          />
        </ExperienceRow>
        <ExperienceRow>
          <InfoField
            label={t(`eRecruit.application.occupationDetails.rank`)}
            data={rank ?? 'N/A'}
          />
          <InfoField
            label={t(`eRecruit.application.occupationDetails.basicSalary`)}
            data={
              t(`eRecruit.application.review.salaryWithCurrency`, { salary }) ??
              'N/A'
            }
          />
        </ExperienceRow>

        <ExperienceRow>
          <InfoField
            label={t(`eRecruit.application.occupationDetails.dateAppoint`)}
            data={dateAppointed ?? 'N/A'}
          />
          <InfoField
            label={t(`eRecruit.application.occupationDetails.dateTermination`)}
            data={dateTerminaion ?? 'N/A'}
          />
        </ExperienceRow>
      </Column>
    </Column>
  );
}

type QuestionMap = typeof questionsMap;

export function COIReviewSection({
  questionKey,
  children,
  data,
}: {
  questionKey: keyof QuestionMap;
  children?: React.ReactNode;
  data: ApplicationFormResponds | null;
}) {
  const { colors, space, borderRadius } = useTheme();
  const keysOfQuestions = Object.keys(questionsMap) as Array<keyof QuestionMap>;
  const keyIdx = keysOfQuestions?.findIndex(v => v == questionKey) ?? '--';
  return (
    <Box gap={space[3]}>
      <Typography.H7 fontWeight="bold">
        {`Q${typeof keyIdx === 'number' ? keyIdx + 1 : keyIdx} -` +
          (questionsMap?.[questionKey]?.title ?? '--')}
      </Typography.H7>
      <Box gap={space[1]}>
        <Typography.LargeBody color={colors.palette.fwdGreyDarker}>
          {questionsMap?.[questionKey]?.qBody ?? '--'}
        </Typography.LargeBody>
        <Typography.LargeBody>
          {data?.conflictOfInterest?.[questionKey] == null
            ? 'N/A'
            : data?.conflictOfInterest?.[questionKey]
            ? 'Yes'
            : 'No'}
        </Typography.LargeBody>
      </Box>
      {children}
    </Box>
  );
}

function formatDate(dateString?: string | Date | null, format?: string) {
  if (dateString) {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const dayInDD = day < 10 ? `0${day}` : `${day}`;
    const monthInDD = month < 10 ? `0${month}` : `${month}`;
    if (format === 'D/M/YYYY') {
      return `${day}/${month}/${year}`;
    } else {
      return `${dayInDD}/${monthInDD}/${year}`;
    }
  }
  return 'N/A';
}

const getRenderLabel = (
  config?: { value: string; label: string }[],
  dataValue?: string | null,
) => config?.filter(({ value }) => value === dataValue)[0]?.label ?? '';

const TitleContainer = styled(View)(
  ({ theme: { colors, space, borderRadius } }) => ({
    backgroundColor: colors.secondary,
    borderTopLeftRadius: borderRadius.medium,
    borderTopRightRadius: borderRadius.medium,
  }),
);

const Container = styled(View)(
  ({ theme: { colors, space, borderRadius } }) => ({
    borderWidth: 1,
    borderColor: colors.palette.fwdGrey[100],
    paddingHorizontal: space[6],
    paddingVertical: space[5],
    borderBottomLeftRadius: borderRadius.medium,
    borderBottomRightRadius: borderRadius.medium,
  }),
);

const LabelStyle = styled(Typography.H7)(
  ({ theme: { colors, space, borderRadius } }) => ({
    color: colors.palette.fwdGreyDarker,
    flex: 1,
  }),
);

const DataStyle = styled(Typography.H7)(({ theme: { colors } }) => ({
  justifyContent: 'flex-start',
  flex: 2,
}));

const ExperienceRow = styled(Row)(({ theme: { space } }) => ({
  gap: space[10],
  flex: 1,
}));
