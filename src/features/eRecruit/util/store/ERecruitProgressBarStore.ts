import { ObjectUtil } from 'utils';
import { create, StateCreator } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import {
  ProgressGroupWithItems,
  IdnRouteSubgroupKey,
  ProgressItem,
  ProgressSubgroup,
  RouteGroupKey,
  RouteItemKey,
  RouteSubgroupKey,
} from '../../types/progressBarTypes';
import { createWithEqualityFn } from 'zustand/traditional';

const generateRouteKey = (
  keys: [
    RouteGroupKey | undefined,
    RouteSubgroupKey | undefined,
    RouteItemKey | undefined,
  ],
) => {
  return keys.join('-');
};
type ProgressGroup = ProgressGroupWithItems<IdnRouteSubgroupKey>;

export interface ERecruitProgressBarState {
  groups: ProgressGroup[];
  groupKey?: RouteGroupKey;
  subgroupKey?: RouteSubgroupKey;
  itemKey?: RouteItemKey;
  expandedGroupKey?: string;
  group?: ProgressGroup;
  groupIndex: number;
  subgroup?: ProgressSubgroup;
  item?: ProgressItem;
  visible: boolean;
  completedMap: Record<string, boolean>;
  progressBarZIndex: number;
  updatable?: boolean;
}

export interface ERecruitProgressBarAction {
  showModal: () => void;
  hideModal: () => void;
  setProgressBarState: (
    state: Partial<ERecruitProgressBarState & ERecruitProgressBarAction>,
  ) => void;
  showTitleBar: () => void;
  setProgressBarVisible: (data: boolean) => void;
  setProgressBarCompletedMap: (data: Record<string, boolean>) => void;
  resetProgressBarState: () => void;
  buildKeys: () => Array<
    [
      RouteGroupKey | undefined,
      RouteSubgroupKey | undefined,
      RouteItemKey | undefined,
      boolean | undefined,
    ]
  >;
  markCompletedByIndex: (index: number, completed: boolean) => void;
  markCompletedByKeys: (
    keys: [
      RouteGroupKey | undefined,
      RouteSubgroupKey | undefined,
      RouteItemKey | undefined,
    ],
    completed: boolean,
  ) => void;
  updateNextKeys: (index: number, bypassDisable?: boolean) => void;
  next: (bypassDisable?: boolean) => void;
  multipleNext: (multiple: number, bypassDisable?: boolean) => void;
  markGroupCompleted: (group: ProgressGroup) => void;
  nextGroup: (bypassDisable?: boolean) => void;
  markSubgroupCompleted: (
    group: ProgressGroup,
    subgroup: ProgressSubgroup,
  ) => void;
  nextSubgroup: (bypassDisable?: boolean) => void;
  updateProgressBarZIndex: (zIndex: number) => void;
  setActiveItemKey: (itemKey: string) => void;
  onSave?: () => Promise<unknown>;
  setOnSave: (onSave?: () => Promise<unknown>) => void;
}

const createERecruitProgressBarStore: StateCreator<
  ERecruitProgressBarState & ERecruitProgressBarAction
> = (set, get) => ({
  ...ObjectUtil.cloneDeep(initialState),
  showModal: () => {
    const state = get();
    if (state.expandedGroupKey !== state.groupKey) {
      set({ expandedGroupKey: state.groupKey });
    }
    set({ visible: true });
  },
  hideModal: () => {
    set({ visible: false });
  },
  setProgressBarState: (state: Partial<ERecruitProgressBarState>) => {
    set({
      ...get(),
      ...state,
    });
  },
  showTitleBar: () => null,
  setProgressBarVisible: (visible: boolean) => {
    set({ visible });
  },
  setProgressBarCompletedMap: (completedMap: Record<string, boolean>) => {
    set({
      completedMap: {
        ...get().completedMap,
        ...completedMap,
      },
    });
  },
  resetProgressBarState: () => {
    set({
      ...get(),
      ...initialState,
    });
  },
  /**
   * Build list of key tuples (flattening groups)
   * @returns Key tuple array (group key, subgroup key, item key, disabled)[]
   */
  buildKeys: () => {
    const groups = get().groups;
    const result: Array<
      [
        RouteGroupKey | undefined,
        RouteSubgroupKey | undefined,
        RouteItemKey | undefined,
        boolean | undefined,
      ]
    > = [];
    for (const group of groups) {
      if (group.items.length > 0) {
        for (const subgroup of group.items) {
          if (Array.isArray((subgroup as ProgressSubgroup)?.items)) {
            for (const item of (subgroup as ProgressSubgroup).items) {
              result.push([
                group.routeKey,
                subgroup.routeKey as RouteSubgroupKey,
                item.routeKey as RouteItemKey,
                item.disabled || subgroup.disabled || group.disabled,
              ]);
            }
          } else {
            result.push([
              group.routeKey,
              undefined,
              subgroup.routeKey as RouteItemKey,
              subgroup.disabled || group.disabled,
            ]);
          }
        }
      } else {
        result.push([group.routeKey, undefined, undefined, group.disabled]);
      }
    }
    return result;
  },
  markCompletedByIndex: (index: number, completed: boolean) => {
    const keys = get().buildKeys()[index];
    if (Array.isArray(keys)) {
      get().markCompletedByKeys([keys[0], keys[1], keys[2]], completed);
    }
  },
  markCompletedByKeys: (
    keys: [
      RouteGroupKey | undefined,
      RouteSubgroupKey | undefined,
      RouteItemKey | undefined,
    ],
    completed: boolean,
  ) => {
    const key = generateRouteKey(keys);
    get().setProgressBarCompletedMap({ [key]: completed });
  },
  updateNextKeys: (index: number, bypassDisable = false) => {
    const { expandedGroupKey, buildKeys, setProgressBarState } = get();
    const nextKeys = buildKeys()[index];
    if (nextKeys) {
      const disabled = nextKeys[3];
      if (bypassDisable || !disabled) {
        //Do next
        setProgressBarState({
          groupKey: nextKeys[0] as RouteGroupKey,
          subgroupKey: nextKeys[1] as RouteSubgroupKey,
          itemKey: nextKeys[2] as RouteItemKey,
          expandedGroupKey:
            nextKeys[0] !== expandedGroupKey ? nextKeys[0] : expandedGroupKey,
        });
      }
    }
  },
  next: (bypassDisable = false) => {
    const {
      groupKey,
      subgroupKey,
      itemKey,
      buildKeys,
      markCompletedByIndex,
      updateNextKeys,
    } = get();
    const keys = buildKeys();
    const currentIndex = keys.findIndex(
      i => i[0] === groupKey && i[1] === subgroupKey && i[2] === itemKey,
    );
    if (currentIndex >= 0) {
      markCompletedByIndex(currentIndex, true);
      updateNextKeys(currentIndex + 1, bypassDisable);
    }
  },
  multipleNext: (multiple = 1, bypassDisable = false) => {
    const {
      groupKey,
      subgroupKey,
      itemKey,
      next,
      buildKeys,
      markCompletedByIndex,
      updateNextKeys,
    } = get();
    if (multiple === 1) {
      next();
    } else if (multiple > 1) {
      const currentIndex = buildKeys().findIndex(
        i => i[0] === groupKey && i[1] === subgroupKey && i[2] === itemKey,
      );
      if (currentIndex >= 0) {
        let index = currentIndex;
        while (index < currentIndex + multiple) {
          markCompletedByIndex(index, true);
          index += 1;
        }
        updateNextKeys(index, bypassDisable);
      }
    }
  },
  markGroupCompleted: (group: ProgressGroup) => {
    const map: Record<string, boolean> = {};
    if (!group.items || group.items.length === 0) {
      const key = generateRouteKey([group.routeKey, undefined, undefined]);
      map[key] = true;
    }
    group.items.forEach(subgroup => {
      if ((subgroup as ProgressSubgroup).items) {
        (subgroup as ProgressSubgroup).items.forEach(item => {
          const key = generateRouteKey([
            group.routeKey,
            subgroup.routeKey as RouteSubgroupKey,
            item.routeKey,
          ]);
          map[key] = true;
        });
      } else {
        const key = generateRouteKey([
          group.routeKey,
          undefined,
          subgroup.routeKey as RouteItemKey,
        ]);
        map[key] = true;
      }
    });
    get().setProgressBarCompletedMap(map);
  },
  nextGroup: (bypassDisable = false) => {
    const { groups, markGroupCompleted, buildKeys, updateNextKeys, groupKey } =
      get();
    const activeGroupIndex = groups.findIndex(i => i.routeKey === groupKey);
    const activeGroup = groups[activeGroupIndex];
    if (activeGroup) {
      markGroupCompleted(activeGroup);
      const nextGroup = groups[activeGroupIndex + 1];
      const nextGroupKey = nextGroup?.routeKey;
      const subgroup = nextGroup?.items?.[0] as ProgressSubgroup;
      const item = nextGroup?.items?.[0] as ProgressItem;
      const nextSubgroupKey = subgroup?.items ? subgroup?.routeKey : undefined;
      const nextItemKey = subgroup?.items
        ? subgroup.items[0]?.routeKey
        : item?.routeKey;
      const nextIndex = buildKeys().findIndex(
        i =>
          i[0] === nextGroupKey &&
          i[1] === nextSubgroupKey &&
          i[2] === nextItemKey,
      );
      if (nextIndex >= 0) {
        updateNextKeys(nextIndex, bypassDisable);
      }
    }
  },
  markSubgroupCompleted: (group: ProgressGroup, subgroup: ProgressSubgroup) => {
    const { setProgressBarCompletedMap } = get();
    const map: Record<string, boolean> = {};
    subgroup.items.forEach(item => {
      const key = generateRouteKey([
        group.routeKey,
        subgroup.routeKey as RouteSubgroupKey,
        item.routeKey,
      ]);
      map[key] = true;
    });
    setProgressBarCompletedMap(map);
  },
  nextSubgroup: (bypassDisable = false) => {
    const { group, subgroup, buildKeys, updateNextKeys } = get();
    if (!group || !subgroup) return;
    get().markSubgroupCompleted(group, subgroup);
    const currentIndex = buildKeys().findIndex(
      i =>
        i[0] === group.routeKey &&
        i[1] === subgroup.routeKey &&
        i[2] === subgroup.items[subgroup.items.length - 1].routeKey,
    );
    if (currentIndex >= 0) {
      updateNextKeys(currentIndex + 1, bypassDisable);
    }
  },
  updateProgressBarZIndex: (zIndex: number) => {
    set({ progressBarZIndex: zIndex });
  },
  setActiveItemKey: (itemKey: string) => {
    get().setProgressBarState({
      itemKey: itemKey as RouteItemKey,
      groupKey: get().groupKey,
      subgroupKey: get().subgroupKey,
    });
  },
  setOnSave: onSave => {
    set({ onSave });
  },
});

const initialState: ERecruitProgressBarState = {
  groups: [],
  groupKey: undefined,
  subgroupKey: undefined,
  itemKey: undefined,
  expandedGroupKey: undefined,
  group: undefined,
  groupIndex: -1,
  subgroup: undefined,
  item: undefined,
  visible: false,
  completedMap: {},
  progressBarZIndex: 10,
};

export const useERecruitProgressBarStore = createWithEqualityFn(
  immer(devtools(createERecruitProgressBarStore)),
);
