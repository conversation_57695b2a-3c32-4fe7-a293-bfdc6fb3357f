import { FieldPath } from 'react-hook-form';
import { InferType } from 'yup';
import {
  essentialInformationSchema,
  addressInformationSchema,
  backgroundDetailsSchema,
  candidatePositionSchema,
  emergencyContactSchema,
  bankInformationSchema,
  insuranceExperienceSchema,
  healthConditionSchema,
  financialConditionSchema,
  complianceAndReputationSchema,
  amlAndOtherSchema,
  declarationOfInterestSchema,
  remarkOptionSchema,
} from 'features/eRecruit/id/validations/phone';

// ======================================== Core Profile

export type EssentialInformationObj = InferType<
  typeof essentialInformationSchema
>;

export type EssentialInformationFieldPath = FieldPath<
  InferType<typeof essentialInformationSchema>
>;
// ----------------------------------------
export type BackgroundDetailsSchemaObj = InferType<
  typeof backgroundDetailsSchema
>;

export type BackgroundDetailsSFieldPath = FieldPath<
  InferType<typeof backgroundDetailsSchema>
>;
// ----------------------------------------

export type AddressInformationpObj = InferType<typeof addressInformationSchema>;

export type AddressInformationFieldPath = FieldPath<
  InferType<typeof addressInformationSchema>
>;
// ----------------------------------------

export type CandidatePositionObj = InferType<typeof candidatePositionSchema>;

export type CandidatePositionFieldPath = FieldPath<
  InferType<typeof candidatePositionSchema>
>;

// ----------------------------------------

export type EmergencyContactObj = InferType<typeof emergencyContactSchema>;

export type EmergencyContactFieldPath = FieldPath<
  InferType<typeof emergencyContactSchema>
>;

// ----------------------------------------
export type BankInformationObj = InferType<typeof bankInformationSchema>;

export type BankInformationPath = FieldPath<
  InferType<typeof bankInformationSchema>
>;

// ======================================== Declaration
// ----------------------------------------
export type InsuranceExpObj = InferType<typeof insuranceExperienceSchema>;

export type InsuranceExpPath = FieldPath<
  InferType<typeof insuranceExperienceSchema>
>;
// ----------------------------------------
export type HealthConditionObj = InferType<typeof healthConditionSchema>;

export type HealthConditionPath = FieldPath<
  InferType<typeof healthConditionSchema>
>;
// ----------------------------------------
export type FinancialConditionObj = InferType<typeof financialConditionSchema>;

export type FinancialConditionPath = FieldPath<
  InferType<typeof financialConditionSchema>
>;
// ----------------------------------------
export type ComplianceAndReputationObj = InferType<
  typeof complianceAndReputationSchema
>;

export type ComplianceAndReputationPath = FieldPath<
  InferType<typeof complianceAndReputationSchema>
>;
// ----------------------------------------
export type AmlAndOthersObj = InferType<typeof amlAndOtherSchema>;

export type AmlAndOthersPath = FieldPath<InferType<typeof amlAndOtherSchema>>;

// ----------------------------------------
export type DeclarationCOIObj = InferType<typeof declarationOfInterestSchema>;

export type DeclarationCOIPath = FieldPath<
  InferType<typeof declarationOfInterestSchema>
>;

// ----------------------------------------
export type RemarkOptionalObj = InferType<typeof remarkOptionSchema>;

export type RemarkOptionalPath = FieldPath<
  InferType<typeof remarkOptionSchema>
>;
