import { GenderCodeUnion } from 'types';
import { ReligionList } from 'types/eRecruit';
import { ObjectUtil } from 'utils';
import { StateCreator } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { createWithEqualityFn } from 'zustand/traditional';
import {
  EssentialInformationObj,
  AddressInformationpObj,
  BackgroundDetailsSchemaObj,
  CandidatePositionObj,
  EmergencyContactObj,
  BankInformationObj,
  InsuranceExpObj,
  HealthConditionObj,
  FinancialConditionObj,
  ComplianceAndReputationObj,
  AmlAndOthersObj,
  DeclarationCOIObj,
  RemarkOptionalObj,
} from './types';
import { defaultCountryCode } from 'features/eRecruit/config';

export interface DoneStatus {
  done?: boolean;
}
type WithDoneStatus<T> = T & DoneStatus;

export type EssentialInfoFromStore = WithDoneStatus<EssentialInformationObj>;
export type BackgroundDetailsFromStore =
  WithDoneStatus<BackgroundDetailsSchemaObj>;
export type AddressInfoFromStore = WithDoneStatus<AddressInformationpObj>;

export type CandidatePositionFromStore = WithDoneStatus<CandidatePositionObj>;
export type EmergencyContactFromStore = WithDoneStatus<EmergencyContactObj>;
export type BankAccountInfoFromStore = WithDoneStatus<BankInformationObj>;

export type InsuranceExperienceFromStore = WithDoneStatus<InsuranceExpObj>;
export type HealthConditionFromStore = WithDoneStatus<HealthConditionObj>;
export type FinancialConditionFromStore = WithDoneStatus<FinancialConditionObj>;
export type ComplianceAndReputationFromStore =
  WithDoneStatus<ComplianceAndReputationObj>;
export type AmlAndOtherFromStore = WithDoneStatus<AmlAndOthersObj>;
export type DeclarationOfInterestFromStore = WithDoneStatus<DeclarationCOIObj>;
export type RemarkOptionFromStore = WithDoneStatus<RemarkOptionalObj>;

export type IdentityDetailsInfo = {
  citizen: string;
  dateOfBirth: Date | undefined;
  ethnicity: string;
  firstName: string;
  gender: GenderCodeUnion | undefined;
  icNumber: string;
  lastName: string;
  title: string;
  maritalStatus: string;
  religion: ReligionList;
  passportNo: string;
  oldIcNumber: string;
  taxCode: string;
  isTaxCodeAvailable: null | boolean;
} & DoneStatus;

export type ContactDetailsInfo = {
  countryCode: string;
  phoneNumber: string;
  email: string;
  officePhoneNumber: string;
  officeNumberCountryCode: string;
} & DoneStatus;

export type Qualification = {
  educationType: string;
  insuranceCertCeilli: boolean;
  insuranceCertPce: boolean;
  otherQualifications: boolean;
  takafulCertFamily: boolean;
  takafulCertGeneral: boolean;
  otherQualificationsDesc: string;
  MFCPYearOfPassing?: string;
  FPAMYearOfPassing?: string;
} & DoneStatus;

export type SpouseInformation = {
  firstName: string;
  icNumber: string;
  lastName: string;
  numberOfDependence: string;
  occupation: string;
  oldIcNumber: string;
  passportNo: string;
} & DoneStatus;

export type AddressInformation = {
  residentialAddress: {
    line1: string;
    line2: string;
    city: string;
    state: string;
    postCode: string;
  };
  businessAddress: {
    line1: string;
    line2: string;
    city: string;
    state: string;
    postCode: string;
  };
} & DoneStatus;

export type BankInformation = {
  accountNumber: string;
  icNumber: string;
  bankName: string;
} & DoneStatus;

export type Position = {
  position: string;
  jobType: string;
  agencyType: string;
  branchCode: string;
} & DoneStatus;

export type LeaderInformation = {
  agentCode: string;
  name: string;
  alcFwdName: string;
} & DoneStatus;

export interface ERecruitState {
  registrationStagingId?: number;

  essentialInfo: EssentialInfoFromStore;
  backgroundDetails: BackgroundDetailsFromStore;
  addressInfo: AddressInfoFromStore;
  candidatePosition: CandidatePositionFromStore;
  emergencyContact: EmergencyContactFromStore;
  bankAccountInfo: BankAccountInfoFromStore;

  insuranceExperience: InsuranceExperienceFromStore;
  healthCondition: HealthConditionFromStore;
  financialCondition: FinancialConditionFromStore;
  complianceAndReputation: ComplianceAndReputationFromStore;
  amlAndOther: AmlAndOtherFromStore;
  declarationOfInterest: DeclarationOfInterestFromStore;
  remarkOptional: RemarkOptionFromStore;
}

export interface ERecruitStore extends ERecruitState {
  setEssentialInfo: (data: Partial<ERecruitState['essentialInfo']>) => void;
  setAddressInfo: (data: Partial<ERecruitState['addressInfo']>) => void;
  setBackgroundDetails: (
    data: Partial<ERecruitState['backgroundDetails']>,
  ) => void;
  setCandidatePosition: (
    data: Partial<ERecruitState['candidatePosition']>,
  ) => void;
  setEmergencyContact: (
    data: Partial<ERecruitState['emergencyContact']>,
  ) => void;
  setBankAccountInformation: (
    data: Partial<ERecruitState['bankAccountInfo']>,
  ) => void;

  setInsuranceExperience: (
    data: Partial<ERecruitState['insuranceExperience']>,
  ) => void;
  setHealthCondition: (data: Partial<ERecruitState['healthCondition']>) => void;
  setFinancialCondition: (
    data: Partial<ERecruitState['financialCondition']>,
  ) => void;
  setComplianceAndReputation: (
    data: Partial<ERecruitState['complianceAndReputation']>,
  ) => void;
  setAmlAndOther: (data: Partial<ERecruitState['amlAndOther']>) => void;
  setDeclarationOfInterest: (
    data: Partial<ERecruitState['declarationOfInterest']>,
  ) => void;
  setRemarkOptionalset: (
    data: Partial<ERecruitState['remarkOptional']>,
  ) => void;

  setRegistrationStagingId: (registrationStagingId: number) => void;

  resetERecruitmentStoreState: () => void;
}

const createERecruitStore: StateCreator<ERecruitStore> = (set, get) => ({
  ...ObjectUtil.cloneDeep(initialState),
  setEssentialInfo: data => {
    const currentStore = get();
    set(() => ({
      essentialInfo: {
        ...currentStore.essentialInfo,
        ...data,
      },
    }));
  },
  setBackgroundDetails: data => {
    const currentStore = get();
    set(() => ({
      backgroundDetails: {
        ...currentStore.backgroundDetails,
        ...data,
      },
    }));
  },

  setAddressInfo: data => {
    const currentStore = get();
    set(() => ({
      addressInfo: {
        ...currentStore.addressInfo,
        ...data,
      },
    }));
  },
  setCandidatePosition: data => {
    const currentStore = get();
    set(() => ({
      candidatePosition: {
        ...currentStore.candidatePosition,
        ...data,
      },
    }));
  },

  setEmergencyContact: data => {
    const currentStore = get();
    set(() => ({
      emergencyContact: {
        ...currentStore.emergencyContact,
        ...data,
      },
    }));
  },
  setBankAccountInformation: data => {
    const currentStore = get();
    set(() => ({
      bankAccountInfo: {
        ...currentStore.bankAccountInfo,
        ...data,
      },
    }));
  },
  setInsuranceExperience: data => {
    const currentStore = get();
    set(() => ({
      insuranceExperience: {
        ...currentStore.insuranceExperience,
        ...data,
      },
    }));
  },
  setHealthCondition: data => {
    const currentStore = get();
    set(() => ({
      healthCondition: {
        ...currentStore.healthCondition,
        ...data,
      },
    }));
  },
  setFinancialCondition: data => {
    const currentStore = get();
    set(() => ({
      financialCondition: {
        ...currentStore.financialCondition,
        ...data,
      },
    }));
  },
  setComplianceAndReputation: data => {
    const currentStore = get();
    set(() => ({
      complianceAndReputation: {
        ...currentStore.complianceAndReputation,
        ...data,
      },
    }));
  },
  setAmlAndOther: data => {
    const currentStore = get();
    set(() => ({ amlAndOther: { ...currentStore.amlAndOther, ...data } }));
  },
  setDeclarationOfInterest: data => {
    const currentStore = get();
    set(() => ({
      declarationOfInterest: { ...currentStore.declarationOfInterest, ...data },
    }));
  },
  setRemarkOptionalset: data => {
    const currentStore = get();
    set(() => ({
      remarkOptional: { ...currentStore.remarkOptional, ...data },
    }));
  },

  setRegistrationStagingId: registrationStagingId => {
    set(() => ({
      ...get(),
      registrationStagingId,
    }));
  },

  resetERecruitmentStoreState: () => {
    set({
      ...get(),
      ...ObjectUtil.cloneDeep(initialState),
      registrationStagingId: undefined,
    });
  },
});

const addressInitState = {
  line1: '',
  line2: '',
  neighborhoodAssociation: '',
  communityAssociation: '',
  subDistrict: '',
  district: '',
  province: '',
  city: '',
  postCode: '',
} satisfies ERecruitState['addressInfo']['contact']['address'];

const initialState: ERecruitState = {
  registrationStagingId: undefined,

  essentialInfo: {
    done: false,
    identity: {
      fullName: '',
      gender: '',
      dateOfBirth: null as any,
    },
    contact: {
      countryCode: defaultCountryCode,
      phoneNumber: '',
      email: '',
      officeNumberCountryCode: defaultCountryCode,
      officePhoneNumber: '',
    },
  },
  backgroundDetails: {
    done: false,
    identity: {
      identity: '',
      idNumber: '',
      birthPlace: '',
      religion: '',
      maritalStatus: '',
      numberOfDependence: '',
    },
    personalInformation: {
      education: '',
      industry: '',
      presentOccupation: '',
      npwp: '',
    },
  },
  addressInfo: {
    done: false,
    contact: {
      isBusinessSameAsResidentialAddress: true,
      address: addressInitState,
      businessAddress: addressInitState,
    },
  },

  candidatePosition: {
    done: false,
    candidatePosition: { isHaveFinancingProgram: false },
  },

  emergencyContact: {
    done: false,
  },

  bankAccountInfo: {
    done: false,
  },
  insuranceExperience: { done: false },
  healthCondition: { done: false },
  financialCondition: { done: false },
  complianceAndReputation: { done: false },
  amlAndOther: { done: false },
  declarationOfInterest: { done: false },
  remarkOptional: { done: false },
};

export const useERecruitStore = createWithEqualityFn(
  immer(devtools(createERecruitStore)),
);
