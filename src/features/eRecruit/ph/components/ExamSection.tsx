import React from 'react';
import _ from 'lodash';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Column, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { RootStackParamList } from 'types';
import NoRecordSVG from 'features/policy/assets/NoRecordSVG';

const MOCK_DATA = [
  {
    LicenseTypeID: 3,
    LicenseType: 'Variable Life License',
    ExamName: 'ExamTestCamilleExamTestCamilleTest',
    ExamPlatformID: 6,
    ExamPlatform: 'IC Offsite (Face to Face)',
    Venue: 'BGC',
    ExamDateTime: '30 Aug 2025',
  },
  {
    LicenseTypeID: 2,
    LicenseType: 'Life',
    ExamName: 'Exam LifePro',
    ExamPlatformID: 4,
    ExamPlatform: 'IC Online',
    Venue: 'Makati',
    ExamDateTime: '15 Sep 2025',
  },
  {
    LicenseTypeID: 1,
    LicenseType: 'Traditional Life License',
    ExamName: 'Exam Generalist',
    ExamPlatformID: 3,
    ExamPlatform: 'IC Onsite',
    Venue: 'Ortigas',
    ExamDateTime: '10 Oct 2025',
  },
  {
    LicenseTypeID: 4,
    LicenseType: 'Traditional Life License',
    ExamName: 'Exam CompositeX',
    ExamPlatformID: 5,
    ExamPlatform: 'IC Offsite (Face to Face)',
    Venue: 'Cebu',
    ExamDateTime: '22 Nov 2025',
  },
  {
    LicenseTypeID: 5,
    LicenseType: 'Health',
    ExamName: 'Exam HealthPlus',
    ExamPlatformID: 2,
    ExamPlatform: 'IC Online',
    Venue: 'Davao',
    ExamDateTime: '5 Dec 2025',
  },
];

export default function ExamSection() {
  const { t } = useTranslation('eRecruit');
  const { colors, space, borderRadius } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  return (
    <>
      <Row alignItems="center" justifyContent="space-between">
        <Typography.H7 fontWeight="bold" children={'Exam'} />
        <TouchableOpacity
          onPress={() => navigation.navigate('ERecruitExamTable')}>
          <Typography.Label
            fontWeight="medium"
            children={'View all'}
            color={colors.palette.fwdAlternativeOrange[100]}
          />
        </TouchableOpacity>
      </Row>

      <Row
        mt={space[4]}
        px={space[6]}
        py={space[3]}
        borderTopRadius={borderRadius.large}
        bgColor={colors.primary}>
        <Typography.Label
          fontWeight="medium"
          children={'Exam name'}
          color={colors.onPrimary}
          style={{ flex: 2 }}
        />
        <Typography.Label
          fontWeight="medium"
          children={'Date and time'}
          color={colors.onPrimary}
          style={{ flex: 1, textAlign: 'right' }}
        />
      </Row>
      <Column
        px={space[4]}
        py={space[1]}
        borderBottomRadius={borderRadius.large}
        bgColor={colors.background}>
        {_.isEmpty(MOCK_DATA) ? (
          <Column alignItems="center" pt={space[2]} pb={space[4]}>
            <NoRecordSVG />
            <Typography.Body
              children={'No available exams'}
              color={colors.palette.fwdGreyDarker}
            />
          </Column>
        ) : (
          <>
            {MOCK_DATA?.map((item, index) => {
              const isLastItem = item === _.last(MOCK_DATA);
              return (
                <Column key={`exam_${index}`}>
                  <Row px={space[2]} py={space[3]} bgColor={colors.background}>
                    <Typography.Label
                      children={item?.ExamName}
                      color={colors.onSurface}
                      style={{ flex: 2, paddingRight: space[0] }}
                    />
                    <Typography.Label
                      children={item?.ExamDateTime}
                      color={colors.onSurface}
                      style={{ flex: 1, textAlign: 'right' }}
                    />
                  </Row>
                  {!isLastItem && (
                    <Box flex={1} h={1} bgColor={colors.palette.fwdGrey[100]} />
                  )}
                </Column>
              );
            })}
          </>
        )}
      </Column>
    </>
  );
}
