import { useTheme } from '@emotion/react';
import { AnimatedFlashList } from '@shopify/flash-list';
import { Column, Icon, Label, LargeLabel, Row } from 'cube-ui-components';
import { SolidArrow } from 'features/reportGeneration/assets/SolidArrowSVG';
import _ from 'lodash';
import React, { memo, useCallback, useMemo, useRef, useState } from 'react';
import {
  Animated,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { ReportItem } from 'types/report';
import { ExamItem } from '../../types';
import { DATA_GRID_LOADING_HEADER, Placeholder } from './Placeholder';
import {
  CELL_HEIGHT,
  CELL_WIDTH,
  CELL_WIDTH_FACTOR_LARGE,
  CELL_WIDTH_FACTOR_SMALL,
  HEADER_CELL_HEIGHT,
  reorderObjectKeys,
  type Header,
} from './utils';

export default function ExamTable({
  darkMode,
  isLoading,
  isError,
  data = [],
  freezeHeader = [],
  headers = [],
  cellRenderOrder = [],
  setShowToolbar,
}: {
  darkMode: boolean;
  isLoading: boolean;
  isError: boolean;
  data?: ExamItem[];
  freezeHeader: Header[];
  headers: Header[];
  cellRenderOrder: string[];
  setShowToolbar: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const { colors } = useTheme();

  const prevScrollY = useRef(0);
  const freezeScrollY = useRef(new Animated.Value(0)).current;
  const scrollY = useRef(new Animated.Value(0)).current;

  const freezeColScrollRef = useRef<any>(null);
  const colScrollRef = useRef<any>(null);

  const activeColRef = useRef<'freeze' | 'normal' | undefined>();

  const onFreezeScroll = useMemo(
    () =>
      Animated.event(
        [{ nativeEvent: { contentOffset: { y: freezeScrollY } } }],
        {
          useNativeDriver: true,
          listener: (event: NativeSyntheticEvent<NativeScrollEvent>) => {
            const offsetY = event.nativeEvent.contentOffset.y;
            if (activeColRef.current === 'freeze' && colScrollRef.current) {
              colScrollRef.current.scrollToOffset({
                offset: offsetY,
                animated: false,
              });
            }

            if (offsetY < prevScrollY.current || offsetY <= 0) {
              setShowToolbar(true);
            } else if (offsetY >= prevScrollY.current) {
              setShowToolbar(false);
            }

            prevScrollY.current = offsetY;
          },
        },
      ),
    [freezeScrollY, setShowToolbar],
  );

  const onScroll = useMemo(
    () =>
      Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
        useNativeDriver: true,
        listener: (event: NativeSyntheticEvent<NativeScrollEvent>) => {
          const offsetY = event.nativeEvent.contentOffset.y;
          if (activeColRef.current === 'normal' && freezeColScrollRef.current) {
            freezeColScrollRef.current.scrollToOffset({
              offset: offsetY,
              animated: false,
            });
          }
        },
      }),
    [scrollY],
  );

  const onFreezeScrollBeginDrag = useCallback(
    () => (activeColRef.current = 'freeze'),
    [],
  );
  const onScrollBeginDrag = useCallback(
    () => (activeColRef.current = 'normal'),
    [],
  );
  const onScrollAnimationEnd = useCallback(
    () => (activeColRef.current = undefined),
    [],
  );

  /**
   * Sorting
   */
  const [sortedColumn, setSortedColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSort = (columnType: string) => {
    if (sortedColumn === columnType) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortedColumn(columnType);
      setSortDirection('desc');
    }
  };

  const getSortKey = (column: string) => {
    // Handling empty space and upper/lower case for sorting
    if (
      column === 'ExamName' ||
      column === 'LicenseType' ||
      column === 'ExamPlatform' ||
      column === 'Venue'
    ) {
      return (data: ExamItem) => data?.[column]?.trim()?.toLowerCase();
    }
    return column;
  };

  const sortedData = useMemo(() => {
    if (!sortedColumn) return data;

    const sortKey = getSortKey(sortedColumn);

    return _.orderBy(data, [sortKey, sortedColumn], [sortDirection]);
  }, [data, sortedColumn, sortDirection]);

  if (isLoading) {
    return (
      <Column flex={1}>
        <MemoizedHeaderComponent
          darkMode={darkMode}
          headerArray={DATA_GRID_LOADING_HEADER}
          showSortIcon={false}
        />
        <Placeholder.Loading />
      </Column>
    );
  }

  if (isError) {
    return (
      <Column flex={1}>
        <MemoizedHeaderComponent
          darkMode={darkMode}
          headerArray={DATA_GRID_LOADING_HEADER}
          showSortIcon={false}
        />
        <Placeholder.LoadingError />
      </Column>
    );
  }

  if (_.isEmpty(data)) {
    return <Placeholder.EmptyRecord />;
  }

  return (
    <Row flex={1}>
      <Column
        style={{
          backgroundColor: colors.palette.fwdGreyDark[100],
          shadowColor: colors.palette.fwdGreyDark[100],
          shadowOffset: { width: 3, height: -1 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
          zIndex: 1,
        }}>
        <MemoizedHeaderComponent
          darkMode={darkMode}
          headerArray={freezeHeader}
          showSortIcon
          onHeaderPress={handleSort}
          sortedColumn={sortedColumn}
          sortDirection={sortDirection}
        />
        <AnimatedFlashList
          ref={freezeColScrollRef}
          data={sortedData}
          keyExtractor={(item, index) => `freezeCol_${item}_${index}`}
          renderItem={({ item, index }) => (
            <MemoizedFreezeCell item={item} rowIndex={index} />
          )}
          ItemSeparatorComponent={() => <Separator />}
          ListFooterComponent={() => (
            <>
              <Separator />
              <Row h={CELL_HEIGHT * 2} />
            </>
          )}
          nestedScrollEnabled
          bounces={false}
          showsVerticalScrollIndicator={false}
          onScroll={onFreezeScroll}
          scrollEventThrottle={16}
          estimatedItemSize={CELL_HEIGHT + 1} // add 1 to the height to account for the separator
          disableAutoLayout
          removeClippedSubviews
          onScrollBeginDrag={onFreezeScrollBeginDrag}
          onScrollAnimationEnd={onScrollAnimationEnd}
        />
      </Column>

      <ScrollView
        horizontal
        nestedScrollEnabled
        bounces={false}
        showsHorizontalScrollIndicator={false}>
        <Column>
          <MemoizedHeaderComponent
            darkMode={darkMode}
            headerArray={headers}
            showSortIcon
            onHeaderPress={handleSort}
            sortedColumn={sortedColumn}
            sortDirection={sortDirection}
          />
          <AnimatedFlashList
            ref={colScrollRef}
            data={sortedData}
            keyExtractor={(item, index) => `col_${item}_${index}`}
            renderItem={({ item, index }) => (
              <MemoizedRowComponent
                item={item}
                rowIndex={index}
                cellRenderOrder={cellRenderOrder}
              />
            )}
            ItemSeparatorComponent={() => <Separator />}
            ListFooterComponent={() => (
              <>
                <Separator />
                <Row h={CELL_HEIGHT * 2} />
              </>
            )}
            nestedScrollEnabled
            bounces={false}
            showsVerticalScrollIndicator={false}
            onScroll={onScroll}
            scrollEventThrottle={16}
            estimatedItemSize={CELL_HEIGHT + 1} // add 1 to the height to account for the separator
            disableAutoLayout
            removeClippedSubviews
            onScrollBeginDrag={onScrollBeginDrag}
            onScrollAnimationEnd={onScrollAnimationEnd}
          />
        </Column>
      </ScrollView>
    </Row>
  );
}

const MemoizedHeaderComponent = memo(function HeaderComponent({
  darkMode,
  headerArray,
  showSortIcon,
  onHeaderPress,
  sortedColumn,
  sortDirection,
}: {
  darkMode: boolean;
  headerArray: Header[];
  showSortIcon?: boolean;
  onHeaderPress?: (columnType: string) => void;
  sortedColumn?: string | null;
  sortDirection?: 'asc' | 'desc';
}) {
  const { colors, space } = useTheme();

  return (
    <Row bgColor={darkMode ? colors.palette.fwdDarkGreen[100] : colors.primary}>
      {headerArray?.map(({ type, title }) => {
        const width = factorizeCellWidth(type);
        return (
          <TouchableOpacity
            key={type}
            onPress={onHeaderPress && (() => onHeaderPress(type))}
            style={{
              flexDirection: 'row',
              width: width,
              height: HEADER_CELL_HEIGHT,
              padding: space[3],
              backgroundColor: darkMode
                ? colors.palette.fwdDarkGreen[100]
                : colors.primary,
              alignItems: 'center',
              gap: space[1],
            }}>
            <Label children={title} color={colors.onPrimary} />

            {showSortIcon && sortedColumn === type ? (
              sortDirection === 'asc' ? (
                darkMode ? (
                  <Icon.SolidArrowUp />
                ) : (
                  <SolidArrow.UpLight />
                )
              ) : darkMode ? (
                <Icon.SolidArrowDown />
              ) : (
                <SolidArrow.DownLight />
              )
            ) : darkMode ? (
              <Icon.SolidArrowUp fill={colors.palette.fwdDarkGreen[50]} />
            ) : (
              <SolidArrow.UpLight fill={colors.palette.fwdOrange[50]} />
            )}
          </TouchableOpacity>
        );
      })}
    </Row>
  );
});

const MemoizedRowComponent = memo(function RowComponent({
  item,
  rowIndex,
  cellRenderOrder,
}: {
  item: ReportItem;
  rowIndex: number;
  cellRenderOrder: string[];
}) {
  const { colors } = useTheme();

  const orderedItem = reorderObjectKeys(item, cellRenderOrder) ?? {};

  return (
    <Row
      bgColor={
        rowIndex % 2 === 0 ? colors.background : colors.palette.fwdGrey[20]
      }>
      {Object.entries(orderedItem).map(([key, value], index) => {
        return <MemoizedCell key={index} dataKey={key} data={value} />;
      })}
    </Row>
  );
});

const MemoizedFreezeCell = memo(function FreezeCell({
  item,
  rowIndex,
}: {
  item: ExamItem;
  rowIndex: number;
}) {
  const { space, colors } = useTheme();

  return (
    <Row
      w={CELL_WIDTH}
      h={CELL_HEIGHT}
      py={space[3]}
      alignItems="center"
      bgColor={
        rowIndex % 2 === 0 ? colors.background : colors.palette.fwdGrey[20]
      }>
      <Label
        children={String(rowIndex + 1)}
        color={colors.palette.fwdDarkGreen[50]}
        style={{ width: space[10], textAlign: 'center' }}
      />
      <LargeLabel
        fontWeight="medium"
        children={item?.ExamName ?? '--'}
        style={{ width: CELL_WIDTH - space[10] - space[3] }} // space[10] is the width of the row number width, space[3] is the padding right
        numberOfLines={2}
        ellipsizeMode="tail"
      />
    </Row>
  );
});

const MemoizedCell = memo(function Cell({
  dataKey,
  data,
}: {
  dataKey: string;
  data: number | string;
}) {
  const { space } = useTheme();

  const width = factorizeCellWidth(dataKey);

  return (
    <Column w={width} h={CELL_HEIGHT} p={space[3]} justifyContent="center">
      <LargeLabel
        children={data ?? '--'}
        numberOfLines={2}
        ellipsizeMode="tail"
      />
    </Column>
  );
});

const factorizeCellWidth = (type: string) => {
  if (type === 'ExamDateTime') return CELL_WIDTH * CELL_WIDTH_FACTOR_SMALL;

  if (type === 'LicenseType' || type === 'ExamPlatform' || type === 'Venue')
    return CELL_WIDTH * CELL_WIDTH_FACTOR_LARGE;

  return CELL_WIDTH;
};

const Separator = () => {
  const { colors } = useTheme();
  return <Row h={1} bgColor={colors.palette.fwdGrey[100]} />;
};
