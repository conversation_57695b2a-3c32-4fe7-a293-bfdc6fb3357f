// Styles - Assuming each cell has a fixed width for simplicity
export const HEADER_CELL_HEIGHT = 52;
export const CELL_WIDTH = 180;
export const CELL_HEIGHT = 64;
export const TABLET_CELL_WIDTH = 280;

export const CELL_WIDTH_FACTOR_SMALL = 0.75; // For branch and status cell
export const CELL_WIDTH_FACTOR_LARGE = 1.25; // For product cell

// Header configuration
export interface Header {
  type: string;
  title: string;
}

// Reorder object keys by array
export const reorderObjectKeys = (
  obj: Record<string, any>,
  order: string[],
) => {
  if (!obj || !order) return;

  const orderedObj: Record<string, any> = {};

  order?.forEach(key => {
    orderedObj[key] = Object.prototype.hasOwnProperty.call(obj, key)
      ? obj[key]
      : undefined;
  });

  return orderedObj;
};

// License type
export const licenseTypeLabeMapping: { [key: number]: string } = {
  0: 'All',
  1: 'Dual',
  2: 'Traditional',
  3: 'Variable',
};

// Exam platform -  PlatformName mapping by ExamPlatformID
export const examPlatformLabelMapping = (
  arr: { ExamPlatformID?: number; PlatformName?: string }[],
  id: number,
) => {
  const found = arr?.find(item => item?.ExamPlatformID === id);
  return found
    ? found?.PlatformName === 'all'
      ? 'All'
      : found?.PlatformName
    : undefined;
};
