import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Body, Column, H8, Icon, Row, SmallBody } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useTranslation } from 'react-i18next';
import { ScrollView, View } from 'react-native';
import { examPlatformLabelMapping, licenseTypeLabeMapping } from './utils';
import { useGetMaintenanceTableByTableType } from 'features/eRecruit/hooks/ph/useGetMaintenanceTableByTableType';

function MainContainer({ children }: { children: React.ReactNode }) {
  const { space } = useTheme();
  return (
    <Column py={space[3]} gap={space[2]}>
      {children}
    </Column>
  );
}

function ScrollableContainer({ children }: { children: React.ReactNode }) {
  const { space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  return (
    <ScrollView
      horizontal
      bounces={!isTabletMode}
      showsHorizontalScrollIndicator={false}
      // style={{ maxHeight: BAR_HEIGHT }}
      contentContainerStyle={{ paddingHorizontal: space[3], gap: space[1] }}>
      {children}
    </ScrollView>
  );
}

/**
 * Reusable filter button component for the toolbar
 * For both mobile and tablet
 */
function ReportFilterButton({
  title,
  label,
  disabled,
  focused,
  onPress,
}: {
  title: string;
  label: string | undefined;
  focused?: boolean;
  disabled?: boolean;
  onPress: () => void;
}) {
  const { colors, space, sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  return (
    <ButtonContainer
      disabled={disabled}
      focused={focused}
      onPress={onPress}
      style={{
        flex: 1,
        paddingLeft: space[3],
        paddingRight: space[2],
        paddingVertical: space[2],
        justifyContent: 'center',
      }}>
      <Row alignItems="center" justifyContent="space-between" gap={space[1]}>
        <View
          style={{
            flexDirection: isTabletMode ? 'row' : 'column',
            alignItems: isTabletMode ? 'center' : undefined,
            gap: isTabletMode ? space[2] : 0,
          }}>
          <SmallBody
            children={title}
            color={
              disabled
                ? colors.palette.fwdGrey[100]
                : colors.palette.fwdDarkGreen[100]
            }
          />
          <Body
            fontWeight="medium"
            children={label ?? '--'}
            color={
              disabled
                ? colors.palette.fwdGrey[100]
                : colors.palette.fwdDarkGreen[100]
            }
          />
        </View>

        <Icon.ChevronDown
          fill={
            focused
              ? colors.primary
              : disabled
              ? colors.palette.fwdGrey[100]
              : colors.palette.fwdDarkGreen[100]
          }
          size={sizes[4]}
        />
      </Row>
    </ButtonContainer>
  );
}

/**
 * Toolbar buttons
 */
function LicenseType({
  licenseType,
  onPress,
}: {
  licenseType: number; // LicenseType is a number representing the type
  onPress: () => void;
}) {
  return (
    <ReportFilterButton
      title={'License type'}
      label={licenseTypeLabeMapping?.[licenseType] ?? '--'}
      onPress={onPress}
    />
  );
}

function ExamPlatform({
  examPlatform,
  onPress,
}: {
  examPlatform: number; // ExamPlatform is a number representing the type
  onPress: () => void;
}) {
  const { data: EXAM_PLATFORM } =
    useGetMaintenanceTableByTableType('exam-platform');
  const label = examPlatformLabelMapping(EXAM_PLATFORM ?? [], examPlatform);

  return (
    <ReportFilterButton
      title={'Exam platform'}
      label={label ?? '--'}
      onPress={onPress}
    />
  );
}

/**
 * Result(s) count
 */
function ResultCount({ count }: { count?: number }) {
  const { colors, space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { t } = useTranslation('reportGeneration');
  return (
    <H8
      children={`${t('totalResult')} (${count ?? '--'})`}
      color={colors.palette.fwdGreyDarkest}
      style={{ paddingLeft: isTabletMode ? 0 : space[3] }}
    />
  );
}

export const Toolbar = {
  MainContainer,
  ScrollableContainer,
  //
  ReportFilterButton,
  LicenseType,
  ExamPlatform,
  //
  ResultCount,
};

const ButtonContainer = styled.TouchableOpacity<{
  focused?: boolean;
}>(({ theme, focused }) => ({
  // height: BTN_HEIGHT,
  borderWidth: focused ? 2 : 1,
  borderColor: focused
    ? theme.colors.primary
    : theme.colors.palette.fwdGrey[100],
  borderRadius: theme.borderRadius.small,
  backgroundColor: focused
    ? theme.colors.primaryVariant3
    : theme.colors.background,
  margin: focused ? 0 : 1, // UI handling to prevent border width change
}));
