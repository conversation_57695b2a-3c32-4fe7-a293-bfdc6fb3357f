/**
 * This file contains the configuration for the data grid (table) in SRT exam.
 */

export const FREEZE_HEADER = [
  {
    type: 'ExamName',
    title: 'Exam name',
  },
];

export const HEADERS = [
  {
    type: 'ExamDateTime',
    title: 'Date and time',
  },
  {
    type: 'LicenseType',
    title: 'License type',
  },
  {
    type: 'ExamPlatform',
    title: 'Exam platform',
  },
  {
    type: 'Venue',
    title: 'Venue',
  },
];

export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);
