import { useTheme } from '@emotion/react';
import {
  ActionPanel,
  Column,
  RadioButton,
  RadioButtonGroup,
  Row,
} from 'cube-ui-components';
import { useGetMaintenanceTableByTableType } from 'features/eRecruit/hooks/ph/useGetMaintenanceTableByTableType';
import _ from 'lodash';
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { licenseTypeLabeMapping } from '../Table/utils';

/**
 * For mobile only
 * Using in exam table
 */
export default function LicenseTypeActionPanel({
  visible,
  handleClose,
  value,
  updateValue,
}: {
  visible: boolean;
  handleClose: () => void;
  value: number;
  updateValue: (value: number) => void;
}) {
  const { t } = useTranslation('reportGeneration');
  const { colors, space } = useTheme();
  const { bottom } = useSafeAreaInsets();

  // Get maintenance tables (options)
  const { data: LICENSE_TYPE } =
    useGetMaintenanceTableByTableType('license-type');

  return (
    <ActionPanel
      visible={visible}
      handleClose={() => handleClose()}
      title={'License type'}
      contentContainerStyle={{
        padding: 0,
        paddingBottom: Platform.select({
          android: space[4] + bottom,
          ios: 0,
        }),
      }}>
      <Column p={space[4]}>
        <RadioButtonGroup
          value={value}
          onChange={value => {
            updateValue(value);
            handleClose();
          }}>
          {_.orderBy(LICENSE_TYPE ?? [], ['id'], ['asc'])?.map(
            ({ id }, index, arr) => {
              if (id === 1) return null; // Skip Dual License Type
              return (
                <Fragment key={`licenseType_${id}`}>
                  <RadioButton
                    value={id}
                    label={licenseTypeLabeMapping[id as number]}
                    labelStyle={{ paddingVertical: space[2] }}
                  />
                  {arr?.length - 1 > index && (
                    <Row
                      h={1}
                      bgColor={colors.palette.fwdGrey[100]}
                      my={space[3]}
                    />
                  )}
                </Fragment>
              );
            },
          )}
        </RadioButtonGroup>
      </Column>
    </ActionPanel>
  );
}
