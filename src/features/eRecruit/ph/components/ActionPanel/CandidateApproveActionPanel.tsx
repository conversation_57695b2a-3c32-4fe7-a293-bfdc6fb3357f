import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { queryClient } from 'api/RootQueryClient';
import {
  ActionPanel,
  ActionPanelProps,
  Chip,
  Column,
  H6,
  H7,
  Icon,
  RadioButton,
  RadioButtonGroup,
  Row,
  addToast,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import { useGetMaintenanceTableByTableType } from 'features/eRecruit/hooks/ph/useGetMaintenanceTableByTableType';
import {
  QUERY_KEY_APPROVED_RECRUIT_LIST,
  QUERY_KEY_TODO_RECRUIT_LIST,
} from 'features/eRecruit/hooks/ph/useGetRecruitsList';
import { usePostRecruitApproval } from 'features/eRecruit/hooks/ph/usePostRecruitApproval';
import {
  ApplicationInfo,
  RecruitApprovalRequestBody,
} from 'features/eRecruit/ph/types';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Platform,
  TouchableOpacity,
  View,
  useWindowDimensions,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';
import { CommonModal, ModalFormAction } from '../CommonModal';

type PanelProps = ActionPanelProps & {
  visible: boolean;
  applicationInfo: ApplicationInfo;
};

/**
 *  For both mobile and tablet
 */
export default function CandidateApproveActionPanel({
  visible,
  handleClose,
  applicationInfo,
}: PanelProps) {
  const { t } = useTranslation('eRecruit');
  const { colors, space, borderRadius } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { height } = useWindowDimensions();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  // Get maintenance tables (options)
  const { data: RECRUIT_SOURCE } =
    useGetMaintenanceTableByTableType('recruit-source');
  const { data: RECRUIT_SOURCE_PROGRAMS } = useGetMaintenanceTableByTableType(
    'recruit-source-programs',
  );
  const { data: AGENT_TYPE } = useGetMaintenanceTableByTableType('agent-type');
  const { data: DESIGNATION } =
    useGetMaintenanceTableByTableType('designation');

  // Mutation for approving recruit
  const { mutate, isLoading } = usePostRecruitApproval();
  const [recruitSource, setRecruitSource] = useState<number>(1);
  const [recruitSourceProgram, setRecruitSourceProgram] = useState<number>();
  const [agentType, setAgentType] = useState<number>(1);
  const [designation, setDesignation] = useState<number>();

  const isRecruitSourceOrganic = recruitSource === 1; // 1 is organic, 2 is inorganic

  const onRecruitSourceChange = (value: number) => {
    setRecruitSource(value);
    isRecruitSourceOrganic && setRecruitSourceProgram(undefined);
  };

  const resetForm = () => {
    setRecruitSource(1);
    setRecruitSourceProgram(undefined);
    setAgentType(1);
    setDesignation(undefined);
  };

  const onClose = () => {
    resetForm();
    handleClose();
  };

  const isValid = isRecruitSourceOrganic
    ? recruitSource && agentType && designation
    : recruitSource && recruitSourceProgram && agentType && designation;

  const onPressConfirm = () => {
    if (!isValid) return;

    const data: RecruitApprovalRequestBody = {
      case: {
        recruitment: {
          recruiterNo: applicationInfo?.recruiterID,
          recruitID: applicationInfo?.recruitID,
          isApprove: 'True',
          recruitSourceID: recruitSource,
          agentTypeID: agentType,
          designationID: designation,
          ...(recruitSourceProgram
            ? { recruitSourceProgramID: recruitSourceProgram }
            : {}),
        },
      },
    };

    mutate(data, {
      onSuccess: () => {
        addToast([
          {
            message: t('candidates.approve.successToastMessage'),
            IconLeft: <Icon.Tick />,
          },
        ]);
        onClose();

        queryClient.invalidateQueries({
          queryKey: [QUERY_KEY_TODO_RECRUIT_LIST],
        });
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEY_APPROVED_RECRUIT_LIST],
        });

        navigation.canGoBack() && navigation.goBack();
      },
      onError: () => {
        addToast(
          [
            {
              message: t('eRecruit.pleaseTryAgainLater'),
            },
          ],
          undefined,
          true,
        );
        onClose();
      },
    });
  };

  /**
   * Tablet mode: Modal
   */
  if (isTabletMode)
    return (
      <CommonModal
        visible={visible}
        width={'80%'}
        onClose={() => handleClose()}>
        <H6
          fontWeight="bold"
          children={t('candidates.approve.title')}
          style={{ paddingBottom: space[5] }}
        />

        <View style={{ gap: space[6] }}>
          <View style={{ gap: space[3] }}>
            <H7
              fontWeight="bold"
              children={t('candidates.approve.recruitSource')}
            />
            <RadioButtonGroup
              value={recruitSource}
              onChange={value => onRecruitSourceChange(value)}>
              <Row>
                {RECRUIT_SOURCE?.map(item => {
                  return (
                    <RadioButton
                      key={'recruitSource_' + item?.id}
                      value={item?.id}
                      label={item?.name}
                      style={{ flex: 1 }}
                    />
                  );
                })}
              </Row>
            </RadioButtonGroup>
          </View>

          {/* 2 */}
          {recruitSource === 2 && (
            <View style={{ gap: space[3] }}>
              <H7
                fontWeight="bold"
                color={colors.palette.fwdGreyDarker}
                children={t('candidates.approve.selectOneOfTheFollowing') + ':'}
              />
              <Row gap={space[1]}>
                {RECRUIT_SOURCE_PROGRAMS?.map(item => {
                  const isFocused = recruitSourceProgram === item?.id;
                  return (
                    <Chip
                      key={'recruitSourceProgram_' + item?.id}
                      focus={isFocused}
                      onPress={() => setRecruitSourceProgram(item?.id)}
                      label={item?.name}
                      size="large"
                    />
                  );
                })}
              </Row>
            </View>
          )}

          {/* 3 */}
          <View style={{ gap: space[3] }}>
            <H7
              fontWeight="bold"
              children={t('candidates.approve.agentType')}
            />
            <RadioButtonGroup value={agentType} onChange={setAgentType}>
              <Row>
                {AGENT_TYPE?.map(item => {
                  return (
                    <RadioButton
                      key={'agentType_' + item?.id}
                      value={item?.id}
                      label={item?.name}
                      style={{ flex: 1 }}
                    />
                  );
                })}
              </Row>
            </RadioButtonGroup>
          </View>

          {/* 4 */}
          <View style={{ gap: space[3] }}>
            <H7
              fontWeight="bold"
              children={t('candidates.approve.designation')}
            />
            <RadioButtonGroup value={designation} onChange={setDesignation}>
              <Row flexWrap="wrap">
                {DESIGNATION?.map(item => {
                  return (
                    <RadioButton
                      key={'designation_' + item?.id}
                      value={item?.id}
                      label={item?.name}
                      style={{ width: '50%', marginBottom: space[4] }}
                    />
                  );
                })}
              </Row>
            </RadioButtonGroup>
          </View>
        </View>

        <ModalFormAction
          hasShadow={false}
          primaryLabel={t('confirm')}
          onPrimaryPress={onPressConfirm}
          primaryLoading={isLoading}
          primaryDisabled={!isValid || isLoading}
          secondaryLabel={t('cancel')}
          onSecondaryPress={() => {
            handleClose();
          }}
          style={{ paddingBottom: 0 }}
        />
      </CommonModal>
    );

  /**
   * Mobile mode: BottomSheet
   */
  return (
    <Column>
      <ActionPanel
        visible={visible}
        handleClose={() => {
          if (isLoading) return;
          onClose();
        }}
        title={t('candidates.approve.title')}
        contentContainerStyle={{
          paddingBottom: Platform.select({
            android: space[4] + bottom,
            ios: 0,
          }),
          maxHeight: height * 0.75,
          paddingRight: 0,
        }}>
        <ScrollView
          contentContainerStyle={{
            paddingRight: space[4],
            paddingBottom: space[4],
            gap: space[6],
          }}>
          {/* 1 */}
          <View style={{ gap: space[3] }}>
            <H7
              fontWeight="bold"
              children={t('candidates.approve.recruitSource')}
            />
            <RadioButtonGroup
              value={recruitSource}
              onChange={value => onRecruitSourceChange(value)}>
              <Row>
                {RECRUIT_SOURCE?.map(item => {
                  return (
                    <RadioButton
                      key={'recruitSource_' + item?.id}
                      value={item?.id}
                      label={item?.name}
                      style={{ flex: 1 }}
                    />
                  );
                })}
              </Row>
            </RadioButtonGroup>
          </View>

          {/* 2 */}
          {recruitSource === 2 && (
            <View style={{ gap: space[3] }}>
              <H7
                fontWeight="bold"
                color={colors.palette.fwdGreyDarker}
                children={t('candidates.approve.selectOneOfTheFollowing') + ':'}
              />
              <Column gap={space[3]}>
                {RECRUIT_SOURCE_PROGRAMS?.map(item => {
                  const isFocused = recruitSourceProgram === item?.id;
                  return (
                    <TouchableOpacity
                      key={'recruitSourceProgram_' + item?.id}
                      onPress={() => setRecruitSourceProgram(item?.id)}
                      style={{
                        padding: space[4],
                        borderWidth: isFocused ? 2 : 1,
                        borderRadius: borderRadius.small,
                        borderColor: isFocused
                          ? colors.primary
                          : colors.palette.fwdGrey[100],
                        backgroundColor: isFocused
                          ? colors.primaryVariant3
                          : colors.background,
                      }}>
                      <H7 children={item?.name} />
                    </TouchableOpacity>
                  );
                })}
              </Column>
            </View>
          )}

          {/* 3 */}
          <View style={{ gap: space[3] }}>
            <H7
              fontWeight="bold"
              children={t('candidates.approve.agentType')}
            />
            <RadioButtonGroup value={agentType} onChange={setAgentType}>
              <Row>
                {AGENT_TYPE?.map(item => {
                  return (
                    <RadioButton
                      key={'agentType_' + item?.id}
                      value={item?.id}
                      label={item?.name}
                      style={{ flex: 1 }}
                    />
                  );
                })}
              </Row>
            </RadioButtonGroup>
          </View>

          {/* 4 */}
          <View style={{ gap: space[3] }}>
            <H7
              fontWeight="bold"
              children={t('candidates.approve.designation')}
            />
            <RadioButtonGroup value={designation} onChange={setDesignation}>
              <Column gap={space[4]}>
                {DESIGNATION?.map(item => {
                  return (
                    <RadioButton
                      key={'designation_' + item?.id}
                      value={item?.id}
                      label={item?.name}
                    />
                  );
                })}
              </Column>
            </RadioButtonGroup>
          </View>
        </ScrollView>

        <FormAction
          hasShadow={false}
          primaryLabel={t('confirm')}
          primaryDisabled={!isValid || isLoading}
          primaryLoading={isLoading}
          onPrimaryPress={onPressConfirm}
          style={{ paddingLeft: 0 }}
        />
      </ActionPanel>
    </Column>
  );
}
