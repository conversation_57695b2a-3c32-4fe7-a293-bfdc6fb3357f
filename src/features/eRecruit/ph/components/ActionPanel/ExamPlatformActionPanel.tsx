import { useTheme } from '@emotion/react';
import {
  ActionPanel,
  RadioButton,
  RadioButtonGroup,
  Row,
} from 'cube-ui-components';
import { useGetMaintenanceTableByTableType } from 'features/eRecruit/hooks/ph/useGetMaintenanceTableByTableType';
import _ from 'lodash';
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform, ScrollView, useWindowDimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

/**
 * For mobile only
 * Using in exam table
 */
export default function ExamPlatformActionPanel({
  visible,
  handleClose,
  value,
  updateValue,
}: {
  visible: boolean;
  handleClose: () => void;
  value: number;
  updateValue: (value: number) => void;
}) {
  const { t } = useTranslation('reportGeneration');
  const { colors, space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  // Get maintenance tables (options)
  const { data: EXAM_PLATFORM } =
    useGetMaintenanceTableByTableType('exam-platform');

  return (
    <ActionPanel
      visible={visible}
      handleClose={() => handleClose()}
      title={'Exam platform'}
      contentContainerStyle={{
        padding: 0,
        paddingBottom: Platform.select({
          android: space[4] + bottom,
          ios: 0,
        }),
        maxHeight: height * 0.75,
      }}>
      <ScrollView contentContainerStyle={{ padding: space[4] }}>
        <RadioButtonGroup
          value={value}
          onChange={value => {
            updateValue(value);
            handleClose();
          }}>
          {_.orderBy(EXAM_PLATFORM ?? [], ['ExamPlatformID'], ['asc'])?.map(
            ({ ExamPlatformID, PlatformName }, index, arr) => {
              const IS_All = ExamPlatformID === 0 && PlatformName === 'all';
              return (
                <Fragment key={`examPlatform_${ExamPlatformID}`}>
                  <RadioButton
                    value={ExamPlatformID}
                    label={IS_All ? 'All' : PlatformName} // Label mapping for "All" option
                    labelStyle={{ paddingVertical: space[2] }}
                  />
                  {arr?.length - 1 > index && (
                    <Row
                      h={1}
                      bgColor={colors.palette.fwdGrey[100]}
                      my={space[3]}
                    />
                  )}
                </Fragment>
              );
            },
          )}
        </RadioButtonGroup>
      </ScrollView>
    </ActionPanel>
  );
}
