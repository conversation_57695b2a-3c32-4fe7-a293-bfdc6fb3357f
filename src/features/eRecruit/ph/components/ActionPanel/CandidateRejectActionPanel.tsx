import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { queryClient } from 'api/RootQueryClient';
import {
  ActionPanel,
  ActionPanelProps,
  Column,
  H6,
  H7,
  Icon,
  TextField,
  addToast,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import {
  QUERY_KEY_DECLINED_RECRUIT_LIST,
  QUERY_KEY_TODO_RECRUIT_LIST,
} from 'features/eRecruit/hooks/ph/useGetRecruitsList';
import { usePostRecruitApproval } from 'features/eRecruit/hooks/ph/usePostRecruitApproval';
import {
  ApplicationInfo,
  RecruitApprovalRequestBody,
} from 'features/eRecruit/ph/types';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';
import { CommonModal, ModalFormAction } from '../CommonModal';

type PanelProps = ActionPanelProps & {
  visible: boolean;
  applicationInfo: ApplicationInfo;
};

/**
 *  For both mobile and tablet
 */
export default function CandidateRejectActionPanel({
  visible,
  handleClose,
  applicationInfo,
}: PanelProps) {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const keyboardShown = useKeyboardShown();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const { mutate, isLoading } = usePostRecruitApproval();

  const [reason, setReason] = useState('');
  const [, setInputFocused] = useState(false);

  const handelInputFocus = () => setInputFocused(true);
  const handelInputBlur = () => setInputFocused(false);

  const onPressConfirm = () => {
    if (!reason || !reason.trim() || !applicationInfo) return;

    const data: RecruitApprovalRequestBody = {
      case: {
        recruitment: {
          recruiterNo: applicationInfo?.recruiterID,
          recruitID: applicationInfo?.recruitID,
          isApprove: 'False',
          disapproveReason: reason,
          designationID: 1, // mandatory, default 1
          recruitSourceID: 1, // mandatory, default 1
          agentTypeID: 1, // mandatory, default 1
        },
      },
    };

    mutate(data, {
      onSuccess: () => {
        addToast([
          {
            message: t('candidates.reject.successToastMessage'),
            IconLeft: <Icon.Tick />,
          },
        ]);
        handleClose();

        queryClient.invalidateQueries({
          queryKey: [QUERY_KEY_TODO_RECRUIT_LIST],
        });
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEY_DECLINED_RECRUIT_LIST],
        });

        navigation.canGoBack() && navigation.goBack();
      },
      onError: () => {
        addToast(
          [
            {
              message: t('eRecruit.pleaseTryAgainLater'),
            },
          ],
          undefined,
          true,
        );
        handleClose();
      },
    });
  };

  /**
   * Tablet mode: Modal
   */
  if (isTabletMode)
    return (
      <CommonModal
        visible={visible}
        avoidKeyboard
        width={'80%'}
        onClose={() => handleClose()}>
        <H6
          fontWeight="bold"
          children={t('candidates.reject.title')}
          style={{ paddingBottom: space[5] }}
        />

        <H7
          fontWeight="bold"
          children={t('candidates.reject.subTitle')}
          style={{ paddingBottom: space[4] }}
        />

        <TextField
          style={{ paddingVertical: space[4] }}
          label={t('reason')}
          onFocus={handelInputFocus}
          onBlur={handelInputBlur}
          autoCorrect={true}
          autoComplete={'off'}
          spellCheck={false}
          autoExpand
          onChangeText={text => setReason(text)}
          multiline
          maxLength={200} // PH requirement limit to 200 characters
        />
        <ModalFormAction
          hasShadow={false}
          primaryLabel={t('confirm')}
          onPrimaryPress={onPressConfirm}
          primaryDisabled={!reason || !reason.trim() || isLoading}
          secondaryLabel={t('cancel')}
          onSecondaryPress={() => {
            setReason('');
            handleClose();
          }}
          style={{ paddingBottom: 0 }}
        />
      </CommonModal>
    );

  /**
   * Mobile mode: BottomSheet
   */
  return (
    <Column>
      <ScrollView bounces={false}>
        <ActionPanel
          visible={visible}
          handleClose={() => {
            if (isLoading) return;
            setReason('');
            handleClose();
          }}
          title={t('candidates.reject.title')}
          contentContainerStyle={{
            paddingBottom: Platform.select({
              android: space[4] + bottom,
              ios: 0,
            }),
          }}>
          <H7 fontWeight="bold" children={t('candidates.reject.subTitle')} />
          <TextField
            style={{ paddingVertical: space[4] }}
            label={t('reason')}
            onFocus={handelInputFocus}
            onBlur={handelInputBlur}
            autoCorrect={true}
            autoComplete={'off'}
            spellCheck={false}
            autoExpand
            onChangeText={text => setReason(text)}
            multiline
            maxLength={200} // PH requirement limit to 200 characters
          />
          {keyboardShown && <Column height={220} />}
          <FormAction
            hasShadow={false}
            primaryLabel={t('confirm')}
            primaryDisabled={!reason || !reason.trim() || isLoading}
            primaryLoading={isLoading}
            onPrimaryPress={onPressConfirm}
            style={{ paddingHorizontal: 0 }}
          />
        </ActionPanel>
      </ScrollView>
    </Column>
  );
}
