import { useTheme } from '@emotion/react';
import { Column, H6, Icon, Label, LargeLabel, Row } from 'cube-ui-components';
import * as Clipboard from 'expo-clipboard';
import { useGetRecruitmentLink } from 'features/eRecruit/hooks/ph/useGetRecruitmentLink';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform, Share, TouchableOpacity, View } from 'react-native';
import Modal from 'react-native-modal';

/**
 *  For both mobile and tablet
 */
export default function ShareRecruitmentLinkModal({
  visible,
  handleClose,
}: {
  visible: boolean;
  handleClose: () => void;
}) {
  const { t } = useTranslation('eRecruit');
  const { colors, space, borderRadius, sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const { data: purl, isLoading } = useGetRecruitmentLink();

  const [isCopied, setIsCopied] = useState(false);

  const copyPurl = async (purl?: string) => {
    if (!purl) return;
    try {
      await Clipboard.setStringAsync(purl);
      setIsCopied(true); // show green alert after copy
      setTimeout(() => setIsCopied(false), 3000);
    } catch (error) {
      console.error('Failed to copy recruitment link: ' + error);
    }
  };

  const sharePurl = async (purl?: string) => {
    if (!purl) return;
    try {
      await Share.share({
        url: purl,
        ...(Platform.OS === 'android'
          ? { message: 'Grow your business with FWD!\n' + purl }
          : {}),
      });
    } catch (error) {
      console.error('Failed to share recruitment link: ' + error);
    }
  };

  const BUTTON_CONFIG = [
    {
      type: 'copy',
      label: 'recruitmentLink.copyLink',
      icon: <Icon.Copy fill={colors.background} />,
      onPress: () => copyPurl(purl),
      disabled: !purl || isLoading,
    },
    {
      type: 'share',
      label: 'recruitmentLink.share',
      icon: <Icon.Share fill={colors.background} />,
      onPress: () => sharePurl(purl),
      disabled: !purl || isLoading,
    },
  ] as const;

  return (
    <Modal
      isVisible={visible}
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      backdropOpacity={0.5}
      onBackdropPress={handleClose}
      style={isTabletMode ? [{ alignSelf: 'center' }] : undefined}>
      <View
        style={{
          width: 'auto',
          borderRadius: borderRadius.large,
          backgroundColor: colors.background,
          paddingTop: !isTabletMode ? space[6] : space[12],
          paddingBottom: !isTabletMode ? space[6] : space[12],
          paddingLeft: !isTabletMode ? space[6] : space[12],
          paddingRight: !isTabletMode ? space[6] : space[12],
        }}>
        {!isTabletMode && (
          <TouchableOpacity
            onPress={handleClose}
            style={{ alignSelf: 'flex-end' }}>
            <Icon.Close size={sizes[6]} fill={colors.secondary} />
          </TouchableOpacity>
        )}
        <Column gap={space[6]}>
          <H6 fontWeight="bold" children={t('recruitmentLink.title')} />

          {isCopied && (
            <Row
              p={space[2]}
              bgColor={colors.palette.alertGreenLight}
              borderRadius={borderRadius['x-small']}
              alignItems="center"
              gap={space[2]}>
              <Icon.Link fill={colors.palette.alertGreen} />
              <LargeLabel
                fontWeight="medium"
                children={t('recruitmentLink.linkCopied')}
                color={colors.palette.alertGreen}
              />
            </Row>
          )}

          <Row px={space[4]} justifyContent="space-evenly">
            {BUTTON_CONFIG.map(({ type, label, icon, onPress, disabled }) => (
              <Column key={type} gap={space[2]}>
                <TouchableOpacity
                  disabled={disabled}
                  style={{
                    width: 56, // value from Figma
                    height: 56, // value from Figma
                    padding: space[4],
                    backgroundColor: colors.primary,
                    borderRadius: borderRadius.full,
                    opacity: disabled ? 0.5 : 1,
                  }}
                  onPress={onPress}>
                  {icon}
                </TouchableOpacity>
                <Label children={t(label)} style={{ alignSelf: 'center' }} />
              </Column>
            ))}
          </Row>
        </Column>
      </View>
    </Modal>
  );
}
