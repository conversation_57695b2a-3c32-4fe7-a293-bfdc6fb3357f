import { object, string, number } from 'yup';
import { t } from 'i18next';
import { strictEmailRegex } from 'utils/validation/customValidation';

const PH_PHONE_NUMBER_LENGTH = 10;
const REQUIRED_INPUT = 'Required field';
const invalidInputMessage = () => t('common:form.invalidInputFormat');

export const identityDetailsSchema = object({
  firstName: string()
    .required(REQUIRED_INPUT)
    .validateLeadName(invalidInputMessage()),
  middleName: string().nullable(),
  lastName: string()
    .required(REQUIRED_INPUT)
    .validateLeadName(invalidInputMessage()),
  suffix: string().nullable(),
  gender: string().nullable(),
  birthday: string().required(REQUIRED_INPUT),
});

export const contactDetailsSchema = object({
  emailAddress: string()
    .required(REQUIRED_INPUT)
    .test('email', 'Invalid email format', value =>
      !value ? true : strictEmailRegex.test(value),
    ),
  mobileNumber: string()
    .required(REQUIRED_INPUT)
    .max(
      PH_PHONE_NUMBER_LENGTH,
      t('form.phoneNumberTooLong', { length: PH_PHONE_NUMBER_LENGTH }),
    )
    .min(
      PH_PHONE_NUMBER_LENGTH,
      t('form.phoneNumberTooShort', { length: PH_PHONE_NUMBER_LENGTH }),
    ),
});

export const candidateInfoSchema = object({
  gybScheduleID: number().notRequired(),
  recruitSourceID: number().required(REQUIRED_INPUT),
  recruitSourceProgramID: number().test(
    'required-when-recruitSourceID-equals-to-2-inorganic',
    REQUIRED_INPUT,
    function (value) {
      const { recruitSourceID } = this.parent;
      if (recruitSourceID === 2) {
        return value !== undefined && value !== null; // If recruitSourceID is 2, require a value (not null/undefined)
      }
      return true; // Otherwise, it's valid even if empty
    },
  ),
  agentTypeID: number().required(REQUIRED_INPUT),
  designationID: number().required(REQUIRED_INPUT),
});

export const declarationSchema = object({
  hasConflictOfInterest: number().required(REQUIRED_INPUT),
});
