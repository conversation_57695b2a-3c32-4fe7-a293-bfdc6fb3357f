import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Body,
  Card,
  Chip,
  Column,
  CubePictogramIcon,
  H6,
  Row,
} from 'cube-ui-components';
import { useGetApplicationStatus } from 'features/eRecruit/hooks/ph/useGetApplicationStatus';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import ApplicantStatusTab from './ApplicantStatusTab';
import { useGetApplicationCount } from 'features/eRecruit/hooks/ph/useGetApplicationCount';
import RecruitmentBarChart from 'features/eRecruit/ph/components/RecruitmentBarChart';
import { cloneDeep } from 'lodash';
import ExamSection from 'features/eRecruit/ph/components/ExamSection';

type StatusKeyType = 'mtd-applicant-status' | 'ytd-applicant-status';
type CountKeyType = 'mtm-data' | 'yty-data';

const ICON_SIZE = 40;

const monthNames = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];
const getMonthName = (monthNumber: string) => {
  const index = parseInt(monthNumber, 10) - 1;
  return monthNames[index] ?? '';
};

export default function OverviewSection() {
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();

  // MTD/YTD Tab
  const [curTab, setCurTab] = useState<'mtd' | 'ytd'>('mtd');
  const isMTD = curTab === 'mtd';
  const isYTD = curTab === 'ytd';

  // Applicant status
  const [curStatusKey, setCurStatusKey] = useState<StatusKeyType>(
    'mtd-applicant-status',
  );

  const { data: statusData } = useGetApplicationStatus();
  const gybAttendees = statusData?.[curStatusKey]?.['gyb-attendees'] ?? 0;
  const examPassers = statusData?.[curStatusKey]?.['exam-passers'] ?? 0;
  const examRegistered = statusData?.[curStatusKey]?.['exam-registered'] ?? 0;
  const docCompletion =
    statusData?.[curStatusKey]?.['document-completion'] ?? 0;
  const training = statusData?.[curStatusKey]?.training ?? 0;
  const inactive = statusData?.[curStatusKey]?.inactive ?? 0;

  const STATUS_TABS_CONFIG_ROW_ONE = [
    {
      type: 'gybAttendees',
      label: 'overview.status.gybAttendees',
      icon: <CubePictogramIcon.GYBRecruit size={ICON_SIZE} />,
      count: gybAttendees,
      // onPress: () => navigation.navigate('GYBAttendees'),
      onPress: () => console.log('Press gybAttendees'),
    },
    {
      type: 'examPassers',
      label: 'overview.status.examPassers',
      icon: <CubePictogramIcon.ClipboardWithTick size={ICON_SIZE} />,
      count: examPassers,
      onPress: () => console.log('Press examPassers'),
    },
    {
      type: 'examRegistered',
      label: 'overview.status.examRegistered',
      icon: <CubePictogramIcon.Notes size={ICON_SIZE} />,
      count: examRegistered,
      onPress: () => console.log('Press examRegistered'),
    },
  ];

  const STATUS_TABS_CONFIG_ROW_TWO = [
    {
      type: 'docCompletion',
      label: 'overview.status.docCompletion',
      icon: <CubePictogramIcon.DocumentWithTick2 size={ICON_SIZE} />,
      count: docCompletion,
      onPress: () => console.log('Press docCompletion'),
    },
    {
      type: 'training',
      label: 'overview.status.training',
      icon: <CubePictogramIcon.HumanWithBrain size={ICON_SIZE} />,
      count: training,
      onPress: () => console.log('Press training'),
    },
    {
      type: 'inactive',
      label: 'overview.status.inactive',
      icon: <CubePictogramIcon.InactivePerson size={ICON_SIZE} />,
      count: inactive,
      onPress: () => console.log('Press inactive'),
    },
  ];

  // Recruit count
  const [curCountKey, setCurCountKey] = useState<CountKeyType>('mtm-data');

  const { data: countData } = useGetApplicationCount();
  const chartData = countData?.[curCountKey] ?? [];
  const reversedChartData = cloneDeep(chartData)?.reverse(); // Reverse the data to show the latest month first

  // Support functions
  const onPressMtdTab = () => {
    setCurTab('mtd');
    setCurStatusKey('mtd-applicant-status');
    setCurCountKey('mtm-data');
  };

  const onPressYtdTab = () => {
    setCurTab('ytd');
    setCurStatusKey('ytd-applicant-status');
    setCurCountKey('yty-data');
  };

  return (
    <Column gap={space[4]}>
      <Row alignItems="center" justifyContent="space-between">
        <H6 fontWeight="bold" children={t('title.overview')} />
        <Row gap={space[1]}>
          <Chip
            focus={isMTD}
            size="large"
            label={t('overview.mtd')}
            onPress={() => onPressMtdTab()}
          />
          <Chip
            focus={isYTD}
            size="large"
            label={t('overview.ytd')}
            onPress={() => onPressYtdTab()}
          />
        </Row>
      </Row>

      <Card
        borderRadius="large"
        background={colors.background}
        style={{ padding: space[4], gap: space[4] }}>
        <H6
          fontWeight="bold"
          children={
            isMTD
              ? t('overview.mtd.statusTitle')
              : t('overview.ytd.statusTitle')
          }
        />
        <Column gap={space[4]}>
          <Row flex={1} gap={space[3]}>
            {STATUS_TABS_CONFIG_ROW_ONE.map(tab => (
              <ApplicantStatusTab key={tab.type} {...tab} />
            ))}
          </Row>
          <Row flex={1} gap={space[3]}>
            {STATUS_TABS_CONFIG_ROW_TWO.map(tab => (
              <ApplicantStatusTab key={tab.type} {...tab} />
            ))}
          </Row>
        </Column>
      </Card>

      <Card
        borderRadius="large"
        background={colors.background}
        style={{ padding: space[4] }}>
        <H6
          fontWeight="bold"
          children={
            isMTD ? t('overview.mtd.chartTitle') : t('overview.ytd.chartTitle')
          }
        />

        {countData && <RecruitmentBarChart isMTD={isMTD} data={countData} />}

        <Row
          px={space[2]}
          py={space[3]}
          mt={space[2]}
          backgroundColor={colors.palette.fwdGrey[20]}>
          <Row flex={0.75} />
          <Row flex={1.5} alignItems="center" justifyContent="flex-end">
            <Square
              style={{ backgroundColor: colors.palette.fwdYellow[100] }}
            />
            <CategoryText children={t('overview.chart.data.toGoAgent')} />
          </Row>
          <Row flex={1.5} alignItems="center" justifyContent="flex-end">
            <Square style={{ backgroundColor: colors.primary }} />
            <CategoryText children={t('overview.chart.data.codedAgent')} />
          </Row>
          <Row flex={1.25} justifyContent="flex-end">
            <CategoryText children={'Total'} />
          </Row>
        </Row>

        {reversedChartData?.map(({ registered, coded, year, month }) => {
          return (
            <Row
              key={`chartData_${year}_${month}`}
              px={space[2]}
              py={space[3]}
              borderBottom={1}
              borderColor={colors.palette.fwdGrey[100]}>
              <Row flex={0.75} justifyContent="flex-start">
                <CategoryText children={`${getMonthName(month)} ${year}`} />
              </Row>
              <Row flex={1.5} justifyContent="flex-end">
                <Body children={registered ?? 0} />
              </Row>
              <Row flex={1.5} justifyContent="flex-end">
                <Body children={coded ?? 0} />
              </Row>
              <Row flex={1.25} justifyContent="flex-end">
                <Body children={registered ?? 0 + coded ?? 0} />
              </Row>
            </Row>
          );
        })}
      </Card>

      {/* <Column>
        <ExamSection />
      </Column> */}
    </Column>
  );
}

const Square = styled.View(({ theme }) => ({
  width: theme.sizes[4],
  height: theme.sizes[4],
  borderRadius: 2, // value from Figma
  marginRight: theme.space[2],
}));

const CategoryText = styled(Body)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
}));
