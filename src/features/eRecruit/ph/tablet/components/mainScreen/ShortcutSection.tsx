import { useTheme } from '@emotion/react';
import {
  Box,
  Card,
  CubePictogramIcon,
  H7,
  Label,
  Row,
} from 'cube-ui-components';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import ShareRecruitmentLinkModal from '../../../components/ShareRecruitmentLinkModal';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useGetTodoRecruitList } from 'features/eRecruit/hooks/ph/useGetRecruitsList';
import _ from 'lodash';

const ICON_SIZE = 56;

export default function ShortcutSection() {
  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes, borderRadius } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  // Data
  const { data: toDoList, isLoading: isToDoListLoading } =
    useGetTodoRecruitList({ sortOrder: 'DESC' });

  const [shareLinkModalVisible, setShareLinkModalVisible] = useState(false);

  const SHORTCUT_CONFIG = [
    {
      type: 'share',
      label: 'title.share',
      icon: <CubePictogramIcon.Click size={ICON_SIZE} />,
      onPress: () => setShareLinkModalVisible(true),
    },
    {
      type: 'candidates',
      label: 'title.candidates',
      icon: <CubePictogramIcon.People size={ICON_SIZE} />,
      onPress: () => navigation.navigate('CandidateList'),
    },
    {
      type: 'materials',
      label: 'title.materials',
      icon: <CubePictogramIcon.Folder size={ICON_SIZE} />,
      onPress: () => navigation.navigate('Materials'),
    },
  ] as const;

  return (
    <>
      <Card
        borderRadius="large"
        background={colors.background}
        style={{ padding: space[6], gap: space[4] }}>
        <H7 fontWeight="bold" children={'What would you like to do?'} />

        <Row>
          {SHORTCUT_CONFIG.map(({ type, label, icon, onPress }) => (
            <Box key={type} flex={1} alignItems="center" gap={space[2]}>
              <TouchableOpacity
                onPress={onPress}
                style={{
                  width: 72, // align with HomeScreen shortcut width
                  height: 72, // align with HomeScreen shortcut height
                  padding: space[2],
                  borderRadius: borderRadius['x-large'],
                  backgroundColor: colors.palette.fwdOrange[20],
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                {icon}

                {type === 'candidates' &&
                  !isToDoListLoading &&
                  !_.isEmpty(toDoList) && (
                    <Box
                      position="absolute"
                      top={0}
                      right={-8}
                      w={sizes[6]}
                      h={sizes[6]}
                      borderRadius={borderRadius.full}
                      backgroundColor={colors.palette.alertRed}
                      alignItems="center"
                      justifyContent="center">
                      <Label
                        fontWeight="medium"
                        children={toDoList?.length ?? 0}
                        style={{ color: colors.palette.white }}
                      />
                    </Box>
                  )}
              </TouchableOpacity>

              <Label children={t(label)} />
            </Box>
          ))}
        </Row>
      </Card>

      <ShareRecruitmentLinkModal
        visible={shareLinkModalVisible}
        handleClose={() => setShareLinkModalVisible(false)}
      />
    </>
  );
}
