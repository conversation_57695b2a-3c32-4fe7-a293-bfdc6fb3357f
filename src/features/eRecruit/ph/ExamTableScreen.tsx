import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon, Row, Typography } from 'cube-ui-components';
import {
  endLandscape,
  startLandscape,
} from 'features/reportGeneration/ph/ToolbarProvider';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import PhoneScreenHeader from 'navigation/components/ScreenHeader/phone';
import TabletScreenHeader from 'navigation/components/ScreenHeader/tablet';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Animated, { FadeOut, LinearTransition } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ExamTable from './components/Table/ExamTable';
import {
  CELL_RENDER_ORDER,
  FREEZE_HEADER,
  HEADERS,
} from './components/Table/tableConfig';
import { Toolbar } from './components/Table/Toolbar';
import LicenseTypeActionPanel from './components/ActionPanel/LicenseTypeActionPanel';
import ExamPlatformActionPanel from './components/ActionPanel/ExamPlatformActionPanel';

const MOCK_DATA = [
  {
    LicenseTypeID: 3,
    LicenseType: 'Variable Life License',
    ExamName: 'ExamTestCamilleExamTestCamilleTest',
    ExamPlatformID: 6,
    ExamPlatform: 'IC Offsite (Face to Face)',
    Venue: 'BGC',
    ExamDateTime: '30 Aug 2025',
  },
  {
    LicenseTypeID: 2,
    LicenseType: 'Life',
    ExamName: 'Exam LifePro',
    ExamPlatformID: 4,
    ExamPlatform: 'IC Online',
    Venue: 'Makati',
    ExamDateTime: '15 Sep 2025',
  },
  {
    LicenseTypeID: 1,
    LicenseType: 'Traditional Life License',
    ExamName: 'Exam Generalist',
    ExamPlatformID: 3,
    ExamPlatform: 'IC Onsite',
    Venue: 'Ortigas',
    ExamDateTime: '10 Oct 2025',
  },
  {
    LicenseTypeID: 4,
    LicenseType: 'Traditional Life License',
    ExamName: 'Exam CompositeX',
    ExamPlatformID: 5,
    ExamPlatform: 'IC Offsite (Face to Face)',
    Venue: 'Cebu',
    ExamDateTime: '22 Nov 2025',
  },
  {
    LicenseTypeID: 5,
    LicenseType: 'Health',
    ExamName: 'Exam HealthPlus',
    ExamPlatformID: 2,
    ExamPlatform: 'IC Online',
    Venue: 'Davao',
    ExamDateTime: '5 Dec 2025',
  },
];

export default function ExamTableScreen() {
  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { isTabletMode } = useLayoutAdoptionCheck();

  // Table UI state
  const [landscape, setLandscape] = useState(false);
  const [showToolBar, setShowToolbar] = useState(true);

  // Action panel state
  const [showLicenseTypeActionPanel, setShowLicenseTypeActionPanel] =
    useState(false);
  const [showExamPlatformActionPanel, setShowExamPlatformActionPanel] =
    useState(false); // Placeholder for exam platform action panel state

  // Action panel value state
  const [licenseType, setLicenseType] = useState(0);
  const [examPlatform, setExamPlatform] = useState(0);

  return (
    <>
      {!landscape && (
        <>
          {isTabletMode ? (
            <TabletScreenHeader
              route={'ERecruitExamTable'}
              customTitle={'Exam'}
              isLeftArrowBackShown
              showBottomSeparator={false}
            />
          ) : (
            <PhoneScreenHeader
              route={'ERecruitExamTable'}
              customTitle={'Exam'}
              leftChildren={<CustomHeaderBackButton />}
              showBottomSeparator={false}
            />
          )}

          {showToolBar && (
            <Animated.View layout={LinearTransition} exiting={FadeOut}>
              <Toolbar.MainContainer>
                <Toolbar.ScrollableContainer>
                  <Toolbar.LicenseType
                    licenseType={licenseType}
                    onPress={() => setShowLicenseTypeActionPanel(true)}
                  />
                  <Toolbar.ExamPlatform
                    examPlatform={examPlatform}
                    onPress={() => setShowExamPlatformActionPanel(true)}
                  />
                </Toolbar.ScrollableContainer>

                <Typography.H8
                  children={`${t('totalResult')} (${
                    MOCK_DATA?.length ?? '--'
                  })`}
                  color={colors.palette.fwdGreyDarkest}
                  style={{ paddingLeft: space[3] }}
                />
              </Toolbar.MainContainer>
            </Animated.View>
          )}
        </>
      )}

      <Animated.View layout={LinearTransition.delay(50)} style={{ flex: 1 }}>
        <ExamTable
          darkMode={!isTabletMode}
          isLoading={false}
          isError={false}
          data={MOCK_DATA}
          freezeHeader={FREEZE_HEADER}
          headers={HEADERS}
          cellRenderOrder={CELL_RENDER_ORDER}
          setShowToolbar={setShowToolbar}
        />
      </Animated.View>

      {/* ----- Floating UI ----- */}

      {landscape && !isTabletMode && (
        <CloseButtonContainer
          onPress={() => {
            endLandscape();
            setLandscape(false);
          }}>
          <Icon.Close fill={colors.palette.fwdDarkGreen[100]} />
        </CloseButtonContainer>
      )}

      {!landscape && !isTabletMode && (
        <FullTableButtonContainer
          onPress={() => {
            startLandscape();
            setLandscape(true);
          }}
          bottom={bottom}>
          <Row alignItems="center" gap={space[1]}>
            <Icon.Expand size={sizes[5]} fill={colors.secondary} />
            <Typography.H8 children={'Full table'} />
          </Row>
        </FullTableButtonContainer>
      )}

      {/* ----- Action panels ----- */}

      <LicenseTypeActionPanel
        visible={showLicenseTypeActionPanel}
        handleClose={() => setShowLicenseTypeActionPanel(false)}
        value={licenseType}
        updateValue={setLicenseType}
      />

      <ExamPlatformActionPanel
        visible={showExamPlatformActionPanel}
        handleClose={() => setShowExamPlatformActionPanel(false)}
        value={examPlatform}
        updateValue={setExamPlatform}
      />
    </>
  );
}

const CloseButtonContainer = styled.TouchableOpacity(({ theme }) => ({
  position: 'absolute',
  right: 50,
  top: 50,
  //
  width: theme.sizes[10],
  height: theme.sizes[10],
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.full,
  alignItems: 'center',
  justifyContent: 'center',
  //
  shadowColor: theme.colors.palette.fwdGreyDark[100],
  shadowOffset: { width: 0, height: 0 },
  shadowOpacity: 0.3,
  shadowRadius: 2,
  elevation: 5,
}));

const FullTableButtonContainer = styled.TouchableOpacity<{
  bottom: number;
}>(({ theme, bottom }) => ({
  position: 'absolute',
  bottom: theme.space[15],
  // bottom: theme.space[6] + bottom,
  zIndex: 9999,
  alignSelf: 'center',
  //
  borderRadius: 24, // value from Figma
  backgroundColor: theme.colors.background,
  paddingHorizontal: theme.space[4],
  paddingVertical: theme.space[2],
  justifyContent: 'center',
  alignItems: 'center',
  //
  shadowColor: theme.colors.palette.black,
  shadowOpacity: 0.3,
  shadowOffset: { width: 0, height: 4 },
  shadowRadius: 4,
  elevation: 5,
}));
