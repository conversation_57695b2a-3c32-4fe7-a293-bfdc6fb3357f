import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Body, Column, CubePictogramIcon, H7, Row } from 'cube-ui-components';
import RecruitmentBarChart from 'features/eRecruit/ph/components/RecruitmentBarChart';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { format } from 'date-fns';
import { useGetApplicationCount } from 'features/eRecruit/hooks/ph/useGetApplicationCount';
import { useGetApplicationStatus } from 'features/eRecruit/hooks/ph/useGetApplicationStatus';
import ApplicantStatusTab from 'features/eRecruit/ph/phone/components/overview/ApplicantStatusTab';
import ExamSection from '../components/ExamSection';
import ShortcutSection from './components/overview/ShortcutSection';
// import ShareRecruitmentLink from 'features/eRecruit/ph/phone/components/overview/ShareRecruitmentLink';

type StatusKeyType = 'mtd-applicant-status' | 'ytd-applicant-status';
type CountKeyType = 'mtm-data' | 'yty-data';

const ICON_SIZE = 40;

export default function OverviewSection() {
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const currentMonth = format(new Date(), 'LLL'); // e.g. "Jan" for January
  const currentMonthInNumber = format(new Date(), 'LL'); // e.g. "01" for January
  const currentYear = new Date().getFullYear().toString();

  // MTD/YTD Tab
  const [curTab, setCurTab] = useState<'mtd' | 'ytd'>('mtd');
  const isMTD = curTab === 'mtd';
  const isYTD = curTab === 'ytd';

  // Applicant status
  const [curStatusKey, setCurStatusKey] = useState<StatusKeyType>(
    'mtd-applicant-status',
  );

  const { data: statusData } = useGetApplicationStatus();
  const gybAttendees = statusData?.[curStatusKey]?.['gyb-attendees'] ?? 0;
  const examPassers = statusData?.[curStatusKey]?.['exam-passers'] ?? 0;
  const examRegistered = statusData?.[curStatusKey]?.['exam-registered'] ?? 0;
  const docCompletion =
    statusData?.[curStatusKey]?.['document-completion'] ?? 0;
  const training = statusData?.[curStatusKey]?.training ?? 0;
  const inactive = statusData?.[curStatusKey]?.inactive ?? 0;

  const STATUS_TABS_CONFIG = [
    {
      type: 'gybAttendees',
      label: 'overview.status.gybAttendees',
      icon: <CubePictogramIcon.GYBRecruit size={ICON_SIZE} />,
      count: gybAttendees,
      // onPress: () => navigation.navigate('GYBAttendees'),
      onPress: () => console.log('Press gybAttendees'),
    },
    {
      type: 'examPassers',
      label: 'overview.status.examPassers',
      icon: <CubePictogramIcon.ClipboardWithTick size={ICON_SIZE} />,
      count: examPassers,
      onPress: () => console.log('Press examPassers'),
    },
    {
      type: 'examRegistered',
      label: 'overview.status.examRegistered',
      icon: <CubePictogramIcon.Notes size={ICON_SIZE} />,
      count: examRegistered,
      onPress: () => console.log('Press examRegistered'),
    },
    {
      type: 'docCompletion',
      label: 'overview.status.docCompletion',
      icon: <CubePictogramIcon.DocumentWithTick2 size={ICON_SIZE} />,
      count: docCompletion,
      onPress: () => console.log('Press docCompletion'),
    },
    {
      type: 'training',
      label: 'overview.status.training',
      icon: <CubePictogramIcon.HumanWithBrain size={ICON_SIZE} />,
      count: training,
      onPress: () => console.log('Press training'),
    },
    {
      type: 'inactive',
      label: 'overview.status.inactive',
      icon: <CubePictogramIcon.InactivePerson size={ICON_SIZE} />,
      count: inactive,
      onPress: () => console.log('Press inactive'),
    },
  ];

  // Recruit count
  const [curCountKey, setCurCountKey] = useState<CountKeyType>('mtm-data');

  const { data: countData } = useGetApplicationCount();
  const chartData = countData?.[curCountKey] ?? [];

  // Current month/ year data
  const currentData = chartData?.find(data =>
    curCountKey === 'mtm-data'
      ? data?.month === currentMonthInNumber
      : data?.year === currentYear,
  );
  const codedAgent = currentData?.coded ?? 0;
  const toGoAgent = currentData?.registered ?? 0;
  const totalAgent = codedAgent + toGoAgent;

  // Support functions
  const onPressMtdTab = () => {
    setCurTab('mtd');
    setCurStatusKey('mtd-applicant-status');
    setCurCountKey('mtm-data');
  };

  const onPressYtdTab = () => {
    setCurTab('ytd');
    setCurStatusKey('ytd-applicant-status');
    setCurCountKey('yty-data');
  };

  return (
    <>
      <ShortcutSection />

      <Column pt={space[4]} px={space[4]} bgColor={colors.surface}>
        <ToggleContainer>
          <Toggle isActiveTab={isMTD} onPress={() => onPressMtdTab()}>
            <Body
              fontWeight={isMTD ? 'bold' : 'normal'}
              style={{ color: isMTD ? colors.primary : colors.secondary }}
              children={t('overview.mtd')}
            />
          </Toggle>
          <Toggle isActiveTab={isYTD} onPress={() => onPressYtdTab()}>
            <Body
              fontWeight={isYTD ? 'bold' : 'normal'}
              style={{ color: isYTD ? colors.primary : colors.secondary }}
              children={t('overview.ytd')}
            />
          </Toggle>
        </ToggleContainer>

        <H7
          fontWeight="bold"
          children={
            isMTD
              ? t('overview.mtd.statusTitle')
              : t('overview.ytd.statusTitle')
          }
        />
        <TabsContainer>
          {STATUS_TABS_CONFIG.map(tab => (
            <ApplicantStatusTab key={tab.type} {...tab} />
          ))}
        </TabsContainer>

        {/* <View style={{ marginBottom: space[8] }}>
          <ShareRecruitmentLink />
        </View> */}

        <H7
          fontWeight="bold"
          children={
            isMTD ? t('overview.mtd.chartTitle') : t('overview.ytd.chartTitle')
          }
        />
        <StatContainer>
          {countData && <RecruitmentBarChart isMTD={isMTD} data={countData} />}
          <DateContainer
            children={
              <CategoryText
                children={
                  isMTD ? currentMonth + ' ' + currentYear : currentYear
                }
              />
            }
          />

          <InfoContainer>
            <CategoryText children={'Total'} />
            <ValueText children={totalAgent} />
          </InfoContainer>

          <InfoContainer>
            <Row style={{ alignItems: 'center' }}>
              <Square style={{ backgroundColor: colors.primary }} />
              <CategoryText children={t('overview.chart.data.codedAgent')} />
            </Row>
            <ValueText children={codedAgent} />
          </InfoContainer>

          <InfoContainer>
            <Row style={{ alignItems: 'center' }}>
              <Square
                style={{ backgroundColor: colors.palette.fwdYellow[100] }}
              />
              <CategoryText children={t('overview.chart.data.toGoAgent')} />
            </Row>
            <ValueText children={toGoAgent} />
          </InfoContainer>
        </StatContainer>

        <ExamSection />

        <EmptyBottomView />
      </Column>
    </>
  );
}

const ToggleContainer = styled(Row)(({ theme }) => ({
  alignItems: 'center',
  paddingBottom: theme.space[5],
  gap: 10, // value from Figma
}));

const Toggle = styled.TouchableOpacity<{
  isActiveTab: boolean;
}>(({ theme, isActiveTab }) => {
  return {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: theme.sizes[9],
    borderWidth: isActiveTab ? 2 : 1,
    borderColor: isActiveTab
      ? theme.colors.primary
      : theme.colors.palette.fwdGrey[100],
    borderRadius: theme.borderRadius['x-large'],
    backgroundColor: isActiveTab
      ? theme.colors.primaryVariant3
      : theme.colors.background,
  };
});

const TabsContainer = styled(Row)(({ theme }) => ({
  flexWrap: 'wrap',
  gap: theme.space[3],
  marginTop: theme.space[4],
  marginBottom: theme.space[7],
}));

const StatContainer = styled.View(({ theme }) => ({
  paddingTop: theme.space[4],
  paddingHorizontal: theme.space[4],
  paddingBottom: theme.space[5],
  borderRadius: theme.borderRadius.medium,
  backgroundColor: theme.colors.background,
  marginTop: theme.space[4],
  marginBottom: theme.space[7],
}));

const DateContainer = styled.View(({ theme }) => ({
  paddingVertical: theme.space[3],
  paddingHorizontal: theme.space[4],
  backgroundColor: theme.colors.palette.fwdGrey[20],
  alignItems: 'flex-end',
}));

const InfoContainer = styled(Row)(({ theme }) => ({
  paddingVertical: theme.space[3],
  paddingHorizontal: theme.space[2],
  borderBottomWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
  justifyContent: 'space-between',
}));

const Square = styled.View(({ theme }) => ({
  width: theme.sizes[4],
  height: theme.sizes[4],
  borderRadius: 2, // value from Figma
  marginRight: theme.space[2],
}));

const CategoryText = styled(Body)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
}));

const ValueText = styled(Body)(({ theme }) => ({
  paddingRight: theme.space[2],
}));

const EmptyBottomView = styled.View(({ theme }) => ({
  paddingVertical: theme.space[6],
}));
