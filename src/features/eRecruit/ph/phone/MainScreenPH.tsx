import React, { useRef } from 'react';
import { NativeSyntheticEvent, NativeScrollEvent, View } from 'react-native';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import AppTopTabBar from 'components/AppTopTabBar';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { FilterDot } from 'components/FilterDot';
import CandidateSearchHeader from 'features/eRecruit/ph/phone/components/CandidateSearchHeader';
import RecruitmentBanner, {
  ShowComponentHandle,
} from 'features/eRecruit/ph/phone/components/RecruitmentBanner';
import OverviewSection from 'features/eRecruit/ph/phone/OverviewSection';
import CandidatesSection from 'features/eRecruit/ph/phone/CandidatesSection';
import MaterialsSection from 'features/eRecruit/ph/phone/MaterialsSection';
import { useGetTodoRecruitList } from 'features/eRecruit/hooks/ph/useGetRecruitsList';
import _ from 'lodash';
import ScrollViewPanGestureWrapper from 'features/home/<USER>/components/ScrollViewPanGestureWrapper';

export default function MainScreenPH() {
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();
  const RecruitmentTab = createMaterialTopTabNavigator();
  const showComponentRef = useRef<ShowComponentHandle>(null);

  const { data: toDoList } = useGetTodoRecruitList({ sortOrder: 'ASC' });

  const scrollToShow = () => showComponentRef.current?.showComponent();
  const scrollToHide = () => showComponentRef.current?.hideComponent();

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    if (currentOffset > 0) {
      scrollToHide();
    } else if (currentOffset < 0) {
      scrollToShow();
    }
  };

  const scrollViewStyle = {
    paddingTop: space[4],
    paddingHorizontal: space[4],
    backgroundColor: colors.surface,
  };

  const scrollViewContentContainerStyle = {
    paddingBottom: space[28], // Extra screen bottom space for bottom bar
  };

  return (
    <>
      <CandidateSearchHeader />

      <RecruitmentBanner ref={showComponentRef} />

      <RecruitmentTab.Navigator
        tabBar={props => <AppTopTabBar variant="scrollable" {...props} />}
        screenOptions={{ swipeEnabled: false }}>
        <RecruitmentTab.Screen
          name="RecruitmentOverview"
          options={{ tabBarLabel: t('title.overview') }}
          children={() => (
            <ScrollViewPanGestureWrapper
              // style={scrollViewStyle}
              contentContainerStyle={scrollViewContentContainerStyle}
              onShow={scrollToShow}
              onHide={scrollToHide}
              handleScroll={handleScroll}
              scrollEventThrottle={16}>
              <OverviewSection />
            </ScrollViewPanGestureWrapper>
          )}
        />
        <RecruitmentTab.Screen
          name="RecruitmentCandidates"
          options={{
            tabBarLabel: t('title.candidates'),
            tabBarBadge: () => (
              <>
                {!_.isEmpty(toDoList) && (
                  <View>
                    <FilterDot
                      style={{
                        top: 0,
                        borderWidth: 0,
                        backgroundColor: colors.palette.alertRed,
                      }}
                    />
                  </View>
                )}
              </>
            ),
          }}
          children={() => (
            <ScrollViewPanGestureWrapper
              style={[scrollViewStyle, { paddingRight: 0 }]}
              contentContainerStyle={[
                { flexGrow: 1 },
                scrollViewContentContainerStyle,
              ]}
              onShow={scrollToShow}
              onHide={scrollToHide}
              handleScroll={handleScroll}
              scrollEventThrottle={16}>
              <CandidatesSection />
            </ScrollViewPanGestureWrapper>
          )}
        />
        <RecruitmentTab.Screen
          name="RecruitmentMaterials"
          options={{ tabBarLabel: t('title.materials') }}
          children={() => (
            <ScrollViewPanGestureWrapper
              style={scrollViewStyle}
              contentContainerStyle={scrollViewContentContainerStyle}
              onShow={scrollToShow}
              onHide={scrollToHide}
              handleScroll={handleScroll}
              scrollEventThrottle={16}>
              <MaterialsSection />
            </ScrollViewPanGestureWrapper>
          )}
        />
      </RecruitmentTab.Navigator>
    </>
  );
}
