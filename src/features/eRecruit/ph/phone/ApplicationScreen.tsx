import React, { useState } from 'react';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import {
  Box,
  Column,
  Icon,
  PictogramIcon,
  Row,
  Typography,
} from 'cube-ui-components';
import { TouchableOpacity } from 'react-native';
import PictogramManSVG from 'features/eRecruit/assets/icon/PictogramManSVG';
import PictogramPeopleNetworkSVG from 'features/eRecruit/assets/icon/PictogramPeopleNetworkSVG';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import IdentityDetailsActionPanel from './components/application/IdentityDetailsActionPanel';
import ContactDetailsActionPanel from './components/application/ContactDetailsActionPanel';
import CandidateInfoActionPanel from './components/application/CandidateInfoActionPanel';
import { useForm, FormProvider } from 'react-hook-form';
import PictogramFormWithSignatureSVG from 'features/eRecruit/assets/icon/PictogramFormWithSignatureSVG';
import DeclarationActionPanel from './components/application/DeclarationActionPanel';
import SupervisorInfoAndReviewActionPanel from './components/application/SupervisorInfoAndReviewActionPanel';
import { ApplicationFormData } from '../types';
import AttestationAndConsentActionPanel from './components/application/AttestationAndConsentActionPanel';

export default function ApplicationScreen() {
  const { t } = useTranslation('eRecruit');
  const { colors, space, borderRadius } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  // Identity details
  const [showIdentityDetailsPanel, setShowIdentityDetailsPanel] =
    useState(false);
  const [isIdentityDetailsCompleted, setIsIdentityDetailsCompleted] =
    useState(false);

  // Contact details
  const [showContactDetailsPanel, setShowContactDetailsPanel] = useState(false);
  const [isContactDetailsCompleted, setIsContactDetailsCompleted] =
    useState(false);

  // Candidate info
  const [showCandidateInfoActionPanel, setShowCandidateInfoActionPanel] =
    useState(false);
  const [isCandidateInfoCompleted, setIsCandidateInfoCompleted] =
    useState(false);

  // Review
  const [
    showSupervisorInfoAndReviewPanel,
    setShowSupervisorInfoAndReviewPanel,
  ] = useState(false);

  // Consent
  const [showConsentPanel, setShowConsentPanel] = useState(false);

  const [showDeclarationPanel, setShowDeclarationPanel] = useState(false);
  const [isDeclarationCompleted, setIsDeclarationCompleted] = useState(false);

  const PERSONAL_DETAILS_STEPS = [
    {
      type: 'IDENTITY_DETAILS',
      label: 'Identity details',
      icon: <PictogramManSVG />,
      completed: isIdentityDetailsCompleted,
      onPress: () => setShowIdentityDetailsPanel(true),
    },
    {
      type: 'CONTACT_DETAILS',
      label: 'Contact details',
      icon: <PictogramIcon.ActivePhoneCall />,
      completed: isContactDetailsCompleted,
      onPress: () => setShowContactDetailsPanel(true),
    },
    {
      type: 'CANDIDATE_INFO',
      label: 'Candidate information',
      icon: <PictogramPeopleNetworkSVG />,
      completed: isCandidateInfoCompleted,
      onPress: () => setShowCandidateInfoActionPanel(true),
    },
  ];

  const OTHER_DETAILS_STEPS = [
    {
      type: 'DECLARATION_OF_CONFLICT_OF_INTEREST',
      label: 'Declaration of Conflict\nof Interest',
      icon: <PictogramFormWithSignatureSVG />,
      completed: isDeclarationCompleted,
      onPress: () => setShowDeclarationPanel(true),
    },
  ];

  const isAllStepsCompleted =
    isIdentityDetailsCompleted &&
    isContactDetailsCompleted &&
    isCandidateInfoCompleted &&
    isDeclarationCompleted;

  // Main formState
  const methods = useForm<ApplicationFormData>({
    defaultValues: {
      identityDetails: {
        firstName: '',
        middleName: '',
        lastName: '',
        suffix: '',
        gender: '',
        birthday: '',
      },
      contactDetails: { emailAddress: '', mobileNumber: '' },
      candidateInfo: {
        gybScheduleID: undefined,
        designationID: undefined,
        recruitSourceID: undefined,
        agentTypeID: undefined,
        recruitSourceProgramID: undefined,
      },
      declaration: {
        hasConflictOfInterest: undefined,
      },
    },
  });

  return (
    <>
      <ScreenHeader
        route={'ERecruitApplication'}
        customTitle={'Application'}
        leftChildren={<CustomHeaderBackButton />}
        showBottomSeparator={false}
      />

      <Column flex={1} bgColor={colors.surface} pt={space[5]} px={space[4]}>
        <Typography.H6
          fontWeight="bold"
          children={'Personal details'}
          style={{ marginBottom: space[4] }}
        />
        <Box
          p={space[2]}
          mb={space[5]}
          bgColor={colors.background}
          borderRadius={borderRadius.large}
          gap={space[1]}>
          {PERSONAL_DETAILS_STEPS?.map(
            ({ type, label, icon, completed, onPress }) => {
              return (
                <Tab
                  key={type}
                  type={type}
                  label={label}
                  icon={icon}
                  completed={completed}
                  onPress={onPress}
                />
              );
            },
          )}
        </Box>

        <Typography.H6
          fontWeight="bold"
          children={'Other details'}
          style={{ marginBottom: space[4] }}
        />
        <Box
          p={space[2]}
          bgColor={colors.background}
          borderRadius={borderRadius.large}
          gap={space[1]}>
          {OTHER_DETAILS_STEPS?.map(
            ({ type, label, icon, completed, onPress }) => {
              return (
                <Tab
                  key={type}
                  type={type}
                  label={label}
                  icon={icon}
                  completed={completed}
                  onPress={onPress}
                />
              );
            },
          )}
        </Box>
      </Column>

      <FormAction
        hasShadow={false}
        primaryDisabled={!isAllStepsCompleted}
        primaryLabel={'Next'}
        primarySublabel={'Review'}
        onPrimaryPress={() => setShowSupervisorInfoAndReviewPanel(true)}
      />

      <FormProvider {...methods}>
        <IdentityDetailsActionPanel
          visible={showIdentityDetailsPanel}
          handleClose={() => setShowIdentityDetailsPanel(false)}
          setIsIdentityDetailsCompleted={setIsIdentityDetailsCompleted}
        />

        <ContactDetailsActionPanel
          visible={showContactDetailsPanel}
          handleClose={() => setShowContactDetailsPanel(false)}
          setIsContactDetailsCompleted={setIsContactDetailsCompleted}
        />

        <CandidateInfoActionPanel
          visible={showCandidateInfoActionPanel}
          handleClose={() => setShowCandidateInfoActionPanel(false)}
          setIsCandidateInfoCompleted={setIsCandidateInfoCompleted}
        />

        <DeclarationActionPanel
          visible={showDeclarationPanel}
          handleClose={() => setShowDeclarationPanel(false)}
          setIsDeclarationCompleted={setIsDeclarationCompleted}
        />

        <SupervisorInfoAndReviewActionPanel
          visible={showSupervisorInfoAndReviewPanel}
          handleClose={() => setShowSupervisorInfoAndReviewPanel(false)}
          handleShowConsentPanel={() => setShowConsentPanel(true)}
        />

        <AttestationAndConsentActionPanel
          visible={showConsentPanel}
          handleClose={() => setShowConsentPanel(false)}
        />
      </FormProvider>
    </>
  );
}

function Tab({
  type,
  label,
  icon,
  completed,
  onPress,
}: {
  type: string;
  label: string;
  icon: JSX.Element;
  completed: boolean;
  onPress: () => void;
}) {
  const { colors, space, sizes } = useTheme();
  return (
    <TouchableOpacity onPress={() => onPress()}>
      <Row
        key={type}
        py={space[2]}
        alignItems="center"
        justifyContent="space-between">
        <Row alignItems="center" gap={space[2]}>
          <Icon.TickCircle
            fill={
              completed
                ? colors.palette.alertGreen
                : colors.palette.fwdDarkGreen[20]
            }
            size={sizes[7]}
          />
          {icon}
          <Typography.LargeLabel
            fontWeight="bold"
            children={label}
            color={colors.primary}
          />
        </Row>

        <Icon.ChevronRight />
      </Row>
    </TouchableOpacity>
  );
}
