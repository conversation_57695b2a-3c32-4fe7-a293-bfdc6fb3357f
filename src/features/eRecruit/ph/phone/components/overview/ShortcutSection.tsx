import React, { useState } from 'react';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Column, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import ShareRemoteLinkSVG from 'features/eRecruit/assets/icon/ShareRemoteLinkSVG';
import FormSVG from 'features/eRecruit/assets/icon/FormSVG';
import { TouchableOpacity } from 'react-native';
import ShareRecruitmentLinkModal from '../../../components/ShareRecruitmentLinkModal';

export default function ShortcutSection() {
  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes, borderRadius } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const [shareLinkModalVisible, setShareLinkModalVisible] = useState(false);

  const SHORTCUT_CONFIG = [
    {
      type: 'startApplication',
      label: 'title.startApplication',
      icon: <FormSVG />,
      onPress: () => navigation.navigate('ERecruitApplication'),
      isHidden: false,
    },
    {
      type: 'shareRecruitmentLink',
      label: 'title.share',
      icon: <ShareRemoteLinkSVG />,
      onPress: () => setShareLinkModalVisible(true),
    },
  ] as const;

  return (
    <>
      <Column p={space[4]} bgColor={colors.background} gap={space[4]}>
        <Typography.LargeLabel
          children={t('title.whatWouldYouLikeToDo')}
          color={colors.palette.fwdGreyDarkest}
        />

        <Row>
          {SHORTCUT_CONFIG?.map(({ type, label, icon, onPress }) => (
            <Column key={type} flex={1} alignItems="center" gap={space[2]}>
              <TouchableOpacity
                onPress={onPress}
                style={{
                  width: sizes[18],
                  height: sizes[18],
                  borderRadius: borderRadius['x-large'],
                  backgroundColor: colors.palette.fwdOrange[20],
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                {icon && icon}
              </TouchableOpacity>

              <Typography.Label fontWeight="medium" children={t(label)} />
            </Column>
          ))}
        </Row>
      </Column>

      <ShareRecruitmentLinkModal
        visible={shareLinkModalVisible}
        handleClose={() => setShareLinkModalVisible(false)}
      />
    </>
  );
}
