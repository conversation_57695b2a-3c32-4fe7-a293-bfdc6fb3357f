import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  ActionPanel,
  ActionPanelProps,
  Column,
  PictogramIcon,
  Row,
  Typography,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import PictogramFormWithSignatureSVG from 'features/eRecruit/assets/icon/PictogramFormWithSignatureSVG';
import PictogramManSVG from 'features/eRecruit/assets/icon/PictogramManSVG';
import PictogramPeopleNetworkSVG from 'features/eRecruit/assets/icon/PictogramPeopleNetworkSVG';
import { ApplicationFormData } from 'features/eRecruit/ph/types';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Platform, useWindowDimensions } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { calculateAge } from 'utils/helper/calculateAge';
import { dateFormatUtil } from 'utils/helper/formatUtil';

type PanelProps = ActionPanelProps & {
  visible: boolean;
  handleShowConsentPanel: () => void;
};

export default function SupervisorInfoAndReviewActionPanel({
  visible,
  handleClose,
  handleShowConsentPanel,
}: PanelProps) {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  // Main formState
  const { getValues: getMainFormValues } = useFormContext();

  const formData = getMainFormValues();
  const { identityDetails, contactDetails, candidateInfo, declaration } =
    formData as ApplicationFormData;

  // Full name
  const suffix = identityDetails?.suffix ? identityDetails?.suffix + ' ' : '';
  const firstName = identityDetails?.firstName
    ? identityDetails?.firstName + ' '
    : '';
  const middleName = identityDetails?.middleName
    ? identityDetails?.middleName + ' '
    : '';
  const lastName = identityDetails?.lastName ? identityDetails?.lastName : '';
  const fullName = suffix + firstName + middleName + lastName;

  // Date of birth and age
  const dobWithAge = identityDetails?.birthday
    ? `${dateFormatUtil(identityDetails?.birthday)} (${calculateAge(
        identityDetails?.birthday as any,
      )} years old)`
    : '--';

  const IDENTITY_DETAILS_DATA = [
    {
      label: 'Name',
      value: fullName || '--',
    },
    {
      label: 'Gender',
      value: identityDetails?.gender || '--',
    },
    {
      label: 'Date of birth',
      value: dobWithAge || '--',
    },
  ];

  const CONTACT_DETAILS_DATA = [
    {
      label: 'Mobile number',
      value: '+63 ' + contactDetails?.mobileNumber || '--',
    },
    {
      label: 'Email',
      value: contactDetails?.emailAddress || '--',
    },
  ];

  const CANDIDATE_INFO_DATA = [
    {
      label: 'GYB schedule (optional)',
      value: candidateInfo?.gybScheduleID || '--',
    },
    {
      label: 'Recruit source',
      value: candidateInfo?.recruitSourceID || '--',
    },
    {
      label: 'Agent type',
      value: candidateInfo?.agentTypeID || '--',
    },
    { label: 'Designation', value: candidateInfo?.designationID || '--' },
  ];

  const onPrimaryPress = () => {
    handleClose();
    setTimeout(() => {
      handleShowConsentPanel();
    }, 500);
  };

  return (
    <>
      <Column>
        <ActionPanel
          visible={visible}
          handleClose={() => handleClose()}
          contentContainerStyle={{
            paddingBottom: Platform.select({
              android: space[4] + bottom,
              ios: 0,
            }),
            maxHeight: height * 0.8,
            paddingHorizontal: 0,
          }}>
          <ScrollView
            contentContainerStyle={{
              paddingHorizontal: space[4],
              paddingBottom: space[4],
              gap: space[4],
            }}>
            <Typography.LargeLabel
              fontWeight="bold"
              children={'Supervisor information & review'}
            />

            {/* 1 */}
            <Column>
              <TitleContainer>
                <Title fontWeight="bold" children={'Supervisor Information'} />
              </TitleContainer>
              <ContentContainer>
                <Column gap={space[2]}>
                  <InfoField fieldName={'Recruiter'} fieldValue={'--'} />
                  <InfoField
                    fieldName={'Immediate manager'}
                    fieldValue={'--'}
                  />
                </Column>
              </ContentContainer>
            </Column>

            {/* 2 */}
            <Column>
              <TitleContainer>
                <Title fontWeight="bold" children={'Personal details'} />
              </TitleContainer>

              {/* 2.1 */}
              <ContentContainer>
                <Row alignItems="center" pb={space[4]} gap={space[2]}>
                  <PictogramManSVG />
                  <Typography.LargeLabel
                    fontWeight="bold"
                    children={'Identity details'}
                  />
                </Row>
                <Column gap={space[2]}>
                  {IDENTITY_DETAILS_DATA?.map(({ label, value }, index) => (
                    <InfoField
                      key={'identityDetails_' + index}
                      fieldName={label}
                      fieldValue={value}
                    />
                  ))}
                </Column>

                {/* 2.2 */}
                <Row alignItems="center" py={space[4]} gap={space[2]}>
                  <PictogramIcon.ActivePhoneCall />
                  <Typography.LargeLabel
                    fontWeight="bold"
                    children={'Contact details'}
                  />
                </Row>
                <Column gap={space[2]}>
                  {CONTACT_DETAILS_DATA?.map(({ label, value }, index) => (
                    <InfoField
                      key={'contactDetails_' + index}
                      fieldName={label}
                      fieldValue={value}
                    />
                  ))}
                </Column>

                {/* 2.3 */}
                <Row alignItems="center" py={space[4]} gap={space[2]}>
                  <PictogramPeopleNetworkSVG />
                  <Typography.LargeLabel
                    fontWeight="bold"
                    children={'Candidate information'}
                  />
                </Row>
                <Column gap={space[2]}>
                  {CANDIDATE_INFO_DATA?.map(({ label, value }, index) => (
                    <InfoField
                      key={'candidateInfo_' + index}
                      fieldName={label}
                      fieldValue={value}
                    />
                  ))}
                </Column>
              </ContentContainer>
            </Column>

            {/* 3 */}
            <Column>
              <TitleContainer>
                <Title fontWeight="bold" children={'Other details'} />
              </TitleContainer>
              <ContentContainer>
                <Row alignItems="center" pb={space[4]} gap={space[2]}>
                  <PictogramFormWithSignatureSVG />
                  <Typography.LargeLabel
                    fontWeight="bold"
                    children={t('eRecruit.application.declaration.title')}
                  />
                </Row>
                <Column gap={space[2]}>
                  <Typography.Label
                    children={t('eRecruit.application.declaration.question')}
                  />
                  <Row>
                    <InfoField
                      fieldName={'Answer'}
                      fieldValue={declaration?.hasConflictOfInterest}
                    />
                  </Row>
                </Column>
              </ContentContainer>
            </Column>
          </ScrollView>

          <FormAction
            primaryLabel={'Confirm'}
            primaryDisabled={false}
            onPrimaryPress={() => onPrimaryPress()}
            secondaryLabel={'Back to edit'}
            secondaryDisabled={false}
            onSecondaryPress={() => handleClose()}
          />
        </ActionPanel>
      </Column>
    </>
  );
}

function InfoField({
  fieldName,
  fieldValue,
}: {
  fieldName?: string;
  fieldValue?: string | number;
}) {
  const { colors } = useTheme();
  return (
    <Row>
      <Typography.Label
        children={fieldName}
        color={colors.palette.fwdGreyDarkest}
        style={{ width: '50%' }}
      />
      <Typography.Label children={fieldValue} style={{ width: '50%' }} />
    </Row>
  );
}

const TitleContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.secondary,
  paddingVertical: theme.space[3],
  paddingHorizontal: theme.space[4],
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));

const Title = styled(Typography.H7)(({ theme }) => ({
  color: theme.colors.onSecondary,
}));

const ContentContainer = styled.View(({ theme }) => ({
  // flexWrap: 'wrap',
  // flexDirection: 'row',
  backgroundColor: theme.colors.background,
  paddingTop: theme.space[3],
  paddingHorizontal: theme.space[4],
  paddingBottom: theme.space[4],
  borderWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
  borderBottomLeftRadius: theme.borderRadius.large,
  borderBottomRightRadius: theme.borderRadius.large,
}));
