import { useTheme } from '@emotion/react';
import {
  ActionPanel,
  ActionPanelProps,
  Box,
  Checkbox,
  Column,
  Row,
  Typography,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import { ApplicationFormData } from 'features/eRecruit/ph/types';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  Platform,
  Text,
  TouchableOpacity,
  useWindowDimensions,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type PanelProps = ActionPanelProps & {
  visible: boolean;
};

const CONSENT_CONTENT = [
  'eRecruit.application.consent.content.one',
  'eRecruit.application.consent.content.two',
  'eRecruit.application.consent.content.three',
  'eRecruit.application.consent.content.four',
  'eRecruit.application.consent.content.five',
] as const;

export default function AttestationAndConsentActionPanel({
  visible,
  handleClose,
}: PanelProps) {
  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes, borderRadius } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  const [isFullVersion, setIsFullVersion] = useState(false);
  const [isAgree, setIsAgree] = useState(false);

  // Main formState
  const { getValues: getMainFormValues } = useFormContext();

  const formData = getMainFormValues();
  const { identityDetails, contactDetails, candidateInfo, declaration } =
    formData as ApplicationFormData;

  const handlePanelClose = () => {
    setIsFullVersion(false);
    setIsAgree(false);
    handleClose();
  };

  const onPrimaryPress = () => {
    handleClose();
  };

  return (
    <>
      <Column>
        <ActionPanel
          visible={visible}
          handleClose={() => handlePanelClose()}
          contentContainerStyle={{
            paddingBottom: Platform.select({
              android: space[4] + bottom,
              ios: 0,
            }),
            maxHeight: height * 0.8,
            paddingHorizontal: 0,
          }}>
          <ScrollView
            contentContainerStyle={{
              paddingHorizontal: space[4],
              paddingBottom: space[4],
              gap: space[4],
            }}>
            <Typography.LargeLabel
              fontWeight="bold"
              children={'Attestation and consent'}
            />

            {!isFullVersion ? (
              <Box>
                <Typography.Body
                  numberOfLines={4}
                  ellipsizeMode="clip"
                  children={t('eRecruit.application.consent.content.start')}
                />
                <Typography.Body
                  fontWeight="bold"
                  children={'...more'}
                  onPress={() => setIsFullVersion(true)}
                  style={{
                    position: 'absolute',
                    right: 0,
                    bottom: 0,
                    paddingHorizontal: space[2],
                    backgroundColor: colors.background,
                    color: colors.palette.fwdAlternativeOrange[100],
                  }}
                />
              </Box>
            ) : (
              <>
                <Typography.Body
                  children={t('eRecruit.application.consent.content.start')}
                />
                {CONSENT_CONTENT.map((content, index) => (
                  <Row key={content}>
                    <Typography.Body
                      children={String(index + 1 + '.')}
                      style={{ width: sizes[5] }}
                    />
                    <Typography.Body
                      children={t(content)}
                      style={{ flex: 1 }}
                    />
                  </Row>
                ))}

                <Text>
                  <Typography.Body
                    children={
                      t('eRecruit.application.consent.content.end') + ' '
                    }
                  />
                  <Typography.Body
                    fontWeight="bold"
                    children={' Close'}
                    onPress={() => setIsFullVersion(false)}
                    color={colors.palette.fwdAlternativeOrange[100]}
                  />
                </Text>
              </>
            )}

            <TouchableOpacity
              onPress={() => setIsAgree(!isAgree)}
              style={{
                flexDirection: 'row',
                padding: space[4],
                borderWidth: 1,
                borderRadius: borderRadius.small,
                borderColor: colors.palette.fwdGrey[100],
                backgroundColor: colors.background,
                gap: space[3],
                alignItems: 'center',
              }}>
              <Checkbox checked={isAgree} />
              <Typography.H7
                children={'I agree to the attestation and consent'}
              />
            </TouchableOpacity>
          </ScrollView>

          <FormAction
            primaryLabel={'Submit'}
            primaryDisabled={!isAgree}
            onPrimaryPress={() => onPrimaryPress()}
            secondaryLabel={'Cancel'}
            onSecondaryPress={() => handlePanelClose()}
          />
        </ActionPanel>
      </Column>
    </>
  );
}
