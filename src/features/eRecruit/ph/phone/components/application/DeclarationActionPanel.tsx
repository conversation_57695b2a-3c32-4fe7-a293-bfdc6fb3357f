import { useTheme } from '@emotion/react';
import { yupResolver } from '@hookform/resolvers/yup';
import Input from 'components/Input';
import {
  ActionPanel,
  ActionPanelProps,
  Column,
  RadioButton,
  RadioButtonGroup,
  Row,
  Typography,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import PictogramFormWithSignatureSVG from 'features/eRecruit/assets/icon/PictogramFormWithSignatureSVG';
import { declarationSchema } from 'features/eRecruit/ph/components/ApplicationForm/applicationFormSchema';
import { Declaration } from 'features/eRecruit/ph/types';
import { Dispatch, SetStateAction, useEffect } from 'react';
import { useForm, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Platform, useWindowDimensions } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type PanelProps = ActionPanelProps & {
  visible: boolean;
  setIsDeclarationCompleted: Dispatch<SetStateAction<boolean>>;
};

const DECLARATION_CONTENT = [
  'eRecruit.application.declaration.content.one',
  'eRecruit.application.declaration.content.two',
  'eRecruit.application.declaration.content.three',
  'eRecruit.application.declaration.content.four',
  'eRecruit.application.declaration.content.five',
] as const;

export default function DeclarationActionPanel({
  visible,
  handleClose,
  setIsDeclarationCompleted,
}: PanelProps) {
  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes, borderRadius } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  // Main formState
  const { getValues: getMainFormValues, setValue: setMainFormValues } =
    useFormContext<Declaration>();

  // Local formState
  const {
    control,
    watch,
    setValue,
    trigger,
    formState: { isValid },
  } = useForm({
    defaultValues: {
      hasConflictOfInterest: undefined,
    },
    mode: 'onChange',
    resolver: yupResolver(declarationSchema),
  });
  const hasConflictOfInterest = watch('hasConflictOfInterest');

  const handlePanelClose = () => {
    const formValues = getMainFormValues('declaration');
    setValue('hasConflictOfInterest', formValues?.hasConflictOfInterest);
    handleClose();
  };

  const onPrimaryPress = () => {
    setMainFormValues(
      'declaration.hasConflictOfInterest',
      hasConflictOfInterest,
    );
    setIsDeclarationCompleted(true);
    handleClose();
  };

  useEffect(() => {
    trigger();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  return (
    <>
      <Column>
        <ActionPanel
          visible={visible}
          handleClose={() => handlePanelClose()}
          contentContainerStyle={{
            paddingBottom: Platform.select({
              android: space[4] + bottom,
              ios: 0,
            }),
            maxHeight: height * 0.75,
            paddingHorizontal: 0,
          }}>
          <ScrollView
            contentContainerStyle={{
              paddingHorizontal: space[4],
              paddingBottom: space[4],
              gap: space[6],
            }}>
            <Row alignItems="center" gap={space[2]}>
              <PictogramFormWithSignatureSVG />
              <Typography.LargeLabel
                fontWeight="bold"
                children={t('eRecruit.application.declaration.title')}
                color={colors.primary}
              />
            </Row>

            <Typography.H7
              children={t('eRecruit.application.declaration.question')}
            />
            <Input
              control={control}
              name="hasConflictOfInterest"
              as={RadioButtonGroup}>
              <Row>
                <RadioButton value={1} label={'Yes'} style={{ flex: 1 }} />
                <RadioButton value={0} label={'No'} style={{ flex: 1 }} />
              </Row>
            </Input>

            <Column
              h={320}
              bgColor={colors.palette.fwdGrey[20]}
              borderRadius={borderRadius.small}>
              <ScrollView
                contentContainerStyle={{ padding: space[5], gap: space[4] }}>
                <Typography.H7
                  fontWeight="bold"
                  children={t('eRecruit.application.declaration.title')}
                  color={colors.palette.fwdDarkGreen[50]}
                />

                <Typography.LargeLabel
                  children={t('eRecruit.application.declaration.hint')}
                />
                {DECLARATION_CONTENT.map((content, index) => (
                  <Row key={content}>
                    <Typography.Body
                      children={String(index + 1 + '.')}
                      style={{ width: sizes[5] }}
                    />
                    <Typography.Body
                      children={t(content)}
                      style={{ flex: 1 }}
                    />
                  </Row>
                ))}
              </ScrollView>
            </Column>
          </ScrollView>

          <FormAction
            primaryLabel={'Done'}
            primaryDisabled={!isValid}
            onPrimaryPress={() => onPrimaryPress()}
          />
        </ActionPanel>
      </Column>
    </>
  );
}
