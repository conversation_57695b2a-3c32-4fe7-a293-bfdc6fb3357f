import { useTheme } from '@emotion/react';
import { yupResolver } from '@hookform/resolvers/yup';
import Input from 'components/Input';
import {
  ActionPanel,
  ActionPanelProps,
  Column,
  DatePicker,
  Dropdown,
  Picker,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import PictogramManSVG from 'features/eRecruit/assets/icon/PictogramManSVG';
import { useGetMaintenanceTableByTableType } from 'features/eRecruit/hooks/ph/useGetMaintenanceTableByTableType';
import { identityDetailsSchema } from 'features/eRecruit/ph/components/ApplicationForm/applicationFormSchema';
import { IdentityDetailsForm } from 'features/eRecruit/ph/types';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { Dispatch, SetStateAction, useMemo } from 'react';
import { useForm, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Platform, useWindowDimensions } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { calculateAge } from 'utils/helper/calculateAge';
import { dateFormatUtil } from 'utils/helper/formatUtil';

type PanelProps = ActionPanelProps & {
  visible: boolean;
  setIsIdentityDetailsCompleted: Dispatch<SetStateAction<boolean>>;
};

export default function IdentityDetailsActionPanel({
  visible,
  handleClose,
  setIsIdentityDetailsCompleted,
}: PanelProps) {
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  // Main formState
  const { getValues: getMainFormValues, setValue: setMainFormValues } =
    useFormContext<IdentityDetailsForm>();

  // Local formState
  const {
    control,
    watch,
    setValue,
    trigger,
    formState: { errors, isValid },
  } = useForm({
    defaultValues: {
      firstName: '',
      middleName: '',
      lastName: '',
      suffix: '',
      gender: '',
      birthday: '',
    },
    mode: 'onBlur',
    resolver: yupResolver(identityDetailsSchema),
  });
  const firstName = watch('firstName');
  const middleName = watch('middleName');
  const lastName = watch('lastName');
  const suffix = watch('suffix');
  const gender = watch('gender');
  const birthdayValue = watch('birthday');

  // Suffix option
  const { data: SUFFIX } = useGetMaintenanceTableByTableType('suffix');

  // Gender option
  const { data: optionList } = useGetOptionList();
  const { genderOptions } = useMemo(() => {
    return {
      genderOptions:
        optionList?.GENDER?.options.map(item => ({
          text: item?.label,
          value: item?.value,
        })) || [],
    };
  }, [optionList]);

  // Age calculation
  const birthday = watch('birthday');
  const age = birthday ? calculateAge(new Date(birthday)) : '';

  const handlePanelClose = () => {
    const formValues = getMainFormValues('identityDetails');
    setValue('firstName', formValues?.firstName);
    setValue('middleName', formValues?.middleName);
    setValue('lastName', formValues?.lastName);
    setValue('suffix', formValues?.suffix);
    setValue('gender', formValues?.gender);
    setValue('birthday', formValues?.birthday);
    handleClose();
  };

  const onPrimaryPress = () => {
    setMainFormValues('identityDetails.firstName', firstName);
    setMainFormValues('identityDetails.middleName', middleName ?? '');
    setMainFormValues('identityDetails.lastName', lastName);
    setMainFormValues('identityDetails.suffix', suffix ?? '');
    setMainFormValues('identityDetails.gender', gender ?? '');
    setMainFormValues('identityDetails.birthday', birthdayValue);
    setIsIdentityDetailsCompleted(true);
    handleClose();
  };

  return (
    <>
      <Column>
        <ActionPanel
          visible={visible}
          handleClose={() => handlePanelClose()}
          contentContainerStyle={{
            paddingBottom: Platform.select({
              android: space[4] + bottom,
              ios: 0,
            }),
            maxHeight: height * 0.75,
            paddingHorizontal: 0,
          }}>
          <ScrollView
            contentContainerStyle={{
              paddingHorizontal: space[4],
              paddingBottom: space[4],
              gap: space[6],
            }}>
            <Row alignItems="center" gap={space[2]}>
              <PictogramManSVG />
              <Typography.LargeLabel
                fontWeight="bold"
                children={'Identity details'}
                color={colors.primary}
              />
            </Row>

            <Input
              control={control}
              as={TextField}
              name="firstName"
              label={'First name'}
              error={errors?.firstName?.message}
            />

            <Input
              control={control}
              as={TextField}
              name="middleName"
              label={'Middle name (optional)'}
              style={{ flex: 2 }}
              error={errors?.middleName?.message}
            />

            <Input
              control={control}
              as={TextField}
              name="lastName"
              label={'Last name'}
              style={{ flex: 2 }}
              error={errors?.lastName?.message}
            />

            <Input
              control={control}
              as={
                Dropdown<
                  { SuffixID?: number; SuffixName?: string },
                  string | number | undefined
                >
              }
              name="suffix"
              label={'Extension name (optional)'}
              modalTitle={'Extension name (optional)'}
              data={SUFFIX ?? []}
              getItemValue={item => item?.SuffixID}
              getItemLabel={item => item?.SuffixName ?? '--'}
            />

            <Input
              control={control}
              as={Picker}
              name="gender"
              type="text"
              label={'Gender'}
              error={errors.gender?.message}
              items={genderOptions}
            />

            <Row style={{ gap: space[3] }}>
              <Input
                control={control}
                as={DatePicker}
                name="birthday"
                label={'Date of birth'}
                modalTitle={'Date of birth'}
                maxDate={new Date()}
                formatDate={value => (value ? dateFormatUtil(value) : '')}
                onTouchEnd={() => trigger('birthday')}
                style={{ flex: 1 }}
              />
              <TextField
                disabled={true}
                label={'Age'}
                value={age === 0 ? '0' : age}
                style={{ flex: 0.3 }}
              />
            </Row>
          </ScrollView>

          <FormAction
            primaryLabel={'Done'}
            primaryDisabled={!isValid}
            onPrimaryPress={() => onPrimaryPress()}
          />
        </ActionPanel>
      </Column>
    </>
  );
}
