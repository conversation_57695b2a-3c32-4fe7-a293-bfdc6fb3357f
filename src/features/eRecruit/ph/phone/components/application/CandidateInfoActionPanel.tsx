import { useTheme } from '@emotion/react';
import { yupResolver } from '@hookform/resolvers/yup';
import Input from 'components/Input';
import {
  ActionPanel,
  ActionPanelProps,
  Column,
  Dropdown,
  RadioButton,
  RadioButtonGroup,
  Row,
  Typography,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import PictogramPeopleNetworkSVG from 'features/eRecruit/assets/icon/PictogramPeopleNetworkSVG';
import { useGetMaintenanceTableByTableType } from 'features/eRecruit/hooks/ph/useGetMaintenanceTableByTableType';
import { candidateInfoSchema } from 'features/eRecruit/ph/components/ApplicationForm/applicationFormSchema';
import { CandidateInfoForm } from 'features/eRecruit/ph/types';
import { Dispatch, SetStateAction, useEffect } from 'react';
import { useForm, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Platform, useWindowDimensions, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type PanelProps = ActionPanelProps & {
  visible: boolean;
  setIsCandidateInfoCompleted: Dispatch<SetStateAction<boolean>>;
};

export default function CandidateInfoActionPanel({
  visible,
  handleClose,
  setIsCandidateInfoCompleted,
}: PanelProps) {
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  // Main formState
  const { getValues: getMainFormValues, setValue: setMainFormValues } =
    useFormContext<CandidateInfoForm>();

  // Local formState
  const {
    control,
    watch,
    setValue,
    trigger,
    formState: { isValid },
  } = useForm({
    defaultValues: {
      gybScheduleID: undefined,
      recruitSourceID: undefined,
      recruitSourceProgramID: undefined,
      agentTypeID: undefined,
      designationID: undefined,
    },
    mode: 'onChange',
    resolver: yupResolver(candidateInfoSchema),
  });
  const gybScheduleID = watch('gybScheduleID');
  const recruitSourceID = watch('recruitSourceID');
  const recruitSourceProgramID = watch('recruitSourceProgramID');
  const agentTypeID = watch('agentTypeID');
  const designationID = watch('designationID');

  // Get maintenance tables (options)
  const { data: GYB_LIST } = useGetMaintenanceTableByTableType('gyb-list');
  const { data: RECRUIT_SOURCE } =
    useGetMaintenanceTableByTableType('recruit-source');
  const { data: RECRUIT_SOURCE_PROGRAMS } = useGetMaintenanceTableByTableType(
    'recruit-source-programs',
  );
  const { data: AGENT_TYPE } = useGetMaintenanceTableByTableType('agent-type');
  const { data: DESIGNATION } =
    useGetMaintenanceTableByTableType('designation');

  const handlePanelClose = () => {
    const formValues = getMainFormValues('candidateInfo');
    setValue('gybScheduleID', formValues?.gybScheduleID);
    setValue('recruitSourceID', formValues?.recruitSourceID);
    setValue('recruitSourceProgramID', formValues?.recruitSourceProgramID);
    setValue('agentTypeID', formValues?.agentTypeID);
    setValue('designationID', formValues?.designationID);
    handleClose();
  };

  const onPrimaryPress = () => {
    setMainFormValues('candidateInfo.gybScheduleID', gybScheduleID);
    setMainFormValues('candidateInfo.recruitSourceID', recruitSourceID);
    setMainFormValues(
      'candidateInfo.recruitSourceProgramID',
      recruitSourceProgramID as number, // recruitSourceProgramID can be undefined when recruitSourceID is 1 (organic)
    );
    setMainFormValues('candidateInfo.agentTypeID', agentTypeID);
    setMainFormValues('candidateInfo.designationID', designationID);
    setIsCandidateInfoCompleted(true);
    handleClose();
  };

  useEffect(() => {
    trigger();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  return (
    <>
      <Column>
        <ActionPanel
          visible={visible}
          handleClose={() => handlePanelClose()}
          contentContainerStyle={{
            paddingBottom: Platform.select({
              android: space[4] + bottom,
              ios: 0,
            }),
            maxHeight: height * 0.8,
            paddingHorizontal: 0,
          }}>
          <ScrollView
            contentContainerStyle={{
              paddingHorizontal: space[4],
              paddingBottom: space[4],
              gap: space[6],
            }}>
            <Row alignItems="center" gap={space[2]}>
              <PictogramPeopleNetworkSVG />
              <Typography.LargeLabel
                fontWeight="bold"
                children={'Candidate information'}
                color={colors.primary}
              />
            </Row>

            {/* 1 */}
            <Input
              control={control}
              as={
                Dropdown<
                  {
                    id?: number;
                    name?: string;
                    date?: string;
                    venue?: string;
                    GybTypeID?: number;
                    dateTime?: string;
                  },
                  string | number | undefined
                >
              }
              name="gybScheduleID"
              label={'GYB schedule (optional)'}
              modalTitle={'GYB schedule'}
              data={GYB_LIST ?? []}
              getItemValue={item => item?.id}
              getItemLabel={item => 'GYB Event Date - ' + item?.dateTime}
            />

            {/* 2 */}
            <View style={{ gap: space[3] }}>
              <Typography.H7
                fontWeight="bold"
                children={t('candidates.approve.recruitSource')}
              />
              <Input
                control={control}
                name="recruitSourceID"
                as={RadioButtonGroup}>
                <Row>
                  {RECRUIT_SOURCE?.map(item => {
                    return (
                      <RadioButton
                        key={'recruitSourceID_' + item?.id}
                        value={item?.id}
                        label={item?.name}
                        style={{ flex: 1 }}
                      />
                    );
                  })}
                </Row>
              </Input>
            </View>

            {/* 3 */}
            {recruitSourceID === 2 && (
              <View style={{ gap: space[3] }}>
                <Typography.H7 fontWeight="bold" children={'Please select'} />
                <Input
                  control={control}
                  name="recruitSourceProgramID"
                  as={RadioButtonGroup}>
                  {RECRUIT_SOURCE_PROGRAMS?.map(item => {
                    return (
                      <RadioButton
                        key={'recruitSourceProgramID_' + item?.id}
                        value={item?.id}
                        label={item?.name}
                        style={{ flex: 1 }}
                      />
                    );
                  })}
                </Input>
              </View>
            )}

            {/* 4 */}
            <View style={{ gap: space[3] }}>
              <Typography.H7
                fontWeight="bold"
                children={t('candidates.approve.agentType')}
              />
              <Input control={control} name="agentTypeID" as={RadioButtonGroup}>
                <Row>
                  {AGENT_TYPE?.map(item => {
                    return (
                      <RadioButton
                        key={'agentTypeID_' + item?.id}
                        value={item?.id}
                        label={item?.name}
                        style={{ flex: 1 }}
                      />
                    );
                  })}
                </Row>
              </Input>
            </View>

            {/* 5 */}
            <View style={{ gap: space[3] }}>
              <Typography.H7
                fontWeight="bold"
                children={t('candidates.approve.designation')}
              />
              <Input
                control={control}
                name="designationID"
                as={RadioButtonGroup}>
                {DESIGNATION?.map(item => {
                  return (
                    <RadioButton
                      key={'designationID_' + item?.id}
                      value={item?.id}
                      label={item?.name}
                      style={{ flex: 1 }}
                    />
                  );
                })}
              </Input>
            </View>
          </ScrollView>

          <FormAction
            primaryLabel={'Done'}
            primaryDisabled={!isValid}
            onPrimaryPress={() => onPrimaryPress()}
          />
        </ActionPanel>
      </Column>
    </>
  );
}
