import { useTheme } from '@emotion/react';
import { yupResolver } from '@hookform/resolvers/yup';
import Input from 'components/Input';
import PhoneField from 'components/PhoneField';
import {
  ActionPanel,
  ActionPanelProps,
  Column,
  PictogramIcon,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import { contactDetailsSchema } from 'features/eRecruit/ph/components/ApplicationForm/applicationFormSchema';
import { ContactDetailsForm } from 'features/eRecruit/ph/types';
import { Dispatch, SetStateAction } from 'react';
import { useForm, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Platform, useWindowDimensions } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type PanelProps = ActionPanelProps & {
  visible: boolean;
  setIsContactDetailsCompleted: Dispatch<SetStateAction<boolean>>;
};

export default function ContactDetailsActionPanel({
  visible,
  handleClose,
  setIsContactDetailsCompleted,
}: PanelProps) {
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  // Main formState
  const { getValues: getMainFormValues, setValue: setMainFormValues } =
    useFormContext<ContactDetailsForm>();

  // Local formState
  const {
    control,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm({
    defaultValues: {
      emailAddress: '',
      mobileNumber: '',
    },
    mode: 'onBlur',
    resolver: yupResolver(contactDetailsSchema),
  });
  const emailAddress = watch('emailAddress');
  const mobileNumber = watch('mobileNumber');

  const handlePanelClose = () => {
    const formValues = getMainFormValues('contactDetails');
    setValue('emailAddress', formValues?.emailAddress);
    setValue('mobileNumber', formValues?.mobileNumber);
    handleClose();
  };

  const onPrimaryPress = () => {
    setMainFormValues('contactDetails.emailAddress', emailAddress);
    setMainFormValues('contactDetails.mobileNumber', mobileNumber);
    setIsContactDetailsCompleted(true);
    handleClose();
  };

  return (
    <>
      <Column>
        <ActionPanel
          visible={visible}
          handleClose={() => handlePanelClose()}
          contentContainerStyle={{
            paddingBottom: Platform.select({
              android: space[4] + bottom,
              ios: 0,
            }),
            maxHeight: height * 0.75,
            paddingHorizontal: 0,
          }}>
          <ScrollView
            contentContainerStyle={{
              paddingHorizontal: space[4],
              paddingBottom: space[4],
              gap: space[6],
            }}>
            <Row alignItems="center" gap={space[2]}>
              <PictogramIcon.ActivePhoneCall />
              <Typography.LargeLabel
                fontWeight="bold"
                children={'Contact details'}
                color={colors.primary}
              />
            </Row>

            <Input
              control={control}
              as={TextField}
              name="emailAddress"
              label={'Email'}
              error={errors?.emailAddress?.message}
            />

            <Row gap={space[4]}>
              <TextField
                disabled
                label={'Code'}
                value={'+63'}
                style={{ minWidth: space[26] }} // same as in AddLeadForm
              />
              <Input
                control={control}
                as={PhoneField}
                name="mobileNumber"
                label={'Mobile number'}
                style={{ flex: 1 }}
                error={errors?.mobileNumber?.message}
                keyboardType="phone-pad"
              />
            </Row>
          </ScrollView>

          <FormAction
            primaryLabel={'Done'}
            primaryDisabled={!isValid}
            onPrimaryPress={() => onPrimaryPress()}
          />
        </ActionPanel>
      </Column>
    </>
  );
}
