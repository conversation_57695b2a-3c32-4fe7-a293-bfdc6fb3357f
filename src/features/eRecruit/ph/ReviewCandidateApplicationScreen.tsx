import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { H7, Label, Row } from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import SrtFooter from './tablet/components/SrtFooter';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import PhoneScreenHeader from 'navigation/components/ScreenHeader/phone';
import TabletScreenHeader from 'navigation/components/ScreenHeader/tablet';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, View } from 'react-native';
import { RootStackParamList } from 'types';
import CandidateApproveActionPanel from './components/ActionPanel/CandidateApproveActionPanel';
import CandidateRejectActionPanel from './components/ActionPanel/CandidateRejectActionPanel';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';

/**
 * For both tablet and mobile
 */
export default function ReviewCandidateApplicationScreen() {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const route =
    useRoute<RouteProp<RootStackParamList, 'ReviewCandidateApplication'>>();
  const { applicationInfo } = route.params;

  const { data: agentProfile } = useGetAgentProfile();
  const IS_LEADER = agentProfile?.designation?.toLowerCase() !== 'fwp';

  const [showApprovePanel, setShowApprovePanel] = useState(false);
  const [showRejectPanel, setShowRejectPanel] = useState(false);

  const PERSONAL_INFO_CONFIG = [
    {
      type: 'fullName',
      label: t('candidates.review.fullName'),
      value: applicationInfo?.recruitName ?? '--',
    },
    {
      type: 'gender',
      label: t('candidates.review.gender'),
      value: applicationInfo?.formattedGender ?? '--',
    },
    {
      type: 'dob',
      label: t('candidates.review.dob'),
      value: applicationInfo?.dob ?? '--',
    },
    {
      type: 'contactNumber',
      label: t('candidates.review.contactNumber'),
      value: applicationInfo?.mobileNumber ?? '--',
    },
    {
      type: 'email',
      label: t('candidates.review.email'),
      value: applicationInfo?.emailAddress ?? '--',
    },
    {
      type: 'recruiter',
      label: t('candidates.review.recruiter'),
      value: applicationInfo?.recruiterName ?? '--',
    },
  ];

  return (
    <>
      {isTabletMode ? (
        <TabletScreenHeader
          route={'ReviewCandidateApplication'}
          customTitle={t('candidates.review.title')}
          isLeftArrowBackShown
        />
      ) : (
        <PhoneScreenHeader
          route={'ReviewCandidateApplication'}
          customTitle={t('candidates.review.title')}
          leftChildren={<CustomHeaderBackButton />}
          showBottomSeparator={true}
        />
      )}

      <ScrollView
        bounces={false}
        style={{
          paddingTop: isTabletMode ? space[5] : space[4],
          paddingHorizontal: isTabletMode ? space[10] : space[4],
        }}>
        <TitleContainer>
          <Row style={{ alignItems: 'center' }}>
            <Title
              fontWeight="bold"
              children={t('candidates.review.personalInfo')}
            />
          </Row>
        </TitleContainer>

        <ContentContainer>
          {PERSONAL_INFO_CONFIG.map(({ type, label, value }) => {
            return (
              <View
                key={type}
                style={{
                  flexDirection: isTabletMode ? 'row' : 'column',
                  width: isTabletMode ? '45%' : '100%',
                }}>
                <FieldName children={label} style={{ flex: 1 }} />
                <Label children={value} style={{ flex: 1 }} />
              </View>
            );
          })}
        </ContentContainer>
      </ScrollView>

      {!IS_LEADER ? (
        <></>
      ) : isTabletMode ? (
        <SrtFooter
          label={t('approve')}
          onPress={() => setShowApprovePanel(true)}
          secondaryLabel={t('reject')}
          onSecondaryPress={() => setShowRejectPanel(true)}
          secondaryStyle={{ width: 200 }}
        />
      ) : (
        <FormAction
          primaryLabel={t('approve')}
          onPrimaryPress={() => setShowApprovePanel(true)}
          secondaryLabel={t('reject')}
          onSecondaryPress={() => setShowRejectPanel(true)}
        />
      )}

      {/* Approval panels */}
      <CandidateApproveActionPanel
        applicationInfo={applicationInfo}
        visible={showApprovePanel}
        handleClose={() => setShowApprovePanel(false)}
      />

      <CandidateRejectActionPanel
        applicationInfo={applicationInfo}
        visible={showRejectPanel}
        handleClose={() => setShowRejectPanel(false)}
      />
    </>
  );
}

const TitleContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.secondary,
  paddingVertical: theme.space[3],
  paddingHorizontal: theme.space[4],
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));

const Title = styled(H7)(({ theme }) => ({
  color: theme.colors.onSecondary,
}));

const ContentContainer = styled.View(({ theme }) => ({
  flexWrap: 'wrap',
  flexDirection: 'row',
  backgroundColor: theme.colors.background,
  paddingTop: theme.space[3],
  paddingHorizontal: theme.space[4],
  paddingBottom: theme.space[4],
  borderWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
  borderBottomLeftRadius: theme.borderRadius.large,
  borderBottomRightRadius: theme.borderRadius.large,
  gap: theme.space[5],
}));

const FieldName = styled(Label)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarker,
}));
