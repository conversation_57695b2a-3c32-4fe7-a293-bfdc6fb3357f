import { Maybe } from 'yup';

/**
 * Overview
 */
export type PurlResponse = {
  purl: string;
  statusCode: number;
  message: string;
};

export type GenesisPurlResponse = {
  genesis: string;
};

export type ApplicationStatusResponse = {
  'mtd-applicant-status': {
    'gyb-attendees': number;
    'exam-passers': number;
    'exam-registered': number;
    'document-completion': number;
    training: number;
    inactive: number;
  };
  'ytd-applicant-status': {
    'gyb-attendees': number;
    'exam-passers': number;
    'exam-registered': number;
    'document-completion': number;
    training: number;
    inactive: number;
  };
  statusCode: number;
  message: string;
};

type ApplicationCountItem = {
  registered: number;
  coded: number;
  year: string;
  month:
    | '01'
    | '02'
    | '03'
    | '04'
    | '05'
    | '06'
    | '07'
    | '08'
    | '09'
    | '10'
    | '11'
    | '12';
};

export type ApplicationCountResponse = {
  'mtm-data': ApplicationCountItem[];
  'yty-data': ApplicationCountItem[];
  statusCode: number;
  message: string;
};

/**
 * Progress meter in candidate profile screen
 */
export type ProgressMeterModule =
  | 'GYB'
  | 'Trainings'
  | 'Application Form'
  | 'Exam'
  | 'Documents'
  | 'Payments'
  | 'Clearance';

export type ProgressMeterListData = {
  title: string;
  status: 'Pending' | 'Approved' | 'Completed';
  'completed-date': string;
};

export type ProgressMeterData = {
  module: ProgressMeterModule;
  isCompleted: boolean;
  'module-status': string;
  'module-completed-date': string;
  list: ProgressMeterListData[];
};

export type ProgressMeterResponse = {
  data: ProgressMeterData[];
  statusCode: number;
  message: string;
  completionPercentage: number;
};

/**
 * Candidates
 */
export type CandidateStatus = 'toDo' | 'approved' | 'declined';

export type CandidateStatusKeyType = 'ToDo' | 'Approved' | 'Declined'; // API response status type

export type PendingLicenseKeyType = 'Dual' | 'Traditional' | 'Variable'; // API response pending license type

export type FilterStatusKeyType =
  // API response filter status type
  | 'pending-review'
  | 'auth-to-deduct'
  | 'pending-dual'
  | 'pending-trad'
  | 'pending-var'
  | 'completed';

export type CandidateListItem = {
  recruitID: number;
  recruitName: string;
  birthDate: string;
  gender: string;
  emailAddress: string;
  mobileNumber: string;
  recruiterID: string;
  recruiterName: string;
  branch: string;
  status: CandidateStatusKeyType;
  filterStatus: FilterStatusKeyType;
  approveRegistrationDueDate: string;
  registeredDate: string;
  updated_at: string;
  pendingLicenseType: PendingLicenseKeyType;
  authToDeductLicenseTypeRequest: any[];
  disapproveReason?: string;
  approveRegistrationDateTime?: string;
  declinedRegistrationDateTime?: string;
};

export type RecruitsListRequestBody = {
  case: {
    recruitment: {
      leaderNo: string;
      searchText: string;
      sortBy: 'ASC' | 'DESC';
      status: CandidateStatusKeyType;
    };
  };
};

export type RecruitsListResponse = {
  recruits: CandidateListItem[];
  statusCode: number;
  message: string;
  totalRecordsCount: number;
};

/**
 * Recruit Approval
 */
export type ApplicationInfo = {
  // Pass values to ReviewCandidateApplicationScreen and CandidateProfileScreen by params
  status: CandidateStatusKeyType;
  recruitID: number;
  recruitName: string;
  recruiterID: string;
  recruiterName: string;
  formattedGender: string;
  dob: string;
  age: string;
  emailAddress: string;
  mobileNumber: string;
  formattedRegDate: string;
  disapproveReason?: string;
  formattedDeclinedDate?: string;
};

export type RecruitApprovalRequestBody = {
  case: {
    recruitment: {
      recruiterNo: string;
      recruitID: number;
      isApprove: 'True' | 'False';
      disapproveReason?: string;
      designationID?: number;
      recruitSourceID?: number;
      agentTypeID?: number;
      recruitSourceProgramID?: number;
    };
  };
};

export type TableType =
  | 'designation'
  | 'license-type'
  | 'authority-to-deduct'
  | 'recruit-source'
  | 'agent-type'
  | 'recruit-source-programs'
  | 'exam-platform'
  | 'suffix'
  | 'gyb-list';

export type MaintenanceTableResponse = {
  data: {
    id?: number;
    name?: string;
    amount?: null;
    //
    date?: string; // gyb-list
    venue?: string; // gyb-list
    GybTypeID?: number; // gyb-list
    dateTime?: string; // gyb-list
    //
    ExamPlatformID?: number; // exam-platform only
    PlatformName?: string; // exam-platform only
    LicenseTypeID?: number; // exam-platform only
    //
    SuffixID?: number; // suffix only
    SuffixName?: string; // suffix only
  }[];
  statusCode: number;
  message: string;
};

/**
 * Exam
 */
export type ExamItem = {
  LicenseTypeID: number;
  LicenseType: string;
  ExamName: string;
  ExamPlatformID: number;
  ExamPlatform: string;
  Venue: string;
  ExamDateTime: string;
};

/**
 * Application form (main formSate)
 */
export type ApplicationFormData = {
  identityDetails: {
    firstName: string;
    middleName?: string;
    lastName: string;
    suffix?: string;
    gender: string;
    birthday: string;
  };
  contactDetails: {
    emailAddress: string;
    mobileNumber: string;
  };
  candidateInfo: {
    gybScheduleID: number;
    designationID: number;
    recruitSourceID: number;
    agentTypeID: number;
    recruitSourceProgramID: number;
  };
  declaration: {
    hasConflictOfInterest: number;
  };
};

/**
 * Application form (each part in useFormContext)
 */
export type IdentityDetailsForm = {
  identityDetails: {
    firstName: string;
    middleName: string;
    lastName: string;
    suffix: string;
    gender: string;
    birthday: string;
  };
};

export type ContactDetailsForm = {
  contactDetails: {
    emailAddress: string;
    mobileNumber: string;
  };
};

export type CandidateInfoForm = {
  candidateInfo: {
    gybScheduleID: Maybe<number | undefined>;
    designationID: number;
    recruitSourceID: number;
    agentTypeID: number;
    recruitSourceProgramID: number;
  };
};

export type Declaration = {
  declaration: {
    hasConflictOfInterest: number; // 1 for Yes, 0 for No
  };
};
