export default {
  'eRecruit.home': '<PERSON><PERSON><PERSON>',
  'eRecruit.header': 'e-Recruit',
  'eRecruit.searchCandidate': '<PERSON><PERSON>',
  'eRecruit.searchCandidate.hints': 'e.g. Candidate’s name, mobile',
  'eRecruit.recruitment': 'Rekrutmen',
  'eRecruit.application.shareLink': 'Bagikan tautan jarak jauh',
  'eRecruit.application.startApplication': '<PERSON><PERSON>',
  'eRecruit.application.applicationStatus': 'Status Aplikasi',
  'eRecruit.application.viewAll': 'Lihat Semua',
  'eRecruit.application.remote': 'Remote',
  'eRecruit.application.newApplication': 'Aplikasi Baru',
  'eRecruit.application.shareApplicationLink': 'Bagikan Tautan aplikasi',
  'eRecruit.application.shareApplicationLink.modal.': 'Bagikan tautan aplikasi',
  'eRecruit.remote.modal.title': '<PERSON>gikan tautan aplikasi',
  'eRecruit.remote.modal.description':
    'Untuk memastikan rekrutmen di bawah Anda <PERSON> tim <PERSON>, mohon bagikan tautan aplikasi unik ini kepada kandidat Anda',
  'eRecruit.remote.linkCopied': 'Tautan disalin',
  'eRecruit.application.sms': 'SMS',
  'eRecruit.application.copyLink': 'Salin Tautan',
  'eRecruit.application.others': 'Lainnya',

  'eRecruit.recruitmentProgress': 'Progres Rekrutmen',
  'eRecruit.week': 'Minggu Ini',
  'eRecruit.month': 'Bulan Ini',
  'eRecruit.candidate': 'Kandidat',
  'eRecruit.candidates': 'Para Kandidat',
  'eRecruit.submitted': 'Dikirim',
  'eRecruit.approved': 'Disetujui',
  'eRecruit.recruitmentConversionThis': 'Konversi Rekrutmen Ini',
  'eRecruit.asOf': 'pada',
  'eRecruit.candidatesInProgress': 'Kandidat dalam proses',
  'eRecruit.targetVsActualNumber':
    'Target {{timePeriodLabel}} Ini VS Angka Sebenarnya',

  'eRecruit.targetOfThis': 'Target dari ini',
  'eRecruit.this': 'Ini',
  'eRecruit.operationData': 'Oops ! Data Kosong.',
  'eRecruit.noTarget': 'Belum ada target yang telah diatur',
  'eRecruit.edit': 'Sunting',
  'eRecruit.thisCycle': 'Siklus {{timeSection}} Ini',
  'eRecruit.pleaseTryAgainLater': 'Mohon coba lagi nanti',
  'eRecruit.ok': 'OK',

  'eRecruit.candidate.filterBy': 'Saring berdasarkan',
  'eRecruit.candidate.sortBy': 'Diatur Berdasarkan',
  'eRecruit.candidate.latestUpdated': 'Pembaharuan Terakhir',
  'eRecruit.candidate.latestCreated': 'Pembuatan Terakhir',
  'eRecruit.candidate.candidates': 'Para Kandidat',
  'eRecruit.candidate.pendingPayment': 'Pembayaran tertunda',
  'eRecruit.candidate.pendingLeaderApproval':
    'Persetujuan leader yang tertunda',
  'eRecruit.candidate.approved': 'Disetujui',
  'eRecruit.candidate.rejected': 'Ditolak',
  'eRecruit.candidate.declined': 'Ditolak',
  'eRecruit.candidate.remoteChecking': 'Pengecekan Remote',
  'eRecruit.candidate.remoteSignature': 'Tanda tangan remote',
  'eRecruit.candidate.resumeApplication': 'Resume application',
  'eRecruit.candidate.created': 'Created',
  'eRecruit.candidate.addNewCandidate': 'Tambah kandidat baru',
  'eRecruit.candidate.firstName': 'Nama Pertama',
  'eRecruit.candidate.lastName': 'Nama Akhir',
  'eRecruit.candidate.fullName': 'Nama Lengkap',
  'eRecruit.candidate.gender': 'Jenis Kelamin',
  'eRecruit.candidate.male': 'Laki-Laki',
  'eRecruit.candidate.female': 'Perempuan',
  'eRecruit.candidate.code': 'Kode',
  'eRecruit.candidate.phoneNumber': 'Nomor Telepon',
  'eRecruit.candidate.email': 'Email',
  'eRecruit.candidate.cancel': 'Batal',
  'eRecruit.candidate.save': 'Simpan',
  'candidates.profile.declined': 'Ditolak',
  'eRecruit.candidateProfile': 'Profil Kandidat',
  'eRecruit.candidateProfile.profile': 'Profil',
  'eRecruit.candidateProfile.phoneNumberAndEmailAreNotProvided':
    'Nomor telepon dan email tidak disediakan',
  'eRecruit.candidateProfile.personalInfo': 'Data Diri',

  'eRecruit.candidateProfile.name': 'Nama',
  'eRecruit.candidateProfile.gender': 'Jenis Kelamin',
  'eRecruit.candidateProfile.contactNo': 'Nomor Kontak',
  'eRecruit.candidateProfile.email': 'Email',
  'eRecruit.candidateProfile.source': 'Sumber',
  'eRecruit.candidateProfile.candiateNo': 'Nomor Kandidat',
  'eRecruit.candidateProfile.paymentLink': 'Payment Link',
  'eRecruit.candidateProfile.remoteSignatureLink': 'Remote signature link',
  'eRecruit.candidateProfile.shareLinkToComplete':
    'Bagikan tautan untuk menyelesaikan',

  'eRecruit.shareRemoteLink.sms': 'SMS',
  'eRecruit.shareRemoteLink.email': 'Email',
  'eRecruit.shareRemoteLink.more': 'Lainnya',
  'eRecruit.shareRemoteLink.copyLink': 'Salin tautan',
  'eRecruit.shareRemoteLink.whatsapp': 'WhatsApp',
  'eRecruit.shareRemoteLink.shareLinkToComplete':
    'Bagikan tautan untuk menyelesaikan',
  'eRecruit.shareRemoteLink.shareLinkToComplete.desc':
    'Mohon bagikan tautan tanda tangan remote ini kepada kandidat Anda',
  'eRecruit.shareRemoteLink.linkIsNotAvailable':
    'Tautan tidak tersedia. Mohon coba lagi nanti',
  'eRecruit.shareRemoteLink.linkCopied': 'Tautan Disalin',
  'eRecruit.shareRemoteLink.msg':
    'Please share this remote signature link to your candidate.',
  'eRecruit.shareRemoteLink.msg,ifNotReceive':
    'Please send this link to your candidates if they cannot receive it.',

  'eRecruit.candidateProfile.createDate': 'Tanggal Pembuatan',
  'eRecruit.candidateProfile.candidateStatus': 'Status kandidat',
  'eRecruit.candidateProfile.applicationStatus': 'Status aplikasi',
  'eRecruit.candidateProfile.status': 'Status',
  'eRecruit.candidateProfile.updateDate': 'Tanggal diperbaharui',
  'eRecruit.candidateProfile.visited': 'Dikunjungi',
  'eRecruit.candidateProfile.remoteApplicationDone': 'Aplikasi remote selesai',
  'eRecruit.candidateProfile.agentReviewSubmit': 'Tinjau agen dan ajukan',
  'eRecruit.candidateProfile.signedAndSubmitted':
    'Tertandatangani Dan Diajukan',
  'eRecruit.candidateProfile.applicationSubmitted': 'Aplikasi diajukan',
  'eRecruit.candidateProfile.applicationInProgress': 'Application in progress',
  'eRecruit.candidateProfile.candidateSignatureSubmitted':
    'Tanda Tangan Terkirim',
  'eRecruit.candidateProfile.candidateSignature': 'Candidate signature',
  'eRecruit.candidateProfile.awaitingCandidateSignature':
    'Awaiting candidate signature',
  'eRecruit.candidateProfile.paid': 'Terbayar',
  'eRecruit.candidateProfile.pendingPayment': 'Pembayaran tertunda',
  'eRecruit.candidateProfile.leaderRejected': 'leader ditolak',
  'eRecruit.candidateProfile.systemRejected': 'System rejected',
  'eRecruit.candidateProfile.leaderApproved': 'leader disetujui',
  'eRecruit.candidateProfile.pendingLeaderApproval':
    'Persetujuan leader tertunda',
  'eRecruit.candidateProfile.leaderApproval': 'Persetujuan leader',
  'eRecruit.candidateProfile.resumeApplicationWarning':
    'Pengajuan belum lengkap, mohon lanjutkan pengajuan',
  'eRecruit.candidateProfile.remoteSignatureWarning':
    'Pengajuan ini sedang tertunda untuk tanda tangan remote. Kirim tautan lagi.',

  'eRecruit.candidateProfile.removeCandidate': 'Hapus Kandidat',
  'eRecruit.candidateProfile.removecandidate': 'Hapus Kandidat',
  'eRecruit.candidateProfile.candidateIsRemove': 'Kandidat Dihapus',
  'eRecruit.candidateProfile.candidateIsRemoveFail':
    'Terjadi kesalahan menghapus kandidat. Silakan coba lagi',
  'eRecruit.candidateProfile.areYouSure':
    'Apakah Anda yakin ingin menghapus? Data tidak bisa dipulihkan setelah dihapus',
  'eRecruit.candidateProfile.cancel': 'Batal',
  'eRecruit.candidateProfile.yes': 'Ya, Saya Yakin',
  'eRecruit.candidateProfile.no': 'Go back',
  'eRecruit.candidateProfile.shareRemoteLink': 'Bagikan tautan jarak jauh',
  'eRecruit.candidateProfile.resumeApplication': 'Lanjutkan aplikasi',
  'eRecruit.candidateProfile.checkApplication': 'Periksa aplikasi',
  'eRecruit.candidateProfile.startApplication': 'Mulai aplikasi',
  'eRecruit.candidateProfile.linkCopied': 'Tautan Disalin',
  'eRecruit.candidateProfile.agentCodeIssued': 'Kode agen terbit',
  'eRecruit.candidateProfile.agentCode': 'Kode agen',
  'eRecruit.candidateProfile.licenseActivated': 'Lisensi telah aktif',
  'eRecruit.candidateProfile.approvedBy': 'Disetujui Oleh',
  'eRecruit.candidateProfile.rejectedBy': 'Ditolak Oleh',
  'eRecruit.candidateProfile.progressBar.await':
    'Aplikasi tertunda - Mohon lengkapi formulir',
  'eRecruit.candidateProfile.progressBar.pendingSignature':
    'Aplikasi tertunda - Tanda tangan kandidat belum lengkap',
  'eRecruit.candidateProfile.progressBar.pendingApproval':
    'Pengajuan Hampir Diselesaikan - Persetujuan leader Tertunda',
  'eRecruit.candidateProfile.progressBar.licensingCompletion':
    'Aplikasi disetujui , tetapi membutuhkan penyelesaian lisensi keagenan',
  'eRecruit.candidateProfile.progressBar.readyToGo':
    'Agen telah diaktifkan - Anda siap berangkat!',
  'eRecruit.candidateProfile.progressBar.inactiveAccount':
    'Akun agen tidak aktif',
  'eRecruit.candidateProfile.progressBar.activeAccount': 'Akun agen Aktif!',
  'eRecruit.candidateProfile.reviewerComment': 'Komentar peninjau',
  'eRecruit.candidateProfile.autoDeclinedOn': 'Ditolak otomatis pada {{date}}',
  'eRecruit.candidateProfile.createdOn': 'Dibuat pada tanggal {{date}}',

  // Application Form - Personal Details
  'eRecruit.application.personalDetails.identityDetails': 'Rincian identitas',
  'eRecruit.application.personalDetails.essentialInformation':
    'Essential information',
  'eRecruit.application.personalDetails.backgroundDetails':
    'Background details',
  'eRecruit.application.personalDetails.name': 'Nama',
  'eRecruit.application.personalDetails.fullName': 'Nama lengkap',

  'eRecruit.application.personalDetails.firstName': 'Nama pertama',
  'eRecruit.application.personalDetails.lastName': 'Nama belakang',
  'eRecruit.application.personalDetails.gender': 'Jenis kelamin',
  'eRecruit.application.personalDetails.title': 'Gelar',
  'eRecruit.application.personalDetails.dateOfBirth': 'Tanggal Lahir',
  'eRecruit.application.personalDetails.age': ' Umur',
  'eRecruit.application.personalDetails.icNumber': 'Nomor identitas',
  'eRecruit.application.personalDetails.icNumber.optional':
    'Nomor NRIC (opsional)',
  'eRecruit.application.personalDetails.identity': 'Identitas',
  'eRecruit.application.personalDetails.birthPlace': 'Tempat lahir',

  'eRecruit.application.personalDetails.citizen': 'Warga negara',
  'eRecruit.application.personalDetails.ethnicity': 'Etnis',
  'eRecruit.application.personalDetails.religion': 'Agama',
  'eRecruit.application.personalDetails.maritalStatus': 'Status pernikahan',
  'eRecruit.application.personalDetails.numberOfDependents':
    'Jumlah tanggungan',
  'eRecruit.application.personalDetails.npwp': 'NPWP',
  'eRecruit.application.personalDetails.npwp.optional': 'NPWP (opsional)',
  'eRecruit.application.personalDetails.npwp.hint': '16 digit angka',
  'eRecruit.application.personalDetails.passport': 'Nomor paspor (opsional)',
  'eRecruit.application.personalDetails.passportNumber': 'Nomor paspor',
  'eRecruit.application.personalDetails.oldIc':
    'IC Lama/Polisi/Tentara (opsional)',
  'eRecruit.application.personalDetails.oldIc/Police/Army':
    'IC Lama/Polisi/Tentara',
  'eRecruit.application.personalDetails.incomeTax':
    'Nomor file pajak penghasilan',
  'eRecruit.application.personalDetails.incomeTaxFileNumber':
    'Nomor file pajak penghasilan',
  'eRecruit.application.personalDetails.yearsOld': '({{age}} tahun)',
  'eRecruit.application.personalDetails.contactDetails': 'Rincian kontak',
  'eRecruit.application.personalDetails.code': 'Kode',
  'eRecruit.application.personalDetails.mobileNumber': 'Nomor ponsel',
  'eRecruit.application.personalDetails.phoneNumber': 'Phone number',
  'eRecruit.application.personalDetails.address': 'Address',
  'eRecruit.application.personalDetails.email': 'Email',
  'eRecruit.application.personalDetails.officePhoneOptional':
    'Telepon kantor (opsional)',
  'eRecruit.application.personalDetails.officePhone': 'Telepon kantor',
  'eRecruit.application.personalDetails.qualification': 'Kualifikasi',
  'eRecruit.application.personalDetails.academicQualification':
    'Kualifikasi akademik',
  'eRecruit.application.personalDetails.education': 'Pendidikan',
  'eRecruit.application.personalDetails.industry': 'Industri',
  'eRecruit.application.personalDetails.presentOccupation':
    'Pekerjaan saat ini',

  'eRecruit.application.personalDetails.takaful':
    'Sertifikat takaful (Pilih 1 atau lebih)',
  'eRecruit.application.personalDetails.tbeFamily': 'TBE Keluarga',
  'eRecruit.application.personalDetails.tbeGeneral': 'TBE Umum',
  'eRecruit.application.personalDetails.insuranceCertificate':
    'Sertifikat asuransi',
  'eRecruit.application.personalDetails.insuranceCertificate.oneOrMore':
    'Sertifikat asuransi (Pilih 1 atau lebih)',

  'eRecruit.application.personalDetails.pce': 'PCE',
  'eRecruit.application.personalDetails.ceilli': 'CEILLI',
  'eRecruit.application.personalDetails.general': 'Umum',
  'eRecruit.application.personalDetails.otherInsuranceQualifications':
    'Kualifikasi asuransi lainnya',
  'eRecruit.application.personalDetails.otherInsuranceQualifications.oneOrMore':
    'Kualifikasi asuransi lainnya (Pilih 1 atau lebih)',
  'eRecruit.application.personalDetails.islamic': 'Islamic RFP/CFP/ChFC',
  'eRecruit.application.personalDetails.module2MFPC':
    'Modul 2 (Manajemen Risiko dan Perencanaan Takaful) - Perencana Keuangan Terdaftar Syariah yang ditawarkan oleh MFPC',
  'eRecruit.application.personalDetails.yearOfPassing': 'Tahun kelulusan',
  'eRecruit.application.personalDetails.module2FPAM':
    'Modul 2 (Manajemen Risiko dan Perencanaan Takaful) - Perencanaan Keuangan Islam yang ditawarkan oleh FPAM',
  'eRecruit.application.personalDetails.otherQualifications':
    'Kualifikasi profesional lainnya',
  'eRecruit.application.personalDetails.spouseInformation':
    'Informasi pasangan',
  'eRecruit.application.personalDetails.numOfDependents': 'Jumlah tanggungan',
  'eRecruit.application.personalDetails.occupation': 'Pekerjaan',
  'eRecruit.application.personalDetails.occupationDetails': 'Rincian pekerjaan',
  'eRecruit.application.personalDetails.popup.title': 'TBE Keluarga',
  'eRecruit.application.personalDetails.popup.content':
    'Untuk memproses aplikasi, kandidat harus memiliki sertifikat Takaful TBE Keluarga. Apakah kandidat Anda memenuhi syarat dengan sertifikat TBE Keluarga?',
  'eRecruit.application.personalDetails.popup.content.V2':
    'Untuk menyelesaikan aplikasi, kandidat harus memiliki sertifikasi PCE. Memilih "Ya" akan secara otomatis memperbarui status PCE kandidat.',
  'eRecruit.application.personalDetails.popup.title.PCE': 'PCE',
  'eRecruit.application.personalDetails.popup.title.PCE.V2':
    'Apakah kandidat Anda bersertifikat PCE?',
  'eRecruit.application.personalDetails.targetCert.PCE': 'PCE',
  'eRecruit.application.personalDetails.targetCert.TakafulTBEFamily':
    'Takaful TBE Keluarga',
  'eRecruit.application.personalDetails.popup.contentWithTargetCert':
    'Untuk memproses aplikasi, kandidat harus memiliki sertifikat {{target}}. Apakah kandidat Anda memenuhi syarat dengan sertifikat {{target}}?',
  'eRecruit.application.personalDetails.popup.yes':
    'Ya. TBE Keluarga Memenuhi Syarat',
  'eRecruit.application.personalDetails.popup.yesWithTargetCert':
    'Ya. {{target}} Memenuhi Syarat',
  'eRecruit.application.personalDetails.popup.yesWithTargetCert.V2':
    'Ya, PCE Memenuhi Syarat',
  'eRecruit.application.personalDetails.popup.no': 'Tidak. Akhiri aplikasi',
  'eRecruit.application.personalDetails.popup.no.V2':
    'Tidak, keluar dari aplikasi',
  'eRecruit.application.personalDetails.goToTheField': 'Pergi ke lapangan',
  'eRecruit.application.personalDetails.incompleteFields':
    '{{total}} bidang yang tidak lengkap',
  'eRecruit.application.personalDetails.nricPlaceholder': 'YYMMDD - PB - 000G',
  'eRecruit.application.personalDetails.nricHint': 'YYMMDD-PB-###G',
  'eRecruit.application.personalDetails.insuranceExperience':
    'Pengalaman asuransi',
  'eRecruit.application.personalDetails.questionOne':
    'Apakah Anda pernah bekerja di perusahaan asuransi jiwa?',
  'eRecruit.application.personalDetails.questionTwo':
    'Apakah Anda pernah bekerja di perusahaan asuransi non-jiwa?',
  'eRecruit.application.personalDetails.question.yes': 'Ya',
  'eRecruit.application.personalDetails.question.no': 'Tidak',
  'eRecruit.application.personalDetails.agentApproval.withEmailParam':
    'Agent candidates must complete a binding approval letter from the current employer by sending them to <emailLink>{{email}}</emailLink> or via FWD Ping!',
  'eRecruit.application.personalDetails.submissionError':
    'Sorry, we cannot proceed',
  'eRecruit.application.personalDetails.backToHome': 'Back to home',

  // Application Form - Occupation Details
  'eRecruit.application.dateHint': 'DD/MM/YYYY',
  'eRecruit.application.occupationDetails.currentOccupation':
    'Pekerjaan saat ini',
  'eRecruit.application.occupationDetails.currentOccupation(optional).phone':
    'Pekerjaan saat ini (opsional)',
  'eRecruit.application.occupationDetails.position': 'Jabatan yang dipegang',
  'eRecruit.application.occupationDetails.nameOfCompany(inFull)':
    'Nama perusahaan (lengkap)',
  'eRecruit.application.occupationDetails.companyName': 'Nama perusahaan',
  'eRecruit.application.occupationDetails.basicSalary(RM)': 'Gaji terakhir',
  'eRecruit.application.occupationDetails.basicSalary': 'Gaji terakhir',
  'eRecruit.application.occupationDetails.dateAppoint': 'Tanggal diangkat',
  'eRecruit.application.occupationDetails.dateTermination': 'Tanggal pemutusan',
  'eRecruit.application.occupationDetails.companyPhoneCountryCode': 'Kode',
  'eRecruit.application.occupationDetails.contactNumber': 'Nomor kontak',
  'eRecruit.application.occupationDetails.contactNo': 'Nomor kontak',
  'eRecruit.application.occupationDetails.companyEmail': 'Email',
  'eRecruit.application.occupationDetails.companyAddress': 'Alamat perusahaan',
  'eRecruit.application.occupationDetails.previousOccupation':
    'Pekerjaan sebelumnya',
  'eRecruit.application.occupationDetails.previousOccupation(optional)':
    'Pekerjaan sebelumnya (opsional)',
  'eRecruit.application.occupationDetails.previousOccupation(optional).phone':
    'Pekerjaan sebelumnya (opsional)',
  'eRecruit.application.occupationDetails.company': 'Perusahaan',
  'eRecruit.application.occupationDetails.add': 'Tambah',
  'eRecruit.application.occupationDetails.family(optional)':
    'Pengalaman Takaful Keluarga / Umum (opsional)',
  'eRecruit.application.occupationDetails.family(optional).phone':
    'Pengalaman Takaful Keluarga / Umum (opsional)',
  'eRecruit.application.occupationDetails.family':
    'Pengalaman Takaful Keluarga / Umum',
  'eRecruit.application.occupationDetails.typeOfIntermediary':
    'Jenis perantara',
  'eRecruit.application.occupationDetails.rank': 'Pangkat',
  'eRecruit.application.occupationDetails.lifeInsurance(optional)':
    'Pengalaman Asuransi Jiwa / Umum (opsional)',
  'eRecruit.application.occupationDetails.lifeInsurance(optional).phone':
    'Pengalaman Asuransi Jiwa / Umum (opsional)',
  'eRecruit.application.occupationDetails.lifeInsurance':
    'Pengalaman Asuransi Jiwa / Umum',
  'eRecruit.application.occupationDetails.spouseInsurance':
    'Pengalaman Asuransi Pasangan',
  'eRecruit.application.occupationDetails.spouseTakaful':
    'Apakah pasangan Anda saat ini mewakili perusahaan Takaful atau Asuransi',
  'eRecruit.application.occupationDetails.spouseTakafulState':
    'Pasangan Anda saat ini mewakili perusahaan Takaful atau Asuransi',
  'eRecruit.application.occupationDetails.yes': 'Ya',
  'eRecruit.application.occupationDetails.no': 'Tidak',
  'eRecruit.application.occupationDetails.otherDetails': 'Detail lainnya',

  // Application Form - Other Details
  'eRecruit.application.otherDetails.otherDetails': 'Detail lainnya',
  'eRecruit.application.otherDetails.saveForLater': 'Simpan untuk nanti',
  'eRecruit.application.otherDetails.next': 'Berikutnya',
  'eRecruit.application.otherDetails.reviewDocuments': 'Review documents',
  'eRecruit.application.otherDetails.yourDecision': 'Your decision',
  'eRecruit.application.otherDetails.review': 'Tinjau',
  'eRecruit.application.otherDetails.addressInformation': 'Informasi alamat',
  'eRecruit.application.otherDetails.residentialAddress':
    'Alamat tempat tinggal',
  'eRecruit.application.otherDetails.malaysia': 'Malaysia',
  'eRecruit.application.otherDetails.businessAddress': 'Alamat bisnis',
  'eRecruit.application.otherDetails.nationalIdAddress':
    'Alamat Identitas Nasional',
  'eRecruit.application.otherDetails.sameAsResidentialAddress':
    'Sama dengan alamat tempat tinggal',
  'eRecruit.application.otherDetails.addressLine': 'Baris alamat',

  'eRecruit.application.otherDetails.addressLine1': 'Baris alamat 1',
  'eRecruit.application.otherDetails.addressLine2': 'Baris alamat 2',
  'eRecruit.application.otherDetails.addressLine2.optional':
    'Baris alamat 2 (opsional)',
  'eRecruit.application.otherDetails.addressLine1.hint':
    'No. rumah/Unit/Lot, Blok, Tempat tinggal/Bangunan',
  'eRecruit.application.otherDetails.addressLine2.hint': 'No. jalan/Nama jalan',
  'eRecruit.application.otherDetails.neighborhoodAssociation': 'RT',
  'eRecruit.application.otherDetails.communityAssociation': 'RW',
  'eRecruit.application.otherDetails.subdistrict': 'Kelurahan/Desa',
  'eRecruit.application.otherDetails.district': 'Kecamatan',
  'eRecruit.application.otherDetails.province': 'Provinsi',
  'eRecruit.application.otherDetails.residenceNumber': 'Nomor tempat tinggal',
  'eRecruit.application.otherDetails.residenceNumber.optional':
    'Nomor tempat tinggal (opsional)',

  'eRecruit.application.otherDetails.residentialAddress.postCode': 'Kode pos',
  'eRecruit.application.otherDetails.residentialAddress.city': 'Kota',
  'eRecruit.application.otherDetails.residentialAddress.province': 'Provinsi',
  'eRecruit.application.otherDetails.residentialAddress.state': 'Negara bagian',
  'eRecruit.application.otherDetails.agencyType': 'Jenis agensi',
  'eRecruit.application.otherDetails.bankAccountInformation':
    'Informasi rekening bank',
  'eRecruit.application.otherDetails.emergencyContact': 'Kontak darurat',
  'eRecruit.application.otherDetails.emergencyContact.optional':
    'Kontak darurat (opsional)',
  'eRecruit.application.otherDetails.financialCondition': 'Kondisi keuangan',
  'eRecruit.application.otherDetails.complianceAndReputationRecords':
    'Catatan Kepatuhan dan Reputasi',
  'eRecruit.application.otherDetails.complianceReputationRecords':
    'Compliance & reputation records',
  'eRecruit.application.otherDetails.bankName': 'Nama bank',
  'eRecruit.application.otherDetails.branch': 'Cabang bank',
  'eRecruit.application.otherDetails.bankInfoQuestionOne':
    '1. Untuk kenyamanan dan kelancaran proses pembayaran komisi, disarankan menggunakan rekening gaji CIMBA NIAGA.',
  'eRecruit.application.otherDetails.bankInfoQuestionTwo':
    '2. Penerimaan komisi ke rekening selain CIMBA NIAGA akan dikenakan biaya transfer yang berlaku.',

  'eRecruit.application.otherDetails.accountNumber': 'Nomor rekening',
  'eRecruit.application.otherDetails.NRIC': 'Nomor NRIC sesuai catatan bank',
  'eRecruit.application.otherDetails.supervisorCandidateInformation':
    'Informasi supervisor & kandidat',
  'eRecruit.application.otherDetails.candidateInformation': 'Posisi kandidat',
  'eRecruit.application.otherDetails.salesOffice':
    'Kantor penjualan/GA/Titik layanan',
  'eRecruit.application.otherDetails.domicile': 'Domisili',
  'eRecruit.application.otherDetails.areaManager': 'Manajer wilayah',
  'eRecruit.application.otherDetails.supervisor': 'Supervisor',
  'eRecruit.application.otherDetails.ref': 'Ref',
  'eRecruit.application.otherDetails.financingProgram': 'Program tunjangan',
  'eRecruit.application.otherDetails.comment': 'Comments',
  'eRecruit.application.otherDetails.comment.optional': 'Komentar (opsional)',
  'eRecruit.application.otherDetails.comment.label':
    'Silakan tinggalkan komentar Anda',
  'eRecruit.application.otherDetails.regulatory.comment.label':
    'Leave your comment',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.jobType':
    'Jenis pekerjaan',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.agencyType':
    'Jenis agensi',
  'eRecruit.application.otherDetails.declarationOfCOI':
    'Deklarasi Konflik Kepentingan',
  'eRecruit.application.otherDetails.declarationOfCOI.phone':
    'Deklarasi Konflik Kepentingan',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.reportingBranch':
    'Cabang pelaporan',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition':
    'Posisi kandidat',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.introducerName':
    'Nama pengenal',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.introducerCode':
    'Kode pengenal',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.branchCode':
    'Kode cabang',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.branchName':
    'Nama cabang',
  'eRecruit.application.otherDetails.leaderInformation': 'Informasi leader',
  'eRecruit.application.otherDetails.leaderInformation.optional':
    'Informasi leader (opsional)',
  'eRecruit.application.otherDetails.leaderInformation.immediateLeaderName':
    'Nama atasan',
  'eRecruit.application.otherDetails.leaderInformation.immediateLeaderCode':
    'Kode atasan',
  'eRecruit.application.otherDetails.leaderInformation.ALCFWDName':
    'Nama ALC/FA',
  'eRecruit.application.otherDetails.leaderInformation.ALCFWDName.optional':
    'Nama ALC/FA (opsional)',
  'eRecruit.application.otherDetails.leaderInformation.check': 'Periksa',
  'eRecruit.application.otherDetails.uploadDocument': 'Upload Document',
  'eRecruit.application.otherDetails.remark': 'Remark',
  'eRecruit.inactiveUser': 'Inactive user',
  'application.otherDetails.agencyManagerCode': 'Kode manajer agensi',
  'application.otherDetails.agencyManagerName': 'Nama manajer agensi',
  'application.otherDetails.agentCodeVerifyMsg.success': 'Verifikasi berhasil',
  'application.otherDetails.agentCodeVerifyMsg.fail': 'Kode atasan tidak valid',
  'application.otherDetails.remark.optional': 'Remarks (optional)',
  'application.otherDetails.remark': 'Remarks',

  // Application Form - Declaration of COI
  'application.COI.record': 'Catatan {{number}}',
  'application.COI.addRecord': 'Tambahkan catatan',
  'application.COI.ownershipInterest': 'Kepentingan Kepemilikan',
  'application.COI.externalEmployment': 'Direktur Eksternal / Pekerjaan',
  'application.COI.businessAffiliationInterest': 'Kepentingan Afiliasi Bisnis',
  'application.COI.relationshipGovernmentOfficial':
    'Hubungan dengan Pejabat Pemerintah',
  'application.COI.otherInterest':
    'Konflik Kepentingan Lainnya yang Terlihat, Potensial atau Aktual',
  'application.COI.nameOfBusinessEnterpriseOrEntity':
    'Nama perusahaan / entitas',
  'application.COI.natureOfBusiness': 'Jenis bisnis',
  'application.COI.nameOfOwner': 'Nama pemilik',
  'application.COI.nameOfOwnerAndRelationship':
    'Name of owner and relationship',
  'application.COI.relationship': 'Hubungan',
  'application.COI.dateAcquired': 'Tanggal diperoleh',
  'application.COI.percentageOfOwnership': 'Persentase kepemilikan',
  'application.COI.position': 'Posisi',
  'application.COI.details': 'Rincian',
  'application.COI.detailsDescription':
    'misalnya Deskripsi Pekerjaan, Waktu Terlibat, Ada / Diusulkan',
  'application.COI.compensationReceived': 'Kompensasi yang diterima',
  'application.COI.yes': 'Ya',
  'application.COI.no': 'Tidak',
  'application.COI.nameOfFamilyMember': 'Nama anggota keluarga',
  'application.COI.nameOfFamilyMemberAndRelationship':
    'Name of family member and relationship',
  'application.COI.positionDepartment': 'Posisi dan departemen',
  'application.COI.dateCommencementEmployment': 'Tanggal mulai bekerja',
  'application.COI.nameOfGovernment': 'Nama entitas / organisasi pemerintah',
  'application.COI.relationshipWithGovOfficials':
    'Hubungan dengan anggota keluarga dekat',
  'application.COI.otherDetails': 'Rincian lainnya',
  'application.COI.declarationTitle': 'Deklarasi',
  'application.COI.declarationMsg':
    'Saya menegaskan bahwa informasi di atas adalah benar, lengkap dan benar pada tanggal di bawah ini. Saya memahami bahwa saya memiliki kewajiban selama bekerja di Perusahaan untuk mendapatkan persetujuan dari Kepala Departemen saya dan Kepatuhan Grup sebelum terlibat dalam kegiatan komersial di luar FWD. Saya juga setuju untuk segera memberi tahu Perusahaan jika ada perubahan dalam rincian di atas.',

  // Application Form - Review Sheet
  'eRecruit.application.review.reviewInformation': 'Tinjau informasi',
  'eRecruit.application.review.personalDetails': 'Informasi pribadi',
  'eRecruit.application.review.occupationDetails': 'Detail pekerjaan',
  'eRecruit.application.review.candidatePositionAndOtherDetails':
    'Candidate position & other details',
  'eRecruit.application.review.otherDetails': 'Detail lainnya',
  'eRecruit.application.review.salaryWithCurrency': 'Rp {{salary}}',
  'eRecruit.application.review.back': 'Kembali',
  'eRecruit.application.review.confirm': 'Konfirmasi',
  'eRecruit.application.review.nameOfGovernment':
    'Nama Entitas/Organisasi Pemerintah',
  'eRecruit.application.review.positionDepartment': 'Posisi dan Departemen',
  'eRecruit.application.review.relationshipWithGovOfficials':
    'Hubungan dengan Anggota Keluarga Terdekat',

  // Application Form - Documents
  'eRecruit.application.documents.doc': 'Dokumen',
  'eRecruit.application.documents.fileSize':
    'Format yang diterima: jpg, jpeg, png, pdf, bmp, gif, doc, atau docx (hingga 2MB masing-masing)',
  'eRecruit.application.documents.consent': 'Persetujuan',
  'eRecruit.application.documents.upload': 'Unggah',
  'eRecruit.application.documents.uploadImageDescr': 'Unggah gambar oleh',
  'eRecruit.application.documents.uploading': 'Mengunggah dokumen...',
  'eRecruit.application.documents.image.uploadLimit':
    'Maximum 5 images allowed.',
  'eRecruit.application.documents.file.uploadLimit': 'Maximum 1 file allowed.',

  // Application Form - Consent
  'eRecruit.application.consent.privacy':
    'Pemberitahuan Privasi dan Deklarasi Oleh Agen',
  'eRecruit.application.consent.nextCodeOfEthics': 'Kode Etik Berikutnya',
  'eRecruit.application.consent.nextConsentForm':
    'Formulir Persetujuan Berikutnya',
  'eRecruit.application.consent.nextSignature': 'Tanda Tangan Berikutnya',
  'eRecruit.application.consent.nextAgencyAgreement':
    'Perjanjian Agen Berikutnya',
  'eRecruit.application.consent.voicePrint': 'Voice Print',
  'eRecruit.application.consent.faceToFaceSignature': 'Face to Face Signature',
  'eRecruit.application.consent.nextLeaderAgreement': 'Next Leader Agreement',
  'eRecruit.application.consent.nextAAJICodeOfEthics':
    'Next AAJI Code of Ethics',
  'eRecruit.application.consent.nextFWDCodeOfEthicsOfSales':
    'Next FWD Insurance code of Ethics of Sales',
  'eRecruit.application.consent.nextFWDCodeOfEthics': 'Next FWD code of Ethics',
  'eRecruit.application.consent.nextPersonalDataProtection':
    'Next Personal Data Protection Confirmation',
  'eRecruit.application.consent.nextConfirmationLetter':
    'Next Confirmation Letter',

  'eRecruit.application.consent.next': 'Tanda Tangan Berikutnya',
  'eRecruit.application.consent.codeOfEthics': 'Kode Etik',
  'eRecruit.application.consent.consentForm': 'Formulir Persetujuan',
  'eRecruit.application.consent.agreement': 'Perjanjian Agen',
  'eRecruit.application.consent.agentAgreement': 'Agent Agreement',
  'eRecruit.application.consent.leaderAgreement': 'Leader Agreement',
  'eRecruit.application.consent.aajiCodeOfEthics': 'AAJI Code of Ethics',
  'eRecruit.application.consent.fwdCodeOfEthicsOfSales':
    'FWD Insurance code of Ethics of Sales',
  'eRecruit.application.consent.fwdCodeOfEthics': 'FWD code of Ethics',
  'eRecruit.application.consent.nonTwistingLetter': 'Non Twisting Letter',
  'eRecruit.application.consent.personalDataProtection':
    'Personal Data Protection Confirmation',
  'eRecruit.application.consent.confirmationLetter': 'Confirmation Letter',
  'eRecruit.application.consent.remoteSignature': 'Tanda tangan jarak jauh',
  'eRecruit.application.consent.agree': 'Setuju',
  'eRecruit.application.consent.scrollToBottom': 'Silakan gulir ke bawah',
  'eRecruit.application.consent.checkBoxStatement':
    'Saya mengerti, setuju dan menyatakan bahwa pernyataan di atas adalah benar dan sesuai.',
  'eRecruit.application.consent.checkBoxStatementLeader':
    'I, as an agent leader, understand, agree and declare that the contents stated are true and correct.',
  'eRecruit.application.consent.checkBoxStatementAgent':
    'I, as an agent, understand, agree and declare that the contents stated in Section B are true and correct',
  'eRecruit.application.consent.clear': 'Bersihkan',
  'eRecruit.application.consent.signatureWillBeAppeared':
    'Tanda tangan Anda akan muncul di dokumen yang telah Anda tinjau',
  'eRecruit.application.consent.applicationSubmitted':
    'Aplikasi dikirimkan, tautan pembayaran dikirim melalui email ke kandidat. Silakan merujuk ke "Lacak status kandidat saya".',
  'eRecruit.application.consent.remoteSignature.applicationSubmitted':
    'Menunggu tanda tangan jarak jauh dari kandidat. Silakan merujuk ke "Lacak status kandidat saya".',
  'eRecruit.application.consent.pendingRemoteSignature':
    'Aplikasi dikirimkan, tanda tangan jarak jauh diperlukan. Silakan merujuk ke "Lacak status kandidat saya".',
  'eRecruit.application.consent.candidateSignature': 'Tanda tangan kandidat',
  'eRecruit.application.consent.referrerSignature':
    'Referrer’s signature (recruiter)',
  'eRecruit.application.consent.agentSignature':
    'Agent’s signature (recruiter’s direct leader)',
  'eRecruit.application.consent.witnessSignature': 'Tanda tangan saksi',
  'eRecruit.application.consent.signatureStatement':
    'Saya menyatakan bahwa semua rincian dan informasi yang diberikan pada aplikasi ini adalah benar dan benar sesuai dengan pengetahuan saya dan saya tidak menyembunyikan fakta material apa pun.',
  'eRecruit.application.consent.submit': 'Kirim',
  'eRecruit.application.consent.cancel': 'Batal',
  'eRecruit.application.consent.dateOfSigning': 'Tanggal penandatanganan:',
  'eRecruit.application.consent.payor': 'Pembayar',
  'eRecruit.application.consent.candidate': 'Kandidat',
  'eRecruit.application.consent.agent': 'Agent',

  'eRecruit.application.consent.shareRemoteSignatureLink':
    'Bagikan tautan tanda tangan jarak jauh',
  'eRecruit.application.consent.sharePaymentLink': 'Share payment link',

  'eRecruit.application.consent.toCompleteTheApplication':
    'Untuk menyelesaikan aplikasi, kandidat Anda dapat melanjutkan persetujuan persetujuan dan tanda tangan kandidat dari tautan yang dihasilkan setelah langkah ini.',

  'eRecruit.application.signature.next': 'Berikutnya',
  'eRecruit.application.signature.submit': 'Kirim',
  'eRecruit.application.signature.moreDetails': 'Detail lebih lanjut',
  'eRecruit.application.signature.declarationMadeByCandidate':
    'Deklarasi dibuat oleh kandidat',
  'eRecruit.application.signature.declarationMadeByWitness':
    'Deklarasi dibuat oleh saksi',
  'eRecruit.application.signature.submitted':
    'Aplikasi dikirimkan. Menunggu persetujuan leader untuk saat ini.',

  'eRecruit.application.navigationBlock':
    'Tidak dapat kembali ke tahap aplikasi ini. Silakan lanjutkan dengan aplikasi.',
  'eRecruit.application.saved': 'Aplikasi disimpan.',
  'eRecruit.application.saveFailed': 'Silakan coba lagi nanti.',
  'eRecruit.application.unableToProceed': 'Tidak dapat melanjutkan',
  'eRecruit.application.submitFailed':
    'Pengiriman aplikasi gagal. Silakan coba lagi nanti.',
  'eRecruit.application.done': 'Selesai',
  'eRecruit.application.errorCode.USER_EMAIL_IS_USED':
    'email already registered, email {{email}}',
  'eRecruit.application.errorCode.USER_PHONE_IS_USED':
    'mobile phone number already registered, mobile phone number {{phoneNumberWithCode}}',
  'eRecruit.application.errorCode.ID_NUMBER_REGISTERED':
    'id number already registered, id type {{idType}}, id number {{idNumber}}',
  'eRecruit.application.errorCode.NPWP_IS_REGISTERED':
    'npwp already registered, npwp {{npwp}}',
  'eRecruit.application.errorCode.BANK_ACCOUNT_IS_REGISTERED':
    'bank account already registered, bank account {{bankAccount}}',

  'eRecruit.candidateProfile.pendingAgentReview':
    'Aplikasi ini menunggu tinjauan agen.',

  'eRecruit.deleteUploadFile.areYouSureToDelete':
    'Apakah Anda yakin ingin menghapus?',
  'eRecruit.deleteUploadFile.areYouSureToUploadFile':
    'Apakah Anda yakin ingin menghapus file yang diunggah?',
  'eRecruit.deleteUploadFile.cancel': 'Batal',
  'eRecruit.deleteUploadFile.delete': 'Hapus',
  'eRecruit.deleteUploadFile.remove': 'Hapus',
  'eRecruit.formButton.add': 'Tambah',
  'eRecruit.formButton.upload': 'Unggah',
  'eRecruit.progressBar.personalDetails': 'Detail pribadi',
  'eRecruit.progressBar.coreProfileAndIdentity': 'Core profile & identity',
  'eRecruit.progressBar.personalInformation': 'Personal information',
  'eRecruit.progressBar.occupationDetails': 'Detail pekerjaan',
  'eRecruit.progressBar.otherDetails': 'Posisi kandidat & detail lainnya',
  'eRecruit.progressBar.declaration': 'Declaration',
  'eRecruit.progressBar.reviewInfo': 'Review information',
  'eRecruit.progressBar.documents': 'Dokumen',
  'eRecruit.progressBar.consent': 'Persetujuan',

  'applicationStatus.approved': 'Disetujui',
  'applicationStatus.approvedCandidates': 'Kandidat yang disetujui',
  'applicationStatus.inProgress': 'Sedang berlangsung',
  'applicationStatus.rejected': 'Ditolak',
  'applicationStatus.rejectedCandidates': 'Kandidat yang ditolak',
  'applicationStatus.filterBy': 'Filter berdasarkan',
  'applicationStatus.totalCaseShownFromLast90Days':
    'Total kasus ({{count}})   |   Menampilkan data dari 90 hari terakhir',
  'applicationStatus.table.candidateName': 'Nama kandidat',
  'applicationStatus.table.position': 'Posisi',
  'applicationStatus.table.status': 'Status',
  'applicationStatus.table.approvalDate': 'Tanggal persetujuan',
  'applicationStatus.table.lastDate': 'Tanggal terakhir',
  'applicationStatus.table.lastUpdate': 'Last update',
  'applicationStatus.table.rejectDate': 'Tanggal penolakan',
  'applicationStatus.table.emptyRecord': 'Catatan kosong',

  // Filter Panel
  'filterPanel.title': 'Status dalam proses',
  'filterPanel.reset': 'Atur ulang',
  'filterPanel.apply': 'Terapkan',
  'candidate.status.created': 'Dibuat',
  'candidate.status.resumeApplication': 'Lanjutkan aplikasi',
  'candidate.status.remoteCheckingRequired':
    'Pemeriksaan jarak jauh diperlukan',
  'candidate.status.pendingRemoteSignature': 'Tanda tangan jarak jauh tertunda',
  'candidate.status.pendingPayment': 'Pembayaran tertunda',
  'candidate.status.pendingLeaderApproval': 'Persetujuan leader tertunda',

  // Candidate Status tab
  'candidate.total.withCount': 'Total ({{count}})',

  //Banner
  'banner.title': 'Perekrutan',
  'banner.slogan': 'Bangun Sebuah Tim Yang Kuat',

  // Dashboard
  'title.overview': 'Gambaran Umum',
  'title.application': 'Pengajuan',

  //Follow-up list
  'followUpList.emptyMsg':
    'Tidak ada aplikasi yang menunggu tindakan Anda dalam 30 hari terakhir',

  // Review Application
  'review.application.totalCase': 'Total kasus ({{count}})',
  'review.application.displayingDataFrom':
    'Menampilkan data dari 90 hari terakhir.',
  'review.application.agencyType': 'Jenis agen: {{value}}',
  'review.application.recruiterName': 'Nama Perekrut: {{value}}',
  'review.application.position': 'Posisi: {{value}}',
  'review.application.lastUpdate': 'Pembaruan terakhir: {{value}}',
  'review.application.actionDate': '{{action}} tanggal: {{date}}',
  'review.application.interviewDate': 'Tanggal wawancara: {{date}}',
  'review.application.yourDecision': 'Keputusan Anda',
  'review.application.approve': 'Setuju',
  'review.application.reject': 'Tolak',
  'review.application.view': 'View',
  'review.application.confirmInterview':
    'Apakah Anda mengkonfirmasi bahwa Anda telah mewawancarai kandidat ini?',
  'review.application.confirmDeclaration':
    'Saya dengan ini menyatakan bahwa saya telah mewawancarai pelamar secara pribadi dan saya puas bahwa pernyataan yang diberikan dalam aplikasi ini untuk Agen adalah benar, dan saya setuju bahwa jika deklarasi ini salah dalam hal apapun, FWD berhak untuk mengakhiri Perjanjian Agen segera tanpa merujuk kepada saya.',
  'review.application.enterInterviewDate': 'Silakan masukkan tanggal wawancara',
  'review.application.interviewdate': 'Tanggal wawancara',
  'review.application.dateFormat': 'DD/MM/YYYY',
  'review.application.decisionDetails': 'Detail tentang keputusan Anda',
  'review.application.comments': 'Komentar',
  'review.application.approvedMessage': 'Aplikasi disetujui',
  'review.application.rejectedMessage': 'Aplikasi ditolak',
  'review.title': "Review agent's submission",
  'review.reviewApplication': 'Review application',
  'review.approved': 'Approved',
  'review.rejected': 'Rejected',
  'review.table.candidateName': 'Candidate name',
  'review.table.agencyType': 'Agency type',
  'review.table.position': 'Position',
  'review.table.recruiterName': 'Recruiter name',
  'review.table.lastUpdated': 'Last updated',
  'review.table.salesOffice': 'Sales office/GA/\nService point',
  'review.table.salesOfficeInLine': 'Sales office/GA/Service point',
  'review.table.decision': 'Decision',
  'review.application.approvalTracker': 'Approval tracker',
  'review.application.approved': 'approved',
  'review.application.rejected': 'rejected',
  'review.application.approvedDate': 'Approved date',
  'review.application.rejectedDate': 'Rejected date',

  // Validation
  'validation.address.notExceed':
    'Panjang alamat tidak boleh melebihi {{length}} karakter',
  'validation.invalidPercentRange': 'Rentang persentase tidak valid',
  'otpModal.requested': 'OTP diminta',
  'otpModal.sentMessageToCandidate':
    'Kami telah mengirimkan OTP (kode 6 digit) ke nomor ponsel dan email kandidat.',
  'otpModal.sentMessageToCandidate.withPhone':
    'We have sent an OTP (6-digit code) to candidate’s mobile number {{phone}}.',
  'otpModal.sentMessageGeneric.withPhone':
    'We have sent an OTP (6-digit code) to the mobile number {{phone}}.',
  'otpModal.placeholder': 'Kode sandi sekali pakai',
  'otpModal.failToReceiveOtp': 'Gagal menerima OTP?',
  'otpModal.resendCode': 'Kirim ulang kode',
  'otpModal.changeMobile': 'Ubah ponsel',
  'otpModal.verifyAndContinue': 'Verifikasi & lanjutkan',
  'otpModal.incorrect': 'OTP tidak benar, silakan coba lagi',

  'reviewModal.title': 'Review complete?',
  'reviewModal.content':
    'Please make sure everything is correct before you proceed.',
  'reviewModal.review.button': 'Review again',
  'reviewModal.confirm.button': 'Confirm',
  // E-recruit Materials
  'materials.shortCut.materials': 'Materials',
  'materials.category.recruitment': 'Recruitment',
  'materials.category.gyb': 'GYB Presenter',
  'materials.category.agent_to_agent': 'Agent to agent Presenter',
  'materials.fileType.video': 'Video',
  'materials.fileType.pdf': 'PDF',
  'materials.share': 'Share',
  'materials.search.placeholder': 'Search materials',
  'materials.search.total': 'Total',
  'materials.search.result': 'Result',
  'materials.search.searchResult': 'Search result',
  'materials.sort.newest': 'Newest',
  'materials.sort.oldest': 'Oldest',
  'materials.filter': 'Filter by',
  'materials.filter.type': 'Material type',
  'materials.filter.label.recruitment': 'Recruitment',
  'materials.filter.label.gyb': 'GYB Presenter',
  'materials.filter.label.agentToAgent': 'Agent to agent Presenter',
  'materials.filter.reset': 'Reset',
  'materials.filter.apply': 'Apply',
  'materials.networkAlert':
    'No WiFi Connection. You are not currently connected to a WiFi network. Please be aware that streaming video may consume significant mobile data.',
  'materials.search.noResult': 'No results found, try another filter',
};
