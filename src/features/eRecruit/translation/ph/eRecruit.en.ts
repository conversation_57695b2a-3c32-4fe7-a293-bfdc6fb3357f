export default {
  filterBy: 'Filter by',
  total: 'Total',
  newest: 'Newest',
  oldest: 'Oldest',
  review: 'Review',
  reject: 'Reject',
  approve: 'Approve',
  reason: 'Reason',
  emptyRecord: 'Empty record',
  confirm: 'Confirm',
  cancel: 'Cancel',
  status: 'Status',
  optional: 'Optional',
  totalResult: 'Total results',
  //
  'banner.title': 'Recruitment',
  'banner.slogan': 'Build a strong team',
  //
  'title.overview': 'Overview',
  'title.candidates': 'Candidates',
  'title.materials': 'Materials',
  'title.whatWouldYouLikeToDo': 'What would you like to do?',
  'title.share': 'Share recruitment link',
  'title.startApplication': 'Start application',
  //
  searchCandidate: 'Search candidate',
  recentSearchTitle: 'Recent search',
  //
  'overview.mtd': 'MTD',
  'overview.mtd.statusTitle': 'Month to date applicant status',
  'overview.mtd.chartTitle': 'Month to month new recruit count',
  'overview.ytd': 'YTD',
  'overview.ytd.statusTitle': 'Year to date applicant status',
  'overview.ytd.chartTitle': 'Year to year new recruit count',
  'overview.status.gybAttendees': 'GYB attendees',
  'overview.status.examPassers': 'Exam passers',
  'overview.status.examRegistered': 'Exam registered',
  'overview.status.docCompletion': 'Document completion',
  'overview.status.training': 'Training',
  'overview.status.inactive': 'Inactive',
  'overview.chart.data.codedAgent': 'Coded agent',
  'overview.chart.data.toGoAgent': 'To-go agent',
  //
  'candidates.toDo': 'To-do',
  'candidates.approved': 'Approved',
  'candidates.declined': 'Declined',
  'candidates.pendingLicense': 'Pending license',
  'candidates.pendingLicenseType': 'Pending license type',
  'candidates.pendingLicenseType.dual': 'Dual',
  'candidates.pendingLicenseType.traditional': 'Traditional',
  'candidates.pendingLicenseType.variable': 'Variable',
  'candidates.allStatusCompleted': 'All status completed',
  'candidates.displayDataDuration': 'Displaying data from last 4 months',
  'candidates.recruitBy': 'Recruit by',
  'candidates.dueOnAt': 'Due on {{dueDate}} at {{dueTime}}',
  'candidates.dueTodayAt': 'Due today at {{dueTime}}',
  'candidates.profile.title': 'Candidate profile',
  'candidates.profile.candidateStatus': 'Candidate status',
  'candidates.review.title': 'Review application',
  'candidates.review.personalInfo': 'Personal information',
  'candidates.review.personalInfo2': 'Personal info',
  'candidates.profile.completed': 'completed',
  'candidates.profile.declined': 'Declined',
  'candidates.profile.declinedOn': 'Declined on {{declinedDate}}',
  'candidates.profile.declinedReason': 'Declined reason',
  'candidates.review.fullName': 'Full name',
  'candidates.review.gender': 'Gender',
  'candidates.review.dob': 'Date of birth',
  'candidates.review.contactNumber': 'Contact no.',
  'candidates.review.email': 'Email',
  'candidates.review.recruiter': 'Recruiter',
  'candidates.approve.title': 'Approve application',
  'candidates.approve.recruitSource': 'Recruit source',
  'candidates.approve.selectOneOfTheFollowing':
    'Please select one of the following',
  'candidates.approve.agentType': 'Agent type',
  'candidates.approve.designation': 'Designation',
  'candidates.approve.successToastMessage': 'Application marked as approved.',
  'candidates.reject.title': 'Reject application',
  'candidates.reject.subTitle': 'Please tell us the reason to reject',
  'candidates.reject.successToastMessage': 'Application is Declined.',
  //
  'materials.category.recruitment': 'Recruitment',
  'materials.category.gyb': 'GYB Presenter',
  'materials.category.agent_to_agent': 'Agent to agent Presenter',
  'materials.fileType.video': 'Video',
  'materials.fileType.pdf': 'PDF',
  'materials.share': 'Share',
  'materials.search.placeholder': 'Search materials',
  'materials.search.total': 'Total',
  'materials.search.result': 'Result',
  'materials.search.searchResult': 'Search result',
  'materials.sort.newest': 'Newest',
  'materials.sort.oldest': 'Oldest',
  'materials.filter': 'Filter by',
  'materials.filter.type': 'Material type',
  'materials.filter.label.recruitment': 'Recruitment',
  'materials.filter.label.gyb': 'GYB Presenter',
  'materials.filter.label.agentToAgent': 'Agent to agent Presenter',
  'materials.filter.reset': 'Reset',
  'materials.filter.apply': 'Apply',
  'materials.networkAlert':
    'No WiFi Connection. You are not currently connected to a WiFi network. Please be aware that streaming video may consume significant mobile data.',
  'materials.search.noResult': 'No results found, try another filter',
  //
  'GYBAttendees.attendee': 'Attendee',
  'GYBAttendees.recruiterLeader': 'Recruiter, Leader',
  'GYBAttendees.attendedDate': 'Date attended',
  'GYBAttendees.recruiter': 'Recruiter',
  'GYBAttendees.leader': 'Leader',
  'GYBAttendees.export': 'Export',
  'GYBAttendees.MTD': 'MTD',
  'GYBAttendees.YTD': 'YTD',
  //
  'recruitmentLink.title': 'Share your recruitment link',
  'recruitmentLink.copyLink': 'Copy link',
  'recruitmentLink.copied': 'Copied',
  'recruitmentLink.linkCopied': 'Link copied',
  'recruitmentLink.share': 'Share',
  //
  'eRecruit.candidate.pendingPayment': 'Pending payment',
  'eRecruit.candidate.pendingLeaderApproval': 'Pending leader approval',
  'eRecruit.candidate.approved': 'Approved',
  'eRecruit.candidate.rejected': 'Rejected',
  'eRecruit.candidate.remoteChecking': 'Remote checking',
  'eRecruit.candidate.remoteSignature': 'Pending remote signature',
  'eRecruit.candidate.resumeApplication': 'Resume application',
  'eRecruit.candidate.created': 'Created',
  //
  'eRecruit.application.declaration.title': 'Conflict of interest declaration',
  'eRecruit.application.declaration.question':
    'Do you have any conflicts of interest to declare?',
  'eRecruit.application.declaration.hint':
    'Please select "Yes" if any of the following statements apply to you.',
  'eRecruit.application.declaration.content.one':
    'Is there any significant shareholding or interests held by you or by a member of your immediate family* in any business enterprise / entity / trust (I.e. greater than 5% of issued share capital / interests)? ',
  'eRecruit.application.declaration.content.two':
    'Is there any existing or proposed position held by you as a director in any external business enterprise / entity or are you engaged in any employment or commercial duties (paid or unpaid) outside FWD?',
  'eRecruit.application.declaration.content.three':
    'Is an immediate family* member employed by any business enterprise / entity within FWD group? The information provided will be used to determine if there is a perceived, potential or actual conflict of interests. ',
  'eRecruit.application.declaration.content.four':
    'Are you or any immediate family* member a government official** with a senior position***?',
  'eRecruit.application.declaration.content.five':
    'Are there any other Perceived, Potential or Actual Conflict of Interests to declare? ',
  //
  'eRecruit.application.consent.content.start': `I, the undersigned recruiter, hereby affirm that all information provided and registered under my account regarding the recruit is complete, true, and accurate to the best of my knowledge. I acknowledge that any misrepresentation or falsification of data may result in appropriate corrective or disciplinary action.\n\nThe recruit was informed of the following:\n\nAuthorize the Company to collect, process, store, modify, and destroy provided information, as well as disclose, share or transfer this information to its affiliates, third-party service providers, partners, agents and/or regulatory entities, for legitimate purposes, including but not limited to:`,
  'eRecruit.application.consent.content.one':
    'Processing of application, including assisting in the application for license to the Insurance Commission, conducting any background checks;',
  'eRecruit.application.consent.content.two':
    'Administer commissions, overrides, other benefits and entitlements, if any;',
  'eRecruit.application.consent.content.three':
    'Provide advice or information covering products, services, promotions, contest, customer related services and the like, or communicate with me through mail/email/social media account/fax/SMS/telephone for any purpose;',
  'eRecruit.application.consent.content.four':
    'Manage, review and analyse results of the information provided, production, and other performance-based results for data analytics;',
  'eRecruit.application.consent.content.five':
    'Comply with applicable laws or regulations.',
  'eRecruit.application.consent.content.end': `Furthermore, I confirm that the recruit’s details, as submitted, have been reviewed and approved by the designated immediate leader, and that the leader has agreed to the inclusion of the recruit under their oversight.  Applicable if Recruiter Designation is FWP\n\nBy signing or submitting this agreement, I accept full responsibility for the accuracy of the data and the coordination with the respective leader and recruit.`,
};
