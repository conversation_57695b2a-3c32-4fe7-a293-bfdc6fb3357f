import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { CandidateProfileResponds } from 'types/eRecruit';
import { Text } from 'cube-ui-components';
import { RootStackParamListMap } from 'types';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useState } from 'react';
import * as Clipboard from 'expo-clipboard';
import { country } from 'utils/context';

export default function ShareLinkButton({
  candidateProfileDetails,
  onOpenModal,
}: {
  candidateProfileDetails: CandidateProfileResponds | undefined;
  onOpenModal: () => void;
}) {
  const { t } = useTranslation('eRecruit');
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['my']>>();

  const clipboardHandler = async () => {
    if (
      candidateProfileDetails?.cubeStatus == 'REMOTE_SIGNATURE' &&
      candidateProfileDetails?.link
    ) {
      await Clipboard.setStringAsync(candidateProfileDetails.link);
    }
    if (
      candidateProfileDetails?.cubeStatus == 'PENDING_PAYMENT' &&
      candidateProfileDetails?.paymentUrl
    ) {
      await Clipboard.setStringAsync(candidateProfileDetails.paymentUrl);
    }
  };

  return (
    <Button
      onPress={() => {
        onOpenModal();
        clipboardHandler();
      }}>
      <ButtonText fontWeight="bold">
        {candidateProfileDetails?.cubeStatus == 'REMOTE_SIGNATURE'
          ? t('eRecruit.application.consent.shareRemoteSignatureLink')
          : candidateProfileDetails?.cubeStatus == 'PENDING_PAYMENT'
          ? t('eRecruit.application.consent.sharePaymentLink')
          : '--'}
      </ButtonText>
    </Button>
  );
}

const Button = styled(TouchableOpacity)(
  ({ theme: { space, colors, sizes } }) => ({
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: space[6],
    paddingVertical: space[4],
    borderRadius: space[1],
    borderColor: colors.primary,
    borderWidth: 1,
    height: '100%',
    backgroundColor: colors.primary,
    lineHeight: sizes[5], // Ensure paddingVertical does not cause text clipping on smaller screens
  }),
);

const ButtonText = styled(Text)(({ theme: { colors, sizes } }) => ({
  color: colors.background,
  fontSize: sizes[4],
}));
