import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { Box, H6, LargeLabel, Row, TextField } from 'cube-ui-components';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import { Container } from 'features/eRecruit/id/tablet/components/StyledComponents';
import WebPage2SVG from 'features/eRecruit/assets/WebPage2SVG';
import RecruitYesNoInput from 'features/eRecruit/components/RecruitYesNoInput';
import { otherDetailsSchema } from 'features/eRecruit/id/validations/otherDetailsSchema';
import { OtherDetailsSchemaType } from 'features/eRecruit/types';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { IDNRegulatorysQuestionsOnly } from 'types/eRecruit';
import { renderLabelByLanguage } from 'utils/helper/translation';

export default function AmlAndOthers({
  hookForm,
}: {
  hookForm: UseFormReturn<OtherDetailsSchemaType<typeof otherDetailsSchema>>;
}) {
  const { space } = useTheme();
  const { t } = useTranslation(['eRecruit']);

  const { regulatoryList } = useGetERecruitOptionListForAppForm();

  // TBC
  const amlInfoObj = regulatoryList?.find(i => i.section == 'S-5');

  const control = hookForm.control;
  const trigger = hookForm.trigger;
  const watch = hookForm.watch;

  return (
    <Container>
      <Row gap={space[2]}>
        <WebPage2SVG />
        <H6 fontWeight="bold">{renderLabelByLanguage(amlInfoObj?.longDesc)}</H6>
      </Row>
      {amlInfoObj?.regulatoryList?.map((item, index) => {
        const fieldKey = item.key as keyof IDNRegulatorysQuestionsOnly;
        const isYes = watch(`regulatorys.${fieldKey}.checked`);
        const isNo = watch(`regulatorys.${fieldKey}.checked`) == false;
        const questionNumber = index + 1;
        return (
          <Box key={fieldKey} gap={space[7]}>
            <Box gap={space[4]}>
              <LargeLabel fontWeight="bold">
                {`${questionNumber}.  ` + renderLabelByLanguage(item?.longDesc)}
              </LargeLabel>
              <Box>
                <RecruitYesNoInput
                  control={control}
                  name={`regulatorys.${fieldKey}.checked`}
                  onChange={() =>
                    setTimeout(() => {
                      trigger(`regulatorys.${fieldKey}`);
                    }, 250)
                  }
                  shouldHighlightOnUntouched={
                    Input.defaultHighlightCheckForBoolean
                  }
                  initialHighlight={false}
                />
              </Box>
            </Box>
            {(questionNumber == 1 && isYes) || (questionNumber == 2 && isNo) ? (
              <Input
                as={TextField}
                control={control}
                name={`regulatorys.${fieldKey}.detail`}
                shouldHighlightOnUntouched={
                  Input.defaultHighlightCheckForBoolean
                }
                initialHighlight={false}
                label={t(
                  'eRecruit:eRecruit.application.otherDetails.regulatory.comment.label',
                )}
              />
            ) : null}
          </Box>
        );
      })}
    </Container>
  );
}
