// npm pkg
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {
  SubmitE<PERSON><PERSON><PERSON><PERSON><PERSON>,
  SubmitHandler,
  useForm,
  FieldPath,
  UseFormSetValue,
  FormProvider,
} from 'react-hook-form';
import { useTranslation, Trans } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { yupResolver } from '@hookform/resolvers/yup';
import React, { useEffect, useRef, useState } from 'react';
import { InferType, ObjectSchema } from 'yup';
import { AxiosError } from 'axios';

// components
import {
  addToast,
  Box,
  H7,
  Icon,
  Row,
  Label,
  LargeBody,
  addErrorBottomToast,
  Button,
} from 'cube-ui-components';

// features
import { defaultCountryCode } from 'features/eRecruit/config';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import { useSaveERecruitApplicationForm } from 'features/eRecruit/hooks/useSaveERecruitApplicationForm';
import ProgressStepBar from 'features/eRecruit/components/tablet/ProgressStepBar';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { BlacklistedIDErrorModal } from 'features/eRecruit/id/tablet/components/BlacklistedIDErrorModal';
import { personalDetailsSchema } from 'features/eRecruit/id/validations/personalDetailsSchema';

// utils
import { build } from 'utils/context';
import GATracking from 'utils/helper/gaTracking';
import {
  backendToFormValues,
  personalDetailsFunctions,
  PersonalDetailsFunctionsProps,
} from 'features/eRecruit/id/utils/personalDetailsFunctions';
import { useGetERecruitConfig } from 'features/eRecruit/hooks/useGetERecruitConfig';
import { getErrorMessageFromCode } from 'features/eRecruit/id/utils';
import useBoundStore from 'hooks/useBoundStore';

// UI / Icons

// types
import { NewApplicationFormValues, SavedActionProps } from 'types/eRecruit';
import {
  CubeResponse,
  IDNNewApplicationFormParmaList,
  RootStackParamListMap,
} from 'types';
import { PersonalDetailsTabERecruitApp } from 'types/eRecruit';
import { CountryCode } from 'types/optionList';

//  Others
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import EmergencyContact from './EmergencyContact';
import AddressInformation from './AddressInformation';
import BankAccountInformation from './BankAccountInformation';
import CandidatePosition from './CandidatePosition';
import ERecruitIdentityVerificationTablet from './ERecruitIdentityVerificationTablet';
import { IdentityDetailsSection } from './IdentityDetailsSection';
import { ContactDetailsSection } from './ContactDetailsSection';
import { TabFooter } from './TabFooter';
import { useDukcapilValidation } from 'features/eAppV2/id/hooks/useDukcapilValidation';
import { format } from 'date-fns';
import DukcapilValidationPopup from 'features/eAppV2/id/components/DukcapilValidationPopup';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { renderLabelByLanguage } from 'utils/helper/translation';

type SchemaType<T> = T extends ObjectSchema<infer U> ? U : never;
export type PersonalDetailsSchemaType = SchemaType<
  typeof personalDetailsSchema
>;

export type PersonalDetailsFieldPath = FieldPath<
  InferType<typeof personalDetailsSchema>
>;

const REGISTRATION_ID_BLACKLIST = 'REGISTRATION_STAGING_NRIC_BLACKLIST';

export type FormType = InferType<typeof personalDetailsSchema>;

export default function PersonalDetailsTab({
  isRerouted,
  setIsRerouted,
}: PersonalDetailsTabERecruitApp) {
  const [isErrorModalVisible, setIsErrorModalVisible] =
    useState<boolean>(false);

  const { space, colors } = useTheme();
  const { t } = useTranslation(['eApp', 'eRecruit', 'proposal']);
  // Navigation
  const route =
    useRoute<RouteProp<RootStackParamListMap['my'], 'ERecruitApplication'>>();
  const navigationToMain =
    useNavigation<NavigationProp<RootStackParamListMap['my']>>();
  const navigation =
    useNavigation<NavigationProp<IDNNewApplicationFormParmaList>>();
  const registrationStagingId = route.params?.registrationStagingId ?? '';

  // React query
  const { data: recruitmentData } = useGetApplicationData(
    registrationStagingId ?? '',
  );

  const {
    mutateAsync: validateDukcapil,
    isLoading: isDukcapilValidating,
    data: dukcapilResult,
    reset: resetDukcapilResult,
  } = useDukcapilValidation();

  const { mutateAsync, isLoading: isSavingApplication } =
    useSaveERecruitApplicationForm();

  const { isLoading: isConfigLoading, idTypeList } =
    useGetERecruitOptionListForAppForm();

  const { data: eRecruitOnlyOptionList, isLoading: isERCLoading } =
    useGetERecruitConfig();

  // Variables
  const recruitmentCache = registrationStagingId ? recruitmentData : null;

  const agentCode = useBoundStore(state => state.auth.agentCode);

  const cityList = eRecruitOnlyOptionList?.cityList || [];

  const dataFromBE = recruitmentData
    ? backendToFormValues(recruitmentData, cityList, agentCode)
    : undefined;

  const isSameAddrFromBE =
    dataFromBE?.contact?.isBusinessSameAsResidentialAddress ?? true;

  const [isSameAsResidentialAddress, setIsSameAsResidentialAddress] =
    useState<boolean>(isSameAddrFromBE);

  // React hook form
  const hookForm = useForm({
    defaultValues: {
      // dataSource: 'manual',
      contact: {
        countryCode: defaultCountryCode,
        officeNumberCountryCode: defaultCountryCode,
        isBusinessSameAsResidentialAddress: true,
      },
      candidatePosition: {
        isHaveFinancingProgram: false,
      },
      emergencyContact: {
        mobileCountryCode: defaultCountryCode,
      },
      identity: {
        numberOfDependence: '0',
      },
    },
    values: dataFromBE,
    mode: 'onBlur',
    resolver: yupResolver(personalDetailsSchema),
  });

  // check the Dukcapil response returned

  // if returned status = ‘success’, OR returned status = ‘error’ with error code [ERR03], [ERR04], [ERR05], [ERR06], [ERR07], [ERR08], [ERR09], [ERR10], [ERR12], [ERR16], [ERR17], [ERR99], flag = Y (passed)

  // if local service is down, flag = N (failed)

  // if returned status = ‘success’, flag = Y (passed)

  // if returned status = ‘error’ with error code
  // Case 2
  // [ERR03], [ERR04], [ERR05], [ERR06],
  // [ERR07], [ERR08], [ERR09], [ERR10],
  // [ERR12], [ERR16], [ERR17], [ERR99],
  //
  // Case 9
  // OR local service is down, flag = N (failed)

  // else, applications should be blocked so no flag will be sent.

  const {
    trigger,
    watch,
    handleSubmit,
    getValues,
    setValue,
    control,
    resetField,
    formState: { isValid, errors },
  } = hookForm;

  useEffect(() => {
    const resetToDefaultIdentity = () =>
      resetField('identity.identity', {
        defaultValue:
          idTypeList?.find(i => i.value == 'KTP')?.value ?? undefined,
      });
    if (watch('identity.identity') == '') {
      resetToDefaultIdentity();
      console.log(
        'identity.identity is not set, setting default value: ',
        watch('identity.identity') == '',
      );
    }
    if (dataFromBE) {
      //  filling the FE form with data from BE shall be done in useForm hook, not in useEffect
      return;
    }

    // if not dataFromBE, set default value for identity.identity
    // idTypeList is feteched from BE, so it is not available in the first render
    // when idTypeList is available, using it in defaultValues in useForm hook does not trigger rendering,
    // and in values in useForm hook raise type error
    // so set default value for identity.identity in useEffect
    resetToDefaultIdentity();
  }, [watch, dataFromBE, idTypeList, resetField]);

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { totalIncompleteRequiredFields, focusOnNextIncompleteField } =
    useIncompleteFields({
      schema: personalDetailsSchema,
      control,
      watch,
      scrollRef,
      scrollTo: option =>
        scrollRef.current?.scrollToPosition(0, option.y || 0, option.animated),
    });

  const currentFormData = getValues();

  const saveAction = (
    props: SavedActionProps<InferType<typeof personalDetailsSchema>>,
    options?: PersonalDetailsFunctionsProps['options'],
  ) => {
    try {
      console.log('~~~saveAction~~');
      const { data, onSuccess, onError } = props;
      if (!recruitmentCache) {
        console.log(
          'recruitmentCache is undefined, but not block in PersonalDetailsTab',
        );
        // return;
      }
      const pressAction = props.pressAction ? props.pressAction : 'save';
      const updatedData = personalDetailsFunctions({
        input: data,
        recruitmentCache,
        pressAction,
        cityList,
        isSameAsResidentialAddress,
        options,
      });
      mutateAsync(updatedData as any, {
        onSuccess: onSuccess,
        onError:
          (onError as (err: unknown) => void) ??
          (error => {
            addToast([
              {
                message: t('eRecruit:eRecruit.pleaseTryAgainLater'),
              },
            ]);
            console.log(
              '🚀 ~ file: ProgressStepBar==== ~ onValidSubmit error:',
              error,
            );
          }),
      });
    } catch (error) {
      console.error('Error in saveAction --- PersonalDetailsTab:', error);
    }
  };

  const onValidSave: SubmitHandler<
    InferType<typeof personalDetailsSchema>
  > = data => {
    const isNecessaryToSave = () =>
      trigger([
        'identity.fullName',
        'identity.gender',
        'contact.email',
        'contact.countryCode',
        'contact.phoneNumber',
      ]);
    isNecessaryToSave().then(isValid => {
      if (!isValid) {
        return;
      }
      saveAction({
        data,
        onSuccess: async data => {
          if (data?.registrationStagingId) {
            GATracking.logCustomEvent('recruitment', {
              action_type: 'application_created',
            });
            navigationToMain.navigate('Main');
            addToast([
              {
                IconLeft: Icon.Tick,
                message: t('eRecruit:eRecruit.application.saved'),
              },
            ]);
            return;
          }
        },
        onError: (error: AxiosError<CubeResponse<unknown>>) => {
          if (error?.response?.data?.status === REGISTRATION_ID_BLACKLIST) {
            return onToggleErrorModal();
          }

          const errMsg = getErrorMessageFromCode({
            errCode: error?.response?.data?.status,
            getValues,
            t,
          });

          addToast([
            {
              message: errMsg,
            },
          ]);
          console.log(
            '🚀 ~ file: PersonalDetailsTab ~ onValidSave error:',
            error,
          );
        },
      });
    });
    console.log('--- isNecssaryToSave: ');
  };

  const onInvalidSave: SubmitErrorHandler<NewApplicationFormValues> = error => {
    console.log('===== onInvalidSave error: ', error);
  };

  const onValidSubmit = (
    data: InferType<typeof personalDetailsSchema>,
    options?: PersonalDetailsFunctionsProps['options'],
  ) => {
    saveAction(
      {
        data,
        pressAction: 'next',
        onError: (error: AxiosError<CubeResponse<unknown>>) => {
          if (error?.response?.data?.status === REGISTRATION_ID_BLACKLIST) {
            return onToggleErrorModal();
          }

          const errMsg = getErrorMessageFromCode({
            errCode: error?.response?.data?.status,
            getValues,
            t,
          });

          addToast([
            {
              message: errMsg,
            },
          ]);
          console.log(
            '🚀 ~ file: PersonalDetailsTab ~ onValidSubmit error:',
            error,
          );
        },
        onSuccess: async data => {
          if (data?.registrationStagingId) {
            GATracking.logCustomEvent('recruitment', {
              action_type: 'application_created',
            });
            console.log(
              '🚀 ~ data?.registrationStagingId:',
              data?.registrationStagingId,
            );
            navigation.navigate('newApplicationOtherDetails', {
              registrationStagingId: String(data.registrationStagingId),
            });
            setIsRerouted && setIsRerouted(true);
            return;
          }
        },
      },
      options,
    );
  };

  const onInvalidSubmit: SubmitErrorHandler<
    NewApplicationFormValues
  > = error => {
    console.log('~~~~ onInvalidSubmit error: ', error);
  };

  const onNextHandler = async (isSkipDukcapil = false) => {
    const currentData = getValues();

    const formattedDob = format(currentData.identity.dateOfBirth, 'yyyy-MM-dd');

    // set up for allowing skip-Dukcapil when testing
    if (isSkipDukcapil && build !== 'prd') {
      handleSubmit(data => onValidSubmit(data), onInvalidSubmit)();
      return;
    }
    const validationRes = await validateDukcapil(
      {
        type: 'idInfoOnly',
        nationalId: currentData.identity.idNumber,
        fullName: currentData.identity.fullName,
        dob: formattedDob,
        gender:
          currentData.identity.gender == 'F'
            ? 'Perempuan'
            : currentData.identity.gender == 'M'
            ? 'Laki Laki'
            : (currentData.identity.gender as any),
      },
      {
        onError: err => {
          console.log('error', err);

          addErrorBottomToast([
            {
              message: t('proposal:error.initError'),
              IconLeft: <Icon.AlarmAlert fill={colors.error} />,
            },
          ]);
        },
      },
    );

    if (validationRes.code == 'ALLOW_PROCEED') {
      const options = {
        nricFlag: validationRes.flag == 'Dukcapil passed',
      } satisfies PersonalDetailsFunctionsProps['options'];
      handleSubmit(data => onValidSubmit(data, options), onInvalidSubmit)();
    }
  };

  const isAllMandatoryFieldsFilled = watch(mandatoryFieldsForSingle).every(
    (inputData, idx) => {
      const fieldPath = mandatoryFieldsForSingle[idx];
      return Boolean(inputData);
    },
  );

  const isSubmitButtonDisabled = () =>
    !isAllMandatoryFieldsFilled || isLoading || !isValid;

  useEffect(() => {
    if (isRerouted) {
      console.log(
        'already rerounted during this application----',
        recruitmentData?.stage,
      );
      return;
    }
    if (recruitmentData?.stage) {
      const currentStage = recruitmentData.stage;
      // * temp setTimeout hack for fixing no navigation when resuming App flow
      setTimeout(() => {
        console.log('useEffect~~~~~~~~ renavigating to other page');

        switch (currentStage) {
          case 'PERSONAL_DETAILS':
            break;
          case 'OTHER_DETAILS':
            navigation.navigate('newApplicationOtherDetails', {
              registrationStagingId: registrationStagingId,
            });
            break;
          case 'DOCUMENT':
            navigation.navigate('newApplicationDocuments', {
              registrationStagingId: registrationStagingId,
            });
            break;
          case 'CONSENT':
            navigation.navigate('newApplicationConsent', {
              registrationStagingId: registrationStagingId,
            });
            break;
          default:
            break;
        }
      }, 250);
      setIsRerouted && setIsRerouted(true);
    }
  }, [
    recruitmentData?.stage,
    navigation,
    isRerouted,
    setIsRerouted,
    registrationStagingId,
  ]);

  const onToggleErrorModal = () => setIsErrorModalVisible(prev => !prev);

  const isLoading =
    isSavingApplication || isConfigLoading || isDukcapilValidating;

  const currentGenderOption = eRecruitOnlyOptionList?.genderList?.find(
    g => g.itemCode == watch('identity.gender'),
  )?.longDesc;
  return (
    <FormProvider {...hookForm}>
      <DukcapilValidationPopup
        isAlwaysBackToEdit
        visible={dukcapilResult?.code === 'NOT_ALLOW_PROCEED'}
        onClose={resetDukcapilResult}
        result={dukcapilResult}
        customFieldMapping={{
          dob: dateFormatUtil(watch('identity.dateOfBirth')),
          fullName: watch('identity.fullName'),
          gender:
            renderLabelByLanguage(currentGenderOption) ??
            watch('identity.gender'),
          primaryId: watch('identity.idNumber'),
        }}
      />
      <ProgressStepBar
        type="triggerWithStepBarControlsOnSuccess"
        triggerValidation={trigger}
        onPressValidSave={saveAction}
        saveForLaterData={currentFormData}
        onPressInValidSave={() =>
          console.log('Personal Details Tab ~ onPressInValidSave')
        }
      />
      <KeyboardAwareScrollView
        ref={scrollRef}
        enableResetScrollToCoords={false}
        style={{
          backgroundColor: colors.palette.fwdGrey[50],
        }}
        contentContainerStyle={{
          paddingHorizontal: space[8],
          paddingVertical: space[4],
          backgroundColor: colors.palette.fwdGrey[50],
        }}>
        <View style={{ flex: 1, gap: space[3] }}>
          {build == 'dev' && (
            <ERecruitIdentityVerificationTablet setFormValue={setValue} />
          )}
          <IdentityDetailsSection hookForm={hookForm} />
          <ContactDetailsSection hookForm={hookForm} />
          <EmergencyContact hookForm={hookForm} />
          <AddressInformation
            hookForm={hookForm}
            isSameAsResidentialAddress={isSameAsResidentialAddress}
            setIsSameAsResidentialAddress={setIsSameAsResidentialAddress}
          />
          <BankAccountInformation hookForm={hookForm} />
          <CandidatePosition hookForm={hookForm} />
        </View>
        <LargeBody style={{ paddingVertical: space[3] }}>
          <Trans
            i18nKey={
              'eRecruit:eRecruit.application.personalDetails.agentApproval.withEmailParam' as any
            }
            values={{ email: '<EMAIL>' }}
            components={{
              emailLink: <LargeBody color={colors.primary} />,
            }}
          />
        </LargeBody>
      </KeyboardAwareScrollView>
      <TabFooter
        onSaveForLater={() => {
          // setIsSavingNotNext(true);
          // handleSubmit(onValidSave, onInvalidSave)();
          const currentFormData = getValues();
          onValidSave(currentFormData);
        }}
        onNext={onNextHandler}
        isLoading={isLoading}
        isSaveBtnDisabled={isLoading}
        isSubmitBtnDisabled={isSubmitButtonDisabled()}
        LeftComponent={() => (
          <Row justifyContent="flex-start" gap={space[5]}>
            {isAllMandatoryFieldsFilled
              ? null
              : totalIncompleteRequiredFields !== undefined &&
                totalIncompleteRequiredFields > 0 && (
                  <Row ml={space[1]} alignSelf="center" alignItems="center">
                    <Icon.Warning size={space[6]} />
                    <Box width={space[2]} />
                    <LargeBody>
                      {t(
                        'eRecruit:eRecruit.application.personalDetails.incompleteFields',
                        { total: totalIncompleteRequiredFields },
                      )}
                    </LargeBody>
                    <Box width={space[4]} />
                    <TouchableOpacity
                      hitSlop={ICON_HIT_SLOP}
                      onPress={focusOnNextIncompleteField}>
                      <Row alignItems="center">
                        <Label
                          fontWeight="medium"
                          color={colors.palette.fwdAlternativeOrange[100]}>
                          {t(
                            'eRecruit:eRecruit.application.personalDetails.goToTheField',
                          )}
                        </Label>
                        <Icon.ChevronDown
                          fill={colors.palette.fwdAlternativeOrange[100]}
                          size={space[4]}
                        />
                      </Row>
                    </TouchableOpacity>
                  </Row>
                )}
          </Row>
        )}
      />
      <BlacklistedIDErrorModal
        onCancel={onToggleErrorModal}
        visible={isErrorModalVisible}
      />
    </FormProvider>
  );
}

// function MismatchModal() {
//   const { space, colors, borderRadius } = useTheme();
//   const { t } = useTranslation(['eApp', 'eRecruit']);

//   return (
//     <BasicModal visible={true}>
//       <Box
//         minW={762}
//         borderRadius={borderRadius.large}
//         backgroundColor={colors.background}
//         p={space[12]}
//         gap={space[6]}>
//         <H6>{t('eApp:dukcapilValidationPopup.mismatchDetected')}</H6>
//       </Box>
//     </BasicModal>
//   );
// }

export type GroupedItems = {
  [key: string]: ReturnType<
    typeof useGetERecruitOptionListForAppForm
  >['occupationList'];
};

export const Container = styled(View)(
  ({ theme: { colors, space, borderRadius } }) => ({
    backgroundColor: colors.background,
    padding: space[6],
    borderRadius: borderRadius.large,
    gap: space[5],
    flex: 1,
  }),
);

export const LabelStyle = styled(Row)(({ theme: { space } }) => ({
  justifyContent: 'flex-start',
  alignItems: 'center',
  gap: space[2],
}));

export const SaveButtonStyle = styled(TouchableOpacity)(
  ({ theme: { space, colors }, disabled }) => ({
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: space[2],
    borderRadius: space[1],
    borderColor: colors.primary,
    borderWidth: 2,
    lineHeight: 52,
    height: '100%',
    width: 160,
    opacity: disabled ? 0.5 : 1,
  }),
);

export const SaveButtonText = styled(H7)(({ theme: { colors } }) => ({}));

export const BottomStyle = styled(Row)(({ theme: { space, colors } }) => ({
  gap: 15,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
}));

export const Footer = styled(View)(({ theme: { space, colors } }) => {
  const { bottom: bottomInset } = useSafeAreaInsets();
  return {
    maxHeight: 100,
    width: '100%',
    paddingHorizontal: space[8],
    borderWidth: 1,
    borderColor: colors.background,
    paddingTop: space[4],
    paddingBottom: space[2] + bottomInset,
    backgroundColor: colors.background,
  };
});

export const getCountryCodeValue = (item: CountryCode) =>
  item?.value.split(' - ')[0];

export const updateDependenceValue = (
  currentValue: string | undefined,
  incrementValue: number,
  setValue: UseFormSetValue<any>,
  trigger?: () => void,
) => {
  const cur = isNaN(parseInt(currentValue || ''))
    ? 0
    : parseInt(currentValue || '');
  const newValue = Math.max(cur + incrementValue, 0); // Prevent negative values
  setValue('identity.numberOfDependence', newValue.toString());
  trigger && trigger();
};

const mandatoryFieldsForSingle = [
  'identity.fullName',
  'identity.gender',
  'identity.dateOfBirth',
  'identity.identity',
  'identity.idNumber',
  'identity.birthPlace',
  'identity.religion',
  'identity.maritalStatus',
  'identity.numberOfDependence',
  'personalInformation.education',
  'personalInformation.industry',
  'personalInformation.presentOccupation',
  'contact.phoneNumber',
  'contact.countryCode',
  'contact.email',
] as const satisfies PersonalDetailsFieldPath[];
