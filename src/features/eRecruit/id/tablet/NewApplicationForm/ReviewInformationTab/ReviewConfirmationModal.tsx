import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {
  Box,
  Button,
  H6,
  LargeBody,
  Row,
  addErrorBottomToast,
} from 'cube-ui-components';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform } from 'react-native';
import { RootStackParamList, RootStackParamListMap } from 'types';

import BasicModal from 'components/BasicModal';
import { OTPCard } from 'components/OTP/OTPCard';
import isToday from 'date-fns/isToday';
import {
  useSendErecruitOTP,
  useVerifyErecruitOTP,
} from 'features/eRecruit/hooks/useErecruitOTP';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import { build } from 'utils/context';
import { isBefore } from 'date-fns';

export default function ReviewConfirmationModal({
  isModalOn,
  setIsModalOn,
  // onConfirm,
  phoneNumber,
  proceedToDocument,
  registrationStagingId,
  onVerifySuccess,
  onVerifyError,
}: {
  isModalOn: boolean;
  setIsModalOn: React.Dispatch<React.SetStateAction<boolean>>;
  proceedToDocument: () => void;
  phoneNumber: string;
  registrationStagingId: string;
  onVerifySuccess?: () => void;
  onVerifyError?: () => void;
}) {
  const { space, colors, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');

  const [mode, setMode] = useState<'confirmation' | 'otp'>('confirmation');
  const [manualOTPError, setManualOTPError] = useState<string | null>(null);
  const [allowedResendAt, setAllowedResendAt] = useState<Date | null>(null);
  // cons

  // const route =
  //   useRoute<RouteProp<RootStackParamListMap['my'], 'ERecruitApplication'>>();

  // const registrationStagingId = route.params?.registrationStagingId ?? '';

  const {
    mutateAsync: mutateAsyncVerify,
    isLoading: isVerifyLoading,
    error: errorVerify,
    data: verifyOTPRes,
    reset: resetVerify,
  } = useVerifyErecruitOTP();

  const {
    mutateAsync: mutateAsyncSend,
    isLoading: isSendOTPLoading,
    error: errorSend,
    data: sendOTPRes,
    reset: resetSend,
  } = useSendErecruitOTP();

  const isInTestEnv = build != 'prd' && build != 'uat' && build != 'stg';
  const moveToOTP = () => {
    setMode('otp');
    setManualOTPError(null);
  };
  const sendOTP = () =>
    mutateAsyncSend(
      {
        registrationStagingId,
      },
      {
        onSuccess: () => moveToOTP(),
        onError: err => {
          addErrorBottomToast([
            {
              message: 'Error when sending OTP',
            },
          ]);
          console.log('=======onError === err: ', err);
        },
        onSettled: res => {
          if (res && res.allowedResendAt) {
            setAllowedResendAt(new Date(res.allowedResendAt));
          }
        },
      },
    );

  const [testingByPressCount, setTestingByPressCount] = useState(0);

  const reset = () => {
    resetSend();
    resetVerify();
    setManualOTPError(null);
    setIsModalOn(false);
    setMode('confirmation');
  };
  const shallBlockResend =
    sendOTPRes?.maxResend != undefined &&
    sendOTPRes?.resendCount != undefined &&
    sendOTPRes?.lastSentAt != undefined &&
    sendOTPRes.maxResend <= sendOTPRes.resendCount &&
    isToday(new Date(sendOTPRes.lastSentAt));

  const errorHandler = (
    err: unknown,
    type: 'verify' | 'request',
    msg?: string,
  ) => {
    if (!err) {
      return null;
    }
    console.log(`=== errorHandler === error when ${type} : `, err);

    return msg
      ? msg
      : `Error when ${type == 'verify' ? 'verifying OTP' : 'requesting OTP'} `;
  };

  return (
    <BasicModal visible={isModalOn}>
      {mode == 'otp' && (
        <OTPCard
          onClose={reset}
          isVerifyLoading={isVerifyLoading}
          isResendLoading={isSendOTPLoading}
          onVerifyAndContinue={token => {
            if (!token) {
              console.log('===onVerifyAndContinue=== token not found');
              return;
            }
            if (!registrationStagingId) {
              console.log(
                '===onVerifyAndContinue=== registrationStagingId: ',
                registrationStagingId || '--',
              );
              return;
            }

            mutateAsyncVerify(
              {
                registrationStagingId,
                token,
              },
              {
                onSuccess: res => {
                  if (res.status == 'VERIFIED') {
                    if (onVerifySuccess) {
                      console.log('------ onVerifySuccess');
                      onVerifySuccess();
                    }

                    setTimeout(() => {
                      console.log('------ proceedToDocument');
                      proceedToDocument();
                      reset();
                    }, 500);
                  } else {
                    __DEV__ &&
                      console.log(
                        '===onVerifyAndContinue=== res.status not VERIFIED',
                        res,
                      );
                    setManualOTPError('OTP is not valid, please try again.');
                  }
                },
                onError: err => {
                  onVerifyError && onVerifyError();

                  console.log('----------- Error OTP when verifying: ', err);
                },
              },
            );
          }}
          onResend={() => {
            console.log('-----====------ onResend');

            if (shallBlockResend) {
              return;
            }
            sendOTP();
          }}
          disabledResend={shallBlockResend}
          phoneNumber={phoneNumber}
          errorVerify={errorHandler(errorVerify ?? manualOTPError, 'verify')}
          errorSend={errorHandler(errorSend, 'request')}
          onTestPress={() => {
            setTestingByPressCount(c => c + 1);

            if (isInTestEnv == false) {
              return;
            }

            if (testingByPressCount < 5) {
              console.log(
                '------ press testingByPassCount ',
                testingByPressCount,
              );
              return;
            }

            try {
              proceedToDocument();
            } catch (err) {
              console.log('--- ----- ---- err: ', err);
            }
          }}
        />
      )}

      {mode == 'confirmation' && (
        <AnimatedViewWrapper>
          <Box
            maxW={
              space[47] + space[47] + (Platform.OS == 'android' ? space[9] : 0)
            }
            gap={space[6]}
            backgroundColor={colors.background}
            borderRadius={borderRadius['large']}
            p={space[12]}>
            <Box gap={space[4]}>
              <H6 fontWeight="bold">{t('reviewModal.title')}</H6>
              <LargeBody>{t('reviewModal.content')}</LargeBody>
            </Box>
            <Row gap={space[2]}>
              <Button
                style={{ flex: 1 }}
                variant="secondary"
                text={t('reviewModal.review.button')}
                onPress={() => {
                  setIsModalOn(false);
                  setTestingByPressCount(0);
                }}
              />
              <Button
                style={{ flex: 1 }}
                text={t('reviewModal.confirm.button')}
                loading={isSendOTPLoading}
                onPress={() => {
                  if (
                    allowedResendAt &&
                    isBefore(new Date(), allowedResendAt)
                  ) {
                    moveToOTP();
                    return;
                  }
                  sendOTP();
                }}
              />
            </Row>
          </Box>
        </AnimatedViewWrapper>
      )}
    </BasicModal>
  );
}
