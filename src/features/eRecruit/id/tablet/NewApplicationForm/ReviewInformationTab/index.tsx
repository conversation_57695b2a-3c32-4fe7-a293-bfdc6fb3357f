import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { Box, H6, H7, LargeBody, PictogramIcon, Row } from 'cube-ui-components';
import ProgressStepBar from 'features/eRecruit/components/tablet/ProgressStepBar';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import { useSaveERecruitApplicationForm } from 'features/eRecruit/hooks/useSaveERecruitApplicationForm';
import { convertSavedApplicationForHookForm as convertToOtherDetailsFormValues } from 'features/eRecruit/id/utils/otherDetailsFuntions';

import NewAddrIcon from 'features/eRecruit/ib/tablet/asset/NewAddrIcon';
import NewBankIcon from 'features/eRecruit/ib/tablet/asset/NewBankIcon';
import NewContactDetailsIcon from 'features/eRecruit/ib/tablet/asset/NewContactDetailsIcon';
import NewDocForFormIcon from 'features/eRecruit/ib/tablet/asset/NewDocForFormIcon';
import NewIdentitySectionIcon from 'features/eRecruit/ib/tablet/asset/NewIdentitySectionIcon';
import NewInsuranceExpIcon from 'features/eRecruit/ib/tablet/asset/NewInsuranceExpIcon';
import { questionsMap as IbQuestionsMap } from 'features/eRecruit/ib/tablet/config';
import { IdnQuestionsMap } from 'features/eRecruit/id/config';
import { otherDetailsSchema } from 'features/eRecruit/id/validations/otherDetailsSchema';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';
import {
  IDNNewApplicationFormParmaList,
  RootStackParamList,
  RootStackParamListMap,
} from 'types';
import {
  ApplicationFormResponds,
  IDNRegulatorysQuestionsOnly,
} from 'types/eRecruit';
import { ObjectSchema } from 'yup';
import { useGetERecruitConfig } from 'features/eRecruit/hooks/useGetERecruitConfig';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import { backendToFormValues } from 'features/eRecruit/id/utils/personalDetailsFunctions';
import { calculateAgeAlternative } from 'features/eRecruit/util/calculateAgeAlternative';
import useBoundStore from 'hooks/useBoundStore';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { renderLabelByLanguage } from 'utils/helper/translation';
import AlarmNewSVG from 'features/eRecruit/assets/AlarmNewSVG';
import MoneyBagNewSVG from 'features/eRecruit/assets/MoneyBagNewSVG';
import FormDuoNewSVG from 'features/eRecruit/assets/FormDuoNewSVG';
import ReviewConfirmationModal from 'features/eRecruit/id/tablet/NewApplicationForm/ReviewInformationTab/ReviewConfirmationModal';
import {
  getTextFromConfig,
  getLabelFromConfig,
  formatPhoneNumberWithCountryCode,
  formatAddress,
  formatEmergencyContactAddress,
  getYesOrNo,
} from 'features/eRecruit/id/tablet/NewApplicationForm/ReviewInformationTab/utils';
import TabFooter from 'features/eRecruit/id/tablet/NewApplicationForm/ReviewInformationTab/TabFooter';
import NoteBookSVG from 'features/eRecruit/assets/NoteBookSVG';
import { MedicalReport2 } from 'cube-ui-components/dist/cjs/icons/pictograms/cube';
import WebPage2SVG from 'features/eRecruit/assets/WebPage2SVG';
import { RegulatorySection } from './RegulatorySection';

type SchemaType<T> = T extends ObjectSchema<infer U> ? U : never;

export type OtherDetailsSchemaType = SchemaType<typeof otherDetailsSchema>;

export default function ReviewInformationTab() {
  const route =
    useRoute<RouteProp<RootStackParamListMap['my'], 'ERecruitApplication'>>();
  const navigationToMain = useNavigation<NavigationProp<RootStackParamList>>();
  const navigation =
    useNavigation<NavigationProp<IDNNewApplicationFormParmaList>>();

  const registrationStagingId = route.params?.registrationStagingId ?? '';
  const registrationStagingIdParam = registrationStagingId;
  const currentLanguage = useBoundStore(state => state.language);

  const { t } = useTranslation('eRecruit');
  const { space, colors, borderRadius } = useTheme();

  const [isReviewModalOn, setIsReviewModalOn] = useState(false);
  const agentCode = useBoundStore(state => state.auth.agentCode);

  const { data: recruitmentCache, isInitialLoading } = useGetApplicationData(
    registrationStagingIdParam,
  );
  const { mutateAsync, isLoading } = useSaveERecruitApplicationForm();

  const proceedToDocument = () => {
    // * pressAction default to be 'save', so stage is saved with current stage
    console.log('~~~saveAction~~');

    if (!recruitmentCache) {
      console.log('recruitmentCache is undefined');
      return;
    }

    mutateAsync(
      {
        ...recruitmentCache,
        contact: {
          ...recruitmentCache.contact,
          emergencyContact:
            recruitmentCache?.contact?.emergencyContact &&
            Object.values(
              recruitmentCache?.contact?.emergencyContact ?? {},
            ).every(v => v == null)
              ? null
              : ({
                  ...recruitmentCache?.contact?.emergencyContact,
                } as ApplicationFormResponds['contact']['emergencyContact']),
        },
        stage: 'DOCUMENT',
      },
      {
        onSuccess: props => {
          // onSuccess&&onSuccess();
          navigation.navigate('newApplicationDocuments', {
            registrationStagingId,
          });
        },
      },
    );
  };

  const { regulatoryList } = useGetERecruitOptionListForAppForm();
  const financialInfoObj = regulatoryList?.find(i => i.section == 'S-1');
  const compliAndRepuObj = regulatoryList?.find(i => i.section == 'S-2');
  const healthInfoObj = regulatoryList?.find(i => i.section == 'S-4');
  const amlInfoObj = regulatoryList?.find(i => i.section == 'S-5');

  const { data: eRecruitOnlyOptionList, isLoading: isERCLoading } =
    useGetERecruitConfig();

  const cityList = eRecruitOnlyOptionList?.cityList || [];

  const perosonalDetailsMap = recruitmentCache
    ? backendToFormValues(recruitmentCache, cityList, agentCode)
    : undefined;

  const otherDetailsMap = recruitmentCache
    ? convertToOtherDetailsFormValues({
        parsedObj: recruitmentCache,
        currentAgentCode: agentCode,
      })
    : undefined;

  const COIOwnershipInterestItem =
    otherDetailsMap?.conflictOfInterest?.ownershipInterests?.[0];
  const COIExternalEmploymentItem =
    otherDetailsMap?.conflictOfInterest?.externalEmployments?.[0];
  const COIBusinessAffiliationInterestItem =
    otherDetailsMap?.conflictOfInterest?.businessAffiliationInterests?.[0];
  const COIRelationshipGovernmentOfficialItem =
    otherDetailsMap?.conflictOfInterest?.relationshipGovernmentOfficials?.[0];
  const COIOtherInterestItem =
    otherDetailsMap?.conflictOfInterest?.otherInterests?.[0];

  const {
    isLoading: isERecruitOptionListLoading,
    genderConfig,
    maritalConfig,
    religionConfig,
    occupationList,
    industryList,
    bankOptions,
    allBankBranchList,
    salesOfficeList,
    domicileList,
    refList,
    superiorAgentCodeList,
    positionList,
    cityOptions,
    provinceList,
    educationConfig,
  } = useGetERecruitOptionListForAppForm();

  return (
    <>
      <ProgressStepBar type="justNavigateForReviewPage" />
      <ScrollView
        contentContainerStyle={{
          paddingHorizontal: space[10],
          paddingVertical: space[5],
          gap: space[4],
        }}>
        {/* ----------------- First Card */}
        <ReviewInfoCard
          title={t('eRecruit.progressBar.coreProfileAndIdentity')}>
          {/* ----- personalDetails */}
          <Box gap={space[5]}>
            <SubHeaderContainer>
              <NewIdentitySectionIcon />
              <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
                {t(`eRecruit.application.personalDetails.identityDetails`)}
              </SubHeaderLabel>
            </SubHeaderContainer>
            <Row flexWrap="wrap" gap={space[2]}>
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.fullName`)}
                fieldValue={perosonalDetailsMap?.identity?.fullName}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.gender`)}
                fieldValue={getTextFromConfig(
                  genderConfig,
                  perosonalDetailsMap?.identity?.gender,
                )}
              />
              <FieldValuePair
                fieldName={t(
                  `eRecruit.application.personalDetails.dateOfBirth`,
                )}
                fieldValue={
                  perosonalDetailsMap?.identity?.dateOfBirth
                    ? dateFormatUtil(
                        perosonalDetailsMap?.identity?.dateOfBirth,
                      ) +
                      t('eRecruit.application.personalDetails.yearsOld', {
                        age: calculateAgeAlternative(
                          perosonalDetailsMap?.identity?.dateOfBirth,
                        ),
                      })
                    : '--'
                }
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.identity`)}
                fieldValue={perosonalDetailsMap?.identity?.identity}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.icNumber`)}
                fieldValue={perosonalDetailsMap?.identity?.idNumber}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.birthPlace`)}
                fieldValue={perosonalDetailsMap?.identity?.birthPlace}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.religion`)}
                fieldValue={getTextFromConfig(
                  religionConfig,
                  perosonalDetailsMap?.identity?.religion,
                )}
              />
              <FieldValuePair
                fieldName={t(
                  `eRecruit.application.personalDetails.maritalStatus`,
                )}
                fieldValue={getTextFromConfig(
                  maritalConfig,
                  perosonalDetailsMap?.identity?.maritalStatus,
                )}
              />
              <FieldValuePair
                fieldName={t(
                  `eRecruit.application.personalDetails.numOfDependents`,
                )}
                fieldValue={perosonalDetailsMap?.identity?.numberOfDependence}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.education`)}
                fieldValue={getTextFromConfig(
                  educationConfig,
                  perosonalDetailsMap?.personalInformation?.education,
                )}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.industry`)}
                fieldValue={getLabelFromConfig(
                  industryList,
                  perosonalDetailsMap?.personalInformation?.industry,
                )}
              />
              <FieldValuePair
                fieldName={t(
                  `eRecruit.application.personalDetails.presentOccupation`,
                )}
                fieldValue={getLabelFromConfig(
                  occupationList,
                  perosonalDetailsMap?.personalInformation?.presentOccupation,
                )}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.npwp`)}
                fieldValue={perosonalDetailsMap?.personalInformation?.npwp}
                fallbackValue={'N/A'}
              />
            </Row>
          </Box>
          {/* ------ contactDetails */}
          <Box gap={space[5]}>
            <SubHeaderContainer>
              <NewContactDetailsIcon />
              <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
                {t(`eRecruit.application.personalDetails.contactDetails`)}
              </SubHeaderLabel>
            </SubHeaderContainer>
            <Row flexWrap="wrap" gap={space[2]}>
              <FieldValuePair
                fieldName={t(
                  `eRecruit.application.personalDetails.mobileNumber`,
                )}
                fieldValue={formatPhoneNumberWithCountryCode(
                  perosonalDetailsMap?.contact?.countryCode,
                  perosonalDetailsMap?.contact?.phoneNumber,
                )}
                // (perosonalDetailsMap?.contact?.countryCode
                //   ? perosonalDetailsMap?.contact?.countryCode + ' '
                //   : '') + (perosonalDetailsMap?.contact?.phoneNumber ?? '--')
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.email`)}
                fieldValue={perosonalDetailsMap?.contact?.email}
              />
              <FieldValuePair
                fieldName={t(
                  `eRecruit.application.personalDetails.officePhone`,
                )}
                fieldValue={formatPhoneNumberWithCountryCode(
                  perosonalDetailsMap?.contact?.officeNumberCountryCode,
                  perosonalDetailsMap?.contact?.officePhoneNumber,
                )}
                fallbackValue={'N/A'}
              />
            </Row>

            {/* <Row flexWrap="wrap" gap={space[2]}>
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.fullName`)}
                fieldValue={perosonalDetailsMap?.identity?.fullName}
              />
            </Row> */}
          </Box>
          {/*------- Emergency Contact */}
          <Box gap={space[5]}>
            <SubHeaderContainer>
              <AlarmNewSVG />
              <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
                {t('eRecruit.application.otherDetails.emergencyContact')}
              </SubHeaderLabel>
            </SubHeaderContainer>
            <Box gap={space[2]}>
              <Row flexWrap="wrap" gap={space[2]}>
                <FieldValuePair
                  fieldName={t(`eRecruit.application.personalDetails.fullName`)}
                  fieldValue={
                    perosonalDetailsMap?.emergencyContact?.fullName || '--'
                  }
                />
                <FieldValuePair
                  fieldName={t(
                    `eRecruit.application.otherDetails.residenceNumber`,
                  )}
                  fieldValue={
                    perosonalDetailsMap?.emergencyContact?.residenceNumber ||
                    '--'
                  }
                />
                <FieldValuePair
                  fieldName={t('eRecruit.candidate.phoneNumber')}
                  fieldValue={formatPhoneNumberWithCountryCode(
                    perosonalDetailsMap?.emergencyContact?.mobileCountryCode,
                    perosonalDetailsMap?.emergencyContact?.mobileNumber,
                  )}
                />
              </Row>
              <Row gap={space[5]}>
                <FieldLabel flex={0.7}>
                  {t('eRecruit.application.otherDetails.addressLine')}
                </FieldLabel>
                <FieldValue flex={3.7}>
                  {formatEmergencyContactAddress({
                    emergencyContact: perosonalDetailsMap?.emergencyContact,
                    provinceList,
                    cityOptions,
                  })}
                </FieldValue>
              </Row>
            </Box>
          </Box>

          {/*------- Address information */}
          <Box gap={space[5]}>
            <SubHeaderContainer>
              <NewAddrIcon />
              {/* <PictogramIcon.HomeAddress size={space[10]} /> */}
              <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
                {t('eRecruit.application.otherDetails.addressInformation')}
              </SubHeaderLabel>
            </SubHeaderContainer>
            <Box gap={space[1]}>
              <LargeBody
                color={colors.palette.fwdGreyDarker}
                style={{ flex: 1 }}>
                {t('eRecruit.application.otherDetails.residentialAddress')}
              </LargeBody>
              <LargeBody style={{ flex: 1 }}>
                {perosonalDetailsMap?.contact?.address
                  ? formatAddress(
                      perosonalDetailsMap?.contact?.address,
                      provinceList,
                      cityOptions,
                    )
                  : '--'}
              </LargeBody>
              <LargeBody
                color={colors.palette.fwdGreyDarker}
                style={{ flex: 1 }}>
                {t('eRecruit.application.otherDetails.nationalIdAddress')}
              </LargeBody>
              <LargeBody style={{ flex: 1 }}>
                {perosonalDetailsMap?.contact?.businessAddress
                  ? formatAddress(
                      perosonalDetailsMap?.contact?.businessAddress,
                      provinceList,
                      cityOptions,
                    )
                  : '--'}
              </LargeBody>
            </Box>
          </Box>

          {/*------- Bank information */}
          <Box gap={space[5]}>
            <SubHeaderContainer>
              <NewBankIcon />
              <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
                {t('eRecruit.application.otherDetails.bankAccountInformation')}
              </SubHeaderLabel>
            </SubHeaderContainer>
            <Row flexWrap="wrap" gap={space[2]}>
              <FieldValuePair
                fieldName={t(`eRecruit.application.otherDetails.bankName`)}
                fieldValue={getLabelFromConfig(
                  bankOptions,
                  perosonalDetailsMap?.bankInformation?.bankName,
                )}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.otherDetails.branch`)}
                fieldValue={getLabelFromConfig(
                  allBankBranchList,
                  perosonalDetailsMap?.bankInformation?.branchName,
                )}
              />
              <FieldValuePair
                fieldName={t('eRecruit.application.otherDetails.accountNumber')}
                fieldValue={perosonalDetailsMap?.bankInformation?.accountNumber}
                fallbackValue={'N/A'}
              />
            </Row>
          </Box>

          {/*------- Candidate information */}
          <Box gap={space[5]}>
            <SubHeaderContainer>
              <PictogramIcon.ManWithShield />
              <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
                {t('eRecruit.application.otherDetails.candidateInformation')}
              </SubHeaderLabel>
            </SubHeaderContainer>
            <Row flexWrap="wrap" gap={space[2]}>
              <FieldValuePair
                fieldName={t(
                  `eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition`,
                )}
                fieldValue={getLabelFromConfig(
                  positionList,
                  perosonalDetailsMap?.candidatePosition?.position,
                )}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.otherDetails.salesOffice`)}
                fieldValue={getLabelFromConfig(
                  salesOfficeList,
                  perosonalDetailsMap?.candidatePosition?.salesOffice,
                )}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.otherDetails.domicile`)}
                fieldValue={getLabelFromConfig(
                  domicileList,
                  perosonalDetailsMap?.candidatePosition?.domicile,
                )}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.otherDetails.areaManager`)}
                fieldValue={
                  perosonalDetailsMap?.candidatePosition?.osAreaManager
                }
              />

              <FieldValuePair
                fieldName={t(`eRecruit.application.otherDetails.supervisor`)}
                fieldValue={getLabelFromConfig(
                  superiorAgentCodeList,
                  perosonalDetailsMap?.candidatePosition?.superiorAgentCode,
                )}
              />
              <FieldValuePair
                fieldName={t(`eRecruit.application.otherDetails.ref`)}
                fieldValue={getLabelFromConfig(
                  refList,
                  perosonalDetailsMap?.candidatePosition?.ref,
                )}
              />
              <FieldValuePair
                fieldName={t(
                  `eRecruit.application.otherDetails.financingProgram`,
                )}
                fieldValue={
                  perosonalDetailsMap?.candidatePosition
                    ?.isHaveFinancingProgram === true
                    ? t('eRecruit.application.personalDetails.question.yes')
                    : perosonalDetailsMap?.candidatePosition
                        ?.isHaveFinancingProgram === false
                    ? t('eRecruit.application.personalDetails.question.no')
                    : undefined
                }
              />
            </Row>
          </Box>
        </ReviewInfoCard>

        {/* ========================== Second Card */}
        <ReviewInfoCard title={t('eRecruit:eRecruit.progressBar.declaration')}>
          {/* ---- insuranceExperience */}
          <Box gap={space[5]}>
            <SubHeaderContainer>
              <NewInsuranceExpIcon />
              <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
                {t(`eRecruit.application.personalDetails.insuranceExperience`)}
              </SubHeaderLabel>
            </SubHeaderContainer>
            <Box gap={space[1]}>
              <LargeBody
                color={colors.palette.fwdGreyDarker}
                style={{ flex: 1 }}>
                1. {t('eRecruit.application.personalDetails.questionOne')}
              </LargeBody>
              <LargeBody style={{ flex: 1 }}>
                {otherDetailsMap?.insuranceExperience?.haveLifeInsuranceExp
                  ? t('eRecruit.application.personalDetails.question.yes')
                  : t('eRecruit.application.personalDetails.question.no')}
              </LargeBody>
            </Box>
            <Box gap={space[1]}>
              <LargeBody
                color={colors.palette.fwdGreyDarker}
                style={{ flex: 1 }}>
                2. {t('eRecruit.application.personalDetails.questionTwo')}
              </LargeBody>
              <LargeBody style={{ flex: 1 }}>
                {otherDetailsMap?.insuranceExperience?.haveGeneralInsuranceExp
                  ? t('eRecruit.application.personalDetails.question.yes')
                  : t('eRecruit.application.personalDetails.question.no')}
              </LargeBody>
            </Box>
          </Box>

          {/*------- Regulatury -  health */}
          <RegulatorySection
            icon={<MedicalReport2 size={space[10]} />}
            sectionLabel={renderLabelByLanguage(healthInfoObj?.longDesc)}
            regulatoryList={healthInfoObj?.regulatoryList ?? []}
            otherDetailsMap={otherDetailsMap}
          />

          {/*------- Regulatury -  financial */}
          <RegulatorySection
            icon={<MoneyBagNewSVG size={space[10]} />}
            sectionLabel={renderLabelByLanguage(financialInfoObj?.longDesc)}
            regulatoryList={financialInfoObj?.regulatoryList ?? []}
            otherDetailsMap={otherDetailsMap}
          />

          {/*------- Regulatury - compliance */}
          <RegulatorySection
            icon={<FormDuoNewSVG size={space[10]} />}
            sectionLabel={renderLabelByLanguage(compliAndRepuObj?.longDesc)}
            regulatoryList={compliAndRepuObj?.regulatoryList ?? []}
            otherDetailsMap={otherDetailsMap}
          />

          {/*------- Regulatury - AML */}
          <RegulatorySection
            icon={<WebPage2SVG size={space[10]} />}
            sectionLabel={renderLabelByLanguage(amlInfoObj?.longDesc)}
            regulatoryList={amlInfoObj?.regulatoryList ?? []}
            otherDetailsMap={otherDetailsMap}
            questionToConfigMapToOverride={{
              'S-5-1': {
                shownCommentWhen: 'Yes',
              },
              'S-5-2': {
                shownCommentWhen: 'No',
              },
              'S-5-3': {
                shownCommentWhen: 'Never',
              },
              'S-5-4': {
                shownCommentWhen: 'Never',
              },
              'S-5-5': {
                shownCommentWhen: 'Never',
              },
              'S-5-6': {
                shownCommentWhen: 'Never',
              },
            }}
          />

          {/*------- Regulatury - COI */}
          <Box gap={space[5]}>
            <SubHeaderContainer>
              <NewDocForFormIcon />
              {/* <PictogramIcon.Bank2 size={space[10]} /> */}
              <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
                {t('eRecruit.application.otherDetails.declarationOfCOI')}
              </SubHeaderLabel>
            </SubHeaderContainer>
            <Box gap={space[5]}>
              {Object.values(
                currentLanguage === 'id' ? IdnQuestionsMap : IbQuestionsMap,
              )?.map((item, index) => {
                const fieldKey = item.key as keyof (
                  | typeof IdnQuestionsMap
                  | typeof IbQuestionsMap
                );

                return (
                  <Box key={fieldKey} gap={space[3]}>
                    <FieldValue fontWeight="bold">
                      {`Q${index + 1} - ` + item.title}
                    </FieldValue>
                    <FieldLabel>{item.qBody.join('')}</FieldLabel>
                    <FieldValue>
                      {getYesOrNo(
                        otherDetailsMap?.conflictOfInterest?.[fieldKey],
                        t,
                      )}
                    </FieldValue>
                    {otherDetailsMap?.conflictOfInterest?.[fieldKey] == true ? (
                      <Box gap={space[3]}>
                        <FieldValue fontWeight="bold">
                          {t('application.COI.record', { number: '' })}
                        </FieldValue>
                        <Row flexWrap="wrap" gap={space[2]}>
                          {fieldKey == 'ownershipInterest' ? (
                            <>
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.nameOfBusinessEnterpriseOrEntity',
                                )}
                                fieldValue={
                                  COIOwnershipInterestItem?.nameOfBusiness
                                }
                              />
                              <FieldValuePair
                                fieldName={t(
                                  `application.COI.natureOfBusiness`,
                                )}
                                fieldValue={
                                  COIOwnershipInterestItem?.natureOfBusiness
                                }
                              />
                              <FieldValuePair
                                fieldName={t(
                                  `application.COI.nameOfOwnerAndRelationship`,
                                )}
                                fieldValue={
                                  COIOwnershipInterestItem?.nameOfOwner
                                }
                              />
                              <FieldValuePair
                                fieldName={t(
                                  `application.COI.percentageOfOwnership`,
                                )}
                                fieldValue={
                                  COIOwnershipInterestItem?.percentageOfOwnership
                                    ? COIOwnershipInterestItem.percentageOfOwnership +
                                      '%'
                                    : undefined
                                }
                              />
                              <FieldValuePair
                                fieldName={t(`application.COI.dateAcquired`)}
                                fieldValue={
                                  COIOwnershipInterestItem?.dateAcquired
                                    ? dateFormatUtil(
                                        COIOwnershipInterestItem?.dateAcquired,
                                      )
                                    : undefined
                                }
                              />
                            </>
                          ) : fieldKey == 'externalEmployment' ? (
                            <>
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.nameOfBusinessEnterpriseOrEntity',
                                )}
                                fieldValue={
                                  COIExternalEmploymentItem?.nameOfBusiness
                                }
                              />
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.natureOfBusiness',
                                )}
                                fieldValue={
                                  COIExternalEmploymentItem?.natureOfBusiness
                                }
                              />
                              <FieldValuePair
                                fieldName={t('application.COI.position')}
                                fieldValue={COIExternalEmploymentItem?.position}
                              />
                              <FieldValuePair
                                fieldName={t('application.COI.details')}
                                fieldValue={COIExternalEmploymentItem?.details}
                              />
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.compensationReceived',
                                )}
                                fieldValue={getYesOrNo(
                                  COIExternalEmploymentItem?.compensationReceived,
                                  t,
                                )}
                              />
                            </>
                          ) : fieldKey == 'businessAffiliationInterest' ? (
                            <>
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.nameOfBusinessEnterpriseOrEntity',
                                )}
                                fieldValue={
                                  COIBusinessAffiliationInterestItem?.nameOfBusiness
                                }
                              />
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.natureOfBusiness',
                                )}
                                fieldValue={
                                  COIBusinessAffiliationInterestItem?.natureOfBusiness
                                }
                              />
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.nameOfFamilyMemberAndRelationship',
                                )}
                                fieldValue={
                                  COIBusinessAffiliationInterestItem?.nameOfFamilyMember
                                }
                              />
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.positionDepartment',
                                )}
                                fieldValue={
                                  COIBusinessAffiliationInterestItem?.positionDepartment
                                }
                              />
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.dateCommencementEmployment',
                                )}
                                fieldValue={
                                  COIBusinessAffiliationInterestItem?.dateCommencementEmployment
                                    ? dateFormatUtil(
                                        COIBusinessAffiliationInterestItem?.dateCommencementEmployment,
                                      )
                                    : undefined
                                }
                              />
                            </>
                          ) : fieldKey == 'relationshipGovernmentOfficial' ? (
                            <>
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.nameOfGovernment',
                                )}
                                fieldValue={
                                  COIRelationshipGovernmentOfficialItem?.nameOfGovernment
                                }
                              />
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.positionDepartment',
                                )}
                                fieldValue={
                                  COIRelationshipGovernmentOfficialItem?.positionDepartment
                                }
                              />
                              <FieldValuePair
                                fieldName={t(
                                  'application.COI.relationshipWithGovOfficials',
                                )}
                                fieldValue={
                                  COIRelationshipGovernmentOfficialItem?.relationship
                                }
                              />
                            </>
                          ) : fieldKey == 'otherInterest' ? (
                            <Row gap={space[5]} flex={1}>
                              <FieldLabel>
                                {t('application.COI.otherDetails')}
                              </FieldLabel>
                              <FieldValue flex={3.7}>
                                {COIOtherInterestItem?.details}
                              </FieldValue>
                            </Row>
                          ) : null}
                        </Row>
                      </Box>
                    ) : null}
                  </Box>
                );
              })}
            </Box>
            {/* <Row>
              <FieldLabel style={{ flex: 0.3 }}>
                {t('eRecruit.application.review.otherDetails')}
              </FieldLabel>
              <FieldValue>
                {recruitmentCache?.approvalComments?.find(
                  section => section.approverAgentCode === agentCode,
                )?.comment ?? '--'}
              </FieldValue>
            </Row> */}
          </Box>
          <Box gap={space[5]}>
            <SubHeaderContainer>
              <NoteBookSVG />
              <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
                {t('application.otherDetails.remark')}
              </SubHeaderLabel>
            </SubHeaderContainer>
            <FieldValue>
              {recruitmentCache?.approvalComments?.find(
                section => section.approverAgentCode === agentCode,
              )?.comment ?? '--'}
            </FieldValue>
          </Box>
        </ReviewInfoCard>
      </ScrollView>
      <TabFooter
        isLoading={isLoading}
        onNext={() => setIsReviewModalOn(v => !v)}
      />
      <ReviewConfirmationModal
        registrationStagingId={registrationStagingId}
        isModalOn={isReviewModalOn}
        setIsModalOn={setIsReviewModalOn}
        proceedToDocument={proceedToDocument}
        phoneNumber={
          formatPhoneNumberWithCountryCode(
            perosonalDetailsMap?.contact?.countryCode,
            perosonalDetailsMap?.contact?.phoneNumber,
          ) ?? '--'
        }
      />
    </>
  );
}

function FieldValuePair({
  fieldName,
  fieldValue,
  fallbackValue = '--',
}: {
  fieldName: string;
  fieldValue: string | undefined | null;
  fallbackValue?: string;
}) {
  const { space, colors, borderRadius } = useTheme();
  return (
    <Row flexBasis={'48%'} gap={space[5]}>
      <FieldLabel>{fieldName}</FieldLabel>
      <FieldValue>{fieldValue ?? fallbackValue}</FieldValue>
    </Row>
  );
}

function ReviewInfoCard({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) {
  const { space, colors, borderRadius } = useTheme();

  return (
    <Box>
      <Box
        backgroundColor={colors.secondary}
        px={space[6]}
        py={space[3]}
        borderTopRadius={borderRadius['large']}>
        <H7 fontWeight={'bold'} color={colors.background}>
          {title}
        </H7>
      </Box>
      <Box
        px={space[6]}
        py={space[4]}
        gap={space[8]}
        backgroundColor={colors.background}
        borderBottomRadius={borderRadius['large']}>
        {children}
      </Box>
    </Box>
  );
}

const FieldLabel = styled(LargeBody)<{ flex?: number }>(({ theme, flex }) => ({
  color: theme.colors.palette.fwdGreyDarker,
  flex: flex ?? 0.6,
}));

const FieldValue = styled(LargeBody)<{ flex?: number }>(({ flex }) => ({
  flex: flex ?? 1.2,
}));

const SubHeaderContainer = styled(Row)(({ theme: { space } }) => ({
  justifyContent: 'flex-start',
  alignItems: 'center',
  gap: space[2],
}));

const SubHeaderLabel = styled(H6)<{ flex?: number }>(({ theme }) => ({
  flex: 1,
}));
