import { Platform, View } from 'react-native';
import React, { useMemo } from 'react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { CubeResponse, RootStackParamList } from 'types';
import { Theme, useTheme } from '@emotion/react';
import {
  Row,
  TextField,
  Dropdown,
  Picker,
  Column,
  addToast,
  Icon,
  Button,
} from 'cube-ui-components';
import Input from 'components/Input';
import {
  SubmitError<PERSON><PERSON>ler,
  SubmitHandler,
  UseFormHandleSubmit,
  useForm,
} from 'react-hook-form';
import { useValidationYupResolver } from 'utils/validation';
import styled from '@emotion/native';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import Animated, { LinearTransition } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { country } from 'utils/context';
import { CountryCode } from 'types/optionList';
import {
  initialCandidateData,
  NewCandidateFormValues,
} from 'features/eRecruit/id/validations/addNewCandidateSchema';
import { usePostNewCandidate } from 'features/eRecruit/hooks/usePostNewCandidate';
import { useQueryClient } from '@tanstack/react-query';
import { GenderKeys, NewCandidateRequestBody } from 'types/eRecruit';
import { RECRUIT_ENDPOINT } from 'api/eRecruitApi';
import { DATA_COUNTRY_CODE } from 'features/lead/components/AddLeadForm/countryCodeList';
import PhoneField from 'components/PhoneField';
import { addNewCandidateSchema } from 'features/eRecruit/id/validations/addNewCandidateSchema';
import GATracking from 'utils/helper/gaTracking';
import { defaultCountryCode } from 'features/eRecruit/config';
import { getErrorMessageFromCode } from 'features/eRecruit/id/utils';
import { AxiosError } from 'axios';

export default function AddNewCandidateForm() {
  const { t } = useTranslation(['eRecruit', 'eApp']);
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();
  const { space, sizes } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { bottom } = useSafeAreaInsets();
  const isiOS = Platform.OS === 'ios';

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isValid },
  } = useForm<NewCandidateFormValues>({
    mode: 'onChange',
    defaultValues: {
      ...initialCandidateData,
      countryCode: defaultCountryCode,
    },
    resolver: useValidationYupResolver(addNewCandidateSchema),
  });

  const { mutateAsync, isLoading } = usePostNewCandidate();
  const queryClient = useQueryClient();

  const onValidSubmit: SubmitHandler<HandleSubmitSchema> = values => {
    const newCandidate: NewCandidateRequestBody = {
      applicationId: null,
      stage: 'NEW_APPLICATION',
      identity: {
        fullname: values.fullName,
        gender: values.gender as GenderKeys,
      },
      contact: {
        email: values.emailAddress,
        phones: [
          {
            countryCode: values.countryCode,
            number: values.phoneNumber,
            type: 'MOBILE',
          },
        ],
      },
    };

    mutateAsync(newCandidate, {
      onSuccess: () => {
        GATracking.logCustomEvent('recruitment', {
          action_type: 'candidate_created',
        });
        cancelHandler();
        (country === 'ib' || country === 'id') &&
          addToast([
            {
              IconLeft: Icon.Tick,
              message: 'Candidate is added.',
            },
          ]);
        queryClient.invalidateQueries({
          queryKey: [RECRUIT_ENDPOINT],
        });
        navigation.goBack();
      },
      onError: err => {
        const error = err as AxiosError<CubeResponse<unknown>>;
        const currentForm = watch();
        const errMsg = getErrorMessageFromCode({
          type: 'dataPassedByMap',
          errCode: error?.response?.data?.status,
          dataMap: {
            email: currentForm.emailAddress,
            phoneNumberWithCode:
              currentForm.countryCode + currentForm.phoneNumber,
          },
          t,
        });

        addToast([
          {
            message: errMsg,
          },
        ]);
      },
    });
  };

  const onInvalidSubmit: SubmitErrorHandler<HandleSubmitSchema> = error => {
    console.log(
      '🚀 ~ file: AddNewCandidate ~ AddNewCandidateForm ~ error:',
      error,
    );
  };

  type HandleSubmitSchema = typeof handleSubmit extends UseFormHandleSubmit<
    infer U
  >
    ? U
    : never;

  const cancelHandler = () => {
    reset();
  };

  const countryCodeOptions = useMemo(
    () =>
      DATA_COUNTRY_CODE.map(({ label, value }) => ({
        label,
        value,
      })),
    [],
  );

  const hasErrors = Object.keys(errors).length > 0;

  return (
    <>
      <Animated.View layout={LinearTransition} style={{ flex: 1 }}>
        <KeyboardAwareScrollView
          contentContainerStyle={[
            {
              paddingTop: sizes[6],
              paddingBottom: sizes[12],
              paddingHorizontal: sizes[4],
            },
            isWideScreen && {
              paddingTop: sizes[5],
              paddingBottom: sizes[15],
              paddingHorizontal: sizes[4],
            },
            isNarrowScreen && {
              paddingTop: sizes[4],
              paddingBottom: sizes[8],
              paddingHorizontal: sizes[3],
            },
          ]}>
          <Column style={{ flex: 1, gap: sizes[5] }}>
            <Input
              control={control}
              as={TextField}
              name="fullName"
              label={t(`eRecruit:eRecruit.candidate.fullName`)}
              error={errors.fullName?.message}
            />
            <Input
              control={control}
              as={Picker}
              name="gender"
              type="text"
              label={t(`eRecruit:eRecruit.candidate.gender`)}
              error={errors?.gender?.message}
              items={[
                {
                  value: 'M',
                  text: t(`eRecruit:eRecruit.candidate.male`),
                },
                {
                  value: 'F',
                  text: t(`eRecruit:eRecruit.candidate.female`),
                },
              ]}
            />
            <Row gap={space[4]}>
              <Column
                style={{
                  minWidth: sizes[26],
                }}>
                <Input
                  control={control}
                  as={Dropdown<CountryCode, string>}
                  name="countryCode"
                  label={t(`eRecruit:eRecruit.candidate.code`)}
                  modalTitle={'Country code'}
                  data={countryCodeOptions}
                  getItemValue={item => item.value}
                  getItemLabel={item => item.label}
                  getDisplayedLabel={item => getCountryCodeValue(item)}
                  keyExtractor={item => item.value + item.label}
                  disabled
                />
              </Column>
              <Input
                control={control}
                as={PhoneField}
                name="phoneNumber"
                label={t(`eRecruit:eRecruit.candidate.phoneNumber`)}
                style={{ flex: 2 }}
                error={errors?.phoneNumber?.message}
              />
            </Row>
            <Input
              autoCapitalize="none"
              control={control}
              as={TextField}
              name="emailAddress"
              label={t(`eRecruit:eRecruit.candidate.email`)}
              style={{ flex: 1 }}
              error={errors?.emailAddress?.message}
            />
          </Column>
        </KeyboardAwareScrollView>
      </Animated.View>
      <SubmitButtonContainer isWideScreen={isWideScreen} isiOS={isiOS}>
        <Button
          variant={'primary'}
          loading={isLoading}
          text={t(`eRecruit:eRecruit.candidate.save`)}
          onPress={handleSubmit(onValidSubmit, onInvalidSubmit)}
          disabled={isLoading || !isValid || hasErrors}
          style={[{ maxWidth: 400, width: '100%', marginBottom: bottom }]}
        />
      </SubmitButtonContainer>
    </>
  );
}

type SubmitButtonContainerProps = {
  theme?: Theme;
  isWideScreen: boolean;
  isiOS: boolean;
};

const SubmitButtonContainer = styled(View)(
  ({ theme, isWideScreen, isiOS }: SubmitButtonContainerProps) => {
    if (isWideScreen) {
      return {
        backgroundColor: theme?.colors.background,
        padding: theme?.sizes[5],
        borderTopWidth: 1,
        borderTopColor: theme?.colors.palette.fwdGrey[50],
        alignItems: 'center',
      };
    }
    return {
      backgroundColor: theme?.colors.background,
      padding: theme?.sizes[4],
      borderTopWidth: 1,
      borderTopColor: theme?.colors.palette.fwdGrey[50],
      alignItems: 'center',
    };
  },
);

const getCountryCodeValue = (item: CountryCode) => item.value.split(' - ')[0];
