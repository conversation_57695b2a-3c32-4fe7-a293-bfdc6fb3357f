import styled from '@emotion/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TabView } from 'react-native-tab-view';
import { useState } from 'react';
import { useTheme } from '@emotion/react';
import { useGetERecruitTab } from 'features/eRecruit/hooks/useGetERecruitTab';
import ProgressBar from './components/ProgressBar/ProgressBar';

export default function ApplicationForm() {
  const { space } = useTheme();
  const [containerSize, setContainerSize] = useState<{
    height?: number;
    width?: number;
  }>({});
  const [topOffset, setTopOffset] = useState(0);

  const { setIndex, navigationState, renderScene } = useGetERecruitTab();
  const [isRerouted, setIsRerouted] = useState(false);

  return (
    <Container
      edges={['top']}
      style={containerSize}
      onLayout={e => {
        if (!containerSize.height || !containerSize.width) {
          setContainerSize(e.nativeEvent.layout);
        }
      }}>
      <ProgressBar
        isRerouted={isRerouted}
        setIsRerouted={setIsRerouted}
        onChangeTopOffset={setTopOffset}
      />
      <TabView
        swipeEnabled={false}
        navigationState={navigationState}
        onIndexChange={setIndex}
        renderScene={renderScene}
        lazy
        renderTabBar={() => null}
        sceneContainerStyle={{
          marginTop: 0,
        }}
        style={{
          marginTop: space[24],
        }}
      />
    </Container>
  );
}

const Container = styled(SafeAreaView)(({ theme }) => {
  return {
    flex: 1,
    backgroundColor: theme.colors.surface,
  };
});
