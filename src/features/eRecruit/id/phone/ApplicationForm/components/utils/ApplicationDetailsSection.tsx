import { useTheme } from '@emotion/react';
import { Box, Icon, LargeLabel, Row } from 'cube-ui-components';
import useAutoDone from 'features/eApp/hooks/useAutoDone';
import React, { useCallback, useState } from 'react';
import { TouchableOpacity } from 'react-native';
import { Schema } from 'yup';

export type ApplicationDetailsForm<F> = {
  value: F;
  onDone: (value: F) => void;
  onDismiss: () => void;
};

export type ApplicationDetailsSectionProps<
  F,
  P extends ApplicationDetailsForm<F>,
> = {
  name: string;
  icon: React.ComponentType<{ size?: number }>;
  form: React.ComponentType<P>;
  done?: boolean;
  value: F;
  onDone: (value?: F) => void;
  disabled?: boolean;
  schema?: Schema;
  hidden?: boolean;
} & Partial<P>;

export default function ApplicationDetailsSection<
  F,
  P extends ApplicationDetailsForm<F>,
>({
  name,
  icon,
  form: Form,
  done,
  value,
  onDone,
  disabled,
  schema,
  hidden = false,
  ...props
}: P extends ApplicationDetailsForm<F>
  ? ApplicationDetailsSectionProps<F, P>
  : ApplicationDetailsSectionProps<F, P>) {
  const { space, colors, sizes } = useTheme();
  const [visible, setVisible] = useState(false);
  const show = useCallback(() => setVisible(true), []);
  const hide = useCallback(() => setVisible(false), []);
  useAutoDone({ schema, value, done, onDone });

  if (hidden) {
    return null;
  }

  return (
    <>
      <TouchableOpacity onPress={show} disabled={disabled}>
        <Row
          paddingLeft={space[2]}
          paddingRight={space[3]}
          paddingY={space[2]}
          backgroundColor={colors.background}
          alignItems="center">
          <Icon.TickCircle
            size={sizes[7]}
            fill={
              done ? colors.palette.alertGreen : colors.palette.fwdDarkGreen[20]
            }
          />
          <Box marginLeft={space[1]} marginRight={space[2]}>
            {React.createElement(icon, { size: sizes[10] })}
          </Box>
          <LargeLabel
            fontWeight="bold"
            color={colors.primary}
            style={{
              flex: 1,
            }}>
            {name}
          </LargeLabel>
          {/*<Box flex={1} />*/}
          <Icon.ChevronRight size={sizes[6]} fill={colors.primary} />
        </Row>
      </TouchableOpacity>
      {visible && (
        <Form value={value} onDone={onDone} onDismiss={hide} {...props} />
      )}
    </>
  );
}
