import { useTheme } from '@emotion/react';
import { Button } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import React from 'react';

export const AddButton = ({
  onPress,
  isDisabled = false,
  customText,
  isHidden = false,
}: {
  onPress: () => void;
  isDisabled: boolean;
  isHidden?: boolean;
  customText?: string;
}) => {
  const { t } = useTranslation('eRecruit');
  const { colors, sizes } = useTheme();
  if (isHidden) {
    return null;
  }
  return (
    <Button
      variant="secondary"
      onPress={() => onPress()}
      disabled={isDisabled}
      style={{ width: sizes[17] }}
      text={
        customText
          ? customText
          : t(`eRecruit.application.occupationDetails.add`)
      }
      textStyle={{
        color: isDisabled ? colors.primaryVariant : colors.primary,
        fontWeight: 'bold',
      }}
    />
  );
};
