import React, { useState } from 'react';
import {
  Box,
  Button,
  Checkbox,
  Column,
  LargeBody,
  Row,
} from 'cube-ui-components';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Animated, { SlideInDown } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { RecruitKey } from 'utils/translation/i18next';

const AnimatedModal = Animated.createAnimatedComponent(View);

export default function ConsentModal({
  nextTab,
  isRemoteSignature,
  onClick,
  consentAgreementText,
  onClickRemoteSignature,
}: {
  tab: string;
  nextTab: string;
  isRemoteSignature: boolean;
  onClickRemoteSignature: () => void;
  onClick: () => void;
  consentAgreementText: RecruitKey;
}) {
  const [isTick, setIsTick] = useState(false);

  const { t } = useTranslation('eRecruit');

  const { space, colors, animation } = useTheme();
  const { bottom: bottomInset } = useSafeAreaInsets();

  return (
    <AnimatedModal
      style={{ width: '100%' }}
      entering={SlideInDown.duration(animation.duration)}>
      <InnerContainer
        style={{
          paddingBottom: bottomInset + space[4],
        }}>
        <Row gap={space[2]}>
          <Box>
            <Checkbox
              style={{ borderColor: colors.primary }}
              value={isTick}
              onChange={() => setIsTick(!isTick)}
            />
          </Box>
          <Box flex={1}>
            <LargeBody>{t(consentAgreementText)}</LargeBody>
          </Box>
        </Row>

        <ButtonContainer>
          {/* Hidden*/}
          {/*{isRemoteSignature && (
            <Button
              disabled={!isTick}
              text={t('eRecruit.application.consent.remoteSignature')}
              variant="secondary"
              style={{ flex: 1 }}
              onPress={() => {
                if (isTick) {
                  onClickRemoteSignature();
                }
              }}
            />
          )}*/}

          <Button
            disabled={!isTick}
            text={t('eRecruit.application.consent.agree')}
            subtext={nextTab}
            variant="primary"
            style={{ flex: 1 }}
            // contentStyle={isRemoteSignature ? { flex: 1 } : {}}
            onPress={() => {
              if (isTick) {
                onClick();
                setIsTick(false);
              }
            }}
          />
        </ButtonContainer>
      </InnerContainer>
    </AnimatedModal>
  );
}

const InnerContainer = styled(Column)(({ theme: { space, colors } }) => ({
  width: '100%',
  paddingVertical: space[4],
  paddingHorizontal: space[4],
  backgroundColor: colors.background,
  borderTopWidth: 1,
  borderColor: colors.surface,
  justifyContent: 'center',
  alignItems: 'center',
}));

const ButtonContainer = styled(Row)(({ theme: { space } }) => ({
  marginTop: space[4],
  gap: space[4],
}));
