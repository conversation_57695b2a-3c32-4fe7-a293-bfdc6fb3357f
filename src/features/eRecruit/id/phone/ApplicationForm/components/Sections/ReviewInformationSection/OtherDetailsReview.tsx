import { useTheme } from '@emotion/react';
import { getAgentInfo } from 'api/eRecruitApi';
import { Box, Column, H7, H8, Row, Typography } from 'cube-ui-components';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import NewAddrIcon from 'features/eRecruit/ib/tablet/asset/NewAddrIcon';
import { AgencyType, ReviewSectionProps } from 'features/eRecruit/ib/type';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AgentInfoResponse } from 'types/eRecruit';
import { getRenderLabel } from '../../utils/GetRenderLabel';
import NewIdentitySectionIcon from 'features/eRecruit/ib/tablet/asset/ApplicationDetailsListIcon';
import { InfoField } from '../../utils/InfoField';
import NewBankIcon from 'features/eRecruit/ib/tablet/asset/NewBankIcon';
import NewCandidatePosIcon from 'features/eRecruit/ib/tablet/asset/NewCandidatePosIcon';
import NewSpouseInfoIcon from 'features/eRecruit/ib/tablet/asset/NewSpouseInfoIcon';
import NewDocForFormIcon from 'features/eRecruit/ib/tablet/asset/NewDocForFormIcon';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { COIReviewSection } from './COIReviewSection';
import { getCandidateBranchNameByListAndAgentTypeAndCode } from 'features/eRecruit/ib/utils/otherDetailsFuntions';

export function OtherDetailsReview({
  data,
  showAgencyType = true,
  showDeclarationOfConflictOfInterest = true,
}: {
  showAgencyType?: boolean;
  showDeclarationOfConflictOfInterest?: boolean;
} & ReviewSectionProps) {
  const { space, colors, borderRadius, sizes } = useTheme();

  const {
    jobTypeOptions,
    agencyTypeOptions,
    positionList,
    allBranchOptions,
    cityOptions,
    stateOptions,
    bankOptions,
  } = useGetERecruitOptionListForAppForm();

  const { t } = useTranslation('eRecruit');

  const [agencyManagerInfo, setAgencyManagerInfo] = useState<
    Pick<AgentInfoResponse, 'agencyManagerCode' | 'agencyManagerName'>
  >({
    agencyManagerCode: null,
    agencyManagerName: null,
  });

  const contact = data?.contact;
  const leader = data?.leaderInformation;
  const leaderCode = leader?.agentCode;
  const position = data?.position;

  const branchName = getCandidateBranchNameByListAndAgentTypeAndCode(
    allBranchOptions,
    position?.agencyType as AgencyType,
    //* backend /proc/recruitment/application/1766 retrun null for FA right now
    // position?.branchCode ?? 'FA',
    position?.branchCode,
  );

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      const setAgentName = async (agentCode: string) => {
        if (!agentCode) {
          return;
        }

        try {
          const agentInfo = await getAgentInfo(agentCode);
          if (!agentInfo) {
            return;
          }
          setAgencyManagerInfo({
            agencyManagerCode: agentInfo?.agencyManagerCode,
            agencyManagerName: agentInfo?.agencyManagerName,
          });
        } catch (e) {
          console.log('-- useEffect -- setAgentName -- err: ', e);
        }
      };
      leaderCode ? setAgentName(leaderCode) : console.log('leaderCode is null');
    }, 500);

    return () => {
      clearTimeout(delayDebounceFn);
    };
  }, [leaderCode]);

  return (
    <Column>
      <Box
        backgroundColor={colors.secondary}
        borderTopLeftRadius={borderRadius.medium}
        borderTopRightRadius={borderRadius.medium}>
        <H7
          fontWeight="bold"
          color={colors.palette.white}
          style={{
            paddingHorizontal: space[6],
            paddingVertical: space[3],
          }}>
          {t(`eRecruit.application.otherDetails.otherDetails`)}
        </H7>
      </Box>

      <Box
        backgroundColor={colors.background}
        borderWidth={1}
        borderColor={colors.palette.fwdGrey[100]}
        paddingY={space[4]}
        paddingX={space[3]}
        borderBottomLeftRadius={borderRadius.medium}
        borderBottomRightRadius={borderRadius.medium}>
        <Column gap={space[4]}>
          {/* Address */}
          <Column>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[4] }}>
              <NewAddrIcon width={sizes[10]} height={sizes[10]} />
              <H7 fontWeight="bold">
                {t(`eRecruit.application.otherDetails.addressInformation`)}
              </H7>
            </Row>
            <Column gap={space[2]}>
              <Box gap={space[2]}>
                <H8 color={colors.palette.fwdGreyDarkest}>
                  {t(`eRecruit.application.otherDetails.residentialAddress`)}
                </H8>
                <H8 color={colors.palette.fwdDarkGreen[100]}>
                  {[
                    contact?.address.line1,
                    contact?.address.line2,
                    getRenderLabel(cityOptions, contact?.address?.city),
                    getRenderLabel(stateOptions, contact?.address?.state),
                    t(`eRecruit.application.otherDetails.malaysia`),
                  ]
                    .filter(Boolean)
                    .join(',')}
                </H8>
              </Box>
              <Box gap={space[2]}>
                <H8 color={colors.palette.fwdGreyDarkest}>
                  {t(`eRecruit.application.otherDetails.businessAddress`)}
                </H8>
                <H8 color={colors.palette.fwdDarkGreen[100]}>
                  {[
                    contact?.businessAddress.line1,
                    contact?.businessAddress.line2,
                    getRenderLabel(cityOptions, contact?.businessAddress?.city),
                    getRenderLabel(
                      stateOptions,
                      contact?.businessAddress?.state,
                    ),
                    t(`eRecruit.application.otherDetails.malaysia`),
                  ]
                    .filter(Boolean)
                    .join(',')}
                </H8>
              </Box>
            </Column>
          </Column>
          {/* Agency Type */}
          {showAgencyType && (
            <Column>
              <Row
                gap={space[2]}
                style={{ alignItems: 'center', paddingBottom: space[4] }}>
                <NewIdentitySectionIcon width={sizes[10]} height={sizes[10]} />
                <H7 fontWeight="bold">
                  {t('eRecruit.application.otherDetails.agencyType')}
                </H7>
              </Row>
              <Column gap={space[2]}>
                <Row>
                  <InfoField
                    label={t(
                      `eRecruit.application.otherDetails.supervisorCandidateInformation.agencyType`,
                    )}
                    data={
                      getRenderLabel(agencyTypeOptions, position?.agencyType) ??
                      'N/A'
                    }
                  />
                </Row>
              </Column>
            </Column>
          )}

          {/* Bank */}
          {position?.agencyType !== 'FA' && (
            <Column>
              <Row
                gap={space[2]}
                style={{ alignItems: 'center', paddingBottom: space[4] }}>
                <NewBankIcon width={sizes[10]} height={sizes[10]} />
                <H7 fontWeight="bold">
                  {t(
                    `eRecruit.application.otherDetails.bankAccountInformation`,
                  )}
                </H7>
              </Row>
              <Column gap={space[2]}>
                <InfoField
                  label={t(
                    `eRecruit.application.occupationDetails.companyName`,
                  )}
                  data={
                    getRenderLabel(
                      bankOptions,
                      contact?.bankInformation?.bankName,
                    ) ?? 'N/A'
                  }
                />
                <InfoField
                  label={t(`eRecruit.application.otherDetails.accountNumber`)}
                  data={contact?.bankInformation.accountNumber ?? 'N/A'}
                />

                <InfoField
                  label={t(`eRecruit.application.otherDetails.NRIC`)}
                  data={contact?.bankInformation.icNumber ?? 'N/A'}
                />
              </Column>
            </Column>
          )}

          {/* Candidate Position */}
          <Column>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[4] }}>
              <NewCandidatePosIcon width={sizes[10]} height={sizes[10]} />
              <H7 fontWeight="bold">
                {t(
                  'eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition',
                )}
              </H7>
            </Row>
            <Column gap={space[2]}>
              <InfoField
                label={t(
                  `eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition`,
                )}
                data={getRenderLabel(positionList, position?.position) ?? 'N/A'}
              />
              <InfoField
                label={t(
                  `eRecruit.application.otherDetails.supervisorCandidateInformation.jobType`,
                )}
                data={
                  getRenderLabel(jobTypeOptions, position?.jobType) ?? 'N/A'
                }
              />
              <InfoField
                label={t(
                  `eRecruit.application.otherDetails.leaderInformation.ALCFWDName`,
                )}
                data={leader?.alcFwdName ?? 'N/A'}
              />
              <InfoField
                label={t(
                  `eRecruit.application.otherDetails.supervisorCandidateInformation.reportingBranch`,
                )}
                data={branchName as string}
              />
              <InfoField
                label={t(
                  `eRecruit.application.otherDetails.supervisorCandidateInformation.introducerName`,
                )}
                data={data?.introducerName ?? 'N/A'}
              />
              <InfoField
                label={t(
                  `eRecruit.application.otherDetails.supervisorCandidateInformation.introducerCode`,
                )}
                data={data?.introducerCode ?? 'N/A'}
              />
            </Column>
          </Column>
          {/* Leader Info */}
          <Column>
            <Row
              gap={space[2]}
              style={{ alignItems: 'center', paddingBottom: space[4] }}>
              <NewSpouseInfoIcon />
              <Typography.H6 fontWeight="bold">
                {t(`eRecruit.application.otherDetails.leaderInformation`)}
              </Typography.H6>
            </Row>
            <Column gap={space[2]}>
              <InfoField
                label={t(
                  `eRecruit.application.otherDetails.leaderInformation.immediateLeaderCode`,
                )}
                data={leader?.agentCode ? leader?.agentCode : 'N/A'}
              />
              <InfoField
                label={t(
                  `eRecruit.application.otherDetails.leaderInformation.immediateLeaderName`,
                )}
                data={leader?.name ? leader?.name : 'N/A'}
              />

              <InfoField
                label={t('application.otherDetails.agencyManagerCode')}
                data={agencyManagerInfo?.agencyManagerCode ?? 'N/A'}
              />
              <InfoField
                label={t('application.otherDetails.agencyManagerName')}
                data={agencyManagerInfo?.agencyManagerName ?? 'N/A'}
              />
            </Column>
          </Column>

          {/* Declaration of Conflict of Interest */}
          {showDeclarationOfConflictOfInterest && (
            <Column gap={space[4]}>
              <Row gap={space[2]} style={{ alignItems: 'center' }}>
                <NewDocForFormIcon width={sizes[10]} height={sizes[10]} />
                <H7 fontWeight="bold">
                  {t('eRecruit.application.otherDetails.declarationOfCOI')}
                </H7>
              </Row>
              {/* Q1 */}

              <COIReviewSection data={data} questionKey="ownershipInterest">
                {data?.conflictOfInterest?.ownershipInterest
                  ? data?.conflictOfInterest?.ownershipInterests?.map(
                      (item, idx) => (
                        <Column gap={space[3]} key={`${item}_${idx}`}>
                          <H7 fontWeight="bold">{`Record ${idx + 1}`}</H7>
                          <Column gap={space[2]}>
                            <InfoField
                              label={'Name of business enterprise / entity'}
                              data={item.nameOfBusiness ?? 'N/A'}
                            />
                            <InfoField
                              label={'Nature of business'}
                              data={item.natureOfBusiness ?? 'N/A'}
                            />

                            <InfoField
                              label={'Name of owner'}
                              data={item.nameOfOwner ?? 'N/A'}
                            />
                            <InfoField
                              label={'Relationship'}
                              data={item.relationship ?? 'N/A'}
                            />

                            <InfoField
                              label={'Percentage of ownership'}
                              data={
                                item.percentageOfOwnership
                                  ? item.percentageOfOwnership + '%'
                                  : 'N/A'
                              }
                            />
                            <InfoField
                              label={'Date acquired'}
                              data={
                                dateFormatUtil(new Date(item.dateAcquired)) ??
                                'N/A'
                              }
                            />
                          </Column>
                        </Column>
                      ),
                    )
                  : null}
              </COIReviewSection>
              {/* Q2 */}
              <COIReviewSection data={data} questionKey="externalEmployment">
                {data?.conflictOfInterest?.externalEmployment
                  ? data?.conflictOfInterest?.externalEmployments?.map(
                      (item, idx) => (
                        <Column gap={space[3]} key={`${item}_${idx}`}>
                          <H7 fontWeight="bold">{`Record ${idx + 1}`}</H7>
                          <Column gap={space[2]}>
                            <InfoField
                              label={'Name of business enterprise / entity'}
                              data={item.nameOfBusiness ?? 'N/A'}
                            />
                            <InfoField
                              label={'Nature of business'}
                              data={item.natureOfBusiness ?? 'N/A'}
                            />

                            <InfoField
                              label={'Position'}
                              data={item.position ?? 'N/A'}
                            />
                            <InfoField
                              label={'Details'}
                              data={item.details ?? 'N/A'}
                            />

                            <InfoField
                              label={'Compensation received'}
                              data={item.compensationReceived ? 'Yes' : 'No'}
                            />
                            <InfoField label={''} data={''} />
                          </Column>
                        </Column>
                      ),
                    )
                  : null}
              </COIReviewSection>
              {/* Q3 */}
              <COIReviewSection
                data={data}
                questionKey="businessAffiliationInterest">
                {data?.conflictOfInterest?.businessAffiliationInterest &&
                  data?.conflictOfInterest?.businessAffiliationInterests?.map(
                    (item, idx) => (
                      <Column gap={space[3]} key={`${item}_${idx}`}>
                        <H7 fontWeight="bold">{`Record ${idx + 1}`}</H7>
                        <Column gap={space[2]}>
                          <InfoField
                            label={'Name of business enterprise / entity'}
                            data={item.nameOfBusiness ?? 'N/A'}
                          />
                          <InfoField
                            label={'Nature of business'}
                            data={item.natureOfBusiness ?? 'N/A'}
                          />

                          <InfoField
                            label={'Name of family member'}
                            data={item.nameOfFamilyMember ?? 'N/A'}
                          />
                          <InfoField
                            label={'Relationship'}
                            data={item.relationship ?? 'N/A'}
                          />

                          <InfoField
                            label={'Position and department'}
                            data={item.positionDepartment ?? 'N/A'}
                          />
                          <InfoField
                            label={'Date of commencement of employment'}
                            data={
                              dateFormatUtil(
                                new Date(item.dateCommencementEmployment),
                              ) ?? 'N/A'
                            }
                          />
                        </Column>
                      </Column>
                    ),
                  )}
              </COIReviewSection>
              {/* Q4 */}
              <COIReviewSection
                data={data}
                questionKey="relationshipGovernmentOfficial">
                {data?.conflictOfInterest?.relationshipGovernmentOfficial &&
                  data?.conflictOfInterest?.relationshipGovernmentOfficials?.map(
                    (item, idx) => (
                      <Column gap={space[3]} key={`${item}_${idx}`}>
                        <H7 fontWeight="bold">{`Record ${idx + 1}`}</H7>
                        <Column gap={space[2]}>
                          <InfoField
                            label={t(
                              'eRecruit.application.review.nameOfGovernment',
                            )}
                            data={item.nameOfGovernment ?? 'N/A'}
                          />
                          <InfoField
                            label={t(
                              'eRecruit.application.review.positionDepartment',
                            )}
                            data={item.positionDepartment ?? 'N/A'}
                          />

                          <InfoField
                            label={t(
                              'eRecruit.application.review.relationshipWithGovOfficials',
                            )}
                            data={item.relationship ?? 'N/A'}
                          />
                        </Column>
                      </Column>
                    ),
                  )}
              </COIReviewSection>
              {/* Q5 */}
              <COIReviewSection data={data} questionKey="otherInterest">
                {data?.conflictOfInterest?.otherInterest &&
                  data?.conflictOfInterest?.otherInterests?.map((item, idx) => (
                    <Column gap={space[3]} key={`${item}_${idx}`}>
                      <H7 fontWeight="bold">{`Record ${idx + 1}`}</H7>
                      <Column gap={space[5]}>
                        <Row>
                          <Box flex={1}>
                            <H8 color={colors.palette.fwdGreyDarkest}>
                              {t('application.COI.otherDetails')}
                            </H8>
                          </Box>
                          <Box flex={1}>
                            <H8 color={colors.palette.fwdDarkGreen[100]}>
                              {item?.details ?? 'N/A'}
                            </H8>
                          </Box>
                        </Row>
                      </Column>
                    </Column>
                  ))}
              </COIReviewSection>
            </Column>
          )}
        </Column>
      </Box>
    </Column>
  );
}
