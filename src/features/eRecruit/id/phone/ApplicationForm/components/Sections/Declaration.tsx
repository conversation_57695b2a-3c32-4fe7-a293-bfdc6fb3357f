import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { addToast, Box, Column, H6, Icon } from 'cube-ui-components';
import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { shallow } from 'zustand/shallow';
import ERecruitFooter from '../utils/ERecruitFooter';
import { useERecruitStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import ApplicationDetailsSection from '../utils/ApplicationDetailsSection';
// import AddressInformationSection from './OtherDetailsSection/AddressInformationSection';
// import CandidatePositionSection from './OtherDetailsSection/CandidatePositionSection';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { RootStackParamList, RootStackParamListMap } from 'types';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import {
  ApplicationFormResponds,
  NewApplicationFormValues,
  ParsingAppFormDataActionKeys,
  SavedActionProps,
} from 'types/eRecruit';
import ReviewInformation from './ReviewInformation';
import { useSaveERecruitApplicationForm } from 'features/eRecruit/hooks/useSaveERecruitApplicationForm';
import {
  convertSavedApplicationForHookForm,
  InputForParsingOtherDetailsData,
  parsingOtherDetailsData,
} from 'features/eRecruit/id/utils/otherDetailsFuntions';

import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import NewInsuranceExpIcon from 'features/eRecruit/ib/tablet/asset/NewInsuranceExpIcon';
import {
  insuranceExperienceSchema,
  healthConditionSchema,
  financialConditionSchema,
  complianceAndReputationSchema,
  amlAndOtherSchema,
  declarationOfInterestSchema,
  remarkOptionSchema,
} from 'features/eRecruit/id/validations/phone';
import InsuranceExperience from './DeclarationWithOthers/InsuranceExperience';
import HealthCondition from './DeclarationWithOthers/HealthCondition';
import FinancialCondition from './DeclarationWithOthers/FinancialCondition';
import ComplianceAndReputationRecords from './DeclarationWithOthers/ComplianceAndReputationRecords';
import AmlAndOthers from './DeclarationWithOthers/AmlAndOthers';
import DeclarationOfCOIPhone from './DeclarationWithOthers/DeclarationOfCOI.phone';
import RemarksOptional from './DeclarationWithOthers/RemarksOptional';

import { renderLabelByLanguage } from 'utils/helper/translation';

import { MedicalReport2 } from 'cube-ui-components/dist/cjs/icons/pictograms/cube';
import LoadingIndicator from 'components/LoadingIndicator';
import MoneyBagNewSVG from 'features/eRecruit/assets/MoneyBagNewSVG';
import FormDuoNewSVG from 'features/eRecruit/assets/FormDuoNewSVG';
import WebPage2SVG from 'features/eRecruit/assets/WebPage2SVG';
import NewDocForFormIcon from 'features/eRecruit/ib/tablet/asset/NewDocForFormIcon';
import NoteBookSVG from 'features/eRecruit/assets/NoteBookSVG';
import { satisfies } from 'compare-versions';
import useBoundStore from 'hooks/useBoundStore';
import { doneChecker } from 'features/eRecruit/id/validations/otherDetailsSchema';

const emptyValues = {};

export default function Declaration() {
  const { space } = useTheme();
  const [showReviewSheet, setShowReviewSheet] = useState(false);

  const {
    registrationStagingId,
    // data
    insuranceExperience,
    healthCondition,
    financialCondition,
    complianceAndReputation,
    amlAndOther,
    declarationOfInterest,
    remarkOptional,
    // setter
    setInsuranceExperience,
    setHealthCondition,
    setFinancialCondition,
    setComplianceAndReputation,
    setAmlAndOther,
    setDeclarationOfInterest,
    setRemarkOptionalset,
    // setIdentityDetailsData(data as IdentityDetailsInfo);
    // setRegistrationStagingId,
  } = useERecruitStore(
    state => ({
      registrationStagingId: state.registrationStagingId,
      // data
      insuranceExperience: state.insuranceExperience,
      healthCondition: state.healthCondition,
      financialCondition: state.financialCondition,
      complianceAndReputation: state.complianceAndReputation,
      amlAndOther: state.amlAndOther,
      declarationOfInterest: state.declarationOfInterest,
      remarkOptional: state.remarkOptional,

      // setters
      setInsuranceExperience: state.setInsuranceExperience,
      setHealthCondition: state.setHealthCondition,
      setFinancialCondition: state.setFinancialCondition,
      setComplianceAndReputation: state.setComplianceAndReputation,
      setAmlAndOther: state.setAmlAndOther,
      setDeclarationOfInterest: state.setDeclarationOfInterest,
      setRemarkOptionalset: state.setRemarkOptionalset,
    }),
    shallow,
  );

  const route =
    useRoute<RouteProp<RootStackParamListMap['ib'], 'ERecruitApplication'>>();
  const routeRegistrationStagingId = route.params?.registrationStagingId ?? '';
  const registrationStagingIdParam = routeRegistrationStagingId;

  const { data: recruitmentCache, isInitialLoading } = useGetApplicationData(
    registrationStagingId
      ? `${registrationStagingId}`
      : registrationStagingIdParam,
  );
  const { done, ...regulatorysData } = {
    ...healthCondition,
    ...financialCondition,
    ...complianceAndReputation,
    ...amlAndOther,
  };

  const data = {
    insuranceExperience: {
      ...insuranceExperience.insuranceExperience,
    },
    conflictOfInterest: {
      ...declarationOfInterest.conflictOfInterest,
    },
    regulatorys: {
      ...regulatorysData,
    },
    candidatePosition: {
      optionalComment: remarkOptional.optionalComment,
    },
  } satisfies InputForParsingOtherDetailsData;

  const isSaveForLaterButtonDisabled = true;

  const { t } = useTranslation(['eRecruit']);
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const { mutateAsync, isLoading } = useSaveERecruitApplicationForm();

  const saveAction = (
    props: SavedActionProps<InputForParsingOtherDetailsData>,
  ) => {
    const { data, onSuccess, onError } = props;
    if (!recruitmentCache) {
      console.log(
        'recruitmentCache is undefined, but not block in PersonalDetailsTab',
      );
    }
    const pressAction = props.pressAction ? props.pressAction : 'save';
    const updatedData = parsingOtherDetailsData({
      input: data,
      recruitmentCache,
      pressAction,
    });

    mutateAsync(updatedData as unknown as ApplicationFormResponds, {
      onSuccess: onSuccess,
      onError: onError as (err: unknown) => void,
    });
  };

  const onValidSave = (
    data: InputForParsingOtherDetailsData,
    pressAction?: ParsingAppFormDataActionKeys,
    isReview?: boolean,
  ) => {
    try {
      console.log('onValidSave data: ', data);
      saveAction({
        data,
        pressAction,
        onSuccess: async data => {
          if (data?.registrationStagingId) {
            // setRegistrationStagingId(data?.registrationStagingId);

            if (pressAction !== 'next' && !isReview) {
              navigation?.navigate('Main');
              addToast([
                {
                  IconLeft: Icon.Tick,
                  message: t('eRecruit:eRecruit.application.saved'),
                },
              ]);
            }

            if (isReview) {
              setShowReviewSheet(true);
            }
            return;
          }
        },
        onError: error => {
          addToast([
            {
              message: t('eRecruit:eRecruit.application.saveFailed'),
            },
          ]);
        },
      });
    } catch (err) {
      console.log('---err: ', err);
    }
  };

  const { regulatoryList } = useGetERecruitOptionListForAppForm();
  const healthInfoObj = regulatoryList?.find(i => i.section == 'S-4');
  const financialInfoObj = regulatoryList?.find(i => i.section == 'S-1');
  const compliAndRepuObj = regulatoryList?.find(i => i.section == 'S-2');
  const amlInfoObj = regulatoryList?.find(i => i.section == 'S-5');

  const agentCode = useBoundStore(state => state.auth.agentCode);

  useEffect(() => {
    if (!recruitmentCache) {
      return;
    }
    const parsedObj = convertSavedApplicationForHookForm({
      parsedObj: recruitmentCache,
      currentAgentCode: agentCode,
    });
    setInsuranceExperience({
      insuranceExperience: {
        ...parsedObj.insuranceExperience,
      },
      done:
        parsedObj.insuranceExperience.haveGeneralInsuranceExp != null &&
        parsedObj.insuranceExperience.haveLifeInsuranceExp != null,
    });
    setHealthCondition({
      'S-4-1': {
        ...parsedObj.regulatorys['S-4-1'],
      },
      done: doneChecker('checkedTrueToShow', parsedObj.regulatorys['S-4-1']),
    });
    setFinancialCondition({
      'S-1-1': { ...parsedObj.regulatorys['S-1-1'] },
      'S-1-2': { ...parsedObj.regulatorys['S-1-2'] },
      done:
        doneChecker('checkedTrueToShow', parsedObj.regulatorys['S-1-1']) &&
        doneChecker('checkedTrueToShow', parsedObj.regulatorys['S-1-2']),
    });
    setComplianceAndReputation({
      'S-2-1': { ...parsedObj.regulatorys['S-2-1'] },
      'S-2-2': { ...parsedObj.regulatorys['S-2-2'] },
      'S-2-3': { ...parsedObj.regulatorys['S-2-3'] },
      done:
        doneChecker('checkedTrueToShow', parsedObj.regulatorys['S-2-1']) &&
        doneChecker('checkedTrueToShow', parsedObj.regulatorys['S-2-2']) &&
        doneChecker('checkedTrueToShow', parsedObj.regulatorys['S-2-3']),
    });
    setAmlAndOther({
      'S-5-1': { ...parsedObj.regulatorys['S-5-1'] },
      'S-5-2': { ...parsedObj.regulatorys['S-5-2'] },
      'S-5-3': { ...parsedObj.regulatorys['S-5-3'] },
      'S-5-4': { ...parsedObj.regulatorys['S-5-4'] },
      'S-5-5': { ...parsedObj.regulatorys['S-5-5'] },
      'S-5-6': { ...parsedObj.regulatorys['S-5-6'] },
      done:
        doneChecker('checkedTrueToShow', parsedObj.regulatorys['S-5-1']) &&
        doneChecker('checkedFalseToShow', parsedObj.regulatorys['S-5-2']) &&
        parsedObj.regulatorys['S-5-3'].checked != null &&
        parsedObj.regulatorys['S-5-4'].checked != null &&
        parsedObj.regulatorys['S-5-5'].checked != null &&
        parsedObj.regulatorys['S-5-6'].checked != null,
    });
    setDeclarationOfInterest({
      ...parsedObj.conflictOfInterest,
      done: false,
    });

    setRemarkOptionalset({
      optionalComment: parsedObj.candidatePosition.optionalComment,
      done: Boolean(parsedObj.candidatePosition.optionalComment),
    });
  }, [
    agentCode,
    recruitmentCache,
    setAmlAndOther,
    setComplianceAndReputation,
    setDeclarationOfInterest,
    setFinancialCondition,
    setHealthCondition,
    setInsuranceExperience,
    setRemarkOptionalset,
  ]);

  const isNextDisabled =
    insuranceExperience.done == false ||
    healthCondition.done == false ||
    financialCondition.done == false ||
    complianceAndReputation.done == false ||
    amlAndOther.done == false ||
    declarationOfInterest.done == false;
  return (
    <Column flex={1}>
      <ScrollView>
        <Box my={space[4]} mx={space[isNarrowScreen ? 3 : 4]}>
          <H6 fontWeight="bold">
            {t('eRecruit:eRecruit.progressBar.declaration')}
          </H6>
        </Box>
        <Container>
          {/*------------ Insurance Experience*/}
          <ApplicationDetailsSection
            icon={
              NewInsuranceExpIcon as React.ComponentType<{
                size?: number;
              }>
            }
            name={t(
              `eRecruit:eRecruit.application.personalDetails.insuranceExperience`,
            )}
            disabled={isLoading}
            done={insuranceExperience.done}
            form={InsuranceExperience}
            value={insuranceExperience || emptyValues}
            onDone={data => {
              data && setInsuranceExperience(data);
            }}
            schema={insuranceExperienceSchema}
          />

          {/*------------ Health Condition*/}
          <ApplicationDetailsSection
            icon={
              MedicalReport2 as React.ComponentType<{
                size?: number;
              }>
            }
            name={
              renderLabelByLanguage(healthInfoObj?.longDesc)
                ? renderLabelByLanguage(healthInfoObj?.longDesc)
                : ((
                    <Box>
                      <LoadingIndicator />
                    </Box>
                  ) as any)
            }
            disabled={isLoading}
            done={healthCondition.done}
            form={HealthCondition}
            value={healthCondition || emptyValues}
            onDone={data => {
              data && setHealthCondition(data);
            }}
            schema={healthConditionSchema}
          />

          {/*------------Financial Condition*/}
          <ApplicationDetailsSection
            icon={
              MoneyBagNewSVG as React.ComponentType<{
                size?: number;
              }>
            }
            name={
              renderLabelByLanguage(financialInfoObj?.longDesc)
                ? renderLabelByLanguage(financialInfoObj?.longDesc)
                : ((
                    <Box>
                      <LoadingIndicator />
                    </Box>
                  ) as any)
            }
            disabled={isLoading}
            done={financialCondition.done}
            form={FinancialCondition}
            value={financialCondition || emptyValues}
            onDone={data => {
              data && setFinancialCondition(data);
            }}
            schema={financialConditionSchema}
          />

          {/*------------Compliance And Reputation */}
          <ApplicationDetailsSection
            icon={
              FormDuoNewSVG as React.ComponentType<{
                size?: number;
              }>
            }
            name={
              renderLabelByLanguage(compliAndRepuObj?.longDesc)
                ? renderLabelByLanguage(compliAndRepuObj?.longDesc)
                : ((
                    <Box>
                      <LoadingIndicator />
                    </Box>
                  ) as any)
            }
            disabled={isLoading}
            done={complianceAndReputation.done}
            form={ComplianceAndReputationRecords}
            value={complianceAndReputation || emptyValues}
            onDone={data => {
              data && setComplianceAndReputation(data);
            }}
            schema={complianceAndReputationSchema}
          />

          {/*------------AML */}
          <ApplicationDetailsSection
            icon={
              WebPage2SVG as React.ComponentType<{
                size?: number;
              }>
            }
            name={
              renderLabelByLanguage(amlInfoObj?.longDesc)
                ? renderLabelByLanguage(amlInfoObj?.longDesc)
                : ((
                    <Box>
                      <LoadingIndicator />
                    </Box>
                  ) as any)
            }
            disabled={isLoading}
            done={amlAndOther.done}
            form={AmlAndOthers}
            value={amlAndOther || emptyValues}
            onDone={data => {
              data && setAmlAndOther(data);
            }}
            schema={amlAndOtherSchema}
          />

          {/*------------COI */}
          <ApplicationDetailsSection
            icon={
              NewDocForFormIcon as React.ComponentType<{
                size?: number;
              }>
            }
            name={t(
              'eRecruit:eRecruit.application.otherDetails.declarationOfCOI',
            )}
            disabled={isLoading}
            done={declarationOfInterest.done}
            form={DeclarationOfCOIPhone}
            value={declarationOfInterest || emptyValues}
            onDone={data => {
              data && setDeclarationOfInterest(data);
            }}
            schema={declarationOfInterestSchema}
          />

          {/*------------Optional Remark */}
          <ApplicationDetailsSection
            icon={
              NoteBookSVG as React.ComponentType<{
                size?: number;
              }>
            }
            name={t('eRecruit:application.otherDetails.remark.optional')}
            disabled={isLoading}
            done={
              Boolean(remarkOptional.optionalComment)
              // remarkOptional.done
            }
            form={RemarksOptional}
            value={remarkOptional || emptyValues}
            onDone={data => {
              data && setRemarkOptionalset(data);
            }}
            schema={remarkOptionSchema}
          />
        </Container>
        <Box h={space[4]} />
      </ScrollView>
      <ERecruitFooter
        primaryLoading={isInitialLoading || isLoading}
        primaryLabel={t(`eRecruit:eRecruit.application.otherDetails.next`)}
        primarySubLabel={t(`eRecruit:eRecruit.application.otherDetails.review`)}
        primaryDisabled={isNextDisabled}
        onPrimaryPress={() => {
          onValidSave(data, 'save', true);
        }}
        secondaryLoading={isInitialLoading || isLoading}
        secondaryDisabled={false}
        secondaryLabel={t(
          `eRecruit:eRecruit.application.otherDetails.saveForLater`,
        )}
        onSecondaryPress={() => {
          onValidSave(data);
        }}
      />
      <ReviewInformation
        showReviewSheet={showReviewSheet}
        setShowReviewSheet={setShowReviewSheet}
      />
    </Column>
  );
}

const Container = styled.View(({ theme: { colors, space, borderRadius } }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    borderRadius: borderRadius.large,
    paddingVertical: space[2],
    backgroundColor: colors.background,
    marginHorizontal: space[isNarrowScreen ? 3 : 4],
    overflow: 'hidden',
  };
});
