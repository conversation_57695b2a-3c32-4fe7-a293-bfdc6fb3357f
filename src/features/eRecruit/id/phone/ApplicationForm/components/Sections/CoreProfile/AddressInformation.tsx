import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import SearchableDropdown from 'components/SearchableDropdown';
import Input from 'components/Input';
import Portal from 'components/Portal/Portal';
import {
  Box,
  Checkbox,
  Column,
  H7,
  H8,
  LargeLabel,
  Row,
  TextField,
} from 'cube-ui-components';
import useGetERecruitOptionListForAppForm, {
  GroupedCityItems,
} from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import {
  // initialApplicationData,
  addressInformationSchema,
} from 'features/eRecruit/id/validations/phone';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useCallback, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { AddressInfoFromStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FormFooter from '../../utils/FormFooter';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';
import NewAddrIcon from 'features/eRecruit/ib/tablet/asset/NewAddrIcon';
import { AddressInformationFieldPath } from 'features/eRecruit/util/store/id/types';
import { createAddressInput } from 'features/eRecruit/id/utils';
import { useGetERecruitConfig } from 'features/eRecruit/hooks/useGetERecruitConfig';

interface Props {
  onDismiss: () => void;
  value: AddressInfoFromStore;
  onDone: (data: AddressInfoFromStore) => void;
}

export default function AddressInformation({
  onDismiss,
  value,
  onDone,
}: Props) {
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes } = useTheme();

  const hookForm = useForm({
    defaultValues: {
      // ...initialApplicationData.contact,
      contact: {
        isBusinessSameAsResidentialAddress: true,
      },
    },
    resolver: yupResolver(addressInformationSchema),
    mode: 'onBlur',
  });

  const {
    control,
    trigger,
    formState: { errors },
    getValues,
    setValue,
    resetField,
    watch,
  } = hookForm;

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints();
  // const {
  //   animatedHandleHeight,
  //   animatedSnapPoints,
  //   animatedContentHeight,
  //   handleContentLayout,
  // } = useBottomSheetDynamicSnapPoints(snapPoints);

  const { cityOptions, provinceList, postCodeListWithStateCity } =
    useGetERecruitOptionListForAppForm();
  const { data: eRecruitOnlyOptionList, isLoading: isERCLoading } =
    useGetERecruitConfig();

  const cityList = eRecruitOnlyOptionList?.cityList ?? [];
  const mainAddressProvince = watch('contact.address.province');
  const secondaryAddressProvince = watch('contact.businessAddress.province');

  const groupedCityByProvince: GroupedCityItems = useMemo(
    () =>
      cityOptions
        ? cityOptions.reduce((acc, item) => {
            const prefix = item.value.split('-')[0];
            if (!acc[prefix]) {
              acc[prefix] = [];
            }
            acc?.[prefix]?.push(item);

            return acc;
          }, {} as GroupedCityItems)
        : {},
    [cityOptions],
  );

  const provinceBasedCityListForMain = mainAddressProvince
    ? groupedCityByProvince[mainAddressProvince]
    : [];
  const provinceBasedCityListForSecondary = secondaryAddressProvince
    ? groupedCityByProvince[secondaryAddressProvince]
    : [];

  const alwaysMandatoryFields = [
    'contact.address.line1',
    'contact.address.neighborhoodAssociation',
    'contact.address.communityAssociation',
    'contact.address.subDistrict',
    'contact.address.district',
    'contact.address.province',
    'contact.address.city',
    'contact.address.postCode',
    // 'contact.isBusinessSameAsResidentialAddress',
  ] as const satisfies AddressInformationFieldPath[];

  const businessAddressFields = [
    'contact.businessAddress.line1',
    'contact.businessAddress.neighborhoodAssociation',
    'contact.businessAddress.communityAssociation',
    'contact.businessAddress.subDistrict',
    'contact.businessAddress.district',
    'contact.businessAddress.province',
    'contact.businessAddress.city',
    'contact.businessAddress.postCode',
  ] as const satisfies AddressInformationFieldPath[];

  // 2. Watch the value of the controlling checkbox
  const isBusinessSameAsResidential = watch(
    'contact.isBusinessSameAsResidentialAddress',
  );
  const mandatoryFields = isBusinessSameAsResidential
    ? alwaysMandatoryFields
    : [...alwaysMandatoryFields, ...businessAddressFields];

  const isAllMandatoryFieldsFilled = watch(mandatoryFields).every(item =>
    Boolean(item),
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  useEffect(() => {
    if (value) {
      // --- Set Residential Address Fields ---

      setValue('contact.address.line1', value.contact?.address?.line1 ?? '');
      setValue('contact.address.line2', value.contact?.address?.line2 ?? '');
      setValue(
        'contact.address.neighborhoodAssociation',
        value.contact?.address?.neighborhoodAssociation ?? '',
      );
      setValue(
        'contact.address.communityAssociation',
        value.contact?.address?.communityAssociation ?? '',
      );
      setValue(
        'contact.address.subDistrict',
        value.contact?.address?.subDistrict ?? '',
      );
      setValue(
        'contact.address.district',
        value.contact?.address?.district ?? '',
      );

      setValue('contact.address.province', value.contact?.address?.state ?? '');
      console.log(
        'value.contact?.address?.city- ',
        value.contact?.address?.city,
      );
      setValue('contact.address.city', value.contact?.address?.city);
      setValue(
        'contact.address.postCode',
        value.contact?.address?.postCode ?? '',
      );

      // --- Set the Checkbox ---
      setValue(
        'contact.isBusinessSameAsResidentialAddress',
        value.contact?.isBusinessSameAsResidentialAddress ?? true,
      );

      // --- Set Business Address Fields ---
      setValue(
        'contact.businessAddress.line1',
        value.contact?.businessAddress?.line1 ?? '',
      );
      setValue(
        'contact.businessAddress.line2',
        value.contact?.businessAddress?.line2 ?? '',
      );
      setValue(
        'contact.businessAddress.neighborhoodAssociation',
        value.contact?.businessAddress?.neighborhoodAssociation ?? '',
      );
      setValue(
        'contact.businessAddress.communityAssociation',
        value.contact?.businessAddress?.communityAssociation ?? '',
      );
      setValue(
        'contact.businessAddress.subDistrict',
        value.contact?.businessAddress?.subDistrict ?? '',
      );
      setValue(
        'contact.businessAddress.district',
        value.contact?.businessAddress?.district ?? '',
      );
      setValue(
        'contact.businessAddress.province',
        value.contact?.businessAddress?.province ?? '',
      );
      setValue(
        'contact.businessAddress.city',
        value.contact?.businessAddress?.city ?? '',
      );
      setValue(
        'contact.businessAddress.postCode',
        value.contact?.businessAddress?.postCode ?? '',
      );
    }
  }, [value, setValue]);

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      const submit = async () => {
        const isValid = await trigger();
        if (isValid) {
          const { contact } = getValues();

          const isBusinessSame = contact.isBusinessSameAsResidentialAddress;

          const residentialAddress = createAddressInput(contact.address);

          const businessAddress = isBusinessSame
            ? { ...residentialAddress }
            : createAddressInput(contact.businessAddress);

          const payload = {
            contact: {
              isBusinessSameAsResidentialAddress: isBusinessSame,
              address: residentialAddress,
              businessAddress,
            },
            done: true,
          } satisfies AddressInfoFromStore;
          onDone(payload);
          bottomSheetProps.bottomSheetRef.current?.close();
        } else {
          console.log('Validation failed', errors);
        }
      };

      return (
        <FormFooter
          {...props}
          primaryDisabled={!isAllMandatoryFieldsFilled}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel={t('eRecruit.application.done')}
        />
      );
    },
    [
      bottomSheetProps.bottomSheetRef,
      errors,
      getValues,
      isAllMandatoryFieldsFilled,
      onDone,
      t,
      trigger,
    ],
  );

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          index={1}
          onDismiss={onDismiss}
          snapPoints={snapPoints}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <Box px={space[isNarrowScreen ? 3 : 4]}>
            <Row alignItems="center" gap={space[1]}>
              <NewAddrIcon width={sizes[10]} height={sizes[10]} />
              <H7 color={colors.primary} fontWeight="bold">
                {t('eRecruit.application.otherDetails.addressInformation')}
              </H7>
            </Row>
          </Box>
          <BottomSheetKeyboardAwareScrollView
            bottomOffset={space[10]}
            style={{
              paddingHorizontal: space[4],
              flex: 1,
            }}>
            <Box paddingBottom={space[4]}>
              <LargeLabel color="#333333" fontWeight="medium"></LargeLabel>
              <Column gap={space[6]}>
                <Row gap={space[5]}>
                  <H8 fontWeight="bold" color={colors.palette.fwdOrange[100]}>
                    {t('eRecruit.application.otherDetails.residentialAddress')}
                  </H8>
                </Row>
                <Input
                  control={control}
                  as={TextField}
                  name="contact.address.line1"
                  label={t('eRecruit.application.otherDetails.addressLine1')}
                  style={{ flex: 1 }}
                  hint={t(
                    'eRecruit.application.otherDetails.addressLine1.hint',
                  )}
                  error={
                    errors?.contact?.address?.line1?.message
                      ? errors?.contact?.address?.line1?.message
                      : errors?.contact?.address?.message
                  }
                />
                <Input
                  control={control}
                  as={TextField}
                  name="contact.address.line2"
                  label={t('eRecruit.application.otherDetails.addressLine2')}
                  style={{ flex: 1 }}
                  hint={t(
                    'eRecruit.application.otherDetails.addressLine2.hint',
                  )}
                  error={
                    errors?.contact?.address?.line2?.message
                      ? errors?.contact?.address?.line2?.message
                      : errors?.contact?.address?.message
                  }
                />
                <Row gap={space[5]} flex={1}>
                  {/* Rukun Tetangga  - neighborhoodAssociation */}
                  <Input
                    control={control}
                    as={TextField}
                    name="contact.address.neighborhoodAssociation"
                    label={t(
                      'eRecruit.application.otherDetails.neighborhoodAssociation',
                    )}
                    style={{ flex: 1 }}
                    error={
                      errors?.contact?.address?.neighborhoodAssociation
                        ?.message ?? errors?.contact?.address?.message
                    }
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />
                  {/* Rukun Warga - communityAssociation */}
                  <Input
                    control={control}
                    as={TextField}
                    name="contact.address.communityAssociation"
                    label={t(
                      'eRecruit.application.otherDetails.communityAssociation',
                    )}
                    style={{ flex: 1 }}
                    error={
                      errors?.contact?.address?.communityAssociation?.message ??
                      errors?.contact?.address?.message
                    }
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />
                </Row>
                <Input
                  control={control}
                  as={TextField}
                  name="contact.address.subDistrict"
                  label={t('eRecruit.application.otherDetails.subdistrict')}
                  style={{ flex: 1 }}
                  error={
                    errors?.contact?.address?.subDistrict?.message ??
                    errors?.contact?.address?.message
                  }
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />

                <Input
                  control={control}
                  as={TextField}
                  name="contact.address.district"
                  label={t('eRecruit.application.otherDetails.district')}
                  style={{ flex: 1 }}
                  error={
                    errors?.contact?.address?.district?.message ??
                    errors?.contact?.address?.message
                  }
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />

                <Input
                  control={control}
                  as={
                    SearchableDropdown<{ value: string; label: string }, string>
                  }
                  name="contact.address.province"
                  label={t('eRecruit.application.otherDetails.province')}
                  data={provinceList ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => String(item.value)}
                  style={{ flex: 1 }}
                  searchable
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />

                <Input
                  control={control}
                  as={
                    SearchableDropdown<{ value: string; label: string }, string>
                  }
                  name="contact.address.city"
                  label={t(
                    'eRecruit.application.otherDetails.residentialAddress.city',
                  )}
                  data={provinceBasedCityListForMain ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => String(item.value)}
                  style={{ flex: 1 }}
                  searchable
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                  disabled={Boolean(mainAddressProvince) == false}
                />
                <Input
                  control={control}
                  as={TextField}
                  name="contact.address.postCode"
                  label={t(
                    'eRecruit.application.otherDetails.residentialAddress.postCode',
                  )}
                  style={{ flex: 1 }}
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />

                <Column gap={space[3]}>
                  <Row>
                    <H8 fontWeight="bold" color={colors.palette.fwdOrange[100]}>
                      {t('eRecruit.application.otherDetails.businessAddress')}
                    </H8>
                  </Row>
                  <Row gap={space[2]}>
                    <Input
                      control={control}
                      as={Checkbox}
                      style={{ flex: 1 }}
                      name="contact.isBusinessSameAsResidentialAddress"
                      label={t(
                        'eRecruit.application.otherDetails.sameAsResidentialAddress',
                      )}
                    />
                  </Row>
                </Column>
                <Column gap={space[6]}>
                  {watch('contact.isBusinessSameAsResidentialAddress') ==
                    false && (
                    <>
                      <Input
                        control={control}
                        as={TextField}
                        name="contact.businessAddress.line1"
                        label={t(
                          'eRecruit.application.otherDetails.addressLine1',
                        )}
                        style={{ flex: 1 }}
                        hint={t(
                          'eRecruit.application.otherDetails.addressLine1.hint',
                        )}
                        error={errors?.contact?.businessAddress?.line1?.message}
                      />
                      <Input
                        control={control}
                        as={TextField}
                        name="contact.businessAddress.line2"
                        label={t(
                          'eRecruit.application.otherDetails.addressLine2',
                        )}
                        style={{ flex: 1 }}
                        hint={t(
                          'eRecruit.application.otherDetails.addressLine2.hint',
                        )}
                        error={errors?.contact?.businessAddress?.line2?.message}
                      />
                      <Row gap={space[5]} flex={1}>
                        {/* Rukun Tetangga  - neighborhoodAssociation */}
                        <Input
                          control={control}
                          as={TextField}
                          name="contact.businessAddress.neighborhoodAssociation"
                          label={t(
                            'eRecruit.application.otherDetails.neighborhoodAssociation',
                          )}
                          style={{ flex: 1 }}
                          error={
                            errors?.contact?.businessAddress
                              ?.neighborhoodAssociation?.message ??
                            errors?.contact?.businessAddress?.message
                          }
                          shouldHighlightOnUntouched={
                            Input.defaultHighlightCheck
                          }
                          initialHighlight={false}
                        />
                        {/* Rukun Warga - communityAssociation */}
                        <Input
                          control={control}
                          as={TextField}
                          name="contact.businessAddress.communityAssociation"
                          label={t(
                            'eRecruit.application.otherDetails.communityAssociation',
                          )}
                          style={{ flex: 1 }}
                          error={
                            errors?.contact?.businessAddress
                              ?.communityAssociation?.message ??
                            errors?.contact?.businessAddress?.message
                          }
                          shouldHighlightOnUntouched={
                            Input.defaultHighlightCheck
                          }
                          initialHighlight={false}
                        />
                      </Row>
                      <Input
                        control={control}
                        as={TextField}
                        name="contact.businessAddress.subDistrict"
                        label={t(
                          'eRecruit.application.otherDetails.subdistrict',
                        )}
                        style={{ flex: 1 }}
                        error={
                          errors?.contact?.businessAddress?.subDistrict
                            ?.message ??
                          errors?.contact?.businessAddress?.message
                        }
                        shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                        initialHighlight={false}
                      />

                      <Input
                        control={control}
                        as={TextField}
                        name="contact.businessAddress.district"
                        label={t('eRecruit.application.otherDetails.district')}
                        style={{ flex: 1 }}
                        error={
                          errors?.contact?.businessAddress?.district?.message ??
                          errors?.contact?.businessAddress?.message
                        }
                        shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                        initialHighlight={false}
                      />

                      <Input
                        control={control}
                        as={
                          SearchableDropdown<
                            { value: string; label: string },
                            string
                          >
                        }
                        name="contact.businessAddress.province"
                        label={t(
                          'eRecruit.application.otherDetails.residentialAddress.province',
                        )}
                        data={provinceList ?? []}
                        getItemLabel={item => item.label}
                        getItemValue={item => String(item.value)}
                        style={{ flex: 1 }}
                        searchable
                        error={
                          errors?.contact?.businessAddress?.province?.message ??
                          errors?.contact?.businessAddress?.message
                        }
                        shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                        initialHighlight={false}
                      />
                      <Input
                        control={control}
                        as={
                          SearchableDropdown<
                            { value: string; label: string },
                            string
                          >
                        }
                        name="contact.businessAddress.city"
                        label={t(
                          'eRecruit.application.otherDetails.residentialAddress.city',
                        )}
                        data={provinceBasedCityListForSecondary ?? []}
                        getItemLabel={item => item.label}
                        getItemValue={item => String(item.value)}
                        style={{ flex: 1 }}
                        searchable
                        error={
                          errors?.contact?.businessAddress?.city?.message ??
                          errors?.contact?.businessAddress?.message
                        }
                        shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                        initialHighlight={false}
                      />
                      <Input
                        control={control}
                        as={TextField}
                        name="contact.businessAddress.postCode"
                        label={t(
                          'eRecruit.application.otherDetails.residentialAddress.postCode',
                        )}
                        style={{ flex: 1 }}
                        error={
                          errors?.contact?.businessAddress?.postCode?.message ??
                          errors?.contact?.businessAddress?.message
                        }
                        shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                        initialHighlight={false}
                      />
                    </>
                  )}
                </Column>
              </Column>
              <BottomSheetFooterSpace />
            </Box>
          </BottomSheetKeyboardAwareScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
