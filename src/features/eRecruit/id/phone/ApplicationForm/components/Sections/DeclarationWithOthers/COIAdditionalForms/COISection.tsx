import { useTheme } from '@emotion/react';
import { Box, H6, LargeBody, Row } from 'cube-ui-components';
import { Fragment } from 'react';
import { questionsMap } from 'features/eRecruit/ib/tablet/config';
import Tooltip from 'components/Tooltip';
import React from 'react';
import { COISectionSeparator } from './COISectionSeparator';
import { COIQuestionMapReturnType } from 'features/eRecruit/ib/tablet/NewApplicationForm/DeclarationOfCOI';

const keysOfQuestionsMap = Object.keys(questionsMap);

export function COISection({
  questionsMap,
  questionKey,
  tooltipPlacement,
  children,
}: {
  questionsMap: COIQuestionMapReturnType;
  questionKey: keyof COIQuestionMapReturnType;
  tooltipPlacement?: 'top' | 'bottom';
  children?: React.ReactNode;
}) {
  const { space } = useTheme();
  const idxOfQuestions = keysOfQuestionsMap.indexOf(questionKey);

  return (
    <Row>
      <H6 fontWeight="bold">{`${
        idxOfQuestions >= 0 ? idxOfQuestions + 1 : '--'
      }. `}</H6>
      <Box gap={space[4]} flex={1}>
        <H6 fontWeight="bold">{questionsMap?.[questionKey]?.title}</H6>
        <LargeBody
          style={{
            flexDirection: 'row',
          }}>
          {questionsMap?.[questionKey]?.qBody.length > 1 &&
          questionsMap?.[questionKey] &&
          questionsMap?.[questionKey]?.tooltipContentList?.length > 0 ? (
            questionsMap?.[questionKey]?.qBody.map((qBody, idx) => {
              return (
                <Fragment
                  key={
                    questionsMap?.[questionKey].key +
                    questionsMap?.[questionKey]?.tooltipContentList?.[idx]
                  }>
                  <LargeBody>{qBody}</LargeBody>
                  {idx != questionsMap?.[questionKey]?.qBody?.length - 1 && (
                    <Tooltip
                      placement={tooltipPlacement ?? 'bottom'}
                      style={{ top: 3 }}
                      content={
                        questionsMap?.[questionKey]?.tooltipContentList?.[idx]
                      }
                    />
                  )}
                </Fragment>
              );
            })
          ) : (
            <LargeBody>{questionsMap?.[questionKey]?.qBody}</LargeBody>
          )}
        </LargeBody>
        {children}
        <COISectionSeparator />
      </Box>
    </Row>
  );
}
