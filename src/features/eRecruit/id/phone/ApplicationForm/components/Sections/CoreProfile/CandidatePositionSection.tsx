import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import Portal from 'components/Portal/Portal';
import {
  Box,
  Column,
  H7,
  LargeLabel,
  Picker,
  PictogramIcon,
  Row,
  SmallLabel,
  Switch,
} from 'cube-ui-components';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useCallback, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  CandidatePositionFromStore,
  useERecruitStore,
} from 'features/eRecruit/util/store/id/ERecruitStore';
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { SharedValue } from 'react-native-reanimated';
import Input from 'components/Input';
import SearchableDropdown from 'components/SearchableDropdown';
import { shallow } from 'zustand/shallow';
import FormFooter from '../../utils/FormFooter';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';
import LoadingIndicator from 'components/LoadingIndicator';
import { candidatePositionSchema } from 'features/eRecruit/id/validations/phone';
import { CandidatePositionFieldPath } from 'features/eRecruit/util/store/id/types';

interface Props {
  onDismiss: () => void;
  value: CandidatePositionFromStore;
  onDone: (data: CandidatePositionFromStore) => void;
}

export default function CandidatePositionSection({
  onDismiss,
  value,
  onDone,
}: Props) {
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes } = useTheme();

  const hookForm = useForm({
    defaultValues: {
      candidatePosition: { isHaveFinancingProgram: false },
    },

    values: {
      candidatePosition: {
        position: value.candidatePosition.position,
        salesOffice: value.candidatePosition.salesOffice,
        domicile: value.candidatePosition.domicile,
        osAreaManager: value.candidatePosition.osAreaManager,
        superiorAgentCode: value.candidatePosition.superiorAgentCode,
        ref: value.candidatePosition.ref,
        isHaveFinancingProgram: value.candidatePosition.isHaveFinancingProgram,
      },
    },
    resolver: yupResolver(candidatePositionSchema),
    mode: 'onBlur',
  });

  const {
    control,
    trigger,
    formState: { errors },
    getValues,
    watch,
  } = hookForm;

  const {
    isLoading,
    salesOfficeList,
    domicileList,
    allBranchOptions,
    areaManagerList,
    refList,
    superiorAgentCodeList,
    positionList,
  } = useGetERecruitOptionListForAppForm();

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const submit = useCallback(async () => {
    const isValid = await trigger();
    const currentFormValues = getValues();
    console.log('-------- currentFormValues: ', currentFormValues);
    if (!isValid) {
      return console.log('Validation failed', errors);
    }
    onDone(
      currentFormValues && {
        done: true,
        candidatePosition: {
          ...currentFormValues.candidatePosition,
        },
      },
    );
    bottomSheetProps.bottomSheetRef.current?.close();
  }, [trigger, getValues, onDone, bottomSheetProps, errors]);

  const mandatoryFields = [
    'candidatePosition.position',
    'candidatePosition.salesOffice',
    'candidatePosition.domicile',
    'candidatePosition.osAreaManager',
    'candidatePosition.superiorAgentCode',
    'candidatePosition.ref',
    'candidatePosition.isHaveFinancingProgram',
  ] as const satisfies CandidatePositionFieldPath[];

  const isAnyMandatoryFieldsEmpty = mandatoryFields.some(
    f => Boolean(f) == false,
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const footerPrimaryLabel = t('eRecruit.application.done');
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => (
      <FormFooter
        {...props}
        primaryDisabled={isAnyMandatoryFieldsEmpty}
        onPrimaryPress={submit}
        primaryLoading={false}
        primaryLabel={footerPrimaryLabel}
      />
    ),
    [isAnyMandatoryFieldsEmpty, footerPrimaryLabel, submit],
  );

  const domicile = watch('candidatePosition.domicile');
  const domicileCodeToNameMap = useMemo(() => {
    return domicileList.reduce((map, item) => {
      map[item.value] = item.label;
      return map;
    }, {} as Record<string, string>);
  }, [domicileList]);

  const domicileBasedAreaManagerList = useMemo(
    () =>
      areaManagerList.filter(
        aMan => aMan.value == domicileCodeToNameMap?.[domicile]?.toUpperCase(),
      ),
    [areaManagerList, domicile, domicileCodeToNameMap],
  );

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <View onLayout={handleContentLayout}>
            <Box px={space[isNarrowScreen ? 3 : 4]}>
              <Row alignItems="center" gap={space[1]}>
                <PictogramIcon.ManWithShield
                  width={sizes[10]}
                  height={sizes[10]}
                />
                <H7 color={colors.primary} fontWeight="bold">
                  {t('eRecruit.application.otherDetails.candidateInformation')}
                </H7>
              </Row>
            </Box>
            <BottomSheetKeyboardAwareScrollView
              keyboardDismissMode="on-drag"
              style={{ paddingHorizontal: space[isNarrowScreen ? 3 : 4] }}>
              <Box paddingBottom={space[4]}>
                <Column gap={space[6]}>
                  <Column gap={space[2]}>
                    <SmallLabel color={colors.palette.fwdGreyDarker}>
                      {/* {t(
                        'eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition',
                      )} */}
                    </SmallLabel>
                    <Input
                      control={control}
                      as={Picker}
                      name="candidatePosition.position"
                      label={t(
                        'eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition',
                      )}
                      items={positionList ?? []}
                      type="chip"
                      size="large"
                      labelStyle={{
                        marginLeft: space[3],
                        color: colors.palette.fwdGreyDarker,
                      }}
                      onChange={() => {
                        setTimeout(() => {
                          trigger('candidatePosition.position');
                        }, 500);
                      }}
                      shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                      initialHighlight={false}
                    />
                    {isLoading && <LoadingIndicator />}
                  </Column>
                  <Input
                    control={control}
                    as={
                      SearchableDropdown<
                        { value: string; label: string },
                        string
                      >
                    }
                    name={'candidatePosition.salesOffice'}
                    label={t('eRecruit.application.otherDetails.salesOffice')}
                    data={salesOfficeList ?? []}
                    getItemLabel={item => item.label}
                    getItemValue={item => String(item.value)}
                    style={{ flex: 2, marginTop: space[2] }}
                    searchable
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />

                  <Input
                    control={control}
                    as={
                      SearchableDropdown<
                        { value: string; label: string },
                        string
                      >
                    }
                    name={'candidatePosition.domicile'}
                    label={t('eRecruit.application.otherDetails.domicile')}
                    data={domicileList ?? []}
                    getItemLabel={item => item.label}
                    getItemValue={item => String(item.value)}
                    style={{ flex: 2, marginTop: space[2] }}
                    searchable
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />
                  <Input
                    control={control}
                    as={
                      SearchableDropdown<
                        { value: string; label: string },
                        string
                      >
                    }
                    name={'candidatePosition.osAreaManager'}
                    label={t('eRecruit.application.otherDetails.areaManager')}
                    data={domicileBasedAreaManagerList ?? []}
                    keyExtractor={item => item.label + item.value}
                    getItemLabel={item => item.label}
                    getItemValue={item => item.label}
                    style={{ flex: 1, marginTop: space[2] }}
                    searchable
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                    disabled={domicile ? false : true}
                  />

                  <Input
                    control={control}
                    as={
                      SearchableDropdown<
                        { value: string; label: string },
                        string
                      >
                    }
                    name={'candidatePosition.superiorAgentCode'}
                    label={t('eRecruit.application.otherDetails.supervisor')}
                    data={superiorAgentCodeList ?? []}
                    getItemLabel={item => item.label}
                    getItemValue={item => String(item.value)}
                    style={{ flex: 1, marginTop: space[2] }}
                    searchable
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />
                  <Input
                    control={control}
                    as={
                      SearchableDropdown<
                        { value: string; label: string },
                        string
                      >
                    }
                    name={'candidatePosition.ref'}
                    label={t('eRecruit.application.otherDetails.ref')}
                    data={refList ?? []}
                    getItemLabel={item => item.label}
                    getItemValue={item => String(item.value)}
                    style={{ flex: 1, marginTop: space[2] }}
                    searchable
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />
                  <Row gap={space[5]}>
                    <Controller
                      control={control}
                      name="candidatePosition.isHaveFinancingProgram"
                      render={({ field: { onChange, value } }) => (
                        <Row gap={space[4]} flex={1}>
                          <LargeLabel fontWeight="bold">
                            {t(
                              'eRecruit.application.otherDetails.financingProgram',
                            )}
                          </LargeLabel>
                          <Switch
                            label={
                              value
                                ? t(
                                    'eRecruit.application.personalDetails.question.yes',
                                  )
                                : t(
                                    'eRecruit.application.personalDetails.question.no',
                                  )
                            }
                            value={value}
                            onChange={() => {
                              onChange(!value);
                            }}
                          />
                        </Row>
                      )}
                    />
                  </Row>
                  {/* <Column gap={space[2]}>
                    <SmallLabel color={colors.palette.fwdGreyDarker}>
                      {t(
                        'eRecruit.application.otherDetails.supervisorCandidateInformation.jobType',
                      )}
                    </SmallLabel>
                    <Row>
                      <Picker
                        type="chip"
                        size="medium"
                        containerStyle={{
                          flex: 1,
                          flexWrap: 'wrap',
                          rowGap: space[2],
                        }}
                        items={jobTypeOptions}
                        value={watch('position.jobType')}
                        onChange={value => {
                          setValue('position.jobType', value);
                        }}
                      />
                    </Row>
                  </Column>

                  <Input
                    control={control}
                    as={TextField}
                    name="leaderInformation.alcFwdName"
                    label={t(alcFwdNameLabelKey)}
                    onChangeText={value => {
                      setValue(
                        'leaderInformation.alcFwdName',
                        correctLengthLimit(value, 50),
                      );
                    }}
                    style={{ flex: 1 }}
                    error={errors?.leaderInformation?.alcFwdName?.message}
                  />
                  <Input
                    // disabled={branchList.length === 0}
                    control={control}
                    as={
                      SearchableDropdown<
                        { value: string; label: string },
                        string
                      >
                    }
                    name={'position.branchCode'}
                    label={t(
                      'eRecruit.application.otherDetails.supervisorCandidateInformation.reportingBranch',
                    )}
                    data={branchList}
                    getItemLabel={item => item.label}
                    getItemValue={item => String(item.value)}
                    style={{ flex: 1, marginTop: space[2] }}
                    searchable
                  />
                  <Column flex={1} gap={space[2]}>
                    <SmallLabel color={colors.palette.fwdGreyDarker}>
                      {t(
                        'eRecruit.application.otherDetails.supervisorCandidateInformation.introducerCode',
                      )}
                    </SmallLabel>
                    <LargeBody>{agentInfo?.agentCode}</LargeBody>
                  </Column>

                  <Column flex={1} gap={space[2]}>
                    <SmallLabel color={colors.palette.fwdGreyDarker}>
                      {t(
                        'eRecruit.application.otherDetails.supervisorCandidateInformation.introducerName',
                      )}
                    </SmallLabel>
                    <LargeBody>{agentInfo?.agentName}</LargeBody>
                  </Column> */}
                </Column>
                <BottomSheetFooterSpace />
              </Box>
            </BottomSheetKeyboardAwareScrollView>
          </View>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
