import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  Column,
  DatePicker,
  H7,
  Icon,
  Row,
  TextField,
} from 'cube-ui-components';
import { useEffect, useState } from 'react';
import { FieldArrayMethodProps, useFieldArray } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { declarationCOIDefaultValue } from 'features/eRecruit/ib/validations/otherDetailsSchema';
import { TouchableOpacity } from 'react-native';
import React from 'react';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { COISectionSeparator } from './COISectionSeparator';
import { AddButton } from '../../../utils/AddButton';
import { isAfter } from 'date-fns';
import { CoiHookFormForAdditionalForms } from './types';

export default function BusinessAffiliationsInterestsForm({
  hookForm,
  maxRecords = 5,
}: {
  hookForm: CoiHookFormForAdditionalForms;
  maxRecords?: number;
}) {
  const { space, colors } = useTheme();
  const { t } = useTranslation('eRecruit');
  const appendListFocusOption: FieldArrayMethodProps = { shouldFocus: false };

  const [showErrorField, setShowErrorField] = useState<string[]>([]);

  const {
    watch,
    control,
    setValue,
    formState: { errors },
    trigger,
  } = hookForm;
  const { fields, append, remove } = useFieldArray({
    name: 'conflictOfInterest.businessAffiliationInterests',
    control,
  });
  const isBusinessAffiliationInterestYes = watch(
    'conflictOfInterest.businessAffiliationInterest',
  );

  useEffect(() => {
    if (isBusinessAffiliationInterestYes) {
      fields.length === 0 &&
        append(
          {
            ...declarationCOIDefaultValue.conflictOfInterest
              .businessAffiliationInterests,
          },
          appendListFocusOption,
        );
    } else {
      fields.length > 0 && remove();
      // remove all item in the list when no index is provided;
    }
  }, [fields, isBusinessAffiliationInterestYes, append, remove]);

  if (isBusinessAffiliationInterestYes == false) {
    return null;
  }

  return (
    <Box minHeight={40} gap={space[4]}>
      {fields.map((field, idx) => {
        const dateApplied = watch(
          `conflictOfInterest.businessAffiliationInterests.${idx}.dateCommencementEmployment`,
        );
        if (dateApplied && isAfter(new Date(dateApplied), new Date())) {
          setValue(
            `conflictOfInterest.businessAffiliationInterests.${idx}.dateCommencementEmployment`,
            `${new Date()}`,
          );
        }
        return (
          <React.Fragment key={field.id}>
            <Column gap={space[4]}>
              <Row justifyContent="space-between">
                <Row alignItems="center" gap={space[2]}>
                  <H7 fontWeight="bold" key={idx}>
                    {t('application.COI.record', {
                      number: (idx ?? 0) + 1,
                    })}
                  </H7>
                </Row>
                {fields?.length === 1 && idx === 0 ? (
                  <TouchableOpacity disabled>
                    <Icon.Delete fill={colors.palette.fwdGreyDark} />
                  </TouchableOpacity>
                ) : (
                  <TouchableOpacity
                    onPress={() => {
                      if (fields?.length === 1) {
                        console.log('last item should not be deleted');
                        return;
                      }
                      remove(idx);
                    }}>
                    <Icon.Delete fill={colors.palette.fwdDarkGreen[100]} />
                  </TouchableOpacity>
                )}
              </Row>

              <Column gap={space[6]}>
                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.businessAffiliationInterests.${idx}.nameOfBusiness`}
                  label={t('application.COI.nameOfBusinessEnterpriseOrEntity')}
                  style={{ flex: 1 }}
                />

                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.businessAffiliationInterests.${idx}.natureOfBusiness`}
                  label={t('application.COI.natureOfBusiness')}
                  style={{ flex: 1 }}
                />
                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.businessAffiliationInterests.${idx}.nameOfFamilyMember`}
                  label={t('application.COI.nameOfFamilyMember')}
                  style={{ flex: 1 }}
                />

                {/*<Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.businessAffiliationInterests.${idx}.relationship`}
                  label={t('application.COI.relationship')}
                  style={{ flex: 1 }}
                />*/}

                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.businessAffiliationInterests.${idx}.positionDepartment`}
                  label={t('application.COI.positionDepartment')}
                  style={{ flex: 1 }}
                />

                <Input
                  control={control}
                  as={DatePicker}
                  name={`conflictOfInterest.businessAffiliationInterests.${idx}.dateCommencementEmployment`}
                  maxDate={new Date()}
                  style={{
                    flex: 1,
                  }}
                  label={t('application.COI.dateCommencementEmployment')}
                  hint={t('eRecruit.application.dateHint')}
                  formatDate={value => (value ? dateFormatUtil(value) : '')}
                  value={
                    watch(
                      `conflictOfInterest.businessAffiliationInterests.${idx}.dateCommencementEmployment`,
                    )
                      ? new Date(
                          watch(
                            `conflictOfInterest.businessAffiliationInterests.${idx}.dateCommencementEmployment`,
                          ),
                        )
                      : undefined
                  }
                  error={
                    !dateApplied
                      ? errors.conflictOfInterest
                          ?.businessAffiliationInterests?.[idx]
                          ?.dateCommencementEmployment?.message
                      : ''
                  }
                />
              </Column>
            </Column>
            {fields?.length > 1 && idx != fields?.length - 1 && (
              <COISectionSeparator />
            )}
          </React.Fragment>
        );
      })}
      <AddButton
        customText={t(`eRecruit.application.occupationDetails.add`)}
        isDisabled={fields.length >= maxRecords}
        isHidden={fields.length >= maxRecords || maxRecords == 1}
        onPress={() => {
          fields.length < maxRecords &&
            append(
              {
                ...declarationCOIDefaultValue.conflictOfInterest
                  .businessAffiliationInterests,
              },
              appendListFocusOption,
            );
        }}
      />
    </Box>
  );
}
