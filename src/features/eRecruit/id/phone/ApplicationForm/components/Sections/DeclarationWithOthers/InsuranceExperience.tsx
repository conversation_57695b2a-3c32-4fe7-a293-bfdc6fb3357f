import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import Input from 'components/Input';
import Portal from 'components/Portal/Portal';
import { Box, Column, H6, Label, Row } from 'cube-ui-components';
import {} from 'cube-ui-components';
import RecruitYesNoInput from 'features/eRecruit/components/RecruitYesNoInput';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';

import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { InsuranceExperienceFromStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FormFooter from '../../utils/FormFooter';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';

// TBC
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { InsuranceExpPath } from 'features/eRecruit/util/store/id/types';
import { insuranceExperienceSchema } from 'features/eRecruit/id/validations/phone';
import { SharedValue } from 'react-native-reanimated';
import NewInsuranceExpIcon from 'features/eRecruit/ib/tablet/asset/NewInsuranceExpIcon';

interface Props {
  onDismiss: () => void;
  value: InsuranceExperienceFromStore;
  onDone: (data: InsuranceExperienceFromStore) => void;
}

export default function InsuranceExperience({
  onDismiss,
  value,
  onDone,
}: Props) {
  console.log('--- value: ', value);
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes } = useTheme();

  const { isLoading, genderConfig, countryCodeOptions } =
    useGetERecruitOptionListForAppForm();

  const hookForm = useForm({
    // defaultValues: {

    // },
    values: {
      insuranceExperience: {
        haveGeneralInsuranceExp:
          value.insuranceExperience?.haveGeneralInsuranceExp,
        haveLifeInsuranceExp: value.insuranceExperience?.haveLifeInsuranceExp,
      },
    },
    resolver: yupResolver(insuranceExperienceSchema),
    mode: 'onBlur',
  });

  const {
    control,
    watch,
    trigger,
    formState: { errors },
    getValues,
    setValue,
  } = hookForm;

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const submit = useCallback(async () => {
    const isValid = await trigger();
    if (isValid) {
      const currentValues = getValues();
      onDone({
        done: true,
        ...currentValues,
      });

      bottomSheetProps.bottomSheetRef.current?.close();
    } else {
      console.log('Validation failed', errors);
    }
  }, [bottomSheetProps.bottomSheetRef, errors, getValues, onDone, trigger]);

  const mandatoryFields = [
    'insuranceExperience.haveGeneralInsuranceExp',
    'insuranceExperience.haveLifeInsuranceExp',
  ] as const satisfies Array<InsuranceExpPath>;

  const isAllMandatoryFieldsFilled = watch(mandatoryFields).every(
    (item: unknown) => item != null,
  );

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          primaryDisabled={!isAllMandatoryFieldsFilled}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel="Done"
        />
      );
    },
    [isAllMandatoryFieldsFilled, submit],
  );

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <Box onLayout={handleContentLayout}>
            <Box px={space[4]}>
              <Row alignItems="center" gap={space[1]}>
                <NewInsuranceExpIcon />
                <H6 color={colors.primary} fontWeight="bold">
                  {t(
                    `eRecruit.application.personalDetails.insuranceExperience`,
                  )}
                </H6>
              </Row>
            </Box>
            <BottomSheetKeyboardAwareScrollView
              bottomOffset={space[10]}
              style={{
                paddingHorizontal: space[4],
                flex: 1,
              }}>
              <Box paddingY={space[4]}>
                <Column gap={space[6]}>
                  <Row gap={space[2]}>
                    <Label style={{ width: space[4] }}>1.</Label>
                    <RecruitYesNoInput
                      control={control}
                      name={'insuranceExperience.haveLifeInsuranceExp'}
                      label={t(
                        'eRecruit.application.personalDetails.questionOne',
                      )}
                      onChange={() => {
                        setTimeout(() => {
                          trigger('insuranceExperience.haveLifeInsuranceExp');
                        }, 500);
                      }}
                      shouldHighlightOnUntouched={
                        Input.defaultHighlightCheckForBoolean
                      }
                      initialHighlight={false}
                    />
                  </Row>
                  <Row gap={space[2]}>
                    <Label style={{ width: space[4] }}>2.</Label>
                    <RecruitYesNoInput
                      control={control}
                      name={'insuranceExperience.haveGeneralInsuranceExp'}
                      label={t(
                        'eRecruit.application.personalDetails.questionTwo',
                      )}
                      onChange={() => {
                        setTimeout(() => {
                          trigger(
                            'insuranceExperience.haveGeneralInsuranceExp',
                          );
                        }, 500);
                      }}
                      shouldHighlightOnUntouched={
                        Input.defaultHighlightCheckForBoolean
                      }
                      initialHighlight={false}
                    />
                  </Row>
                  {/* <Input
                  control={control}
                  as={TextField}
                  name="identity.fullName"
                  style={{ flex: 1 }}
                  label={t(`eRecruit.application.personalDetails.fullName`)}
                  error={errors?.identity?.fullName?.message}
                />

                <Input
                  control={control}
                  as={Picker}
                  name="identity.gender"
                  type="text"
                  label={t(`eRecruit.application.personalDetails.gender`)}
                  style={{ flex: 1 }}
                  items={genderConfig}
                  error={errors?.identity?.gender?.message}
                />

                <Row style={{ flex: 1, gap: space[3] }}>
                  <Input
                    control={control}
                    as={DatePicker}
                    name="identity.dateOfBirth"
                    style={{
                      flex: 8,
                    }}
                    label={t(
                      `eRecruit.application.personalDetails.dateOfBirth`,
                    )}
                    hint="DD/MM/YYYY"
                    formatDate={() => (dob ? dateFormatUtil(dob) : '')}
                    minDate={minDate}
                    maxDate={maxDate}
                    value={dob ?? maxDate}
                    onChange={() =>
                      setTimeout(() => trigger('identity.dateOfBirth'), 100)
                    }
                  />
                  <TextField
                    disabled={true}
                    label={t('eRecruit.application.personalDetails.age')}
                    value={age}
                    style={{ flex: 2 }}
                  />
                </Row>
                <Row style={{ flex: 1, gap: space[3] }}>
                  <Input
                    control={control}
                    as={SearchableDropdown<CountryCode, string>}
                    data={countryCodeOptions}
                    modalTitle={'Country code'}
                    name="contact.countryCode"
                    style={{ flex: 108 }}
                    label={t(`eRecruit.application.personalDetails.code`)}
                    getItemValue={item => item?.value}
                    getItemLabel={item => item?.label}
                    keyExtractor={item => item?.value + item?.label}
                    getDisplayedLabel={item => getCountryCodeValue(item)}
                    error={errors?.contact?.countryCode?.message}
                    disabled
                  />

                  <Input
                    autoCapitalize="none"
                    control={control}
                    as={PhoneField}
                    name="contact.phoneNumber"
                    label={t(
                      `eRecruit.application.personalDetails.mobileNumber`,
                    )}
                    style={{ flex: 223 }}
                    onChangeText={value => {
                      setValue(
                        'contact.phoneNumber',
                        value.replace(PHONE_NUMBER_TYPE_REGEX, ''),
                      );
                    }}
                    keyboardType="numeric"
                    error={errors?.contact?.phoneNumber?.message}
                    size="large"
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />
                </Row>
                <Input
                  autoCapitalize="none"
                  control={control}
                  as={TextField}
                  name="contact.email"
                  style={{ flex: 1 }}
                  label={t(`eRecruit.application.personalDetails.email`)}
                  error={errors?.contact?.email?.message}
                />
                <Row style={{ flex: 1, gap: space[3] }}>
                  <Input
                    control={control}
                    as={SearchableDropdown<CountryCode, string>}
                    data={countryCodeOptions}
                    modalTitle={'Country code'}
                    name="contact.officeNumberCountryCode"
                    style={{ flex: 108 }}
                    label={t(`eRecruit.application.personalDetails.code`)}
                    getItemValue={item => item.value}
                    getItemLabel={item => item.label}
                    keyExtractor={item => item.value + item.label}
                    getDisplayedLabel={item => getCountryCodeValue(item)}
                    error={errors?.contact?.officeNumberCountryCode?.message}
                    disabled
                  />
                  <Input
                    control={control}
                    as={PhoneField}
                    name="contact.officePhoneNumber"
                    label={t(
                      `eRecruit.application.personalDetails.officePhoneOptional`,
                    )}
                    style={{ flex: 223 }}
                    onChangeText={value => {
                      setValue(
                        'contact.officePhoneNumber',
                        value.replace(PHONE_NUMBER_TYPE_REGEX, ''),
                      );
                    }}
                    keyboardType="numeric"
                    error={errors?.contact?.officePhoneNumber?.message}
                    size="large"
                  />
                </Row> */}
                </Column>
              </Box>
              <BottomSheetFooterSpace />
            </BottomSheetKeyboardAwareScrollView>
          </Box>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
