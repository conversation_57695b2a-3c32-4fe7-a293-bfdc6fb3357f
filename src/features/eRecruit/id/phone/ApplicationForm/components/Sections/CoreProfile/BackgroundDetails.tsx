import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import SearchableDropdown from 'components/SearchableDropdown';
import Input from 'components/Input';
import Portal from 'components/Portal/Portal';
import {
  Box,
  Column,
  H7,
  Icon,
  LargeLabel,
  Picker,
  Row,
  TextField,
} from 'cube-ui-components';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import {} from 'features/eRecruit/id/validations/personalDetailsSchema';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useCallback, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { CountryCode } from 'types/optionList';
import { BackgroundDetailsFromStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FormFooter from '../../utils/FormFooter';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';
import { OptionConfig } from 'features/eRecruit/ib/type';
import ApplicationDetailsListIcon from 'features/eRecruit/ib/tablet/asset/ApplicationDetailsListIcon';
import {
  GroupedItems,
  updateDependenceValue,
} from 'features/eRecruit/id/tablet/NewApplicationForm/PersonalDetailsTab';
import { backgroundDetailsSchema } from 'features/eRecruit/id/validations/phone';

interface Props {
  onDismiss: () => void;
  value: BackgroundDetailsFromStore;
  onDone: (data: BackgroundDetailsFromStore) => void;
}

export default function BackgroundDetails({ onDismiss, value, onDone }: Props) {
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes } = useTheme();

  const { countryCodeOptions } = useGetERecruitOptionListForAppForm();
  const getCountryCodeValue = (item: CountryCode) => item.value.split(' - ')[0];
  const {
    isLoading,
    genderConfig,
    maritalConfig,
    religionConfig,
    idTypeList,
    occupationList,
    industryList,
    educationConfig,
  } = useGetERecruitOptionListForAppForm();

  const hookForm = useForm({
    defaultValues: {
      identity: {
        numberOfDependence: '0',
      },
    },
    values: {
      identity: {
        ...value.identity,
        numberOfDependence: value.identity?.numberOfDependence || '0',
        identity: idTypeList?.find(i => i.value == 'KTP')?.value ?? '',
      },
      personalInformation: {
        ...value.personalInformation,
        npwp: value.personalInformation?.npwp ?? '',
      },
    },

    resolver: yupResolver(backgroundDetailsSchema),
    mode: 'onBlur',
  });

  const {
    control,
    trigger,
    formState: { errors },
    getValues,
    setValue,
    resetField,
    watch,
  } = hookForm;

  const mandatoryFields = [
    'identity.identity',
    'identity.idNumber',
    'identity.birthPlace',
    'identity.religion',
    'identity.maritalStatus',
    'identity.numberOfDependence',
    'personalInformation.education',
    'personalInformation.industry',
    'personalInformation.presentOccupation',
    // "personalInformation.npwp",
  ] as const;

  const isAllMandatoryFieldsFilled = watch(mandatoryFields).every(item =>
    Boolean(item),
  );

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints();

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      const submit = async () => {
        const isValid = await trigger();
        const formData = getValues();
        if (isValid) {
          // getValues() returns the whole form data, which should match BackgroundDetailsFromStore
          onDone({
            identity: {
              ...formData.identity,
            },
            personalInformation: {
              ...formData.personalInformation,
            },
            done: true,
          });
          bottomSheetProps.bottomSheetRef.current?.close();
        } else {
          console.log('Validation failed', errors);
        }
      };

      return (
        <FormFooter
          {...props}
          primaryDisabled={!isAllMandatoryFieldsFilled}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel={t('eRecruit.application.done')}
        />
      );
    },
    [
      isAllMandatoryFieldsFilled,
      trigger,
      getValues,
      onDone,
      bottomSheetProps,
      errors,
      t,
    ],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const isIndustrySelected = Boolean(watch('personalInformation.industry'));
  const industry = watch('personalInformation.industry');

  const groupedOccupationByIndustry: GroupedItems = useMemo(
    () =>
      occupationList.reduce((acc, item) => {
        const prefix = item.value.split('-')[0];
        if (!acc[prefix]) {
          acc[prefix] = [];
        }
        acc[prefix].push(item);
        return acc;
      }, {} as GroupedItems),
    [occupationList],
  );

  const filteredOccupationListByIndustry = industry
    ? groupedOccupationByIndustry[industry]
    : [];
  // useEffect(() => {
  //   // Ensure the value object exists before trying to access its properties
  //   if (value) {
  //     // --- Populate Identity Fields ---
  //     // setValue('identity.identity', value.identity?.identity);
  //     setValue('identity.idNumber', value.identity?.idNumber);
  //     setValue('identity.birthPlace', value.identity?.birthPlace);
  //     setValue('identity.religion', value.identity?.religion);
  //     setValue('identity.maritalStatus', value.identity?.maritalStatus);
  //     setValue(
  //       'identity.numberOfDependence',
  //       value.identity?.numberOfDependence ?? '0',
  //     );

  //     // --- Populate Personal Information Fields ---
  //     setValue(
  //       'personalInformation.education',
  //       value.personalInformation?.education,
  //     );
  //     setValue(
  //       'personalInformation.industry',
  //       value.personalInformation?.industry,
  //     );
  //     setValue(
  //       'personalInformation.presentOccupation',
  //       value.personalInformation?.presentOccupation,
  //     );
  //     setValue('personalInformation.npwp', value.personalInformation?.npwp);
  //   }
  //   // This effect should re-run whenever the 'value' prop changes.
  // }, [value, setValue]);

  const numberOfDependence = watch('identity.numberOfDependence');
  const validateNumberOfDependence = () =>
    trigger('identity.numberOfDependence');

  useEffect(() => {
    const resetToDefaultIdentity = () =>
      resetField('identity.identity', {
        defaultValue:
          idTypeList?.find(i => i.value == 'KTP')?.value ?? undefined,
      });
    if (!watch('identity.identity')) {
      resetToDefaultIdentity();
      console.log(
        'identity.identity is not set, setting default value: ',
        watch('identity.identity') == '',
      );
    }
    if (value) {
      //  filling the FE form with data from BE shall be done in useForm hook, not in useEffect
      return;
    }

    // if not dataFromBE, set default value for identity.identity
    // idTypeList is feteched from BE, so it is not available in the first render
    // when idTypeList is available, using it in defaultValues in useForm hook does not trigger rendering,
    // and in values in useForm hook raise type error
    // so set default value for identity.identity in useEffect
    resetToDefaultIdentity();
  }, [watch, value, idTypeList, resetField]);
  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          index={1}
          snapPoints={snapPoints}
          // contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          {/* <View onLayout={handleContentLayout}> */}
          <Box px={space[isNarrowScreen ? 3 : 4]}>
            <Row alignItems="center" gap={space[1]}>
              <ApplicationDetailsListIcon
                width={sizes[10]}
                height={sizes[10]}
              />
              <H7 color={colors.primary} fontWeight="bold">
                {t('eRecruit.application.personalDetails.backgroundDetails')}
              </H7>
            </Row>
          </Box>
          <BottomSheetKeyboardAwareScrollView
            // keyboardDismissMode="on-drag"
            style={{ paddingHorizontal: space[isNarrowScreen ? 3 : 4] }}>
            <Box paddingBottom={space[4]}>
              <LargeLabel color="#333333" fontWeight="medium"></LargeLabel>
              <Column gap={space[4]}>
                <Input
                  control={control}
                  as={SearchableDropdown<OptionConfig, string>}
                  name="identity.identity"
                  style={{ flex: 1 }}
                  label={t(`eRecruit.application.personalDetails.identity`)}
                  error={errors?.identity?.identity?.message}
                  data={idTypeList}
                  getItemLabel={item => item?.label}
                  getItemValue={item => item?.value}
                  searchable
                  disabled={
                    idTypeList.length == 1 && !!watch('identity.identity')
                  }
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />

                <Input
                  control={control}
                  as={TextField}
                  name="identity.idNumber"
                  style={{ flex: 1 }}
                  label={t(`eRecruit.application.personalDetails.icNumber`)}
                  keyboardType="number-pad"
                  error={errors?.identity?.idNumber?.message}
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />

                <Input
                  control={control}
                  as={TextField}
                  name="identity.birthPlace"
                  label={t(`eRecruit.application.personalDetails.birthPlace`)}
                  style={{ flex: 1 }}
                  error={errors?.identity?.birthPlace?.message}
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />
                <Input
                  control={control}
                  as={Picker}
                  name="identity.religion"
                  label={t(`eRecruit.application.personalDetails.religion`)}
                  type="chip"
                  items={religionConfig}
                  size="large"
                  labelStyle={{
                    marginLeft: space[3],
                  }}
                  containerStyle={{
                    flexWrap: 'wrap',
                    rowGap: space[1],
                  }}
                  onChange={() => {
                    setTimeout(() => {
                      trigger('identity.religion');
                    }, 500);
                  }}
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />
                <Input
                  control={control}
                  as={Picker}
                  name="identity.maritalStatus"
                  label={t(
                    `eRecruit.application.personalDetails.maritalStatus`,
                  )}
                  type="chip"
                  items={maritalConfig}
                  size="large"
                  labelStyle={{
                    marginLeft: space[3],
                  }}
                  containerStyle={{
                    flexWrap: 'wrap',
                    rowGap: space[1],
                  }}
                  onChange={() => {
                    setTimeout(() => {
                      trigger('identity.maritalStatus');
                    }, 500);
                  }}
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />
                <Input
                  control={control}
                  as={TextField}
                  name="identity.numberOfDependence"
                  style={{ flex: 1 }}
                  editable={false}
                  value={numberOfDependence}
                  right={
                    <TouchableOpacity
                      onPress={() =>
                        updateDependenceValue(
                          numberOfDependence,
                          1,
                          setValue,
                          validateNumberOfDependence,
                        )
                      }>
                      <Icon.PlusCircle />
                    </TouchableOpacity>
                  }
                  left={
                    <TouchableOpacity
                      onPress={() =>
                        updateDependenceValue(
                          numberOfDependence,
                          -1,
                          setValue,
                          validateNumberOfDependence,
                        )
                      }
                      disabled={
                        !numberOfDependence || !parseInt(numberOfDependence)
                      }>
                      <Icon.MinusCircle
                        fill={
                          numberOfDependence && parseInt(numberOfDependence) > 0
                            ? colors.primary
                            : colors.palette.fwdGreyDark
                        }
                      />
                    </TouchableOpacity>
                  }
                  label={t(
                    `eRecruit.application.personalDetails.numOfDependents`,
                  )}
                  error={errors?.identity?.numberOfDependence?.message}
                  inputStyle={{ textAlign: 'center' }}
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />

                <Input
                  control={control}
                  as={
                    SearchableDropdown<(typeof educationConfig)[number], string>
                  }
                  data={educationConfig}
                  getItemLabel={item => item?.text ?? '--'}
                  getItemValue={item => item?.value}
                  name="personalInformation.education"
                  style={{ flex: 1 }}
                  label={t(`eRecruit.application.personalDetails.education`)}
                  error={errors?.personalInformation?.education?.message}
                  searchable
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />
                <Input
                  control={control}
                  as={SearchableDropdown<OptionConfig, string>}
                  data={industryList}
                  getItemLabel={item => item?.label}
                  getItemValue={item => item?.value}
                  name="personalInformation.industry"
                  style={{ flex: 1 }}
                  label={t(`eRecruit.application.personalDetails.industry`)}
                  error={errors?.personalInformation?.industry?.message}
                  searchable
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />
                <Input
                  control={control}
                  as={SearchableDropdown<OptionConfig, string>}
                  name="personalInformation.presentOccupation"
                  style={{ flex: 1 }}
                  label={t(
                    `eRecruit.application.personalDetails.presentOccupation`,
                  )}
                  disabled={isIndustrySelected == false}
                  error={
                    errors?.personalInformation?.presentOccupation?.message
                  }
                  data={filteredOccupationListByIndustry}
                  getItemLabel={item => item?.label}
                  getItemValue={item => item?.value}
                  searchable
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={false}
                />
                <Input
                  control={control}
                  as={TextField}
                  name="personalInformation.npwp"
                  style={{ flex: 1 }}
                  label={t(
                    `eRecruit.application.personalDetails.npwp.optional`,
                  )}
                  error={errors?.personalInformation?.npwp?.message}
                  hint={t('eRecruit.application.personalDetails.npwp.hint')}
                />
              </Column>
              <BottomSheetFooterSpace />
            </Box>
          </BottomSheetKeyboardAwareScrollView>
          {/* </View> */}
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
