import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import Input from 'components/Input';
import Portal from 'components/Portal/Portal';
import {
  Box,
  Column,
  DatePicker,
  H7,
  <PERSON>er,
  <PERSON>,
  TextField,
} from 'cube-ui-components';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';

import { useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { EssentialInfoFromStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { subYears } from 'date-fns';
import { calculateAgeAlternative } from 'features/eRecruit/util/calculateAgeAlternative';
import FormFooter from '../../utils/FormFooter';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';

// TBC
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { initialApplicationData } from 'features/eRecruit/ib/validations/personalDetailsSchema';
import SearchableDropdown from 'components/SearchableDropdown';
import { CountryCode } from 'types/optionList';
import PhoneField from 'components/PhoneField';
import { getCountryCodeValue } from 'features/eRecruit/id/tablet/NewApplicationForm/PersonalDetailsTab';
import { PHONE_NUMBER_TYPE_REGEX } from 'features/eRecruit/util/inputMaskHelper';
import { defaultCountryCode } from 'features/eRecruit/config';
import NewIdentitySectionIcon from 'features/eRecruit/ib/tablet/asset/NewIdentitySectionIcon';
import { EssentialInformationFieldPath } from 'features/eRecruit/util/store/id/types';
import { essentialInformationSchema } from 'features/eRecruit/id/validations/phone';

interface Props {
  onDismiss: () => void;
  value: EssentialInfoFromStore;
  onDone: (data: EssentialInfoFromStore) => void;
}

export default function EssentialInformation({
  onDismiss,
  value,
  onDone,
}: Props) {
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes } = useTheme();

  const { isLoading, genderConfig, countryCodeOptions } =
    useGetERecruitOptionListForAppForm();

  const hookForm = useForm({
    defaultValues: {
      ...initialApplicationData.identity,
      identity: {},
      contact: {
        countryCode: defaultCountryCode,
        officeNumberCountryCode: defaultCountryCode,
      },
    },
    values: {
      identity: {
        fullName: value.identity?.fullName ?? '',
        gender: value.identity?.gender ?? '',
        dateOfBirth: value.identity.dateOfBirth,
      },
      contact: {
        email: value.contact?.email ?? '',
        countryCode: defaultCountryCode,
        phoneNumber: value.contact?.phoneNumber ?? '',
        officeNumberCountryCode: defaultCountryCode,
        officePhoneNumber: value.contact?.officePhoneNumber ?? '',
      },
    },
    resolver: yupResolver(essentialInformationSchema),
    mode: 'onBlur',
  });

  const {
    control,
    watch,
    trigger,
    formState: { errors },
    getValues,
    setValue,
  } = hookForm;

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints();

  const submit = useCallback(async () => {
    const isValid = await trigger();
    if (isValid) {
      const currentValues = getValues();
      onDone({
        done: true,
        identity: {
          ...currentValues.identity,
        },
        contact: {
          ...currentValues.contact,
        },
      });

      bottomSheetProps.bottomSheetRef.current?.close();
    } else {
      console.log('Validation failed', errors);
    }
  }, [bottomSheetProps.bottomSheetRef, errors, getValues, onDone, trigger]);

  const mandatoryFields = [
    'identity.fullName',
    'identity.gender',
    'identity.dateOfBirth',
    'contact.phoneNumber',
    'contact.email',
  ] as const satisfies Array<EssentialInformationFieldPath>;

  const isAllMandatoryFieldsFilled = watch(mandatoryFields).every(
    (item: unknown) => Boolean(item),
  );

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          primaryDisabled={!isAllMandatoryFieldsFilled}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel="Done"
        />
      );
    },
    [isAllMandatoryFieldsFilled, submit],
  );

  const maxDate = subYears(new Date(), 18);
  const minDate = subYears(new Date(), 60);
  const ageValue = getValues('identity.dateOfBirth');
  const age = ageValue ? calculateAgeAlternative(ageValue) : undefined;
  const dob = watch('identity.dateOfBirth');

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          index={1}
          onDismiss={onDismiss}
          snapPoints={snapPoints}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <Box px={space[4]}>
            <Row alignItems="center" gap={space[1]}>
              <NewIdentitySectionIcon width={sizes[10]} height={sizes[10]} />
              <H7 color={colors.primary} fontWeight="bold">
                {t('eRecruit.application.personalDetails.essentialInformation')}
              </H7>
            </Row>
          </Box>
          <BottomSheetKeyboardAwareScrollView
            bottomOffset={space[10]}
            style={{
              paddingHorizontal: space[4],
              flex: 1,
            }}>
            <Box paddingY={space[4]}>
              <Column gap={space[6]}>
                <Input
                  control={control}
                  as={TextField}
                  name="identity.fullName"
                  style={{ flex: 1 }}
                  label={t(`eRecruit.application.personalDetails.fullName`)}
                  error={errors?.identity?.fullName?.message}
                />

                <Input
                  control={control}
                  as={Picker}
                  name="identity.gender"
                  type="text"
                  label={t(`eRecruit.application.personalDetails.gender`)}
                  style={{ flex: 1 }}
                  items={genderConfig}
                  error={errors?.identity?.gender?.message}
                />

                <Row style={{ flex: 1, gap: space[3] }}>
                  <Input
                    control={control}
                    as={DatePicker}
                    name="identity.dateOfBirth"
                    style={{
                      flex: 8,
                    }}
                    label={t(
                      `eRecruit.application.personalDetails.dateOfBirth`,
                    )}
                    hint="DD/MM/YYYY"
                    formatDate={() => (dob ? dateFormatUtil(dob) : '')}
                    minDate={minDate}
                    maxDate={maxDate}
                    value={dob ?? maxDate}
                    onChange={() =>
                      setTimeout(() => trigger('identity.dateOfBirth'), 100)
                    }
                  />
                  <TextField
                    disabled={true}
                    label={t('eRecruit.application.personalDetails.age')}
                    value={age}
                    style={{ flex: 2 }}
                  />
                </Row>
                <Row style={{ flex: 1, gap: space[3] }}>
                  <Input
                    control={control}
                    as={SearchableDropdown<CountryCode, string>}
                    data={countryCodeOptions}
                    modalTitle={'Country code'}
                    name="contact.countryCode"
                    style={{ flex: 108 }}
                    label={t(`eRecruit.application.personalDetails.code`)}
                    getItemValue={item => item?.value}
                    getItemLabel={item => item?.label}
                    keyExtractor={item => item?.value + item?.label}
                    getDisplayedLabel={item => getCountryCodeValue(item)}
                    error={errors?.contact?.countryCode?.message}
                    disabled
                  />

                  <Input
                    autoCapitalize="none"
                    control={control}
                    as={PhoneField}
                    name="contact.phoneNumber"
                    label={t(
                      `eRecruit.application.personalDetails.mobileNumber`,
                    )}
                    style={{ flex: 223 }}
                    onChangeText={value => {
                      setValue(
                        'contact.phoneNumber',
                        value.replace(PHONE_NUMBER_TYPE_REGEX, ''),
                      );
                    }}
                    keyboardType="numeric"
                    error={errors?.contact?.phoneNumber?.message}
                    size="large"
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />
                </Row>
                <Input
                  autoCapitalize="none"
                  control={control}
                  as={TextField}
                  name="contact.email"
                  style={{ flex: 1 }}
                  label={t(`eRecruit.application.personalDetails.email`)}
                  error={errors?.contact?.email?.message}
                />
                <Row style={{ flex: 1, gap: space[3] }}>
                  <Input
                    control={control}
                    as={SearchableDropdown<CountryCode, string>}
                    data={countryCodeOptions}
                    modalTitle={'Country code'}
                    name="contact.officeNumberCountryCode"
                    style={{ flex: 108 }}
                    label={t(`eRecruit.application.personalDetails.code`)}
                    getItemValue={item => item.value}
                    getItemLabel={item => item.label}
                    keyExtractor={item => item.value + item.label}
                    getDisplayedLabel={item => getCountryCodeValue(item)}
                    error={errors?.contact?.officeNumberCountryCode?.message}
                    disabled
                  />
                  <Input
                    control={control}
                    as={PhoneField}
                    name="contact.officePhoneNumber"
                    label={t(
                      `eRecruit.application.personalDetails.officePhoneOptional`,
                    )}
                    style={{ flex: 223 }}
                    onChangeText={value => {
                      setValue(
                        'contact.officePhoneNumber',
                        value.replace(PHONE_NUMBER_TYPE_REGEX, ''),
                      );
                    }}
                    keyboardType="numeric"
                    error={errors?.contact?.officePhoneNumber?.message}
                    size="large"
                  />
                </Row>
              </Column>
            </Box>
            <BottomSheetFooterSpace />
          </BottomSheetKeyboardAwareScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
