import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import Input from 'components/Input';
import Portal from 'components/Portal/Portal';
import {
  Box,
  Column,
  H6,
  Row,
  TextField,
  LargeLabel,
} from 'cube-ui-components';
import {} from 'cube-ui-components';
import RecruitYesNoInput from 'features/eRecruit/components/RecruitYesNoInput';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';

import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ComplianceAndReputationFromStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FormFooter from '../../utils/FormFooter';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';

// TBC
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { ComplianceAndReputationPath } from 'features/eRecruit/util/store/id/types';
import { complianceAndReputationSchema } from 'features/eRecruit/id/validations/phone';
import { SharedValue } from 'react-native-reanimated';
import { IDNRegulatorysQuestionsOnly } from 'types/eRecruit';
import { renderLabelByLanguage } from 'utils/helper/translation';
import MoneyBagNewSVG from 'features/eRecruit/assets/MoneyBagNewSVG';
import FormDuoNewSVG from 'features/eRecruit/assets/FormDuoNewSVG';

interface Props {
  onDismiss: () => void;
  value: ComplianceAndReputationFromStore;
  onDone: (data: ComplianceAndReputationFromStore) => void;
}

export default function ComplianceAndReputationRecords({
  onDismiss,
  value,
  onDone,
}: Props) {
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes } = useTheme();

  const { isLoading, genderConfig, countryCodeOptions } =
    useGetERecruitOptionListForAppForm();
  const { regulatoryList } = useGetERecruitOptionListForAppForm();
  const compliAndRepuObj = regulatoryList?.find(i => i.section == 'S-2');
  const amlInfoObj = regulatoryList?.find(i => i.section == 'S-5');
  const hookForm = useForm({
    values: {
      'S-2-1': {
        checked: value?.['S-2-1']?.checked,
        detail: value?.['S-2-1']?.detail,
      },
      'S-2-2': {
        checked: value?.['S-2-2']?.checked,
        detail: value?.['S-2-2']?.detail,
      },
      'S-2-3': {
        checked: value?.['S-2-3']?.checked,
        detail: value?.['S-2-3']?.detail,
      },
    },
    resolver: yupResolver(complianceAndReputationSchema),
    mode: 'onBlur',
  });

  const {
    control,
    watch,
    trigger,
    formState: { errors },
    getValues,
    setValue,
  } = hookForm;

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const submit = useCallback(async () => {
    const isValid = await trigger();
    if (isValid) {
      const currentValues = getValues();
      onDone({
        done: true,
        ...currentValues,
      });

      bottomSheetProps.bottomSheetRef.current?.close();
    } else {
      console.log('Validation failed', errors);
    }
  }, [bottomSheetProps.bottomSheetRef, errors, getValues, onDone, trigger]);

  const mandatoryFields = [
    'S-2-1.checked',
    'S-2-2.checked',
    'S-2-3.checked',
  ] as const satisfies Array<ComplianceAndReputationPath>;

  const currentForm = getValues();

  const currentFormArray = Object.entries(currentForm) as Array<
    [
      keyof typeof currentForm,
      {
        detail?: string | null | undefined;
        checked: NonNullable<boolean | undefined>;
      },
    ]
  >;
  // assuming the details is needed
  const isCommentShownAndFilled = currentFormArray;
  const isAllMandatoryFieldsFilled = watch(mandatoryFields).every(
    (item: unknown) => item != null,
  );

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          primaryDisabled={!isAllMandatoryFieldsFilled}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel="Done"
        />
      );
    },
    [isAllMandatoryFieldsFilled, submit],
  );

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <Box onLayout={handleContentLayout}>
            <Box px={space[4]}>
              <Row alignItems="center" gap={space[1]}>
                <FormDuoNewSVG size={space[10]} />
                <H6 color={colors.primary} fontWeight="bold">
                  {renderLabelByLanguage(compliAndRepuObj?.longDesc)}
                </H6>
              </Row>
            </Box>
            <BottomSheetKeyboardAwareScrollView
              bottomOffset={space[10]}
              style={{
                paddingHorizontal: space[4],
                flex: 1,
              }}>
              <Box paddingY={space[4]}>
                <Column gap={space[6]}>
                  {compliAndRepuObj?.regulatoryList?.map((item, index) => {
                    const fieldKey = item.key as keyof Pick<
                      IDNRegulatorysQuestionsOnly,
                      'S-2-1' | 'S-2-2' | 'S-2-3'
                    >;
                    const isYes = watch(`${fieldKey}.checked`);
                    return (
                      <Box key={fieldKey} gap={space[7]}>
                        <Row>
                          <LargeLabel fontWeight="bold">
                            {`${index + 1}.  `}
                          </LargeLabel>
                          <Box gap={space[4]} flex={1}>
                            <LargeLabel fontWeight="bold">
                              {renderLabelByLanguage(item?.longDesc)}
                            </LargeLabel>
                            <Box>
                              <RecruitYesNoInput
                                control={control}
                                name={`${fieldKey}.checked`}
                                onChange={() =>
                                  setTimeout(() => {
                                    trigger(`${fieldKey}`);
                                  }, 250)
                                }
                                shouldHighlightOnUntouched={
                                  Input.defaultHighlightCheckForBoolean
                                }
                                initialHighlight={false}
                              />
                            </Box>
                          </Box>
                        </Row>
                        {isYes ? (
                          <Input
                            as={TextField}
                            control={control}
                            name={`${fieldKey}.detail`}
                            shouldHighlightOnUntouched={
                              Input.defaultHighlightCheckForBoolean
                            }
                            initialHighlight={false}
                            label={t(
                              'eRecruit.application.otherDetails.regulatory.comment.label',
                            )}
                          />
                        ) : null}
                      </Box>
                    );
                  })}
                </Column>
              </Box>
              <BottomSheetFooterSpace />
            </BottomSheetKeyboardAwareScrollView>
          </Box>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
