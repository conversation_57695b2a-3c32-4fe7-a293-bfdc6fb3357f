import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  addToast,
  Box,
  Button,
  Column,
  H6,
  H8,
  Icon,
  LargeBody,
  LoadingIndicator,
  ProgressBar,
  Row,
  Typography,
} from 'cube-ui-components';
import React, { useMemo, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import ERecruitFooter from '../utils/ERecruitFooter';
import { DocumentFileKeyType } from 'features/eRecruit/my/type';
import { SubmitErrorHandler, useForm } from 'react-hook-form';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamListMap } from 'types';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import { useSaveERecruitApplicationForm } from 'features/eRecruit/hooks/useSaveERecruitApplicationForm';
import { useGetDocumentList } from 'features/eRecruit/hooks/useGetDocumentList';
import { parsingDocumentData } from 'features/eRecruit/ib/utils/documentsFuntions';
import {
  ApplicationFormRequest,
  ApplicationFormResponds,
  DocumentListDetail,
  NewApplicationFormValues,
  SaveApplicationFormResponse,
} from 'types/eRecruit';
import { cloneDeep } from 'utils/helper/objectUtil';
import { getFileType } from 'features/eAppV2/common/utils/documentUtils';
import ImagePicker from 'components/ImagePicker';
import { formatBytes } from 'features/eRecruit/ib/utils';
import { UseMutateAsyncFunction } from '@tanstack/react-query';
import useBoundStore from 'hooks/useBoundStore';
import { usePostDocumentUpload } from 'features/eRecruit/hooks/usePostDocumentUpload';
import { useDeleteERecruitDocument } from 'features/eRecruit/hooks/useDeleteERecruitDocument';
import ZoomablePdfViewer from 'features/eRecruit/my/tablet/components/NewApplicationForm/ZoomablePdfViewer';
import { baseUrl, country } from 'utils/context';
import DeleteUploadedFileModal from './DocumentSection/DeleteUploadedFileModal';
import ZoomableImageViewer from './DocumentSection/ZoomableImageViewer';
import { useERecruitStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import { shallow } from 'zustand/shallow';
import { Image } from 'expo-image';
import {
  formSectionToFilesMap,
  handleFormDataForUpload,
  insertDocumentFiles,
} from 'features/eRecruit/util/documentHandler';
import PdfViewer from 'features/pdfViewer/components/PdfViewer';
import {
  allDocMimeTypesMap,
  getKeyByNonImgMimeTypesValue,
  imageFileTypeFromConfig,
} from 'components/ImagePicker/config';
import { ImagePickerProps } from 'components/ImagePicker/utils';

type DocumentFile =
  | Record<
      DocumentFileKeyType,
      Array<{
        fileId: number;
        fileSize: number;
        fileType: string;
      }>
    >
  | undefined;

export default function Documents() {
  const { handleSubmit } = useForm<NewApplicationFormValues>();

  const route =
    useRoute<RouteProp<RootStackParamListMap['ib'], 'ERecruitApplication'>>();

  const { registrationStagingId, setRegistrationStagingId } = useERecruitStore(
    state => ({
      registrationStagingId: state.registrationStagingId,
      setRegistrationStagingId: state.setRegistrationStagingId,
    }),
    shallow,
  );

  const routeRegistrationStagingId = route.params?.registrationStagingId ?? '';
  const registrationStagingIdParam = routeRegistrationStagingId;

  const { data: backendEndAppData, isInitialLoading } = useGetApplicationData(
    registrationStagingId
      ? `${registrationStagingId}`
      : registrationStagingIdParam,
  );

  const { t } = useTranslation('eRecruit');

  const { space, colors } = useTheme();

  const { mutateAsync: saveERApplication, isLoading: saveAppLoading } =
    useSaveERecruitApplicationForm();

  const { data: documentList } = useGetDocumentList({
    agentType: backendEndAppData?.position?.agencyType ?? '',
    registrationStagingId: registrationStagingId?.toString() ?? '',
  });

  const parsedDocumentList = useMemo(() => {
    if (!documentList) return [];

    const mandatory: typeof documentList = [];
    const optional: typeof documentList = [];

    documentList.forEach(item => {
      if (item.mandatory) {
        mandatory.push(item);
      } else {
        optional.push(item);
      }
    });

    return [...mandatory, ...optional];
  }, [documentList]);

  const onValidSubmit = () => {
    const updateData = parsingDocumentData(backendEndAppData);
    saveERApplication(updateData);
  };

  const onInvalidSubmit: SubmitErrorHandler<NewApplicationFormValues> = error =>
    console.log('🚀 ~ file: DocumentsTab ~ error:', error);

  const checkMandatoryDocument = () =>
    documentList
      ?.filter(item => item.mandatory)
      ?.every(item =>
        backendEndAppData?.documentFiles?.[item.fileKey]
          ? backendEndAppData?.documentFiles?.[item.fileKey]?.length > 0
          : false,
      ) ?? false;

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Column flex={1}>
      <ScrollView>
        <Box my={space[4]} mx={space[isNarrowScreen ? 3 : 4]}>
          <H6 fontWeight="bold">{t(`eRecruit.application.documents.doc`)}</H6>
        </Box>
        <Container>
          <H8 color={colors.placeholder}>
            {t(`eRecruit.application.documents.fileSize`)}
          </H8>
          <Column>
            {parsedDocumentList?.map((item, index) => {
              const sectionName = item.fileKey;
              const isIdnAndBlockUploadForKTP = !(
                country == 'id' && (sectionName as any) == 'agt-200001'
              );

              return (
                <React.Fragment key={`application-${item.fileKey}-${index}`}>
                  <UploadSection
                    selectByLibraryEnabled={isIdnAndBlockUploadForKTP}
                    attachmentEnabled={isIdnAndBlockUploadForKTP}
                    index={index}
                    sectionName={item.fileKey}
                    documentListDetails={item}
                    registrationStagingId={
                      registrationStagingId
                        ? registrationStagingId
                        : +registrationStagingIdParam
                    }
                    files={backendEndAppData?.documentFiles}
                    saveERApplication={saveERApplication}
                    applicationFormData={backendEndAppData}
                    saveAppLoading={saveAppLoading}
                  />
                  {parsedDocumentList.length - 1 !== index && <Separator />}
                </React.Fragment>
              );
            })}
          </Column>
        </Container>
        <Box h={space[4]} />
      </ScrollView>
      <ERecruitFooter
        primaryLoading={false}
        primaryDisabled={!checkMandatoryDocument()}
        primaryLabel={t('eRecruit.application.otherDetails.next')}
        primarySubLabel={t('eRecruit.application.documents.consent')}
        onPrimaryPress={handleSubmit(onValidSubmit, onInvalidSubmit)}
      />
    </Column>
  );
}

function UploadSection({
  index,

  sectionName,
  registrationStagingId,
  files,
  applicationFormData,
  documentListDetails,
  saveAppLoading,
  saveERApplication,
  selectByLibraryEnabled,
  attachmentEnabled,
}: {
  index: number;
  sectionName: DocumentFileKeyType;
  registrationStagingId: number | null;
  files: DocumentFile;
  saveERApplication: UseMutateAsyncFunction<
    SaveApplicationFormResponse,
    unknown,
    ApplicationFormResponds,
    unknown
  >;
  applicationFormData: ApplicationFormResponds | undefined;
  saveAppLoading: boolean;
  documentListDetails: DocumentListDetail;
} & Pick<ImagePickerProps, 'selectByLibraryEnabled' | 'attachmentEnabled'>) {
  const { t } = useTranslation('eRecruit');

  const { colors, space, sizes, borderRadius } = useTheme();
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [showPdfViewer, setShowPdfViewer] = useState(false);
  const [uploadingIndex, setUploadingIndex] = useState<number | null>(null);
  const [visibleDocumentUpload, setVisibleDocumentPicker] = useState(false);
  const [deleteUploadedFileModalVisible, setDeleteUploadedFileModalVisible] =
    useState(false);

  const [onSelectItem, setOnSelectItem] = useState({
    fileId: 0,
    fileSize: 0,
    fileType: '',
  });

  const getDocumentFilePath = 'proc/recruitment/document/file';

  const token = useBoundStore(state => state.auth.authInfo.accessToken);
  const agentId = useBoundStore(state => state.auth.agentCode);

  const imageUriHeader = {
    Accept: 'application/json',
    'Content-Type': 'application/json',
    Authorization: 'Bearer ' + token,
  };

  const { mutate, isLoading: isUploadLoading } = usePostDocumentUpload();
  const { mutateAsync: deleteDocument, isLoading: isDeleteLoading } =
    useDeleteERecruitDocument(registrationStagingId);

  const disabledButton = files
    ? files?.[sectionName]?.length >= documentListDetails.imageUploadLimit
    : false;

  const [buttonColor, setButtonColor] = useState(colors.primary);

  const docTitle = documentListDetails.enDesc;

  return (
    <View style={{ gap: space[1] }}>
      <Column gap={space[4]}>
        <Typography.LargeLabel fontWeight="medium">
          {documentListDetails.enDesc}{' '}
          {!documentListDetails.mandatory && `(optional)`}
        </Typography.LargeLabel>
        {files?.[sectionName] && files[sectionName].length > 0 && (
          <Row gap={space[6]}>
            {files?.[sectionName]?.map((item, index) => {
              const uploadedUri = `${baseUrl}/api-gateway/proc/recruitment/document/file?registrationStagingId=${registrationStagingId}&fileId=${item.fileId}`;
              const isPdf = item.fileType == 'application/pdf';
              const docTitle = documentListDetails.enDesc;
              const isImage = imageFileTypeFromConfig.includes(
                item.fileType as (typeof imageFileTypeFromConfig)[number],
              );

              return isImage == false ? (
                <Row alignItems="center">
                  <TouchableOpacity
                    disabled={!isPdf}
                    style={{
                      gap: space[1],
                      flexDirection: 'row',
                      opacity: isPdf ? 1 : 0.6,
                    }}
                    onPress={() => {
                      setShowPdfViewer(true);
                    }}>
                    <Icon.Attachment
                      width={sizes[5]}
                      height={sizes[5]}
                      fill={colors.palette.fwdDarkGreen[100]}
                    />
                    <Typography.Body>
                      {item.fileId}.
                      {getKeyByNonImgMimeTypesValue(item.fileType)}
                    </Typography.Body>
                  </TouchableOpacity>
                  <Row alignItems="center" paddingX={space[5]} gap={space[1]}>
                    <Typography.SmallBody
                      color={colors.palette.fwdGreyDarkest[100]}>
                      {formatBytes(item.fileSize)}
                    </Typography.SmallBody>
                    {showPdfViewer && (
                      <PdfViewer
                        visible={showPdfViewer}
                        pdfGenerator={() => {
                          return new Promise((resolve, reject) => {
                            resolve({
                              url: uploadedUri,
                              fileName: '',
                              headers: {
                                Authorization: `Bearer ${token}`,
                                'x-agent-id': agentId ?? '',
                              },
                            });
                          });
                        }}
                        onClose={() => setShowPdfViewer(false)}
                        title={docTitle ? `${docTitle}.pdf` : ''}
                      />
                    )}
                    <TouchableOpacity
                      onPress={() => {
                        setOnSelectItem(item);
                        setDeleteUploadedFileModalVisible(true);
                      }}>
                      {saveAppLoading && index ? (
                        <LoadingIndicator size={18} />
                      ) : (
                        <Icon.Delete
                          width={sizes[5]}
                          height={sizes[5]}
                          fill={colors.palette.fwdDarkGreen[100]}
                        />
                      )}
                    </TouchableOpacity>
                  </Row>
                </Row>
              ) : (
                <Row
                  justifyContent="space-between"
                  flex={1}
                  alignItems="center">
                  <Row gap={space[3]} alignItems="center">
                    <TouchableOpacity onPress={() => setShowImageViewer(true)}>
                      <ImageContainer>
                        <Image
                          style={{ width: sizes[12], height: sizes[12] }}
                          source={{
                            uri: `${baseUrl}/api-gateway/proc/recruitment/document/file?registrationStagingId=${registrationStagingId}&fileId=${item.fileId}`,
                            headers: imageUriHeader,
                          }}
                        />
                      </ImageContainer>
                    </TouchableOpacity>
                    <Typography.Body>
                      {formatBytes(item.fileSize)}
                    </Typography.Body>

                    {/* View image modal */}
                    {showImageViewer && (
                      <ZoomableImageViewer
                        uri={`${baseUrl}/api-gateway/${getDocumentFilePath}?registrationStagingId=${registrationStagingId}&fileId=${item.fileId}`}
                        isBase64={false}
                        onDismiss={() => setShowImageViewer(false)}
                      />
                    )}
                  </Row>
                  <TouchableOpacity
                    onPress={() => {
                      setOnSelectItem(item);
                      setDeleteUploadedFileModalVisible(true);
                    }}>
                    {saveAppLoading && index ? (
                      <LoadingIndicator size={18} />
                    ) : (
                      <Icon.Delete
                        width={sizes[5]}
                        height={sizes[5]}
                        fill={colors.palette.fwdDarkGreen[100]}
                      />
                    )}
                  </TouchableOpacity>
                </Row>
              );
            })}
            {isUploadLoading && (
              <ImageContainer>
                <ActivityIndicator size={80} />
              </ImageContainer>
            )}
          </Row>
        )}

        {saveAppLoading && uploadingIndex === index && (
          <Box gap={space[2]}>
            <LargeBody color={colors.secondaryVariant}>
              {t(`eRecruit.application.documents.uploading`)}
            </LargeBody>
            <ProgressBar
              type="basic"
              progress={99}
              containerStyle={{
                backgroundColor: colors.palette.fwdGrey[50],
                height: space[1],
              }}
            />
          </Box>
        )}

        <Button
          icon={() => (
            <Icon.Upload
              size={18}
              fill={disabledButton ? colors.palette.fwdOrange[50] : buttonColor}
            />
          )}
          loading={isUploadLoading}
          onPressIn={() => setButtonColor(colors.background)}
          onPressOut={() => setButtonColor(colors.primary)}
          text={t(`eRecruit.application.documents.upload`)}
          disabled={disabledButton || isUploadLoading}
          contentStyle={{
            paddingRight: 0,
            paddingLeft: space[2],
          }}
          size="medium"
          mini
          textStyle={{ fontSize: sizes[4] }}
          onPress={() => {
            if (isUploadLoading) {
              return;
            }
            setVisibleDocumentPicker(true);
          }}
          variant="secondary"
        />
      </Column>

      <DeleteUploadedFileModal
        visible={deleteUploadedFileModalVisible}
        onCancel={() => setDeleteUploadedFileModalVisible(false)}
        onRemove={() => {
          deleteDocument(onSelectItem.fileId, {
            onSuccess: data => {
              const res = data as { result: string };
              if (res.result === 'SUCCESS') {
                setDeleteUploadedFileModalVisible(false);
                addToast([
                  {
                    IconLeft: Icon.Tick,
                    message: 'Deleted the file',
                  },
                ]);
              }
            },
          });
        }}
      />

      <ImagePicker
        extraDocTypes={
          country == 'id'
            ? [
                ...allDocMimeTypesMap.msftOfficeWord,
                ...allDocMimeTypesMap.otherImage,
              ]
            : []
        }
        selectByLibraryEnabled={selectByLibraryEnabled}
        attachmentEnabled={attachmentEnabled ?? true}
        maxSizeInMB={2}
        title={t(`eRecruit.application.documents.uploadImageDescr`)}
        visible={visibleDocumentUpload}
        onDismiss={() => setVisibleDocumentPicker(false)}
        config={{
          compression: 0.5,
          maxHeight: sizes[180],
          maxWidth: sizes[180],
          cameraQuality: 0.7,
        }}
        onDone={async ({ file: asset }) => {
          if (!asset) {
            return;
          }
          const { uri, base64, name: documentName } = asset;
          const b64 = base64.split(',')[1] || base64;
          const sizeInMB = (b64.length * 3) / 4 / (1024 * 1024);
          console.log('local size in mb', sizeInMB);
          if (sizeInMB > 2) {
            Alert.alert(
              `File size is too large. Current file size: ${
                Math.round(sizeInMB * 10) / 10
              }MB`,
            );
            return;
          }

          const parsedForm = handleFormDataForUpload({
            asset,
            sectionName,
          });

          mutate(parsedForm, {
            onError: e => {
              console.log('this is the errror', e);
              addToast([
                {
                  IconLeft: Icon.Error,
                  message: 'Failed to upload file',
                },
              ]);
            },
            onSuccess: res => {
              setUploadingIndex(index);
              const parsedFiles = formSectionToFilesMap({
                files,
                sectionName,
                fileFromRes: {
                  fileId: res.fileId,
                  fileSize: res.fileSize,
                  fileType: res.fileType,
                },
              });

              const parsedData = insertDocumentFiles({
                applicationFormData,
                documentFiles: parsedFiles,
              });

              if (!parsedData) {
                console.log(
                  'parsedData from updateApplicationFormData is null',
                );
                return;
              }
              saveERApplication(parsedData, {
                onSuccess: () => {
                  setUploadingIndex(null);
                },
              });
            },
          });
        }}
      />
    </View>
  );
}

const ImageContainer = styled.View(({ theme }) => ({
  width: theme.sizes[12],
  height: theme.sizes[12],
  borderWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
  borderRadius: theme.borderRadius.small,
  overflow: 'hidden',
  justifyContent: 'center',
}));

const Container = styled.View(({ theme: { colors, space, borderRadius } }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    borderRadius: borderRadius.large,
    paddingVertical: space[4],
    paddingHorizontal: space[4],
    backgroundColor: colors.background,
    marginHorizontal: space[isNarrowScreen ? 3 : 4],
    overflow: 'hidden',
    gap: space[4],
  };
});

const Separator = styled(View)(({ theme }) => ({
  width: '100%',
  height: 1,
  backgroundColor: '#C4C4C4',
  marginVertical: theme.space[4],
}));

const PdfContainer = styled(Column)(({ theme }) => ({
  flex: 1,
  width: '100%',
}));

// const PdfViewer = ({
//   uri,
//   item,
//   title,
// }: {
//   uri: string;
//   item: any;
//   title: string;
// }) => {
//   const token = useBoundStore(state => state.auth.authInfo.accessToken);
//   const agentId = useBoundStore(state => state.auth.agentCode);

//   const headers = {
//     Authorization: `Bearer ${token}`,
//     'x-agent-id': agentId ?? '',
//   };

//   if (Platform.OS === 'android') {
//     return (
//       <Portal>
//         <Box height={sizes[18]} bgColor={colors.background}>
//           <Row margin={space[6]} gap={space[4]} alignItems="center">
//             <TouchableOpacity
//               onPress={() => setShowPdfViewer(false)}
//               hitSlop={ICON_HIT_SLOP}>
//               <Icon.Close fill={colors.palette.fwdDarkGreen[100]} />
//             </TouchableOpacity>
//             <Typography.H6>{title}</Typography.H6>
//           </Row>
//         </Box>
//         <PdfContainer>
//           <Pdf
//             scale={1}
//             source={{
//               uri: uri,
//               headers,
//               cache: true,
//             }}
//             fitPolicy={0}
//             style={{
//               paddingTop: space[5],
//               flex: 1,
//               backgroundColor: colors.palette.fwdGreyDarkest,
//               paddingHorizontal: space[4],
//             }}
//             trustAllCerts={false}
//             onError={error => console.log('PDF Error:', error)}
//           />
//         </PdfContainer>
//       </Portal>
//     );
//   }
//   if (Platform.OS === 'ios') {
//     return (
//       <ZoomablePdfViewer
//         uri={uri}
//         isBase64={false}
//         onDismiss={() => setShowPdfViewer(false)}
//         fileId={item.fileId}
//       />
//     );
//   }
//   return <></>;
// };
