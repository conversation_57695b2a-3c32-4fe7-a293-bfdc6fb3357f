import { MYSQualificationTypes } from 'features/eRecruit/my/type';
import { ParsingAppFormDataActionKeys } from 'types/eRecruit';
import { ApplicationFormResponds } from 'types/eRecruit';
import { format } from 'date-fns';
import { cloneDeep } from 'utils/helper/objectUtil';
import {
  ContactDetailsInfo,
  IdentityDetailsInfo,
  Qualification,
  SpouseInformation,
} from 'features/eRecruit/util/store/id/ERecruitStore';

export type PersonalDetailsData = {
  contact: ContactDetailsInfo;
  personalInformation: Qualification;
  spouseInformation: SpouseInformation;
  identity: IdentityDetailsInfo;
};

export type AlterationPersonalDetailsSection = {
  applicationId: number | null;
  stage: string;
} & PersonalDetailsData;

export function parsingPersonalDetailsData({
  input,
  recruitmentCache,
  pressAction,
}: {
  input: PersonalDetailsData;
  recruitmentCache?: ApplicationFormResponds | null;
  pressAction?: ParsingAppFormDataActionKeys;
}) {
  let stage = '';

  if (pressAction === 'save') {
    stage = 'PERSONAL_DETAILS';
  } else if (pressAction === 'next') {
    stage = 'OCCUPATION_DETAILS';
  }
  const recruitmentCacheClone = recruitmentCache
    ? cloneDeep(recruitmentCache)
    : null;
  const phones = [
    {
      type: 'MOBILE',
      countryCode: input.contact.countryCode,
      number: input.contact.phoneNumber?.replace(/[^0-9]/g, '') ?? null,
    },
  ];
  if (
    input.contact.officePhoneNumber &&
    input.contact.officeNumberCountryCode
  ) {
    phones.push({
      type: 'WORK',
      countryCode: input.contact.officeNumberCountryCode,
      number: input.contact.officePhoneNumber?.replace(/[^0-9]/g, ''),
    });
  }

  const inputSpouseInformation = {
    firstName: input.spouseInformation?.firstName || null,
    lastName: input.spouseInformation?.lastName || null,
    icNumber: input.spouseInformation?.icNumber || null,
    numberOfDependence: input.spouseInformation?.numberOfDependence
      ? parseInt(input.spouseInformation?.numberOfDependence)
      : input.spouseInformation?.numberOfDependence,
    occupation: input.spouseInformation?.occupation || null,
    oldIcNumber: input.spouseInformation?.oldIcNumber || null,
    passportNo: input.spouseInformation?.passportNo || null,
  };

  const inputIdentity = {
    title: input.identity.title,
    firstName: input.identity.firstName,
    lastName: input.identity.lastName,
    dateOfBirth: input.identity.dateOfBirth
      ? format(input.identity.dateOfBirth, 'yyyy-MM-dd')
      : null,
    ethnicity: input.identity.ethnicity,
    gender: input.identity.gender,
    maritalStatus: input.identity.maritalStatus,
    religion: input.identity.religion,
    citizen: input?.identity?.citizen,
    registration: [
      {
        expiryDate: null,
        issueDate: null,
        type: 'NRIC',
        number: input.identity.icNumber,
      },
    ],
  };

  const inputContact = {
    email: input.contact.email ?? null,
    phones,
  };

  const inputAcademic = {
    name: input.personalInformation.educationType ?? null,
    date: null,
  };

  const inputQualifications: {
    academic: {
      name: string;
      date?: string | null;
    };
    certifications:
      | {
          otherDesc?: string | null;
          yearOfPassing?: number | null;
          issueDate?: string | null;
          type: string;
        }[];
  } = {
    academic: inputAcademic,
    certifications: [],
  };

  const output = {
    identity: inputIdentity,
    contact: inputContact,
    qualifications: inputQualifications,
    spouseInformation: inputSpouseInformation,
  };

  if (input.identity.passportNo) {
    inputIdentity.registration.push({
      type: 'PASSPORT',
      number: input.identity.passportNo,
      issueDate: null,
      expiryDate: null,
    });
  }

  if (input.identity.oldIcNumber) {
    inputIdentity.registration.push({
      type: 'OLD_IC',
      number: input.identity.oldIcNumber,
      issueDate: null,
      expiryDate: null,
    });
  }

  if (input.identity.taxCode) {
    inputIdentity.registration.push({
      type: 'INCOME_TAX',
      number: input.identity.taxCode,
      issueDate: null,
      expiryDate: null,
    });
  }

  const MYSCertificateMapping = {
    TAKAFUL_TBE_FAMILY: input.personalInformation.takafulCertFamily,
    TAKAFUL_TBE_GENERAL: input.personalInformation.takafulCertGeneral,
    INSURANCE_PCE: input.personalInformation.insuranceCertPce,
    INSURANCE_GENERAL: false,
    ISLAMIC_RFP_CFP_CHFC: false,
    INSURANCE_CEILLI: input.personalInformation.insuranceCertCeilli,
    MFPC: false,
    FPAM: false,
    OTHER: input.personalInformation.otherQualifications,
  };

  const mysQualificationTypesList = Object.values(MYSQualificationTypes);
  for (let i = 0; i < mysQualificationTypesList.length; i++) {
    const type = mysQualificationTypesList[i];
    if (MYSCertificateMapping[type]) {
      switch (type) {
        case MYSQualificationTypes.MFPC:
          if (input.personalInformation.MFCPYearOfPassing) {
            inputQualifications.certifications.push({
              type,
              issueDate: null,
              yearOfPassing: parseInt(
                input.personalInformation.MFCPYearOfPassing,
              ),
              otherDesc: null,
            });
          }
          break;
        case MYSQualificationTypes.FPAM:
          if (input.personalInformation.FPAMYearOfPassing) {
            inputQualifications.certifications.push({
              type,
              issueDate: null,
              yearOfPassing: parseInt(
                input.personalInformation.FPAMYearOfPassing,
              ),
              otherDesc: null,
            });
          }
          break;
        case MYSQualificationTypes.OTHER:
          inputQualifications.certifications.push({
            type,
            issueDate: null,
            yearOfPassing: null,
            otherDesc: input.personalInformation.otherQualificationsDesc,
          });
          break;

        default:
          inputQualifications.certifications.push({
            type,
            issueDate: null,
            yearOfPassing: null,
            otherDesc: null,
          });
          break;
      }
    }
  }

  return recruitmentCacheClone
    ? {
        ...recruitmentCacheClone,
        identity: { ...recruitmentCacheClone.identity, ...inputIdentity },
        contact: { ...recruitmentCacheClone.contact, ...inputContact },
        qualifications: {
          ...recruitmentCacheClone.qualifications,
          ...inputQualifications,
          academic: {
            ...recruitmentCacheClone.qualifications.academic,
            ...inputAcademic,
          },
        },
        spouseInformation: {
          ...recruitmentCacheClone.spouseInformation,
          ...inputSpouseInformation,
        },
        stage: stage,
      }
    : { ...output, applicationId: null, stage: stage };
}
