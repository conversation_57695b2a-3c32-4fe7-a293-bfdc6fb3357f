import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import Portal from 'components/Portal/Portal';
import { Body, Box, Column, H7, Row, TextField } from 'cube-ui-components';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { BankAccountInfoFromStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { SharedValue } from 'react-native-reanimated';
import NewBankIcon from 'features/eRecruit/ib/tablet/asset/NewBankIcon';
import SearchableDropdown from 'components/SearchableDropdown';
import Input from 'components/Input';
import FormFooter from '../../utils/FormFooter';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';
import { bankInformationSchema } from 'features/eRecruit/id/validations/phone';
import { BankInformationPath } from 'features/eRecruit/util/store/id/types';

interface Props {
  onDismiss: () => void;
  value: BankAccountInfoFromStore;
  onDone: (data?: BankAccountInfoFromStore) => void;
}

export default function BankAccountInformationSection({
  onDismiss,
  value: sharedValue,
  onDone,
}: Props) {
  const { t } = useTranslation(['eRecruit', 'common']);
  const { space, colors, sizes } = useTheme();
  const hookForm = useForm({
    values: {
      bankInformation: {
        ...sharedValue.bankInformation,
      },
    },
    resolver: yupResolver(bankInformationSchema),
    mode: 'onBlur',
  });

  const {
    control,
    trigger,
    watch,
    formState: { errors },
    getValues,
    setValue,
  } = hookForm;

  const { bankOptions, allBankBranchList } =
    useGetERecruitOptionListForAppForm();

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const mandatoryFields = [
    'bankInformation.bankName',
    'bankInformation.accountNumber',
    'bankInformation.branchName',
  ] as const satisfies BankInformationPath[];

  const isAllMandatoryFieldsFilled = watch(mandatoryFields).every(item =>
    Boolean(item),
  );

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      const submit = async () => {
        const isValid = await trigger();
        if (isValid) {
          onDone({
            done: true,
            bankInformation: {
              ...getValues().bankInformation,
            },
          });
          bottomSheetProps.bottomSheetRef.current?.close();
        } else {
          console.log('Validation failed', errors);
        }
      };
      return (
        <FormFooter
          {...props}
          primaryDisabled={!isAllMandatoryFieldsFilled}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel={t('eRecruit:eRecruit.application.done')}
        />
      );
    },
    [
      bottomSheetProps.bottomSheetRef,
      errors,
      getValues,
      isAllMandatoryFieldsFilled,
      onDone,
      t,
      trigger,
    ],
  );

  // useEffect(() => {
  //   if (value) {
  //     setValue('contact.bankInformation.accountNumber', value.accountNumber);
  //     setValue('contact.bankInformation.icNumber', value.icNumber);
  //     setValue('contact.bankInformation.bankName', value.bankName);
  //   }
  // }, [value]);
  //
  const bankName = watch('bankInformation.bankName');

  const filteredBranchList = useMemo(
    () =>
      bankName
        ? allBankBranchList?.filter(item => {
            const bankIdOfTheBranch = item?.value?.split('-', 1)?.[0] ?? '';
            return bankIdOfTheBranch == bankName;
          })
        : [],
    [bankName, allBankBranchList],
  );

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <View onLayout={handleContentLayout}>
            <Box px={space[4]}>
              <Row alignItems="center" gap={space[1]}>
                <NewBankIcon width={sizes[10]} height={sizes[10]} />
                <H7 color={colors.primary} fontWeight="bold">
                  {t(
                    'eRecruit:eRecruit.application.otherDetails.bankAccountInformation',
                  )}
                </H7>
              </Row>
            </Box>
            <BottomSheetKeyboardAwareScrollView
              keyboardDismissMode="on-drag"
              style={{ paddingHorizontal: space[4] }}>
              <Box paddingY={space[4]}>
                <Column gap={space[6]}>
                  <Input
                    control={control}
                    as={
                      SearchableDropdown<
                        { value: string; label: string },
                        string
                      >
                    }
                    name={'bankInformation.bankName'}
                    label={t(
                      'eRecruit:eRecruit.application.otherDetails.bankName',
                    )}
                    data={bankOptions ?? []}
                    getItemLabel={item => item.label}
                    getItemValue={item => String(item.value)}
                    style={{ flex: 1 }}
                    searchable
                    error={
                      errors?.bankInformation?.message ??
                      errors?.bankInformation?.bankName?.message
                    }
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />
                  <Input
                    control={control}
                    as={
                      SearchableDropdown<
                        { value: string; label: string },
                        string
                      >
                    }
                    disabled={bankName ? false : true}
                    data={filteredBranchList}
                    getItemLabel={i => i.label}
                    getItemValue={i => i.value}
                    name="bankInformation.branchName"
                    label={t(
                      'eRecruit:eRecruit.application.otherDetails.branch',
                    )}
                    style={{ flex: 1 }}
                    error={
                      errors?.bankInformation?.message ??
                      errors?.bankInformation?.branchName?.message
                    }
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />
                  <Input
                    control={control}
                    as={TextField}
                    name="bankInformation.accountNumber"
                    label={t(
                      'eRecruit:eRecruit.application.otherDetails.accountNumber',
                    )}
                    style={{ flex: 1 }}
                    error={
                      errors?.bankInformation?.message ??
                      errors?.bankInformation?.accountNumber?.message
                    }
                    shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                    initialHighlight={false}
                  />
                  <Body>
                    {t(
                      'eRecruit:eRecruit.application.otherDetails.bankInfoQuestionOne',
                    )}
                  </Body>
                  <Body>
                    {t(
                      'eRecruit:eRecruit.application.otherDetails.bankInfoQuestionTwo',
                    )}
                  </Body>
                </Column>
                <BottomSheetFooterSpace />
              </Box>
            </BottomSheetKeyboardAwareScrollView>
          </View>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
