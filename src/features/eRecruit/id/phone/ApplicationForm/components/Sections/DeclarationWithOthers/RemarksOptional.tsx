import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import Input from 'components/Input';
import Portal from 'components/Portal/Portal';
import { Box, H6, Row, TextField } from 'cube-ui-components';
import {} from 'cube-ui-components';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';

import { useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { RemarkOptionFromStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FormFooter from '../../utils/FormFooter';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';

// TBC
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { remarkOptionSchema } from 'features/eRecruit/id/validations/phone';
import { SharedValue } from 'react-native-reanimated';
import { renderLabelByLanguage } from 'utils/helper/translation';
import MoneyBagNewSVG from 'features/eRecruit/assets/MoneyBagNewSVG';
import NoteBookSVG from 'features/eRecruit/assets/NoteBookSVG';

interface Props {
  onDismiss: () => void;
  value: RemarkOptionFromStore;
  onDone: (data: RemarkOptionFromStore) => void;
}

export default function RemarksOptional({ onDismiss, value, onDone }: Props) {
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes } = useTheme();

  const { isLoading, genderConfig, countryCodeOptions } =
    useGetERecruitOptionListForAppForm();
  const { regulatoryList } = useGetERecruitOptionListForAppForm();
  // const healthInfoObj = regulatoryList?.find(i => i.section == 'S-4');
  const financialInfoObj = regulatoryList?.find(i => i.section == 'S-1');
  const compliAndRepuObj = regulatoryList?.find(i => i.section == 'S-2');
  const amlInfoObj = regulatoryList?.find(i => i.section == 'S-5');
  const hookForm = useForm({
    values: {
      optionalComment: value.optionalComment,
    },
    resolver: yupResolver(remarkOptionSchema),
    mode: 'onBlur',
  });

  const {
    control,
    watch,
    trigger,
    formState: { errors },
    getValues,
    setValue,
  } = hookForm;

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const submit = useCallback(async () => {
    const isValid = await trigger();
    if (isValid) {
      const currentValues = getValues();
      onDone({
        done: Boolean(currentValues),
        ...currentValues,
      });

      bottomSheetProps.bottomSheetRef.current?.close();
    } else {
      console.log('Validation failed', errors);
    }
  }, [bottomSheetProps.bottomSheetRef, errors, getValues, onDone, trigger]);

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel="Done"
        />
      );
    },
    [submit],
  );

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <Box onLayout={handleContentLayout}>
            <Box px={space[4]}>
              <Row alignItems="center" gap={space[1]}>
                <NoteBookSVG />
                <H6 color={colors.primary} fontWeight="bold">
                  {t('application.otherDetails.remark.optional')}
                </H6>
              </Row>
            </Box>
            <BottomSheetKeyboardAwareScrollView
              bottomOffset={space[10]}
              style={{
                paddingHorizontal: space[4],
                flex: 1,
              }}>
              <Box paddingY={space[4]}>
                <Input
                  control={control}
                  as={TextField}
                  name={'optionalComment'}
                  label={t('eRecruit.application.otherDetails.comment.label')}
                  style={{ flex: 1, marginTop: space[2] }}
                  initialHighlight={false}
                />
              </Box>
              <BottomSheetFooterSpace />
            </BottomSheetKeyboardAwareScrollView>
          </Box>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
