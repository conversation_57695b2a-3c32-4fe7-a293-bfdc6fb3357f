import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import Portal from 'components/Portal/Portal';
import {
  Box,
  Column,
  H7,
  LargeLabel,
  Row,
  TextField,
} from 'cube-ui-components';
import useGetERecruitOptionListForAppForm, {
  GroupedCityItems,
} from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  EmergencyContactFromStore,
  useERecruitStore,
} from 'features/eRecruit/util/store/id/ERecruitStore';
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { SharedValue } from 'react-native-reanimated';
import Input from 'components/Input';
import SearchableDropdown from 'components/SearchableDropdown';
import { shallow } from 'zustand/shallow';
import FormFooter from '../../utils/FormFooter';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';
import AlarmNewSVG from 'features/eRecruit/assets/AlarmNewSVG';
import { CountryCode } from 'types/optionList';
import PhoneField from 'components/PhoneField';
import { getCountryCodeValue } from 'features/eRecruit/id/tablet/NewApplicationForm/PersonalDetailsTab';
import { defaultCountryCode } from 'features/eRecruit/config';
import { emergencyContactSchema } from 'features/eRecruit/id/validations/phone';
import { emergencyContactShape } from 'features/eRecruit/id/validations/personalDetailsSchema';
import { EmergencyContactFieldPath } from 'features/eRecruit/util/store/id/types';

interface Props {
  onDismiss: () => void;
  value: EmergencyContactFromStore;
  onDone: (data?: EmergencyContactFromStore) => void;
}

export default function EmergencyContact({
  onDismiss,
  value: sharedStoreValue,
  onDone,
}: Props) {
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes } = useTheme();

  const hookForm = useForm({
    defaultValues: {
      emergencyContact: {
        mobileCountryCode: defaultCountryCode,
      },
    },
    values: {
      emergencyContact: {
        fullName: sharedStoreValue?.emergencyContact?.fullName,
        residenceNumber: sharedStoreValue?.emergencyContact?.residenceNumber,
        mobileCountryCode: defaultCountryCode,

        mobileNumber: sharedStoreValue?.emergencyContact?.mobileNumber,
        address: sharedStoreValue?.emergencyContact?.address,
        state: sharedStoreValue?.emergencyContact?.state, // as province in IDN
        city: sharedStoreValue?.emergencyContact?.city,
        postCode: sharedStoreValue?.emergencyContact?.postCode,
      },
    },
    resolver: yupResolver(emergencyContactSchema),
    mode: 'onBlur',
  });

  const {
    control,
    watch,
    trigger,
    formState: { errors },
    getValues,
  } = hookForm;

  const { candidatePosition } = useERecruitStore(
    state => ({
      // data
      candidatePosition: state.candidatePosition,
    }),
    shallow,
  );

  const { countryCodeOptions, cityOptions, provinceList } =
    useGetERecruitOptionListForAppForm();

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const submit = useCallback(async () => {
    const isValid = await trigger();
    const currentFormValues = getValues();
    if (!isValid) {
      return console.log('Validation failed', errors);
    }
    onDone(
      currentFormValues && {
        done: true,
        ...currentFormValues,
      },
    );
    bottomSheetProps.bottomSheetRef.current?.close();
  }, [trigger, getValues, onDone, bottomSheetProps, errors]);

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const mandaFileds = [
    'emergencyContact.fullName',
    'emergencyContact.mobileCountryCode',
    'emergencyContact.mobileNumber',
    'emergencyContact.address',
    'emergencyContact.state',
    'emergencyContact.city',
    'emergencyContact.postCode',
  ] satisfies EmergencyContactFieldPath[];
  const isAllMandatoryFieldsFilled = mandaFileds.every((f, i) => {
    return Boolean(watch(f));
  });
  const footerDoneLabel = t('eRecruit.application.done');
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => (
      <FormFooter
        {...props}
        primaryDisabled={isAllMandatoryFieldsFilled == false}
        onPrimaryPress={submit}
        primaryLoading={false}
        primaryLabel={footerDoneLabel}
      />
    ),
    [footerDoneLabel, isAllMandatoryFieldsFilled, submit],
  );

  const isOnFinancingProgram = Boolean(
    candidatePosition?.candidatePosition?.isHaveFinancingProgram ?? false,
  );

  const emergencyContactProvince = watch('emergencyContact.state');

  const groupedCityByProvince: GroupedCityItems = useMemo(
    () =>
      cityOptions
        ? cityOptions.reduce((acc, item) => {
            const prefix = item.value.split('-')[0];
            if (!acc[prefix]) {
              acc[prefix] = [];
            }
            acc?.[prefix]?.push(item);

            return acc;
          }, {} as GroupedCityItems)
        : {},
    [cityOptions],
  );

  const provinceBasedCityList = emergencyContactProvince
    ? groupedCityByProvince[emergencyContactProvince]
    : [];

  const shouldHighlightOnUntouched = useCallback(
    (value: unknown) =>
      isOnFinancingProgram && Input.defaultHighlightCheck(value),
    [isOnFinancingProgram],
  );

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <View onLayout={handleContentLayout}>
            <Box px={space[isNarrowScreen ? 3 : 4]}>
              <Row alignItems="center" gap={space[1]}>
                <AlarmNewSVG width={sizes[10]} height={sizes[10]} />
                <H7 color={colors.primary} fontWeight="bold">
                  {isOnFinancingProgram
                    ? t('eRecruit.application.otherDetails.emergencyContact')
                    : t(
                        'eRecruit.application.otherDetails.emergencyContact.optional',
                      )}
                </H7>
              </Row>
            </Box>
            <BottomSheetKeyboardAwareScrollView
              keyboardDismissMode="on-drag"
              style={{ paddingHorizontal: space[isNarrowScreen ? 3 : 4] }}>
              <Box paddingBottom={space[4]}>
                <LargeLabel color="#333333" fontWeight="medium"></LargeLabel>
                <Column gap={space[6]}>
                  <Input
                    control={control}
                    as={TextField}
                    name="emergencyContact.fullName"
                    style={{ flex: 1 }}
                    label={t(`eRecruit.application.personalDetails.fullName`)}
                    error={errors?.emergencyContact?.fullName?.message}
                    shouldHighlightOnUntouched={shouldHighlightOnUntouched}
                    initialHighlight={false}
                  />
                  <Input
                    control={control}
                    as={TextField}
                    name="emergencyContact.residenceNumber"
                    style={{ flex: 1 }}
                    label={t(
                      `eRecruit.application.otherDetails.residenceNumber.optional`,
                    )}
                  />
                  <Row style={{ flex: 1, gap: space[3] }}>
                    <Input
                      control={control}
                      as={SearchableDropdown<CountryCode, string>}
                      data={countryCodeOptions}
                      modalTitle={'Country code'}
                      name="emergencyContact.mobileCountryCode"
                      style={{ flex: 0.3 }}
                      label={t(`eRecruit.application.personalDetails.code`)}
                      getItemValue={item => item?.value}
                      getItemLabel={item => item?.label}
                      keyExtractor={item => item?.value + item?.label}
                      getDisplayedLabel={item => getCountryCodeValue(item)}
                      error={
                        errors?.emergencyContact?.mobileCountryCode?.message
                      }
                      disabled
                    />

                    <Input
                      control={control}
                      as={PhoneField}
                      name="emergencyContact.mobileNumber"
                      label={t(
                        `eRecruit.application.personalDetails.mobileNumber`,
                      )}
                      style={{ flex: 0.7 }}
                      keyboardType="numeric"
                      error={errors?.emergencyContact?.mobileNumber?.message}
                      size="large"
                      shouldHighlightOnUntouched={shouldHighlightOnUntouched}
                      initialHighlight={false}
                    />
                  </Row>
                  <Input
                    control={control}
                    as={TextField}
                    name="emergencyContact.address"
                    label={t('eRecruit.application.otherDetails.addressLine')}
                    style={{ flex: 2 }}
                    error={errors?.emergencyContact?.address?.message}
                    shouldHighlightOnUntouched={shouldHighlightOnUntouched}
                    initialHighlight={false}
                  />
                  <Input
                    control={control}
                    as={
                      SearchableDropdown<
                        { value: string; label: string },
                        string
                      >
                    }
                    name={'emergencyContact.state'}
                    label={t(
                      'eRecruit.application.otherDetails.residentialAddress.province',
                    )}
                    data={provinceList ?? []}
                    getItemLabel={item => item.label}
                    getItemValue={item => String(item.value)}
                    style={{ flex: 1 }}
                    searchable
                    shouldHighlightOnUntouched={shouldHighlightOnUntouched}
                    initialHighlight={false}
                  />
                  <Input
                    control={control}
                    as={
                      SearchableDropdown<
                        { value: string; label: string },
                        string
                      >
                    }
                    name={'emergencyContact.city'}
                    label={t(
                      'eRecruit.application.otherDetails.residentialAddress.city',
                    )}
                    data={provinceBasedCityList ?? []}
                    getItemLabel={item => item.label}
                    getItemValue={item => String(item.value)}
                    style={{ flex: 1 }}
                    searchable
                    shouldHighlightOnUntouched={shouldHighlightOnUntouched}
                    initialHighlight={false}
                    disabled={Boolean(emergencyContactProvince) === false}
                  />

                  <Input
                    control={control}
                    as={TextField}
                    name={'emergencyContact.postCode'}
                    label={t(
                      'eRecruit.application.otherDetails.residentialAddress.postCode',
                    )}
                    style={{ flex: 1 }}
                    shouldHighlightOnUntouched={shouldHighlightOnUntouched}
                    initialHighlight={false}
                  />
                </Column>
                <BottomSheetFooterSpace />
              </Box>
            </BottomSheetKeyboardAwareScrollView>
          </View>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
