import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { Box, Column, TextField } from 'cube-ui-components';
import { useEffect } from 'react';
import {
  FieldArrayMethodProps,
  useFieldArray,
  UseFormReturn,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { declarationCOIDefaultValue } from 'features/eRecruit/ib/validations/otherDetailsSchema';
import React from 'react';
import { COISectionSeparator } from './COISectionSeparator';
import { NewApplicationFormValues } from 'types/eRecruit';
import { CoiHookFormForAdditionalForms } from './types';

export default function OtherInterestForm({
  hookForm,
}: {
  hookForm: CoiHookFormForAdditionalForms;
}) {
  const { space, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');
  const appendListFocusOption: FieldArrayMethodProps = { shouldFocus: false };

  const {
    watch,
    control,
    formState: { errors },
  } = hookForm;
  const { fields, append, remove } = useFieldArray({
    name: 'conflictOfInterest.otherInterests',
    control,
  });
  const isOtherIntereststYes = watch('conflictOfInterest.otherInterest');

  useEffect(() => {
    if (isOtherIntereststYes) {
      fields.length === 0 &&
        append(
          {
            ...declarationCOIDefaultValue.conflictOfInterest.otherInterests,
          },
          appendListFocusOption,
        );
    } else {
      fields.length > 0 && remove();
      // remove all item in the list when no index is provided;
    }
  }, [fields, isOtherIntereststYes, append, remove]);

  if (isOtherIntereststYes == false) {
    return null;
  }

  return (
    <Box minHeight={sizes[10]} gap={space[4]}>
      {fields.map((field, idx) => (
        <React.Fragment key={field.id}>
          <Column gap={space[4]}>
            <Input
              multiline
              autoExpand
              control={control}
              as={TextField}
              name={`conflictOfInterest.otherInterests.${idx}.details`}
              label={t('application.COI.otherDetails')}
              style={{ flex: 1 }}
              error={
                errors?.conflictOfInterest?.otherInterests?.[idx]?.details
                  ?.message
              }
            />
          </Column>
          {fields?.length > 1 && idx != fields?.length - 1 && (
            <COISectionSeparator />
          )}
        </React.Fragment>
      ))}
    </Box>
  );
}
