import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  Column,
  DatePicker,
  H7,
  Icon,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import { useEffect, useState } from 'react';
import {
  FieldArrayMethodProps,
  useFieldArray,
  UseFormReturn,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { declarationCOIDefaultValue } from 'features/eRecruit/ib/validations/otherDetailsSchema';
import { TouchableOpacity } from 'react-native';
import React from 'react';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { COISectionSeparator } from './COISectionSeparator';
import { AddButton } from '../../../utils/AddButton';
import { isAfter } from 'date-fns';
import { NewApplicationFormValues } from 'types/eRecruit';
import { CoiHookFormForAdditionalForms } from './types';

export default function OwnershipInterestForm({
  hookForm,
  maxRecords = 5,
}: {
  hookForm: CoiHookFormForAdditionalForms;
  maxRecords?: number;
}) {
  const { space, colors } = useTheme();
  const { t } = useTranslation('eRecruit');

  const {
    watch,
    control,
    setValue,
    formState: { errors },
  } = hookForm;
  const { fields, append, remove } = useFieldArray({
    name: 'conflictOfInterest.ownershipInterests',
    control,
  });

  const appendListFocusOption: FieldArrayMethodProps = { shouldFocus: false };

  const isOwnershipInterestYes = watch('conflictOfInterest.ownershipInterest');
  useEffect(() => {
    if (isOwnershipInterestYes) {
      fields.length === 0 &&
        append(
          {
            ...declarationCOIDefaultValue.conflictOfInterest.ownershipInterests,
          },
          appendListFocusOption,
        );
    } else {
      fields.length > 0 && remove();
      // remove all item in the list when no index is provided;
    }
  }, [fields, isOwnershipInterestYes, append, remove]);

  if (isOwnershipInterestYes == false) {
    return null;
  }

  return (
    <Box minHeight={40} gap={space[4]}>
      {fields?.map((field, idx) => {
        const dateApplied = watch(
          `conflictOfInterest.ownershipInterests.${idx}.dateAcquired`,
        );
        if (dateApplied && isAfter(new Date(dateApplied), new Date())) {
          setValue(
            `conflictOfInterest.ownershipInterests.${idx}.dateAcquired`,
            `${new Date()}`,
          );
        }
        return (
          <React.Fragment key={field.id}>
            <Column gap={space[4]}>
              <Row justifyContent="space-between">
                <Row alignItems="center" gap={space[2]}>
                  <H7 fontWeight="bold" key={idx}>
                    {t('application.COI.record', {
                      number: (idx ?? 0) + 1,
                    })}
                  </H7>
                </Row>
                {fields?.length === 1 && idx === 0 ? (
                  <TouchableOpacity disabled>
                    <Icon.Delete fill={colors.palette.fwdGreyDark} />
                  </TouchableOpacity>
                ) : (
                  <TouchableOpacity
                    onPress={() => {
                      if (fields?.length === 1) {
                        console.log('last item should not be deleted');
                        return;
                      }

                      remove(idx);
                    }}>
                    <Icon.Delete fill={colors.palette.fwdDarkGreen[100]} />
                  </TouchableOpacity>
                )}
              </Row>

              <Column gap={space[6]}>
                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.ownershipInterests.${idx}.nameOfBusiness`}
                  label={t('application.COI.nameOfBusinessEnterpriseOrEntity')}
                  style={{ flex: 1 }}
                />

                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.ownershipInterests.${idx}.natureOfBusiness`}
                  label={t('application.COI.natureOfBusiness')}
                  style={{ flex: 1 }}
                />
                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.ownershipInterests.${idx}.nameOfOwner`}
                  label={t('application.COI.nameOfOwner')}
                  style={{ flex: 1 }}
                />

                {/*<Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.ownershipInterests.${idx}.relationship`}
                  label={t('application.COI.relationship')}
                  style={{ flex: 1 }}
                />*/}
                <DebouncedPercentageInput idx={idx} hookForm={hookForm} />
                <Input
                  control={control}
                  as={DatePicker}
                  name={`conflictOfInterest.ownershipInterests.${idx}.dateAcquired`}
                  maxDate={new Date()}
                  style={{
                    flex: 1,
                  }}
                  label={t('application.COI.dateAcquired')}
                  hint={t('eRecruit.application.dateHint')}
                  formatDate={value => (value ? dateFormatUtil(value) : '')}
                  value={dateApplied ? new Date(dateApplied) : undefined}
                  error={
                    !dateApplied
                      ? errors.conflictOfInterest?.ownershipInterests?.[idx]
                          ?.dateAcquired?.message
                      : ''
                  }
                />
              </Column>
            </Column>
            {fields?.length > 1 && idx != fields?.length - 1 && (
              <COISectionSeparator />
            )}
          </React.Fragment>
        );
      })}
      <AddButton
        customText={t(`eRecruit.application.occupationDetails.add`)}
        isDisabled={fields.length >= maxRecords}
        isHidden={fields.length >= maxRecords || maxRecords == 1}
        onPress={() => {
          fields.length < maxRecords &&
            append(
              {
                ...declarationCOIDefaultValue.conflictOfInterest
                  .ownershipInterests,
              },
              appendListFocusOption,
            );
        }}
      />
    </Box>
  );
}

function DebouncedPercentageInput({
  idx,
  hookForm,
}: {
  idx: number;
  hookForm: CoiHookFormForAdditionalForms;
}) {
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');

  const [localValue, setLocalValue] = useState('');
  const {
    control,
    watch,
    setValue,
    formState: { errors },
    trigger,
  } = hookForm;

  const percentageValidator = (value: string) => {
    const numberOnlyStr = value.replace(/[^0-9.]/g, '');
    const demicalPlacesShown = 2;
    const parsedNumber = Number.isNaN(parseFloat(numberOnlyStr))
      ? ''
      : parseFloat(numberOnlyStr);

    const numberInput =
      parsedNumber == ''
        ? ''
        : parsedNumber >= 5 && parsedNumber <= 100
        ? Number.isInteger(parsedNumber)
          ? parsedNumber.toString()
          : parsedNumber.toFixed(demicalPlacesShown).slice(-1) === '0'
          ? parsedNumber.toFixed(demicalPlacesShown).slice(0, -1)
          : parsedNumber.toFixed(demicalPlacesShown)
        : '';

    return numberInput;
  };

  const formValue = watch(
    `conflictOfInterest.ownershipInterests.${idx}.percentageOfOwnership`,
  );

  useEffect(() => {
    formValue && setLocalValue(formValue?.toString());
  }, [formValue]);

  useEffect(() => {
    const text = percentageValidator(localValue);

    const timer = setTimeout(() => {
      const key =
        `conflictOfInterest.ownershipInterests.${idx}.percentageOfOwnership` as const;
      setValue(key, text as any);
    }, 500);
    return () => clearTimeout(timer);
  }, [localValue]);

  return (
    <Input
      control={control}
      keyboardType="numeric"
      as={TextField}
      name={`conflictOfInterest.ownershipInterests.${idx}.percentageOfOwnership`}
      value={formValue}
      label={t('application.COI.percentageOfOwnership')}
      onChangeText={value => setLocalValue(value)}
      style={{ flex: 1 }}
      right={
        <Typography.LargeBody color={colors.palette.fwdDarkGreen[50]}>
          %
        </Typography.LargeBody>
      }
    />
  );
}
