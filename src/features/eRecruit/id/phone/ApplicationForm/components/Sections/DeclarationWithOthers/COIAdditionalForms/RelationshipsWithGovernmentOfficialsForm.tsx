import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { Box, Column, H7, Icon, Row, TextField } from 'cube-ui-components';
import { useEffect, useState } from 'react';
import { FieldArrayMethodProps, useFieldArray } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { declarationCOIDefaultValue } from 'features/eRecruit/ib/validations/otherDetailsSchema';
import { TouchableOpacity } from 'react-native';
import React from 'react';
import { COISectionSeparator } from './COISectionSeparator';
import { AddButton } from 'features/eRecruit/id/phone/ApplicationForm/components/utils/AddButton';
import { CoiHookFormForAdditionalForms } from './types';

export default function RelationshipsWithGovernmentOfficialsForm({
  hookForm,
  maxRecords = 5,
}: {
  hookForm: CoiHookFormForAdditionalForms;
  maxRecords?: number;
}) {
  const { space, colors } = useTheme();
  const { t } = useTranslation('eRecruit');
  const appendListFocusOption: FieldArrayMethodProps = { shouldFocus: false };

  const [showErrorField, setShowErrorField] = useState<string[]>([]);

  const {
    watch,
    control,
    formState: { errors },
  } = hookForm;
  const { fields, append, remove } = useFieldArray({
    name: 'conflictOfInterest.relationshipGovernmentOfficials',
    control,
  });

  const isRelateGovernmentOfficialtYes = watch(
    'conflictOfInterest.relationshipGovernmentOfficial',
  );

  useEffect(() => {
    if (isRelateGovernmentOfficialtYes) {
      fields.length === 0 &&
        append(
          {
            ...declarationCOIDefaultValue.conflictOfInterest
              .relationshipGovernmentOfficials,
          },
          appendListFocusOption,
        );
    } else {
      fields.length > 0 && remove();
      // remove all item in the list when no index is provided;
    }
  }, [fields, isRelateGovernmentOfficialtYes, append, remove]);

  if (isRelateGovernmentOfficialtYes == false) {
    return null;
  }

  return (
    <Box minHeight={40} gap={space[4]}>
      {fields.map((field, idx) => (
        <React.Fragment key={field.id}>
          <Column gap={space[4]}>
            <Row justifyContent="space-between">
              <Row alignItems="center" gap={space[2]}>
                <H7 fontWeight="bold" key={idx}>
                  {t('application.COI.record', {
                    number: (idx ?? 0) + 1,
                  })}
                </H7>
              </Row>
              {fields?.length === 1 && idx === 0 ? (
                <TouchableOpacity disabled>
                  <Icon.Delete fill={colors.palette.fwdGreyDark} />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    if (fields?.length === 1) {
                      console.log('last item should not be deleted');
                      return;
                    }
                    remove(idx);
                  }}>
                  <Icon.Delete fill={colors.palette.fwdDarkGreen[100]} />
                </TouchableOpacity>
              )}
            </Row>

            <Column gap={space[6]}>
              <Input
                control={control}
                as={TextField}
                name={`conflictOfInterest.relationshipGovernmentOfficials.${idx}.nameOfGovernment`}
                label={t('application.COI.nameOfGovernment')}
                style={{ flex: 1 }}
              />

              <Input
                control={control}
                as={TextField}
                name={`conflictOfInterest.relationshipGovernmentOfficials.${idx}.positionDepartment`}
                label={t('application.COI.positionDepartment')}
                style={{ flex: 1 }}
              />
              <Input
                control={control}
                as={TextField}
                name={`conflictOfInterest.relationshipGovernmentOfficials.${idx}.relationship`}
                label={t('application.COI.relationshipWithGovOfficials')}
                style={{ flex: 1 }}
              />
            </Column>
          </Column>
          {fields?.length > 1 && idx != fields?.length - 1 && (
            <COISectionSeparator />
          )}
        </React.Fragment>
      ))}
      <AddButton
        customText={t(`eRecruit.application.occupationDetails.add`)}
        isDisabled={fields.length >= maxRecords}
        isHidden={fields.length >= maxRecords || maxRecords == 1}
        onPress={() => {
          fields.length < maxRecords &&
            append(
              {
                ...declarationCOIDefaultValue.conflictOfInterest
                  .relationshipGovernmentOfficials,
              },
              appendListFocusOption,
            );
        }}
      />
    </Box>
  );
}
