import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import Portal from 'components/Portal/Portal';
import { addToast, Box, Column, H7, Row } from 'cube-ui-components';
import { H6, LargeBody, PictogramIcon } from 'cube-ui-components';
import styled from '@emotion/native';

import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { Fragment, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import * as yup from 'yup';
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { PersonalDetailsReview } from './ReviewInformationSection/PersonalDetailsReview';
import { OccupationDetailsReview } from './ReviewInformationSection/OccupationDetailsReview';
import { OtherDetailsReview } from './ReviewInformationSection/OtherDetailsReview';
import FormFooter from '../utils/FormFooter';
import {
  ApplicationFormResponds,
  NewApplicationFormValues,
  ParsingAppFormDataActionKeys,
  SavedActionProps,
} from 'types/eRecruit';
import { useSaveERecruitApplicationForm } from 'features/eRecruit/hooks/useSaveERecruitApplicationForm';
import { useERecruitStore } from 'features/eRecruit/util/store/id/ERecruitStore';
import { shallow } from 'zustand/shallow';
import { parsingReviewDetailsData } from './ReviewInformationSection/ReviewDetailsFuntions';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamListMap } from 'types';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import { convertSavedApplicationForHookForm as convertToOtherDetailsFormValues } from 'features/eRecruit/id/utils/otherDetailsFuntions';

import NewAddrIcon from 'features/eRecruit/ib/tablet/asset/NewAddrIcon';
import NewBankIcon from 'features/eRecruit/ib/tablet/asset/NewBankIcon';
import NewContactDetailsIcon from 'features/eRecruit/ib/tablet/asset/NewContactDetailsIcon';
import NewDocForFormIcon from 'features/eRecruit/ib/tablet/asset/NewDocForFormIcon';
import NewIdentitySectionIcon from 'features/eRecruit/ib/tablet/asset/NewIdentitySectionIcon';
import NewInsuranceExpIcon from 'features/eRecruit/ib/tablet/asset/NewInsuranceExpIcon';
import { questionsMap as IbQuestionsMap } from 'features/eRecruit/ib/tablet/config';
import { IdnQuestionsMap } from 'features/eRecruit/id/config';
import React, { useState } from 'react';

import { useGetERecruitConfig } from 'features/eRecruit/hooks/useGetERecruitConfig';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import { backendToFormValues } from 'features/eRecruit/id/utils/personalDetailsFunctions';
import { calculateAgeAlternative } from 'features/eRecruit/util/calculateAgeAlternative';
import useBoundStore from 'hooks/useBoundStore';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { renderLabelByLanguage } from 'utils/helper/translation';
import AlarmNewSVG from 'features/eRecruit/assets/AlarmNewSVG';
import MoneyBagNewSVG from 'features/eRecruit/assets/MoneyBagNewSVG';
import FormDuoNewSVG from 'features/eRecruit/assets/FormDuoNewSVG';
import ReviewConfirmationModal from 'features/eRecruit/id/tablet/NewApplicationForm/ReviewInformationTab/ReviewConfirmationModal';
import {
  getTextFromConfig,
  getLabelFromConfig,
  formatPhoneNumberWithCountryCode,
  formatAddress,
  formatEmergencyContactAddress,
  getYesOrNo,
} from 'features/eRecruit/id/tablet/NewApplicationForm/ReviewInformationTab/utils';
import TabFooter from 'features/eRecruit/id/tablet/NewApplicationForm/ReviewInformationTab/TabFooter';
import NoteBookSVG from 'features/eRecruit/assets/NoteBookSVG';
import { MedicalReport2 } from 'cube-ui-components/dist/cjs/icons/pictograms/cube';
import WebPage2SVG from 'features/eRecruit/assets/WebPage2SVG';
import { RegulatorySection } from 'features/eRecruit/id/tablet/NewApplicationForm/ReviewInformationTab/RegulatorySection';
import LoadingIndicator from 'components/LoadingIndicator';

export default function ReviewInformation({
  showReviewSheet,
  setShowReviewSheet,
}: {
  showReviewSheet: boolean;
  setShowReviewSheet: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const { t } = useTranslation('eRecruit');
  const { space, colors } = useTheme();

  const { registrationStagingId, setRegistrationStagingId } = useERecruitStore(
    state => ({
      registrationStagingId: state.registrationStagingId,
      setRegistrationStagingId: state.setRegistrationStagingId,
    }),
    shallow,
  );

  const route =
    useRoute<RouteProp<RootStackParamListMap['ib'], 'ERecruitApplication'>>();
  const routeRegistrationStagingId = route.params?.registrationStagingId ?? '';
  const registrationStagingIdParam = routeRegistrationStagingId;

  const { data: recruitmentCache, isInitialLoading } = useGetApplicationData(
    registrationStagingId
      ? `${registrationStagingId}`
      : registrationStagingIdParam,
  );

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints();

  const handleOnDismiss = () => {
    setShowReviewSheet(false);
  };

  const triggerDismiss = () => {
    bottomSheetProps.bottomSheetRef.current?.dismiss();
  };

  const [isReviewModalOn, setIsReviewModalOn] = useState(false);
  const { data: eRecruitOnlyOptionList, isLoading: isERCLoading } =
    useGetERecruitConfig();
  const agentCode = useBoundStore(state => state.auth.agentCode);

  const cityList = eRecruitOnlyOptionList?.cityList || [];
  const perosonalDetailsMap = recruitmentCache
    ? backendToFormValues(recruitmentCache, cityList, agentCode)
    : undefined;
  const proceedToDocument = () => {
    // * pressAction default to be 'save', so stage is saved with current stage
    console.log('~~~saveAction~~');

    if (!recruitmentCache) {
      console.log('recruitmentCache is undefined');
      return;
    }

    mutateAsync(
      {
        ...recruitmentCache,
        contact: {
          ...recruitmentCache.contact,
          emergencyContact:
            recruitmentCache?.contact?.emergencyContact &&
            Object.values(
              recruitmentCache?.contact?.emergencyContact ?? {},
            ).every(v => v == null)
              ? null
              : ({
                  ...recruitmentCache?.contact?.emergencyContact,
                } as ApplicationFormResponds['contact']['emergencyContact']),
        },
        stage: 'DOCUMENT',
      },
      {
        onSuccess: props => {
          // onSuccess&&onSuccess();
          // navigation.navigate('newApplicationDocuments', {
          //   registrationStagingId,
          // });
        },
      },
    );
  };

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const { mutateAsync, isLoading } = useSaveERecruitApplicationForm();

  const saveAction = (props: SavedActionProps<NewApplicationFormValues>) => {
    const { data, onSuccess, onError } = props;
    const updatedData = parsingReviewDetailsData({
      recruitmentCache: data as unknown as ApplicationFormResponds,
    });

    mutateAsync(updatedData as unknown as ApplicationFormResponds, {
      onSuccess: onSuccess,
      onError: onError as (err: unknown) => void,
    });
  };

  const onValidSave = (
    data: NewApplicationFormValues,
    pressAction?: ParsingAppFormDataActionKeys,
  ) => {
    saveAction({
      data,
      pressAction,
      onSuccess: async data => {
        if (data?.registrationStagingId) {
          setRegistrationStagingId(data?.registrationStagingId);
          setShowReviewSheet(false);
          return;
        }
      },
      onError: error => {
        addToast([
          {
            message: t('eRecruit.pleaseTryAgainLater'),
          },
        ]);
      },
    });
  };

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          primaryDisabled={false}
          onPrimaryPress={() => {
            // onValidSave(
            //   recruitmentCache as unknown as NewApplicationFormValues,
            // );
            setIsReviewModalOn(v => !v);
          }}
          primaryLoading={isInitialLoading || isLoading}
          primaryLabel="Confirm"
          secondaryDisabled={false}
          secondaryLabel="Back"
          onSecondaryPress={triggerDismiss}
          secondaryLoading={false}
        />
      );
    },
    [isInitialLoading, isLoading],
  );

  return (
    <Fragment>
      {showReviewSheet && (
        <Portal>
          <BottomSheetModalProvider>
            <BottomSheetModal
              index={1}
              onDismiss={handleOnDismiss}
              snapPoints={snapPoints}
              {...bottomSheetProps}
              footerComponent={renderFooter}
              style={{ padding: 0 }}>
              <Box px={space[isNarrowScreen ? 3 : 4]} paddingBottom={space[4]}>
                <Row alignItems="center" gap={space[1]}>
                  <H7
                    color={colors.palette.fwdDarkGreen[100]}
                    fontWeight="bold">
                    {t(`eRecruit.application.review.reviewInformation`)}
                  </H7>
                </Row>
              </Box>
              <BottomSheetScrollView
                keyboardDismissMode="on-drag"
                style={{
                  paddingHorizontal: space[isNarrowScreen ? 3 : 4],
                  flex: 1,
                }}
                contentContainerStyle={{
                  gap: space[4],
                }}>
                <ReviewInformationTab
                  routeRegistrationStagingId={routeRegistrationStagingId}
                />

                {/*<Box paddingBottom={space[4]}>
                  <Column gap={space[4]}>
                    <PersonalDetailsReview data={recruitmentCache} />
                    <OccupationDetailsReview data={recruitmentCache} />
                    <OtherDetailsReview data={recruitmentCache} />
                  </Column>
                </Box>*/}
                <BottomSheetFooterSpace />
              </BottomSheetScrollView>
            </BottomSheetModal>
          </BottomSheetModalProvider>
        </Portal>
      )}
      <ReviewConfirmationModal
        registrationStagingId={
          registrationStagingId ? registrationStagingId.toString() : ''
        }
        isModalOn={isReviewModalOn}
        setIsModalOn={setIsReviewModalOn}
        proceedToDocument={proceedToDocument}
        phoneNumber={
          formatPhoneNumberWithCountryCode(
            perosonalDetailsMap?.contact?.countryCode,
            perosonalDetailsMap?.contact?.phoneNumber,
          ) ?? '--'
        }
        onVerifySuccess={() => setShowReviewSheet(false)}
      />
    </Fragment>
  );
}

function ReviewInformationTab({
  routeRegistrationStagingId,
}: {
  routeRegistrationStagingId?: string;
}) {
  const { registrationStagingId, setRegistrationStagingId } = useERecruitStore(
    state => ({
      registrationStagingId: state.registrationStagingId,
      setRegistrationStagingId: state.setRegistrationStagingId,
    }),
    shallow,
  );

  const registrationStagingIdParam = routeRegistrationStagingId;

  const currentLanguage = useBoundStore(state => state.language);

  const { t } = useTranslation('eRecruit');
  const { space, colors, borderRadius } = useTheme();

  const agentCode = useBoundStore(state => state.auth.agentCode);

  const { data: recruitmentCache, isInitialLoading } = useGetApplicationData(
    registrationStagingId != null
      ? registrationStagingId.toString()
      : registrationStagingIdParam ?? '',
  );
  const { mutateAsync, isLoading } = useSaveERecruitApplicationForm();

  const { regulatoryList } = useGetERecruitOptionListForAppForm();
  const financialInfoObj = regulatoryList?.find(i => i.section == 'S-1');
  const compliAndRepuObj = regulatoryList?.find(i => i.section == 'S-2');
  const healthInfoObj = regulatoryList?.find(i => i.section == 'S-4');
  const amlInfoObj = regulatoryList?.find(i => i.section == 'S-5');

  const { data: eRecruitOnlyOptionList, isLoading: isERCLoading } =
    useGetERecruitConfig();

  const cityList = eRecruitOnlyOptionList?.cityList || [];

  const perosonalDetailsMap = recruitmentCache
    ? backendToFormValues(recruitmentCache, cityList, agentCode)
    : undefined;

  const otherDetailsMap = recruitmentCache
    ? convertToOtherDetailsFormValues({
        parsedObj: recruitmentCache,
        currentAgentCode: agentCode,
      })
    : undefined;

  const COIOwnershipInterestItem =
    otherDetailsMap?.conflictOfInterest?.ownershipInterests?.[0];
  const COIExternalEmploymentItem =
    otherDetailsMap?.conflictOfInterest?.externalEmployments?.[0];
  const COIBusinessAffiliationInterestItem =
    otherDetailsMap?.conflictOfInterest?.businessAffiliationInterests?.[0];
  const COIRelationshipGovernmentOfficialItem =
    otherDetailsMap?.conflictOfInterest?.relationshipGovernmentOfficials?.[0];
  const COIOtherInterestItem =
    otherDetailsMap?.conflictOfInterest?.otherInterests?.[0];

  const {
    isLoading: isERecruitOptionListLoading,
    genderConfig,
    maritalConfig,
    religionConfig,
    occupationList,
    industryList,
    bankOptions,
    allBankBranchList,
    salesOfficeList,
    domicileList,
    refList,
    superiorAgentCodeList,
    positionList,
    cityOptions,
    provinceList,
    educationConfig,
  } = useGetERecruitOptionListForAppForm();

  if (isInitialLoading) {
    return (
      <Box>
        <LoadingIndicator />
      </Box>
    );
  }

  return (
    <>
      {/*<ProgressStepBar type="justNavigateForReviewPage" />*/}
      {/*<ScrollView
        contentContainerStyle={{
          paddingHorizontal: space[10],
          paddingVertical: space[5],
          gap: space[4],
        }}>*/}
      {/* ----------------- First Card */}
      <ReviewInfoCard title={t('eRecruit.progressBar.coreProfileAndIdentity')}>
        {/* ----- personalDetails */}
        <Box gap={space[5]}>
          <SubHeaderContainer>
            <NewIdentitySectionIcon />
            <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
              {t(`eRecruit.application.personalDetails.identityDetails`)}
            </SubHeaderLabel>
          </SubHeaderContainer>
          <Row flexWrap="wrap" gap={space[2]}>
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.fullName`)}
              fieldValue={perosonalDetailsMap?.identity?.fullName}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.gender`)}
              fieldValue={getTextFromConfig(
                genderConfig,
                perosonalDetailsMap?.identity?.gender,
              )}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.dateOfBirth`)}
              fieldValue={
                perosonalDetailsMap?.identity?.dateOfBirth
                  ? dateFormatUtil(perosonalDetailsMap?.identity?.dateOfBirth) +
                    t('eRecruit.application.personalDetails.yearsOld', {
                      age: calculateAgeAlternative(
                        perosonalDetailsMap?.identity?.dateOfBirth,
                      ),
                    })
                  : '--'
              }
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.identity`)}
              fieldValue={perosonalDetailsMap?.identity?.identity}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.icNumber`)}
              fieldValue={perosonalDetailsMap?.identity?.idNumber}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.birthPlace`)}
              fieldValue={perosonalDetailsMap?.identity?.birthPlace}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.religion`)}
              fieldValue={getTextFromConfig(
                religionConfig,
                perosonalDetailsMap?.identity?.religion,
              )}
            />
            <FieldValuePair
              fieldName={t(
                `eRecruit.application.personalDetails.maritalStatus`,
              )}
              fieldValue={getTextFromConfig(
                maritalConfig,
                perosonalDetailsMap?.identity?.maritalStatus,
              )}
            />
            <FieldValuePair
              fieldName={t(
                `eRecruit.application.personalDetails.numOfDependents`,
              )}
              fieldValue={perosonalDetailsMap?.identity?.numberOfDependence}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.education`)}
              fieldValue={getTextFromConfig(
                educationConfig,
                perosonalDetailsMap?.personalInformation?.education,
              )}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.industry`)}
              fieldValue={getLabelFromConfig(
                industryList,
                perosonalDetailsMap?.personalInformation?.industry,
              )}
            />
            <FieldValuePair
              fieldName={t(
                `eRecruit.application.personalDetails.presentOccupation`,
              )}
              fieldValue={getLabelFromConfig(
                occupationList,
                perosonalDetailsMap?.personalInformation?.presentOccupation,
              )}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.npwp`)}
              fieldValue={perosonalDetailsMap?.personalInformation?.npwp}
              fallbackValue={'N/A'}
            />
          </Row>
        </Box>
        {/* ------ contactDetails */}
        <Box gap={space[5]}>
          <SubHeaderContainer>
            <NewContactDetailsIcon />
            <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
              {t(`eRecruit.application.personalDetails.contactDetails`)}
            </SubHeaderLabel>
          </SubHeaderContainer>
          <Row flexWrap="wrap" gap={space[2]}>
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.mobileNumber`)}
              fieldValue={formatPhoneNumberWithCountryCode(
                perosonalDetailsMap?.contact?.countryCode,
                perosonalDetailsMap?.contact?.phoneNumber,
              )}
              // (perosonalDetailsMap?.contact?.countryCode
              //   ? perosonalDetailsMap?.contact?.countryCode + ' '
              //   : '') + (perosonalDetailsMap?.contact?.phoneNumber ?? '--')
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.email`)}
              fieldValue={perosonalDetailsMap?.contact?.email}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.personalDetails.officePhone`)}
              fieldValue={formatPhoneNumberWithCountryCode(
                perosonalDetailsMap?.contact?.officeNumberCountryCode,
                perosonalDetailsMap?.contact?.officePhoneNumber,
              )}
              fallbackValue={'N/A'}
            />
          </Row>

          {/* <Row flexWrap="wrap" gap={space[2]}>
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.fullName`)}
                fieldValue={perosonalDetailsMap?.identity?.fullName}
              />
            </Row> */}
        </Box>
        {/*------- Emergency Contact */}
        <Box gap={space[5]}>
          <SubHeaderContainer>
            <AlarmNewSVG />
            <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
              {t('eRecruit.application.otherDetails.emergencyContact')}
            </SubHeaderLabel>
          </SubHeaderContainer>
          <Box gap={space[2]}>
            <Row flexWrap="wrap" gap={space[2]}>
              <FieldValuePair
                fieldName={t(`eRecruit.application.personalDetails.fullName`)}
                fieldValue={
                  perosonalDetailsMap?.emergencyContact?.fullName || '--'
                }
              />
              <FieldValuePair
                fieldName={t(
                  `eRecruit.application.otherDetails.residenceNumber`,
                )}
                fieldValue={
                  perosonalDetailsMap?.emergencyContact?.residenceNumber || '--'
                }
              />
              <FieldValuePair
                fieldName={t('eRecruit.candidate.phoneNumber')}
                fieldValue={formatPhoneNumberWithCountryCode(
                  perosonalDetailsMap?.emergencyContact?.mobileCountryCode,
                  perosonalDetailsMap?.emergencyContact?.mobileNumber,
                )}
              />
            </Row>

            <Row gap={space[5]}>
              <FieldLabel flex={1}>
                {t('eRecruit.application.otherDetails.addressLine')}
              </FieldLabel>
              <FieldValue flex={3.5}>
                {formatEmergencyContactAddress({
                  emergencyContact: perosonalDetailsMap?.emergencyContact,
                  provinceList,
                  cityOptions,
                })}
              </FieldValue>
            </Row>
          </Box>
        </Box>

        {/*------- Address information */}
        <Box gap={space[5]}>
          <SubHeaderContainer>
            <NewAddrIcon />
            {/* <PictogramIcon.HomeAddress size={space[10]} /> */}
            <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
              {t('eRecruit.application.otherDetails.addressInformation')}
            </SubHeaderLabel>
          </SubHeaderContainer>
          <Box gap={space[1]}>
            <LargeBody color={colors.palette.fwdGreyDarker} style={{ flex: 1 }}>
              {t('eRecruit.application.otherDetails.residentialAddress')}
            </LargeBody>
            <LargeBody style={{ flex: 1 }}>
              {perosonalDetailsMap?.contact?.address
                ? formatAddress(
                    perosonalDetailsMap?.contact?.address,
                    provinceList,
                    cityOptions,
                  )
                : '--'}
            </LargeBody>
            <LargeBody color={colors.palette.fwdGreyDarker} style={{ flex: 1 }}>
              {t('eRecruit.application.otherDetails.nationalIdAddress')}
            </LargeBody>
            <LargeBody style={{ flex: 1 }}>
              {perosonalDetailsMap?.contact?.businessAddress
                ? formatAddress(
                    perosonalDetailsMap?.contact?.businessAddress,
                    provinceList,
                    cityOptions,
                  )
                : '--'}
            </LargeBody>
          </Box>
        </Box>

        {/*------- Bank information */}
        <Box gap={space[5]}>
          <SubHeaderContainer>
            <NewBankIcon />
            <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
              {t('eRecruit.application.otherDetails.bankAccountInformation')}
            </SubHeaderLabel>
          </SubHeaderContainer>
          <Row flexWrap="wrap" gap={space[2]}>
            <FieldValuePair
              fieldName={t(`eRecruit.application.otherDetails.bankName`)}
              fieldValue={getLabelFromConfig(
                bankOptions,
                perosonalDetailsMap?.bankInformation?.bankName,
              )}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.otherDetails.branch`)}
              fieldValue={getLabelFromConfig(
                allBankBranchList,
                perosonalDetailsMap?.bankInformation?.branchName,
              )}
            />
            <FieldValuePair
              fieldName={t('eRecruit.application.otherDetails.accountNumber')}
              fieldValue={perosonalDetailsMap?.bankInformation?.accountNumber}
              fallbackValue={'N/A'}
            />
          </Row>
        </Box>

        {/*------- Candidate information */}
        <Box gap={space[5]}>
          <SubHeaderContainer>
            <PictogramIcon.ManWithShield />
            <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
              {t('eRecruit.application.otherDetails.candidateInformation')}
            </SubHeaderLabel>
          </SubHeaderContainer>
          <Row flexWrap="wrap" gap={space[2]}>
            <FieldValuePair
              fieldName={t(
                `eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition`,
              )}
              fieldValue={getLabelFromConfig(
                positionList,
                perosonalDetailsMap?.candidatePosition?.position,
              )}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.otherDetails.salesOffice`)}
              fieldValue={getLabelFromConfig(
                salesOfficeList,
                perosonalDetailsMap?.candidatePosition?.salesOffice,
              )}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.otherDetails.domicile`)}
              fieldValue={getLabelFromConfig(
                domicileList,
                perosonalDetailsMap?.candidatePosition?.domicile,
              )}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.otherDetails.areaManager`)}
              fieldValue={perosonalDetailsMap?.candidatePosition?.osAreaManager}
            />

            <FieldValuePair
              fieldName={t(`eRecruit.application.otherDetails.supervisor`)}
              fieldValue={getLabelFromConfig(
                superiorAgentCodeList,
                perosonalDetailsMap?.candidatePosition?.superiorAgentCode,
              )}
            />
            <FieldValuePair
              fieldName={t(`eRecruit.application.otherDetails.ref`)}
              fieldValue={getLabelFromConfig(
                refList,
                perosonalDetailsMap?.candidatePosition?.ref,
              )}
            />
            <FieldValuePair
              fieldName={t(
                `eRecruit.application.otherDetails.financingProgram`,
              )}
              fieldValue={
                perosonalDetailsMap?.candidatePosition
                  ?.isHaveFinancingProgram === true
                  ? t('eRecruit.application.personalDetails.question.yes')
                  : perosonalDetailsMap?.candidatePosition
                      ?.isHaveFinancingProgram === false
                  ? t('eRecruit.application.personalDetails.question.no')
                  : undefined
              }
            />
          </Row>
        </Box>
      </ReviewInfoCard>

      {/* ========================== Second Card */}
      <ReviewInfoCard title={t('eRecruit.progressBar.declaration')}>
        {/* ---- insuranceExperience */}
        <Box gap={space[5]}>
          <SubHeaderContainer>
            <NewInsuranceExpIcon />
            <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
              {t(`eRecruit.application.personalDetails.insuranceExperience`)}
            </SubHeaderLabel>
          </SubHeaderContainer>
          <Box gap={space[1]}>
            <LargeBody color={colors.palette.fwdGreyDarker} style={{ flex: 1 }}>
              1. {t('eRecruit.application.personalDetails.questionOne')}
            </LargeBody>
            <LargeBody style={{ flex: 1 }}>
              {otherDetailsMap?.insuranceExperience?.haveLifeInsuranceExp
                ? t('eRecruit.application.personalDetails.question.yes')
                : t('eRecruit.application.personalDetails.question.no')}
            </LargeBody>
          </Box>
          <Box gap={space[1]}>
            <LargeBody color={colors.palette.fwdGreyDarker} style={{ flex: 1 }}>
              2. {t('eRecruit.application.personalDetails.questionTwo')}
            </LargeBody>
            <LargeBody style={{ flex: 1 }}>
              {otherDetailsMap?.insuranceExperience?.haveGeneralInsuranceExp
                ? t('eRecruit.application.personalDetails.question.yes')
                : t('eRecruit.application.personalDetails.question.no')}
            </LargeBody>
          </Box>
        </Box>

        {/*------- Regulatury -  health */}
        <RegulatorySection
          icon={<MedicalReport2 size={space[10]} />}
          sectionLabel={renderLabelByLanguage(healthInfoObj?.longDesc)}
          regulatoryList={healthInfoObj?.regulatoryList ?? []}
          otherDetailsMap={otherDetailsMap}
        />

        {/*------- Regulatury -  financial */}
        <RegulatorySection
          icon={<MoneyBagNewSVG size={space[10]} />}
          sectionLabel={renderLabelByLanguage(financialInfoObj?.longDesc)}
          regulatoryList={financialInfoObj?.regulatoryList ?? []}
          otherDetailsMap={otherDetailsMap}
        />

        {/*------- Regulatury - compliance */}
        <RegulatorySection
          icon={<FormDuoNewSVG size={space[10]} />}
          sectionLabel={renderLabelByLanguage(compliAndRepuObj?.longDesc)}
          regulatoryList={compliAndRepuObj?.regulatoryList ?? []}
          otherDetailsMap={otherDetailsMap}
        />

        {/*------- Regulatury - AML */}
        <RegulatorySection
          icon={<WebPage2SVG size={space[10]} />}
          sectionLabel={renderLabelByLanguage(amlInfoObj?.longDesc)}
          regulatoryList={amlInfoObj?.regulatoryList ?? []}
          otherDetailsMap={otherDetailsMap}
          questionToConfigMapToOverride={{
            'S-5-1': {
              shownCommentWhen: 'Yes',
            },
            'S-5-2': {
              shownCommentWhen: 'No',
            },
            'S-5-3': {
              shownCommentWhen: 'Never',
            },
            'S-5-4': {
              shownCommentWhen: 'Never',
            },
            'S-5-5': {
              shownCommentWhen: 'Never',
            },
            'S-5-6': {
              shownCommentWhen: 'Never',
            },
          }}
        />

        {/*------- Regulatury - COI */}
        <Box gap={space[5]}>
          <SubHeaderContainer>
            <NewDocForFormIcon />
            {/* <PictogramIcon.Bank2 size={space[10]} /> */}
            <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
              {t('eRecruit.application.otherDetails.declarationOfCOI')}
            </SubHeaderLabel>
          </SubHeaderContainer>
          <Box gap={space[5]}>
            {Object.values(
              currentLanguage === 'id' ? IdnQuestionsMap : IbQuestionsMap,
            )?.map((item, index) => {
              const fieldKey = item.key as keyof (
                | typeof IdnQuestionsMap
                | typeof IbQuestionsMap
              );

              return (
                <Box key={fieldKey} gap={space[3]}>
                  <FieldValue fontWeight="bold">
                    {`Q${index + 1} - ` + item.title}
                  </FieldValue>
                  <FieldLabel>{item.qBody.join('')}</FieldLabel>
                  <FieldValue>
                    {getYesOrNo(
                      otherDetailsMap?.conflictOfInterest?.[fieldKey],
                      t,
                    )}
                  </FieldValue>
                  {otherDetailsMap?.conflictOfInterest?.[fieldKey] == true ? (
                    <Box gap={space[3]}>
                      <FieldValue fontWeight="bold">
                        {t('application.COI.record', { number: '' })}
                      </FieldValue>
                      <Row flexWrap="wrap" gap={space[2]}>
                        {fieldKey == 'ownershipInterest' ? (
                          <>
                            <FieldValuePair
                              fieldName={t(
                                'application.COI.nameOfBusinessEnterpriseOrEntity',
                              )}
                              fieldValue={
                                COIOwnershipInterestItem?.nameOfBusiness
                              }
                            />
                            <FieldValuePair
                              fieldName={t(`application.COI.natureOfBusiness`)}
                              fieldValue={
                                COIOwnershipInterestItem?.natureOfBusiness
                              }
                            />
                            <FieldValuePair
                              fieldName={t(
                                `application.COI.nameOfOwnerAndRelationship`,
                              )}
                              fieldValue={COIOwnershipInterestItem?.nameOfOwner}
                            />
                            <FieldValuePair
                              fieldName={t(
                                `application.COI.percentageOfOwnership`,
                              )}
                              fieldValue={
                                COIOwnershipInterestItem?.percentageOfOwnership
                                  ? COIOwnershipInterestItem.percentageOfOwnership +
                                    '%'
                                  : undefined
                              }
                            />
                            <FieldValuePair
                              fieldName={t(`application.COI.dateAcquired`)}
                              fieldValue={
                                COIOwnershipInterestItem?.dateAcquired
                                  ? dateFormatUtil(
                                      COIOwnershipInterestItem?.dateAcquired,
                                    )
                                  : undefined
                              }
                            />
                          </>
                        ) : fieldKey == 'externalEmployment' ? (
                          <>
                            <FieldValuePair
                              fieldName={t(
                                'application.COI.nameOfBusinessEnterpriseOrEntity',
                              )}
                              fieldValue={
                                COIExternalEmploymentItem?.nameOfBusiness
                              }
                            />
                            <FieldValuePair
                              fieldName={t('application.COI.natureOfBusiness')}
                              fieldValue={
                                COIExternalEmploymentItem?.natureOfBusiness
                              }
                            />
                            <FieldValuePair
                              fieldName={t('application.COI.position')}
                              fieldValue={COIExternalEmploymentItem?.position}
                            />
                            <FieldValuePair
                              fieldName={t('application.COI.details')}
                              fieldValue={COIExternalEmploymentItem?.details}
                            />
                            <FieldValuePair
                              fieldName={t(
                                'application.COI.compensationReceived',
                              )}
                              fieldValue={getYesOrNo(
                                COIExternalEmploymentItem?.compensationReceived,
                                t,
                              )}
                            />
                          </>
                        ) : fieldKey == 'businessAffiliationInterest' ? (
                          <>
                            <FieldValuePair
                              fieldName={t(
                                'application.COI.nameOfBusinessEnterpriseOrEntity',
                              )}
                              fieldValue={
                                COIBusinessAffiliationInterestItem?.nameOfBusiness
                              }
                            />
                            <FieldValuePair
                              fieldName={t('application.COI.natureOfBusiness')}
                              fieldValue={
                                COIBusinessAffiliationInterestItem?.natureOfBusiness
                              }
                            />
                            <FieldValuePair
                              fieldName={t(
                                'application.COI.nameOfFamilyMemberAndRelationship',
                              )}
                              fieldValue={
                                COIBusinessAffiliationInterestItem?.nameOfFamilyMember
                              }
                            />
                            <FieldValuePair
                              fieldName={t(
                                'application.COI.positionDepartment',
                              )}
                              fieldValue={
                                COIBusinessAffiliationInterestItem?.positionDepartment
                              }
                            />
                            <FieldValuePair
                              fieldName={t(
                                'application.COI.dateCommencementEmployment',
                              )}
                              fieldValue={
                                COIBusinessAffiliationInterestItem?.dateCommencementEmployment
                                  ? dateFormatUtil(
                                      COIBusinessAffiliationInterestItem?.dateCommencementEmployment,
                                    )
                                  : undefined
                              }
                            />
                          </>
                        ) : fieldKey == 'relationshipGovernmentOfficial' ? (
                          <>
                            <FieldValuePair
                              fieldName={t('application.COI.nameOfGovernment')}
                              fieldValue={
                                COIRelationshipGovernmentOfficialItem?.nameOfGovernment
                              }
                            />
                            <FieldValuePair
                              fieldName={t(
                                'application.COI.positionDepartment',
                              )}
                              fieldValue={
                                COIRelationshipGovernmentOfficialItem?.positionDepartment
                              }
                            />
                            <FieldValuePair
                              fieldName={t(
                                'application.COI.relationshipWithGovOfficials',
                              )}
                              fieldValue={
                                COIRelationshipGovernmentOfficialItem?.relationship
                              }
                            />
                          </>
                        ) : fieldKey == 'otherInterest' ? (
                          <Row gap={space[5]} flex={1}>
                            <FieldLabel>
                              {t('application.COI.otherDetails')}
                            </FieldLabel>
                            <FieldValue flex={3.7}>
                              {COIOtherInterestItem?.details}
                            </FieldValue>
                          </Row>
                        ) : null}
                      </Row>
                    </Box>
                  ) : null}
                </Box>
              );
            })}
          </Box>
          {/* <Row>
              <FieldLabel style={{ flex: 0.3 }}>
                {t('eRecruit.application.review.otherDetails')}
              </FieldLabel>
              <FieldValue>
                {recruitmentCache?.approvalComments?.find(
                  section => section.approverAgentCode === agentCode,
                )?.comment ?? '--'}
              </FieldValue>
            </Row> */}
        </Box>
        <Box gap={space[5]}>
          <SubHeaderContainer>
            <NoteBookSVG />
            <SubHeaderLabel fontWeight="bold" style={{ flex: 1 }}>
              {t('application.otherDetails.remark')}
            </SubHeaderLabel>
          </SubHeaderContainer>
          <FieldValue>
            {recruitmentCache?.approvalComments?.find(
              section => section.approverAgentCode === agentCode,
            )?.comment ?? '--'}
          </FieldValue>
        </Box>
      </ReviewInfoCard>
      {/*</ScrollView>*/}
      {/*<TabFooter
        isLoading={isLoading}
        onNext={() => setIsReviewModalOn(v => !v)}
      />*/}
    </>
  );
}

function FieldValuePair({
  fieldName,
  fieldValue,
  fallbackValue = '--',
}: {
  fieldName: string;
  fieldValue: string | undefined | null;
  fallbackValue?: string;
}) {
  const { space, colors, borderRadius } = useTheme();
  return (
    <Row flexBasis={'90%'} gap={space[5]}>
      <FieldLabel>{fieldName}</FieldLabel>
      <FieldValue>{fieldValue ?? fallbackValue}</FieldValue>
    </Row>
  );
}

function ReviewInfoCard({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) {
  const { space, colors, borderRadius } = useTheme();

  return (
    <Box>
      <Box
        backgroundColor={colors.secondary}
        px={space[6]}
        py={space[3]}
        borderTopRadius={borderRadius['large']}>
        <H7 fontWeight={'bold'} color={colors.background}>
          {title}
        </H7>
      </Box>
      <Box
        border={1}
        borderColor={colors.palette.fwdGrey[100]}
        borderTop={0}
        px={space[3]}
        py={space[4]}
        gap={space[8]}
        backgroundColor={colors.background}
        borderBottomRadius={borderRadius['large']}>
        {children}
      </Box>
    </Box>
  );
}

const FieldLabel = styled(LargeBody)<{ flex?: number }>(({ theme, flex }) => ({
  color: theme.colors.palette.fwdGreyDarker,
  flex: flex ?? 0.6,
}));

const FieldValue = styled(LargeBody)<{ flex?: number }>(({ flex }) => ({
  flex: flex ?? 1.2,
}));

const SubHeaderContainer = styled(Row)(({ theme: { space } }) => ({
  justifyContent: 'flex-start',
  alignItems: 'center',
  gap: space[2],
}));

const SubHeaderLabel = styled(H6)<{ flex?: number }>(({ theme }) => ({
  flex: 1,
}));
