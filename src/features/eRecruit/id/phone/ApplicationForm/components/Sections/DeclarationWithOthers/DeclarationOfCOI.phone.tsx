import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import Input from 'components/Input';
import Portal from 'components/Portal/Portal';
import {
  Box,
  Column,
  H6,
  Row,
  TextField,
  LargeLabel,
  RadioButtonGroup,
  RadioButton,
  Checkbox,
  LargeBody,
} from 'cube-ui-components';
import {} from 'cube-ui-components';
import RecruitYesNoInput from 'features/eRecruit/components/RecruitYesNoInput';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';

import { Fragment, useCallback, useMemo, useState } from 'react';
import { useController, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  //
  DeclarationOfInterestFromStore,
} from 'features/eRecruit/util/store/id/ERecruitStore';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FormFooter from '../../utils/FormFooter';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';
import NewDocForFormIcon from 'features/eRecruit/ib/tablet/asset/NewDocForFormIcon';

// TBC
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { DeclarationCOIPath } from 'features/eRecruit/util/store/id/types';
import { declarationOfInterestSchema } from 'features/eRecruit/id/validations/phone';
import { SharedValue } from 'react-native-reanimated';
import { IDNRegulatorysQuestionsOnly } from 'types/eRecruit';
import { renderLabelByLanguage } from 'utils/helper/translation';
import WebPage2SVG from 'features/eRecruit/assets/WebPage2SVG';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { country } from 'utils/context';
import useBoundStore from 'hooks/useBoundStore';
import { IdnQuestionsMap } from 'features/eRecruit/id/config';
import { getQuestionsMap } from 'features/eRecruit/ib/tablet/NewApplicationForm/DeclarationOfCOI';
import { getTypedObjectValues } from 'utils/helper/objectUtil';
import Tooltip from 'components/Tooltip';
import ExternalDirectorshipEmploymentForm from './COIAdditionalForms/ExternalDirectorshipEmploymentForm';
import BusinessAffiliationsInterestsForm from './COIAdditionalForms/BusinessAffiliationsInterestsForm';
import OtherInterestForm from './COIAdditionalForms/OtherInterestForm';
import OwnershipInterestForm from './COIAdditionalForms/OwnershipInterestForm';
import RelationshipsWithGovernmentOfficialsForm from './COIAdditionalForms/RelationshipsWithGovernmentOfficialsForm';
import { COISection } from './COIAdditionalForms/COISection';

interface Props {
  onDismiss: () => void;
  value: DeclarationOfInterestFromStore;
  onDone: (data: DeclarationOfInterestFromStore) => void;
}

export default function AmlAndOthers({ onDismiss, value, onDone }: Props) {
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes, borderRadius } = useTheme();
  const [isAffirmed, setIsAffirmed] = useState(false);

  const { isLoading, genderConfig, countryCodeOptions } =
    useGetERecruitOptionListForAppForm();
  const { regulatoryList } = useGetERecruitOptionListForAppForm();
  const currentLanguage = useBoundStore(state => state.language);

  const questionsMap = useMemo(
    () => getQuestionsMap(country, currentLanguage),
    [currentLanguage],
  );
  const coiTypedObj = getTypedObjectValues(questionsMap);

  // const compliAndRepuObj = regulatoryList?.find(i => i.section == 'S-2');
  // const amlInfoObj = regulatoryList?.find(i => i.section == 'S-5');
  const hookForm = useForm({
    defaultValues: {
      conflictOfInterest: {
        ownershipInterest: false,
        externalEmployment: false,
        businessAffiliationInterest: false,
        relationshipGovernmentOfficial: false,
        otherInterest: false,
      },
    },
    // values: {},
    resolver: yupResolver(declarationOfInterestSchema),
    mode: 'onBlur',
  });

  const {
    control,
    watch,
    trigger,
    formState: { errors },
    getValues,
    setValue,
  } = hookForm;

  const {
    field: { value: q1Value, onChange: onChangeQ1Value },
    formState: { isValid: isQ1Valid },
  } = useController({
    control,
    name: 'conflictOfInterest.ownershipInterest',
  });
  const {
    field: { value: q2Value, onChange: onChangeQ2Value },
    formState: { isValid: isQ2Valid },
  } = useController({
    control,
    name: 'conflictOfInterest.externalEmployment',
  });
  const {
    field: { value: q3Value, onChange: onChangeQ3Value },
    formState: { isValid: isQ3Valid },
  } = useController({
    control,
    name: 'conflictOfInterest.businessAffiliationInterest',
  });
  const {
    field: { value: q4Value, onChange: onChangeQ4Value },
    formState: { isValid: isQ4Valid },
  } = useController({
    control,
    name: 'conflictOfInterest.relationshipGovernmentOfficial',
  });
  const {
    field: { value: q5Value, onChange: onChangeQ5Value },
    formState: { isValid: isQ5Valid },
  } = useController({
    control,
    name: 'conflictOfInterest.otherInterest',
  });

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints();
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const submit = useCallback(async () => {
    const isValid = await trigger();
    if (isValid) {
      const currentValues = getValues();
      onDone({
        done: true,
        ...currentValues,
      });

      bottomSheetProps.bottomSheetRef.current?.close();
    } else {
      console.log('Validation failed', errors);
    }
  }, [bottomSheetProps.bottomSheetRef, errors, getValues, onDone, trigger]);

  const mandatoryFields = [
    // 'S-5-1.checked',
    // 'S-5-2.checked',
    // 'S-5-3.checked',
    // 'S-5-4.checked',
    // 'S-5-5.checked',
    // 'S-5-6.checked',
  ] as const satisfies Array<DeclarationCOIPath>;

  const currentForm = getValues();

  // const currentFormArray = Object.entries(currentForm) as Array<
  //   [
  //     keyof typeof currentForm,
  //     {
  //       detail?: string | null | undefined;
  //       checked: NonNullable<boolean | undefined>;
  //     },
  //   ]
  // >;
  // // assuming the details is needed
  // const isCommentShownAndFilled = currentFormArray;
  const isAllMandatoryFieldsFilled = watch(mandatoryFields).every(
    (item: unknown) => item != null,
  );
  const isValid = isQ1Valid && isQ2Valid && isQ3Valid && isQ4Valid && isQ5Valid;

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          primaryDisabled={!isAffirmed || !isValid}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel="Done"
        />
      );
    },
    [isAffirmed, isValid, submit],
  );
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          index={1}
          onDismiss={onDismiss}
          snapPoints={snapPoints}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <Box px={space[isNarrowScreen ? 3 : 4]}>
            <Row alignItems="center" gap={space[1]}>
              <NewDocForFormIcon />
              <H6 fontWeight="bold" color={colors.primary}>
                {t('eRecruit.application.otherDetails.declarationOfCOI')}
              </H6>
              {/*<H6 color={colors.primary} fontWeight="bold" style={{ flex: 1 }}>
                {renderLabelByLanguage(amlInfoObj?.longDesc)}
              </H6>*/}
            </Row>
          </Box>
          <BottomSheetKeyboardAwareScrollView
            bottomOffset={space[10]}
            style={{
              paddingHorizontal: space[4],
              flex: 1,
            }}>
            <Box paddingY={space[4]}>
              <Column gap={space[4]}>
                <COISection
                  questionsMap={questionsMap}
                  questionKey={'ownershipInterest'}>
                  <Row gap={space[12]}>
                    <RadioButtonGroup
                      value={q1Value}
                      onChange={onChangeQ1Value}>
                      <RadioButton
                        value={true}
                        label={t('application.COI.yes')}
                      />
                      <RadioButton
                        value={false}
                        label={t('application.COI.no')}
                      />
                    </RadioButtonGroup>
                  </Row>
                  <OwnershipInterestForm hookForm={hookForm} maxRecords={1} />
                </COISection>

                <COISection
                  questionsMap={questionsMap}
                  questionKey={'externalEmployment'}>
                  <Row gap={space[12]}>
                    <RadioButtonGroup
                      value={q2Value}
                      onChange={onChangeQ2Value}>
                      <RadioButton
                        value={true}
                        label={t('application.COI.yes')}
                      />
                      <RadioButton
                        value={false}
                        label={t('application.COI.no')}
                      />
                    </RadioButtonGroup>
                  </Row>
                  <ExternalDirectorshipEmploymentForm
                    hookForm={hookForm}
                    maxRecords={1}
                  />
                </COISection>

                <COISection
                  questionsMap={questionsMap}
                  questionKey={'businessAffiliationInterest'}>
                  <Row gap={space[12]}>
                    <RadioButtonGroup
                      value={q3Value}
                      onChange={onChangeQ3Value}>
                      <RadioButton
                        value={true}
                        label={t('application.COI.yes')}
                      />
                      <RadioButton
                        value={false}
                        label={t('application.COI.no')}
                      />
                    </RadioButtonGroup>
                  </Row>
                  <BusinessAffiliationsInterestsForm
                    hookForm={hookForm}
                    maxRecords={1}
                  />
                </COISection>

                <COISection
                  questionsMap={questionsMap}
                  questionKey={'relationshipGovernmentOfficial'}>
                  <Row gap={space[12]}>
                    <RadioButtonGroup
                      value={q4Value}
                      onChange={onChangeQ4Value}>
                      <RadioButton
                        value={true}
                        label={t('application.COI.yes')}
                      />
                      <RadioButton
                        value={false}
                        label={t('application.COI.no')}
                      />
                    </RadioButtonGroup>
                  </Row>
                  <RelationshipsWithGovernmentOfficialsForm
                    hookForm={hookForm}
                    maxRecords={1}
                  />
                </COISection>

                <COISection
                  questionsMap={questionsMap}
                  questionKey={'otherInterest'}>
                  <Row gap={space[12]}>
                    <RadioButtonGroup
                      value={q5Value}
                      onChange={onChangeQ5Value}>
                      <RadioButton
                        value={true}
                        label={t('application.COI.yes')}
                      />
                      <RadioButton
                        value={false}
                        label={t('application.COI.no')}
                      />
                    </RadioButtonGroup>
                  </Row>
                  <OtherInterestForm hookForm={hookForm} />
                </COISection>

                <Box gap={space[4]}>
                  <H6 fontWeight="bold">
                    {t('application.COI.declarationTitle')}
                  </H6>
                  <Row
                    gap={space[2]}
                    border={1}
                    padding={space[3]}
                    backgroundColor={
                      isAffirmed ? colors.palette.fwdOrange[5] : 'transparent'
                    }
                    borderRadius={borderRadius.medium}
                    borderColor={
                      isAffirmed
                        ? colors.palette.fwdOrange[100]
                        : colors.palette.fwdGrey[100]
                    }>
                    <Column py={space[1]} pl={space[1]}>
                      <Checkbox
                        value={isAffirmed}
                        onChange={() => setIsAffirmed(!isAffirmed)}
                      />
                    </Column>
                    <Box flex={1}>
                      <LargeBody>
                        {t('application.COI.declarationMsg')}
                      </LargeBody>
                    </Box>
                  </Row>
                </Box>
              </Column>
            </Box>
            <BottomSheetFooterSpace />
          </BottomSheetKeyboardAwareScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
