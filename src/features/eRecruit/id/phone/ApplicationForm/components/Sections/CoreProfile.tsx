import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  addToast,
  Box,
  Column,
  H6,
  Icon,
  PictogramIcon,
} from 'cube-ui-components';
import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { shallow } from 'zustand/shallow';
import ERecruitFooter from '../utils/ERecruitFooter';
import EssentialInformation from './CoreProfile/EssentialInformation';
import NewIdentitySectionIcon from 'features/eRecruit/ib/tablet/asset/NewIdentitySectionIcon';
import {
  EssentialInfoFromStore,
  BackgroundDetailsFromStore,
  AddressInfoFromStore,
  CandidatePositionFromStore,
  EmergencyContactFromStore,
  BankAccountInfoFromStore,
  useERecruitStore,
} from 'features/eRecruit/util/store/id/ERecruitStore';
import ApplicationDetailsSection from '../utils/ApplicationDetailsSection';
import { useSaveERecruitApplicationForm } from 'features/eRecruit/hooks/useSaveERecruitApplicationForm';
import { ParsingAppFormDataActionKeys, SavedActionProps } from 'types/eRecruit';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { CubeResponse, RootStackParamList, RootStackParamListMap } from 'types';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import GATracking from 'utils/helper/gaTracking';

import {
  essentialInformationSchema,
  backgroundDetailsSchema,
  addressInformationSchema,
  candidatePositionSchema,
  emergencyContactSchema,
  bankInformationSchema,
} from 'features/eRecruit/id/validations/phone';
import ApplicationDetailsListIcon from 'features/eRecruit/ib/tablet/asset/ApplicationDetailsListIcon';
import BackgroundDetails from './CoreProfile/BackgroundDetails';
import NewAddrIcon from 'features/eRecruit/ib/tablet/asset/NewAddrIcon';
import AddressInformation from './CoreProfile/AddressInformation';
import CandidatePositionSection from './CoreProfile/CandidatePositionSection';
import EmergencyContact from './CoreProfile/EmergencyContact';
import AlarmNewSVG from 'features/eRecruit/assets/AlarmNewSVG';
import NewBankIcon from 'features/eRecruit/ib/tablet/asset/NewBankIcon';
import BankAccountInformationSection from './CoreProfile/BankAccountInformation';
import {
  personalDetailsFunctions,
  PersonalDetailsFunctionsProps,
} from 'features/eRecruit/id/utils/personalDetailsFunctions';
import { InferType } from 'yup';
import { personalDetailsSchema } from 'features/eRecruit/id/validations/personalDetailsSchema';
import { useGetERecruitConfig } from 'features/eRecruit/hooks/useGetERecruitConfig';
import { useERecruitProgressBarStore } from 'features/eRecruit/util/store/id/ERecruitProgressBarStore';
import { defaultCountryCode } from 'features/eRecruit/config';
import {
  getCityItemCodeByValue,
  isBusinessAddressSameAsResidential,
} from 'features/eRecruit/id/utils/personalDetailsFunctions';
import { AxiosError } from 'axios';
import { getErrorMessageFromCode } from 'features/eRecruit/id/utils';
import { BlacklistedIDErrorModal } from 'features/eRecruit/id/tablet/components/BlacklistedIDErrorModal';
const REGISTRATION_ID_BLACKLIST = 'REGISTRATION_STAGING_NRIC_BLACKLIST';

const emptyValues = {};

export default function CoreProfile() {
  const { space } = useTheme();
  const [isErrorModalVisible, setIsErrorModalVisible] =
    useState<boolean>(false);

  const onToggleErrorModal = () => setIsErrorModalVisible(prev => !prev);

  const {
    id,
    // data
    essentialInfo,
    backgroundDetails,
    addressInfo,
    candidatePosition,
    emergencyContact,
    bankAccountInfo,
    // setter
    setEssentialInfo,
    setBackgroundDetails,
    setAddressInfo,
    setCandidatePosition,
    setEmergencyContact,
    setBankAccountInformation,
    // setIdentityDetailsData(data as IdentityDetailsInfo);
    setRegistrationStagingId,
  } = useERecruitStore(
    state => ({
      // data
      id: state.registrationStagingId,
      essentialInfo: state.essentialInfo,
      backgroundDetails: state.backgroundDetails,
      addressInfo: state.addressInfo,
      candidatePosition: state.candidatePosition,
      emergencyContact: state.emergencyContact,
      bankAccountInfo: state.bankAccountInfo,

      // setters
      setEssentialInfo: state.setEssentialInfo,
      setBackgroundDetails: state.setBackgroundDetails,
      setAddressInfo: state.setAddressInfo,
      setCandidatePosition: state.setCandidatePosition,
      setRegistrationStagingId: state.setRegistrationStagingId,
      setEmergencyContact: state.setEmergencyContact,
      setBankAccountInformation: state.setBankAccountInformation,
    }),
    shallow,
  );

  const { next, nextGroup, setProgressBarState } = useERecruitProgressBarStore(
    state => ({
      next: state.next,
      nextGroup: state.nextGroup,
      setProgressBarState: state.setProgressBarState,
    }),
    shallow,
  );
  const isSaveForLaterButtonDisabled = !essentialInfo.done;
  const isOnFinancingProgram = Boolean(
    candidatePosition?.candidatePosition?.isHaveFinancingProgram ?? false,
  );
  const isNextDisabled = !(
    essentialInfo.done &&
    backgroundDetails.done &&
    addressInfo.done &&
    candidatePosition.done &&
    (isOnFinancingProgram ? emergencyContact.done : true) &&
    bankAccountInfo.done
  );

  const { t } = useTranslation(['eRecruit']);
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { mutateAsync, isLoading } = useSaveERecruitApplicationForm();
  const { data: eRecruitOnlyOptionList, isLoading: isERCLoading } =
    useGetERecruitConfig();
  const cityList = eRecruitOnlyOptionList?.cityList || [];

  const isSameAsResidentialAddress =
    addressInfo.contact.isBusinessSameAsResidentialAddress;

  const saveAction = (
    props: SavedActionProps<InferType<typeof personalDetailsSchema>>,
    options?: PersonalDetailsFunctionsProps['options'],
  ) => {
    try {
      console.log('~~~saveAction~~');
      const { data, onSuccess, onError } = props;
      if (!recruitmentCache) {
        console.log(
          'recruitmentCache is undefined, but not block in PersonalDetailsTab',
        );
        // return;
      }
      const pressAction = props.pressAction ? props.pressAction : 'save';
      const updatedData = personalDetailsFunctions({
        input: data,
        recruitmentCache,
        pressAction,
        cityList,
        isSameAsResidentialAddress,
        options,
      });

      mutateAsync(updatedData as any, {
        onSuccess: onSuccess,
        onError:
          (onError as (err: unknown) => void) ??
          (error => {
            addToast([
              {
                message: t('eRecruit:eRecruit.pleaseTryAgainLater'),
              },
            ]);
            console.log(
              '🚀 ~ file: ProgressStepBar==== ~ onValidSubmit error:',
              error,
            );
          }),
      });
    } catch (error) {
      console.error('Error in saveAction --- PersonalDetailsTab:', error);
    }
  };

  const onValidSave = (
    data: InferType<typeof personalDetailsSchema>,
    pressAction?: ParsingAppFormDataActionKeys,
  ) => {
    console.log('--------- - onValidSave data: ', data);
    // nextGroup();

    saveAction({
      data,
      pressAction,
      onSuccess: async data => {
        if (data?.registrationStagingId) {
          GATracking.logCustomEvent('recruitment', {
            action_type: 'application_created',
          });
          setRegistrationStagingId(data?.registrationStagingId);

          if (pressAction !== 'next') {
            navigation?.navigate('Main');
            addToast([
              {
                IconLeft: Icon.Tick,
                message: t('eRecruit:eRecruit.application.saved'),
              },
            ]);
            return;
          }

          nextGroup();
        }
      },
      onError: (error: AxiosError<CubeResponse<unknown>>) => {
        if (error?.response?.data?.status === REGISTRATION_ID_BLACKLIST) {
          return onToggleErrorModal();
        }

        const errMsg = getErrorMessageFromCode({
          type: 'dataPassedByMap',
          errCode: error?.response?.data?.status,
          dataMap: {
            email: essentialInfo?.contact?.email,
            phoneNumberWithCode:
              essentialInfo?.contact?.countryCode +
              essentialInfo?.contact?.phoneNumber,
            idType: backgroundDetails?.identity?.identity,
            idNumber: backgroundDetails?.identity?.idNumber,
            npwp: backgroundDetails?.personalInformation?.npwp ?? '--',
            bankAccount: bankAccountInfo?.bankInformation?.accountNumber,
          },
          t,
        });

        addToast([
          {
            message: errMsg,
          },
        ]);
        console.log(
          '🚀 ~ file: PersonalDetailsTab ~ onValidSubmit error:',
          error,
        );
      },
    });
  };

  const route =
    useRoute<RouteProp<RootStackParamListMap['ib'], 'ERecruitApplication'>>();
  const registrationStagingId = route.params?.registrationStagingId ?? '';
  const registrationStagingIdParam = registrationStagingId;

  const { data: recruitmentCache, isInitialLoading } = useGetApplicationData(
    registrationStagingIdParam,
  );

  const formDataToSave = buildFormDataToSave(
    essentialInfo,
    backgroundDetails,
    addressInfo,
    candidatePosition,
    emergencyContact,
    bankAccountInfo,
  );
  useEffect(() => {
    if (!recruitmentCache) {
      return;
    }

    if (recruitmentCache?.applicationId != null) {
      setRegistrationStagingId(recruitmentCache?.applicationId);
    } else {
      console.warn('No application ID found in recruitment cache');
    }

    const phone = recruitmentCache.contact?.phones?.find(
      phone => phone.type === 'MOBILE',
    );
    const officePhone = recruitmentCache.contact?.phones?.find(
      phone => phone.type === 'WORK',
    );

    setEssentialInfo({
      identity: {
        fullName: recruitmentCache.identity.fullname
          ? recruitmentCache.identity.fullname.trim()
          : '',
        gender: recruitmentCache.identity.gender ?? '',
        dateOfBirth: recruitmentCache.identity.dateOfBirth
          ? new Date(recruitmentCache.identity.dateOfBirth)
          : (null as any),
      },
      contact: {
        countryCode: phone?.countryCode ?? defaultCountryCode,
        phoneNumber: phone?.number ?? '',
        email: recruitmentCache.contact.email ?? '',
        officeNumberCountryCode: officePhone?.countryCode ?? defaultCountryCode,
        officePhoneNumber: officePhone?.number,
      },
      done: Boolean(
        recruitmentCache.identity.fullname &&
          recruitmentCache.identity.gender &&
          recruitmentCache.identity.dateOfBirth &&
          phone?.countryCode &&
          phone?.number &&
          recruitmentCache.contact.email,
      ),
    });
    // ----------------------------------
    //

    const idInfo = recruitmentCache.identity?.registration?.find(
      reg => reg.type === 'NRIC',
    );

    setBackgroundDetails({
      identity: {
        identity: idInfo?.rawType ?? '',
        idNumber: idInfo?.number ?? '',
        birthPlace: recruitmentCache.identity.birthPlace ?? '',
        religion: recruitmentCache.identity?.religion ?? '',
        maritalStatus: recruitmentCache.identity?.maritalStatus ?? '',
        numberOfDependence:
          recruitmentCache.spouseInformation?.numberOfDependence?.toString() ??
          '',
      },

      personalInformation: {
        education: recruitmentCache.qualifications?.academic?.name ?? '',
        npwp:
          recruitmentCache.identity?.registration?.find(
            reg => reg.type === 'INCOME_TAX',
          )?.number ?? '',
        presentOccupation:
          recruitmentCache.workingExperiences?.[0]?.presentOccupation ?? '',
        industry: recruitmentCache.workingExperiences?.[0]?.industry ?? '',
      },
      done: Boolean(
        idInfo?.rawType &&
          idInfo?.number &&
          recruitmentCache.identity.birthPlace &&
          phone?.countryCode &&
          phone?.number &&
          recruitmentCache.identity?.religion &&
          recruitmentCache.identity?.maritalStatus &&
          recruitmentCache.spouseInformation?.numberOfDependence?.toString() &&
          recruitmentCache.qualifications?.academic?.name &&
          recruitmentCache.workingExperiences?.[0]?.presentOccupation &&
          recruitmentCache.workingExperiences?.[0]?.industry,
      ),
    });

    // ----------------------------------

    const isBusinessAddrSameAsResidential = isBusinessAddressSameAsResidential(
      recruitmentCache.contact.address as any,
      recruitmentCache.contact.businessAddress as any,
    );

    setAddressInfo({
      contact: {
        address: {
          ...recruitmentCache.contact.address,
          province: recruitmentCache.contact.address.state,
          postCode: recruitmentCache.contact.address?.postCode ?? undefined,
          city: getCityItemCodeByValue(
            recruitmentCache.contact.address.city,
            cityList,
          ),
        },
        isBusinessSameAsResidentialAddress: isBusinessAddrSameAsResidential,
        businessAddress: {
          ...recruitmentCache.contact.businessAddress,
          postCode:
            recruitmentCache.contact.businessAddress?.postCode ?? undefined,
          province: recruitmentCache.contact.businessAddress.state,
          city: getCityItemCodeByValue(
            recruitmentCache.contact.businessAddress.city,
            cityList,
          ),
        },
      },
      done: isBusinessAddrSameAsResidential
        ? Boolean(
            recruitmentCache?.contact?.address.line1 &&
              recruitmentCache?.contact?.address.neighborhoodAssociation &&
              recruitmentCache?.contact?.address.communityAssociation &&
              recruitmentCache?.contact?.address.subDistrict &&
              recruitmentCache?.contact?.address.district &&
              recruitmentCache?.contact?.address.state &&
              recruitmentCache?.contact?.address.city &&
              recruitmentCache?.contact?.address.postCode,
          )
        : Boolean(
            recruitmentCache?.contact?.address.line1 &&
              recruitmentCache?.contact?.address.neighborhoodAssociation &&
              recruitmentCache?.contact?.address.communityAssociation &&
              recruitmentCache?.contact?.address.subDistrict &&
              recruitmentCache?.contact?.address.district &&
              recruitmentCache?.contact?.address.state &&
              recruitmentCache?.contact?.address.city &&
              recruitmentCache?.contact?.address.postCode,
          ) &&
          Boolean(
            recruitmentCache?.contact?.businessAddress.line1 &&
              recruitmentCache?.contact?.businessAddress
                .neighborhoodAssociation &&
              recruitmentCache?.contact?.businessAddress.communityAssociation &&
              recruitmentCache?.contact?.businessAddress.subDistrict &&
              recruitmentCache?.contact?.businessAddress.district &&
              recruitmentCache?.contact?.businessAddress.state &&
              recruitmentCache?.contact?.businessAddress.city &&
              recruitmentCache?.contact?.businessAddress.postCode,
          ),
    });

    // ----------------------------------
    //
    const osAreaManager =
      recruitmentCache.position?.osAreaManager == 'null-null'
        ? ''
        : recruitmentCache.position?.osAreaManager ?? '';
    setCandidatePosition({
      candidatePosition: {
        position: recruitmentCache.position?.position ?? '',
        salesOffice: recruitmentCache.position?.salesOffice ?? '',
        domicile: recruitmentCache.position?.domicile ?? '',
        osAreaManager: osAreaManager,
        superiorAgentCode: recruitmentCache.position?.superiorAgentCode ?? '',
        ref: recruitmentCache.position?.ref ?? '',
        // superiorAgentName: recruitmentCache.position?.superiorAgentName ?? '',
        isHaveFinancingProgram:
          recruitmentCache.position?.financingProgram ?? false,
      },
      done: Boolean(
        recruitmentCache.position?.position &&
          recruitmentCache.position?.salesOffice &&
          recruitmentCache.position?.domicile &&
          osAreaManager &&
          recruitmentCache.position?.superiorAgentCode &&
          recruitmentCache.position?.ref,
      ),
    });
    // ----------------------------------
    const cityCode = getCityItemCodeByValue(
      recruitmentCache?.contact?.emergencyContact?.city ?? null,
      cityList,
    );
    setEmergencyContact({
      emergencyContact: {
        fullName: recruitmentCache?.contact?.emergencyContact?.fullName ?? '',
        residenceNumber:
          recruitmentCache?.contact?.emergencyContact?.residentNumber ?? '',
        mobileCountryCode:
          recruitmentCache?.contact?.emergencyContact?.mobile?.countryCode ??
          defaultCountryCode,
        mobileNumber:
          recruitmentCache?.contact?.emergencyContact?.mobile?.number ?? '',

        address: recruitmentCache?.contact?.emergencyContact?.address ?? '',
        city: cityCode,
        state: recruitmentCache?.contact?.emergencyContact?.state ?? '',
        postCode: recruitmentCache?.contact?.emergencyContact?.postCode ?? '',
      },
      done: Boolean(
        recruitmentCache?.contact?.emergencyContact?.fullName &&
          recruitmentCache?.contact?.emergencyContact?.mobile.countryCode &&
          recruitmentCache?.contact?.emergencyContact?.mobile.number &&
          recruitmentCache?.contact?.emergencyContact?.address &&
          cityCode &&
          recruitmentCache?.contact?.emergencyContact?.state &&
          recruitmentCache?.contact?.emergencyContact?.postCode,
      ),
    });
    // ----------------------------------
    setBankAccountInformation({
      done: Boolean(
        recruitmentCache.contact?.bankInformation?.accountNumber &&
          recruitmentCache.contact?.bankInformation?.bankName &&
          recruitmentCache.contact?.bankInformation?.branchName,
      ),
      bankInformation: {
        accountNumber:
          recruitmentCache.contact?.bankInformation?.accountNumber ?? '',
        bankName: recruitmentCache.contact?.bankInformation?.bankName ?? '',
        branchName: recruitmentCache.contact?.bankInformation?.branchName ?? '',
      },
    });
    // ----------------------------------
  }, [recruitmentCache]);

  return (
    <Column flex={1}>
      <ScrollView>
        <Box my={space[4]} mx={space[isNarrowScreen ? 3 : 4]}>
          <H6 fontWeight="bold">
            {t('eRecruit:eRecruit.progressBar.coreProfileAndIdentity')}
          </H6>
        </Box>
        <Container>
          {/* ---- Essential information */}
          <ApplicationDetailsSection
            icon={
              NewIdentitySectionIcon satisfies React.ComponentType<{
                size?: number;
              }>
            }
            form={EssentialInformation}
            disabled={isInitialLoading || isLoading}
            name={t(
              'eRecruit:eRecruit.application.personalDetails.essentialInformation',
            )}
            done={essentialInfo.done}
            value={essentialInfo || emptyValues}
            onDone={data => {
              if (!data) {
                console.log('Essential information data is undefined');
                return;
              }
              console.log('Essential information data: ', data);
              setEssentialInfo(data);
            }}
            schema={essentialInformationSchema}
          />

          {/* ---- Background details */}
          <ApplicationDetailsSection
            icon={
              ApplicationDetailsListIcon as React.ComponentType<{
                size?: number;
              }>
            }
            disabled={isInitialLoading || isLoading}
            name={t(
              'eRecruit:eRecruit.application.personalDetails.backgroundDetails',
            )}
            done={backgroundDetails.done}
            form={BackgroundDetails}
            value={backgroundDetails || emptyValues}
            onDone={data => {
              data && setBackgroundDetails(data);
            }}
            schema={backgroundDetailsSchema}
          />
          {/* ---- Address info */}
          <ApplicationDetailsSection
            icon={
              NewAddrIcon as React.ComponentType<{
                size?: number;
              }>
            }
            disabled={isInitialLoading || isLoading}
            name={t(
              'eRecruit:eRecruit.application.otherDetails.addressInformation',
            )}
            done={addressInfo.done}
            form={AddressInformation}
            value={addressInfo || emptyValues}
            onDone={data => {
              console.log('------- AddressInformation nDpone: ', data);
              data && setAddressInfo(data);
            }}
            schema={addressInformationSchema}
          />
          {/* ---- Candidate position */}
          <ApplicationDetailsSection
            icon={
              PictogramIcon.ManWithShield as React.ComponentType<{
                size?: number;
              }>
            }
            disabled={isInitialLoading || isLoading}
            name={t(
              'eRecruit:eRecruit.application.otherDetails.candidateInformation',
            )}
            done={candidatePosition.done}
            form={CandidatePositionSection}
            value={candidatePosition || emptyValues}
            onDone={data => {
              data && setCandidatePosition(data);
            }}
            schema={candidatePositionSchema}
          />
          {/* ---- Emergency contact */}
          <ApplicationDetailsSection
            icon={
              AlarmNewSVG as React.ComponentType<{
                size?: number;
              }>
            }
            disabled={isInitialLoading || isLoading}
            name={
              isOnFinancingProgram
                ? t(
                    'eRecruit:eRecruit.application.otherDetails.emergencyContact',
                  )
                : t(
                    'eRecruit:eRecruit.application.otherDetails.emergencyContact.optional',
                  )
            }
            done={emergencyContact.done}
            form={EmergencyContact}
            value={emergencyContact || emptyValues}
            onDone={data => {
              data && setEmergencyContact(data);
            }}
            schema={emergencyContactSchema}
          />
          {/* ---- Bank acc info */}

          <ApplicationDetailsSection
            icon={
              NewBankIcon as React.ComponentType<{
                size?: number;
              }>
            }
            disabled={isInitialLoading || isLoading}
            name={t(
              'eRecruit:eRecruit.application.otherDetails.bankAccountInformation',
            )}
            done={bankAccountInfo.done}
            form={BankAccountInformationSection}
            value={bankAccountInfo || emptyValues}
            onDone={data => {
              data && setBankAccountInformation(data);
            }}
            schema={bankInformationSchema}
          />
        </Container>
        <Box h={space[4]} />
      </ScrollView>
      <ERecruitFooter
        primaryLoading={isLoading}
        primaryLabel={t(`eRecruit:eRecruit.application.otherDetails.next`)}
        primarySubLabel={t(
          `eRecruit:eRecruit.application.personalDetails.occupationDetails`,
        )}
        primaryDisabled={isNextDisabled}
        onPrimaryPress={() => {
          onValidSave(formDataToSave, 'next');
        }}
        secondaryLoading={isLoading}
        secondaryDisabled={isSaveForLaterButtonDisabled}
        secondaryLabel={t(
          `eRecruit:eRecruit.application.otherDetails.saveForLater`,
        )}
        onSecondaryPress={() => {
          onValidSave(formDataToSave);
        }}
      />
      <BlacklistedIDErrorModal
        onCancel={onToggleErrorModal}
        visible={isErrorModalVisible}
      />
    </Column>
  );
}

const Container = styled.View(({ theme: { colors, space, borderRadius } }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    borderRadius: borderRadius.large,
    paddingVertical: space[2],
    backgroundColor: colors.background,
    marginHorizontal: space[isNarrowScreen ? 3 : 4],
    overflow: 'hidden',
  };
});

// ...existing code...

function buildFormDataToSave(
  essentialInfo: EssentialInfoFromStore,
  backgroundDetails: BackgroundDetailsFromStore,
  addressInfo: AddressInfoFromStore,
  candidatePosition: CandidatePositionFromStore,
  emergencyContact: EmergencyContactFromStore,
  bankAccountInfo: BankAccountInfoFromStore,
): InferType<typeof personalDetailsSchema> {
  return {
    identity: {
      fullName: essentialInfo?.identity?.fullName?.trim(),
      gender: essentialInfo?.identity?.gender,
      dateOfBirth: essentialInfo?.identity?.dateOfBirth,
      identity: backgroundDetails?.identity?.identity,
      idNumber: backgroundDetails?.identity?.idNumber,
      birthPlace: backgroundDetails?.identity?.birthPlace,
      religion: backgroundDetails?.identity?.religion,
      maritalStatus: backgroundDetails?.identity?.maritalStatus,
      numberOfDependence: backgroundDetails?.identity?.numberOfDependence,
    },
    contact: {
      countryCode: essentialInfo.contact.countryCode,
      phoneNumber: essentialInfo.contact.phoneNumber,
      email: essentialInfo.contact.email,
      officeNumberCountryCode: essentialInfo.contact.officeNumberCountryCode,
      officePhoneNumber: essentialInfo.contact.officePhoneNumber,
      isBusinessSameAsResidentialAddress:
        addressInfo?.contact?.isBusinessSameAsResidentialAddress,
      address: addressInfo?.contact?.address,
      businessAddress: addressInfo?.contact?.businessAddress,
    },
    personalInformation: {
      ...backgroundDetails.personalInformation,
    },
    candidatePosition: {
      ...candidatePosition.candidatePosition,
    },
    emergencyContact: {
      ...emergencyContact.emergencyContact,
    },
    bankInformation: {
      ...bankAccountInfo.bankInformation,
    },
  };
}
