import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import Input from 'components/Input';
import Portal from 'components/Portal/Portal';
import {
  Box,
  Column,
  H6,
  Row,
  TextField,
  LargeLabel,
} from 'cube-ui-components';
import {} from 'cube-ui-components';
import RecruitYesNoInput from 'features/eRecruit/components/RecruitYesNoInput';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';

import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  AmlAndOtherFromStore,
  ComplianceAndReputationFromStore,
} from 'features/eRecruit/util/store/id/ERecruitStore';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FormFooter from '../../utils/FormFooter';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';

// TBC
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { AmlAndOthersPath } from 'features/eRecruit/util/store/id/types';
import { amlAndOtherSchema } from 'features/eRecruit/id/validations/phone';
import { SharedValue } from 'react-native-reanimated';
import { IDNRegulatorysQuestionsOnly } from 'types/eRecruit';
import { renderLabelByLanguage } from 'utils/helper/translation';
import WebPage2SVG from 'features/eRecruit/assets/WebPage2SVG';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

interface Props {
  onDismiss: () => void;
  value: AmlAndOtherFromStore;
  onDone: (data: AmlAndOtherFromStore) => void;
}

export default function AmlAndOthers({ onDismiss, value, onDone }: Props) {
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes } = useTheme();

  const { isLoading, genderConfig, countryCodeOptions } =
    useGetERecruitOptionListForAppForm();
  const { regulatoryList } = useGetERecruitOptionListForAppForm();
  // const compliAndRepuObj = regulatoryList?.find(i => i.section == 'S-2');
  const amlInfoObj = regulatoryList?.find(i => i.section == 'S-5');
  const hookForm = useForm({
    values: {
      'S-5-1': {
        checked: value?.['S-5-1']?.checked,
        detail: value?.['S-5-1']?.detail,
      },
      'S-5-2': {
        checked: value?.['S-5-2']?.checked,
        detail: value?.['S-5-2']?.detail,
      },
      'S-5-3': {
        checked: value?.['S-5-3']?.checked,
        detail: value?.['S-5-3']?.detail,
      },
      'S-5-4': {
        checked: value?.['S-5-4']?.checked,
        detail: value?.['S-5-4']?.detail,
      },
      'S-5-5': {
        checked: value?.['S-5-5']?.checked,
        detail: value?.['S-5-5']?.detail,
      },
      'S-5-6': {
        checked: value?.['S-5-6']?.checked,
        detail: value?.['S-5-6']?.detail,
      },
    },
    resolver: yupResolver(amlAndOtherSchema),
    mode: 'onBlur',
  });

  const {
    control,
    watch,
    trigger,
    formState: { errors },
    getValues,
    setValue,
  } = hookForm;

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints();
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const submit = useCallback(async () => {
    const isValid = await trigger();
    if (isValid) {
      const currentValues = getValues();
      onDone({
        done: true,
        ...currentValues,
      });

      bottomSheetProps.bottomSheetRef.current?.close();
    } else {
      console.log('Validation failed', errors);
    }
  }, [bottomSheetProps.bottomSheetRef, errors, getValues, onDone, trigger]);

  const mandatoryFields = [
    'S-5-1.checked',
    'S-5-2.checked',
    'S-5-3.checked',
    'S-5-4.checked',
    'S-5-5.checked',
    'S-5-6.checked',
  ] as const satisfies Array<AmlAndOthersPath>;

  const currentForm = getValues();

  const currentFormArray = Object.entries(currentForm) as Array<
    [
      keyof typeof currentForm,
      {
        detail?: string | null | undefined;
        checked: NonNullable<boolean | undefined>;
      },
    ]
  >;
  // assuming the details is needed
  const isCommentShownAndFilled = currentFormArray;
  const isAllMandatoryFieldsFilled = watch(mandatoryFields).every(
    (item: unknown) => item != null,
  );

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          primaryDisabled={!isAllMandatoryFieldsFilled}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel="Done"
        />
      );
    },
    [isAllMandatoryFieldsFilled, submit],
  );
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          index={1}
          onDismiss={onDismiss}
          snapPoints={snapPoints}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <Box px={space[isNarrowScreen ? 3 : 4]}>
            <Row alignItems="center" gap={space[1]}>
              <WebPage2SVG size={space[10]} />

              <H6 color={colors.primary} fontWeight="bold" style={{ flex: 1 }}>
                {renderLabelByLanguage(amlInfoObj?.longDesc)}
              </H6>
            </Row>
          </Box>
          <BottomSheetKeyboardAwareScrollView
            bottomOffset={space[10]}
            style={{
              paddingHorizontal: space[4],
              flex: 1,
            }}>
            <Box paddingY={space[4]}>
              <Column gap={space[6]}>
                {amlInfoObj?.regulatoryList?.map((item, index) => {
                  const fieldKey = item.key as keyof Pick<
                    IDNRegulatorysQuestionsOnly,
                    'S-5-1' | 'S-5-2' | 'S-5-3' | 'S-5-4' | 'S-5-5' | 'S-5-6'
                  >;
                  const isYes = watch(`${fieldKey}.checked`);
                  const isNo = watch(`${fieldKey}.checked`) == false;
                  const questionNumber = index + 1;
                  return (
                    <Box key={fieldKey} gap={space[7]}>
                      <Row>
                        <LargeLabel fontWeight="bold">
                          {`${questionNumber}.  `}
                        </LargeLabel>
                        <Box gap={space[4]} flex={1}>
                          <LargeLabel fontWeight="bold">
                            {renderLabelByLanguage(item?.longDesc)}
                          </LargeLabel>
                          <Box>
                            <RecruitYesNoInput
                              control={control}
                              name={`${fieldKey}.checked`}
                              onChange={() =>
                                setTimeout(() => {
                                  trigger(`${fieldKey}`);
                                }, 250)
                              }
                              shouldHighlightOnUntouched={
                                Input.defaultHighlightCheckForBoolean
                              }
                              initialHighlight={false}
                            />
                          </Box>
                        </Box>
                      </Row>
                      {(questionNumber == 1 && isYes) ||
                      (questionNumber == 2 && isNo) ? (
                        <Input
                          as={TextField}
                          control={control}
                          name={`${fieldKey}.detail`}
                          shouldHighlightOnUntouched={
                            Input.defaultHighlightCheckForBoolean
                          }
                          initialHighlight={false}
                          label={t(
                            'eRecruit.application.otherDetails.regulatory.comment.label',
                          )}
                        />
                      ) : null}
                    </Box>
                  );
                })}
              </Column>
            </Box>
            <BottomSheetFooterSpace />
          </BottomSheetKeyboardAwareScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
