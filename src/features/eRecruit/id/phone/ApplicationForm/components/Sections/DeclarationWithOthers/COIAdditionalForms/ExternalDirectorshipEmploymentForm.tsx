import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  <PERSON>,
  Column,
  H7,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TextField,
} from 'cube-ui-components';
import { useEffect, useState } from 'react';
import {
  FieldArrayMethodProps,
  useFieldArray,
  UseFormReturn,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as yup from 'yup';
import { declarationCOIDefaultValue } from 'features/eRecruit/ib/validations/otherDetailsSchema';
import { TouchableOpacity } from 'react-native';
import React from 'react';
import { COISectionSeparator } from './COISectionSeparator';
import { AddButton } from '../../../utils/AddButton';
import { NewApplicationFormValues } from 'types/eRecruit';
import { CoiHookFormForAdditionalForms } from './types';

export default function ExternalDirectorshipEmploymentForm({
  hookForm,
  maxRecords = 5,
}: {
  hookForm: CoiHookFormForAdditionalForms;
  maxRecords?: number;
}) {
  const {
    control,
    setValue,
    watch,
    formState: { errors },
  } = hookForm;
  const { fields, append, remove } = useFieldArray({
    name: 'conflictOfInterest.externalEmployments',
    control,
  });
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');
  const appendListFocusOption: FieldArrayMethodProps = { shouldFocus: false };

  const isExternalEmploymentsYes = watch(
    'conflictOfInterest.externalEmployment',
  );

  useEffect(() => {
    if (isExternalEmploymentsYes) {
      fields.length === 0 &&
        append(
          {
            ...declarationCOIDefaultValue.conflictOfInterest
              .externalEmployments,
          },
          appendListFocusOption,
        );
      setValue(
        `conflictOfInterest.externalEmployments.0.compensationReceived`,
        false,
      );
    } else {
      fields.length > 0 && remove();
      // remove all item in the list when no index is provided;
    }
  }, [fields, isExternalEmploymentsYes, append, remove]);

  if (isExternalEmploymentsYes == false) {
    return null;
  }

  return (
    <Box minHeight={sizes[10]} gap={space[4]}>
      {fields.map((field, idx) => {
        const compensationReceived = watch(
          `conflictOfInterest.externalEmployments.${idx}.compensationReceived`,
        );
        return (
          <React.Fragment key={field.id}>
            <Column gap={space[4]}>
              <Row justifyContent="space-between">
                <Row alignItems="center" gap={space[2]}>
                  <H7 fontWeight="bold" key={idx}>
                    {t('application.COI.record', {
                      number: (idx ?? 0) + 1,
                    })}
                  </H7>
                </Row>
                {fields?.length === 1 && idx === 0 ? (
                  <TouchableOpacity disabled>
                    <Icon.Delete fill={colors.palette.fwdGreyDark} />
                  </TouchableOpacity>
                ) : (
                  <TouchableOpacity
                    onPress={() => {
                      if (fields?.length === 1) {
                        console.log('last item should not be deleted');
                        return;
                      }
                      remove(idx);
                    }}>
                    <Icon.Delete fill={colors.palette.fwdDarkGreen[100]} />
                  </TouchableOpacity>
                )}
              </Row>

              <Column gap={space[6]}>
                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.externalEmployments.${idx}.nameOfBusiness`}
                  label={t('application.COI.nameOfBusinessEnterpriseOrEntity')}
                  style={{ flex: 1 }}
                />

                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.externalEmployments.${idx}.natureOfBusiness`}
                  label={t('application.COI.natureOfBusiness')}
                  style={{ flex: 1 }}
                />
                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.externalEmployments.${idx}.position`}
                  label={'Position'}
                  style={{ flex: 1 }}
                />

                <Input
                  control={control}
                  as={TextField}
                  name={`conflictOfInterest.externalEmployments.${idx}.details`}
                  label={t('application.COI.details')}
                  style={{ flex: 2 }}
                  hint={t('application.COI.detailsDescription')}
                />
                <Box rowGap={space[2]} flex={1}>
                  <Box paddingLeft={space[3]}>
                    <SmallLabel color={colors.palette.fwdGreyDarker}>
                      {t('application.COI.compensationReceived')}
                    </SmallLabel>
                  </Box>

                  <Row>
                    <Picker
                      type="chip"
                      size="medium"
                      containerStyle={{
                        flex: 1,
                        flexWrap: 'wrap',
                        rowGap: space[2],
                      }}
                      items={[
                        {
                          label: t('application.COI.yes'),
                          value: 'true',
                        },
                        {
                          label: t('application.COI.no'),
                          value: 'false',
                        },
                      ]}
                      value={String(compensationReceived ?? 'false')}
                      onChange={value => {
                        setValue(
                          `conflictOfInterest.externalEmployments.${idx}.compensationReceived`,
                          value == 'true' ? true : false,
                        );
                      }}
                    />
                  </Row>
                </Box>
              </Column>
            </Column>
            {fields?.length > 1 && idx != fields?.length - 1 && (
              <COISectionSeparator />
            )}
          </React.Fragment>
        );
      })}
      <AddButton
        customText={t(`eRecruit.application.occupationDetails.add`)}
        isDisabled={fields.length >= maxRecords}
        isHidden={fields.length >= maxRecords || maxRecords == 1}
        onPress={() => {
          fields.length < maxRecords &&
            append(
              {
                ...declarationCOIDefaultValue.conflictOfInterest
                  .externalEmployments,
              },
              appendListFocusOption,
            );
          setValue(
            `conflictOfInterest.externalEmployments.${fields.length}.compensationReceived`,
            false,
          );
        }}
      />
    </Box>
  );
}
