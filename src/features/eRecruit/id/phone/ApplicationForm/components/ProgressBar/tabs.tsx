import { H6 } from 'cube-ui-components';
import { ERecruitTabs } from 'features/eRecruit/hooks/useGetERecruitTab';
import { IDNCombinedRouteKey } from 'features/eRecruit/types/progressBarTypes';
import { View } from 'react-native';
import CoreProfile from '../Sections/CoreProfile';
import OccupationDetail from '../Sections/OccupationDetail';
import Declaration from '../Sections/Declaration';
import Documents from '../Sections/Documents';
import ReviewInformation from '../Sections/ReviewInformation';
import Consent from '../Sections/Consent';

export const IDNTabs: ERecruitTabs<IDNCombinedRouteKey> = {
  phone: [
    {
      route: 'personalDetails--',
      component: CoreProfile,
    },
    {
      route: 'otherDetails--',
      component: Declaration,
    },
    {
      route: 'reviewInfo--',
      component: () => <></>,
    },
    {
      route: 'documentUpload--',
      component: Documents,
    },
    {
      route: 'consents--',
      component: Consent,
    },
  ],
  tablet: [],
};
