import { UseFormGetValues } from 'react-hook-form';
import { PersonalDetailsSchemaType } from '../tablet/NewApplicationForm/PersonalDetailsTab';
import { TFunction } from 'i18next';
import { AddressExpectedDataForm, addressShapeKeys } from '../type';

type DataMapPassed = {
  email: string;
  phoneNumberWithCode: string;
  idType?: string;
  idNumber?: string;
  npwp?: string;
  bankAccount?: string;
};

export function getErrorMessageFromCode({
  type = 'default',
  errCode,
  t,
  ...rest
}:
  | {
      type?: 'default';
      errCode: string | null | undefined;
      getValues: UseFormGetValues<PersonalDetailsSchemaType>;
      t: TFunction<('eApp' | 'eRecruit')[], undefined, ('eApp' | 'eRecruit')[]>;
    }
  | {
      type: 'dataPassedByMap';
      errCode: string | null | undefined;
      t: TFunction<('eApp' | 'eRecruit')[], undefined, ('eApp' | 'eRecruit')[]>;
      dataMap: DataMapPassed;
    }) {
  // Your mapping object (assuming t and getValues are available in scope)
  const getValues =
    type == 'default' && 'getValues' in rest ? rest?.getValues : undefined;

  const email = getValues
    ? getValues('contact.email') || '--'
    : type == 'dataPassedByMap' && 'dataMap' in rest
    ? rest?.dataMap?.['email']
    : '--';
  const phoneNumberWithCode = getValues
    ? getValues('contact.countryCode') +
      ' ' +
      (getValues('contact.phoneNumber') || '--')
    : type == 'dataPassedByMap' && 'dataMap' in rest
    ? rest?.dataMap?.['phoneNumberWithCode']
    : '--';

  const idType = getValues
    ? getValues('identity.identity') || '--'
    : type == 'dataPassedByMap' && 'dataMap' in rest
    ? rest?.dataMap?.['idType']
    : '--';
  const idNumber = getValues
    ? getValues('identity.idNumber') || '--'
    : type == 'dataPassedByMap' && 'dataMap' in rest
    ? rest?.dataMap?.['idNumber']
    : '--';
  const npwp = getValues
    ? getValues('personalInformation.npwp') || '--'
    : type == 'dataPassedByMap' && 'dataMap' in rest
    ? rest?.dataMap?.['npwp']
    : '--';
  const bankAccount = '--';

  const idErrorMsg = t(
    'eRecruit:eRecruit.application.errorCode.ID_NUMBER_REGISTERED',
    { idType, idNumber },
  );

  const eRecruitDupCodesMapping: Record<ERecruitDupCodes, string> = {
    USER_EMAIL_IS_USED: t(
      'eRecruit:eRecruit.application.errorCode.USER_EMAIL_IS_USED',
      { email },
    ),
    USER_PHONE_IS_USED: t(
      'eRecruit:eRecruit.application.errorCode.USER_PHONE_IS_USED',
      { phoneNumberWithCode },
    ),
    ID_NUMBER_REGISTERED: idErrorMsg,
    NPWP_IS_REGISTERED: t(
      'eRecruit:eRecruit.application.errorCode.NPWP_IS_REGISTERED',
      { npwp },
    ),
    BANK_ACCOUNT_IS_REGISTERED: t(
      'eRecruit:eRecruit.application.errorCode.BANK_ACCOUNT_IS_REGISTERED',
      { bankAccount },
    ),
    REGISTRATION_STAGING_NRIC_BLACKLIST: t(
      'eRecruit:eRecruit.application.personalDetails.submissionError',
    ),
    REGISTRATION_STAGING_NRIC_ALREADY_REGISTERED: idErrorMsg,
  };

  return errCode && errCode in eRecruitDupCodesMapping
    ? eRecruitDupCodesMapping[errCode as ERecruitDupCodes]
    : t('eRecruit:eRecruit.pleaseTryAgainLater');
}

const eRecruitDupCodes = [
  'USER_EMAIL_IS_USED',
  'USER_PHONE_IS_USED',
  'ID_NUMBER_REGISTERED',
  'NPWP_IS_REGISTERED',
  'BANK_ACCOUNT_IS_REGISTERED',
  'REGISTRATION_STAGING_NRIC_BLACKLIST',
  'REGISTRATION_STAGING_NRIC_ALREADY_REGISTERED',
] as const;

type ERecruitDupCodes = (typeof eRecruitDupCodes)[number];

export function createAddressInput(
  source: Record<string, unknown>,
): AddressExpectedDataForm {
  const address: AddressExpectedDataForm = {} as AddressExpectedDataForm;
  addressShapeKeys.forEach(key => {
    address[key] = source?.[key] ?? '';
  });
  return address;
}
