import {
  ApplicationFormResponds,
  IDNApproverComments,
  IDNRegulatorysFull,
  IDNWorkingExperience,
} from 'types/eRecruit';
import { ParsingAppFormDataActionKeys } from 'types/eRecruit';
import { AgencyType, Designation } from 'features/eRecruit/ib/type';
import { cloneDeep } from 'utils/helper/objectUtil';
import { format } from 'date-fns';
import getLabelFromValue from 'utils/helper/getLabelFromValue';
import { InferType } from 'yup';
import { otherDetailsSchema } from 'features/eRecruit/id/validations/otherDetailsSchema';

// converting FE hookform data to fit backend requirements
//
export type InputForParsingOtherDetailsData = InferType<
  typeof otherDetailsSchema
>;
export function parsingOtherDetailsData({
  input,
  recruitmentCache,
  pressAction,
}: {
  input: InputForParsingOtherDetailsData;
  recruitmentCache?: ApplicationFormResponds;
  pressAction?: ParsingAppFormDataActionKeys;
}) {
  const stage = pressAction === 'next' ? 'DOCUMENT' : 'OTHER_DETAILS';

  if (!recruitmentCache) {
    return;
  }
  const recruitmentCacheClone = cloneDeep(recruitmentCache);

  const workExperience = buildWorkExperience(input);

  // We don't have comment input in other details tab atm
  const commentInput = {
    approvalComments: input?.candidatePosition?.optionalComment
      ? [
          {
            comment: input?.candidatePosition?.optionalComment,
          },
        ]
      : [],
  } satisfies IDNApproverComments;

  const ownershipInterestsInForm =
    input?.conflictOfInterest?.ownershipInterests?.[0];
  const externalEmploymentInForm =
    input?.conflictOfInterest?.externalEmployments?.[0];
  const businessAffiliationInterestsInForm =
    input?.conflictOfInterest?.businessAffiliationInterests?.[0];
  const relationshipGovernmentOfficialsInForm =
    input?.conflictOfInterest?.relationshipGovernmentOfficials?.[0];
  const otherInterestInForm = input?.conflictOfInterest?.otherInterests?.[0];
  const parsedWorkExperience = workExperience satisfies Omit<
    IDNWorkingExperience,
    'yearOfExperience' | 'presentOccupation' | 'industry' | 'type'
  >[];

  const regulatoryInput = {
    regulatorys: {
      // various regulatory-------
      'S-1-1': {
        checked: input?.regulatorys['S-1-1']?.checked,
        detail: input?.regulatorys['S-1-1']?.detail ?? null,
      },
      'S-1-2': {
        checked: input?.regulatorys['S-1-2']?.checked,
        detail: input?.regulatorys['S-1-2']?.detail ?? null,
      },
      'S-2-1': {
        checked: input?.regulatorys['S-2-1']?.checked,
        detail: input?.regulatorys['S-2-1']?.detail ?? null,
      },
      'S-2-2': {
        checked: input?.regulatorys['S-2-2']?.checked,
        detail: input?.regulatorys['S-2-2']?.detail ?? null,
      },
      'S-2-3': {
        checked: input?.regulatorys['S-2-3']?.checked,
        detail: input?.regulatorys['S-2-3']?.detail ?? null,
      },
      'S-4-1': {
        checked: input?.regulatorys['S-4-1']?.checked,
        detail: input?.regulatorys['S-4-1']?.detail ?? null,
      },
      'S-5-1': {
        checked: input?.regulatorys['S-5-1']?.checked,
        detail: input?.regulatorys['S-5-1']?.detail ?? null,
      },
      'S-5-2': {
        checked: input?.regulatorys['S-5-2']?.checked,
        detail: input?.regulatorys['S-5-2']?.detail ?? null,
      },
      'S-5-3': {
        checked: input?.regulatorys['S-5-3']?.checked,
        detail: input?.regulatorys['S-5-3']?.detail ?? null,
      },
      'S-5-4': {
        checked: input?.regulatorys['S-5-4']?.checked,
        detail: input?.regulatorys['S-5-4']?.detail ?? null,
      },
      'S-5-5': {
        checked: input?.regulatorys['S-5-5']?.checked,
        detail: input?.regulatorys['S-5-5']?.detail ?? null,
      },
      'S-5-6': {
        checked: input?.regulatorys['S-5-6']?.checked,
        detail: input?.regulatorys['S-5-6']?.detail ?? null,
      },

      // COI-------
      'S-3-1': {
        checked: input?.conflictOfInterest?.ownershipInterest,
        answer:
          ownershipInterestsInForm &&
          input?.conflictOfInterest?.ownershipInterest
            ? {
                s31DateAcquired: format(
                  new Date(ownershipInterestsInForm?.dateAcquired),
                  'yyyy-MM-dd',
                ),
                s31PercentageOfOwnership:
                  ownershipInterestsInForm?.percentageOfOwnership?.toString(),
                s31NameOfBusiness: ownershipInterestsInForm?.nameOfBusiness,
                s31NameOfOwner: ownershipInterestsInForm?.nameOfOwner,
                s31NatureOfBusiness: ownershipInterestsInForm?.natureOfBusiness,
              }
            : undefined,
      },
      'S-3-2': {
        checked: input?.conflictOfInterest?.externalEmployment,
        answer:
          externalEmploymentInForm &&
          input?.conflictOfInterest?.externalEmployment
            ? {
                s32Details: externalEmploymentInForm?.details,
                s32Position: externalEmploymentInForm?.position,
                s32CompensationReceived:
                  externalEmploymentInForm?.compensationReceived
                    ? 'true'
                    : 'false',
                s32NatureOfBusiness: externalEmploymentInForm?.natureOfBusiness,
                s32NameOfBusiness: externalEmploymentInForm?.nameOfBusiness,
              }
            : undefined,
      },
      'S-3-3': {
        checked: input?.conflictOfInterest?.businessAffiliationInterest,
        answer:
          businessAffiliationInterestsInForm &&
          input?.conflictOfInterest?.businessAffiliationInterest
            ? {
                s33DateCommencement: format(
                  new Date(
                    businessAffiliationInterestsInForm?.dateCommencementEmployment,
                  ),
                  'yyyy-MM-dd',
                ),

                s33NameOfFamily:
                  businessAffiliationInterestsInForm?.nameOfFamilyMember,
                s33NatureOfBusiness:
                  businessAffiliationInterestsInForm?.natureOfBusiness,
                s33Position:
                  businessAffiliationInterestsInForm?.positionDepartment,
                s33NameOfBusiness:
                  businessAffiliationInterestsInForm?.nameOfBusiness,
              }
            : undefined,
      },
      'S-3-4': {
        checked: input?.conflictOfInterest?.relationshipGovernmentOfficial,
        answer:
          relationshipGovernmentOfficialsInForm &&
          input?.conflictOfInterest?.relationshipGovernmentOfficial
            ? {
                s34Position:
                  relationshipGovernmentOfficialsInForm?.positionDepartment,
                s34NameOfGovernment:
                  relationshipGovernmentOfficialsInForm?.nameOfGovernment,
                s34Relationship:
                  relationshipGovernmentOfficialsInForm?.relationship,
              }
            : undefined,
      },
      'S-3-5': {
        checked: input?.conflictOfInterest?.otherInterest,
        detail: input?.conflictOfInterest?.otherInterest
          ? otherInterestInForm?.details
          : undefined,
        // answer: 'testing answer',
      },
    },
  } satisfies IDNRegulatorysFull;

  const parsedObj = {
    ...recruitmentCacheClone,
    workingExperiences: [
      // IDN has only one type of work exp now
      {
        ...recruitmentCacheClone.workingExperiences?.[0],
        ...parsedWorkExperience?.[0],
      },
    ],
    stage,
    ...commentInput,
    ...regulatoryInput,
  } satisfies ApplicationFormResponds;

  return parsedObj;
}

export type ResultOfConvertedSavedApplicationForHookForm = ReturnType<
  typeof convertSavedApplicationForHookForm
>;

// converting saved BE application date to allow FE to prefill
export function convertSavedApplicationForHookForm({
  parsedObj,
  currentAgentCode,
}: {
  parsedObj: ApplicationFormResponds;
  currentAgentCode: string | null;
}) {
  return {
    insuranceExperience: {
      haveGeneralInsuranceExp:
        // For IDN, there is only one type of work experience, yet TBC any better way to handle than pointing [0]
        parsedObj.workingExperiences?.[0]?.isHaveExpGeneInsurance ??
        (undefined as any),
      haveLifeInsuranceExp:
        // For IDN, there is only one type of work experience, yet TBC any better way to handle than pointing [0]
        parsedObj.workingExperiences?.[0]?.isHaveExpLifeInsurance ??
        (undefined as any),
    },
    conflictOfInterest: {
      ownershipInterest: parsedObj?.regulatorys?.['S-3-1']?.checked as boolean,
      externalEmployment: parsedObj?.regulatorys?.['S-3-2']?.checked as boolean,
      businessAffiliationInterest: parsedObj?.regulatorys?.['S-3-3']
        ?.checked as boolean,
      relationshipGovernmentOfficial: parsedObj?.regulatorys?.['S-3-4']
        ?.checked as boolean,
      otherInterest: parsedObj?.regulatorys?.['S-3-5']?.checked as boolean,
      ownershipInterests: parsedObj?.regulatorys?.['S-3-1']?.answer
        ? [
            {
              nameOfBusiness:
                parsedObj?.regulatorys?.['S-3-1']?.answer?.s31NameOfBusiness,
              natureOfBusiness:
                parsedObj?.regulatorys?.['S-3-1']?.answer?.s31NatureOfBusiness,
              nameOfOwner:
                parsedObj?.regulatorys?.['S-3-1']?.answer?.s31NameOfOwner,
              percentageOfOwnership: parseFloat(
                parsedObj?.regulatorys?.['S-3-1']?.answer
                  ?.s31PercentageOfOwnership,
              ),
              dateAcquired:
                parsedObj?.regulatorys?.['S-3-1']?.answer?.s31DateAcquired,
              // relationship: '', // Add a default or appropriate value for 'relationship'
            },
          ]
        : [],
      externalEmployments: parsedObj?.regulatorys?.['S-3-2']?.answer
        ? [
            {
              nameOfBusiness:
                parsedObj?.regulatorys?.['S-3-2']?.answer?.s32NameOfBusiness,
              natureOfBusiness:
                parsedObj?.regulatorys?.['S-3-2']?.answer?.s32NatureOfBusiness,
              position: parsedObj?.regulatorys?.['S-3-2']?.answer?.s32Position,
              details: parsedObj?.regulatorys?.['S-3-2']?.answer?.s32Details,
              compensationReceived:
                parsedObj?.regulatorys?.['S-3-2']?.answer
                  ?.s32CompensationReceived === 'true',
            },
          ]
        : [],
      businessAffiliationInterests: parsedObj?.regulatorys?.['S-3-3']?.answer
        ? [
            {
              nameOfBusiness:
                parsedObj?.regulatorys?.['S-3-3']?.answer?.s33NameOfBusiness,
              natureOfBusiness:
                parsedObj?.regulatorys?.['S-3-3']?.answer?.s33NatureOfBusiness,
              nameOfFamilyMember:
                parsedObj?.regulatorys?.['S-3-3']?.answer?.s33NameOfFamily,
              positionDepartment:
                parsedObj?.regulatorys?.['S-3-3']?.answer?.s33Position,
              dateCommencementEmployment:
                parsedObj?.regulatorys?.['S-3-3']?.answer?.s33DateCommencement,
              // relationship: '',
            },
          ]
        : [],
      relationshipGovernmentOfficials: parsedObj?.regulatorys?.['S-3-4']?.answer
        ? [
            {
              nameOfGovernment:
                parsedObj?.regulatorys?.['S-3-4']?.answer?.s34NameOfGovernment,
              positionDepartment:
                parsedObj?.regulatorys?.['S-3-4']?.answer?.s34Position,
              relationship:
                parsedObj?.regulatorys?.['S-3-4']?.answer?.s34Relationship,
            },
          ]
        : [],
      otherInterests: parsedObj?.regulatorys?.['S-3-5']?.detail
        ? [
            {
              details: parsedObj?.regulatorys?.['S-3-5']?.detail,
            },
          ]
        : [],
    },
    regulatorys: {
      'S-1-1': {
        checked: parsedObj?.regulatorys?.['S-1-1']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-1-1']?.detail as string,
      },
      'S-1-2': {
        checked: parsedObj?.regulatorys?.['S-1-2']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-1-2']?.detail as string,
      },
      'S-2-1': {
        checked: parsedObj?.regulatorys?.['S-2-1']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-2-1']?.detail as string,
      },
      'S-2-2': {
        checked: parsedObj?.regulatorys?.['S-2-2']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-2-2']?.detail as string,
      },
      'S-2-3': {
        checked: parsedObj?.regulatorys?.['S-2-3']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-2-3']?.detail as string,
      },
      'S-4-1': {
        checked: parsedObj?.regulatorys?.['S-4-1']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-4-1']?.detail as string,
      },
      'S-5-1': {
        checked: parsedObj?.regulatorys?.['S-5-1']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-5-1']?.detail as string,
      },
      'S-5-2': {
        checked: parsedObj?.regulatorys?.['S-5-2']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-5-2']?.detail as string,
      },
      'S-5-3': {
        checked: parsedObj?.regulatorys?.['S-5-3']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-5-3']?.detail as string,
      },
      'S-5-4': {
        checked: parsedObj?.regulatorys?.['S-5-4']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-5-4']?.detail as string,
      },
      'S-5-5': {
        checked: parsedObj?.regulatorys?.['S-5-5']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-5-5']?.detail as string,
      },
      'S-5-6': {
        checked: parsedObj?.regulatorys?.['S-5-6']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-5-6']?.detail as string,
      },
    },
    candidatePosition: {
      optionalComment: currentAgentCode
        ? parsedObj?.approvalComments?.find(
            section => section.approverAgentCode === currentAgentCode,
          )?.comment ?? null
        : null,
    },
  } satisfies InferType<typeof otherDetailsSchema> as InferType<
    typeof otherDetailsSchema
  >;
}

//  ========================== helper functions
// only agency right now not banca
export function assignCandidateBranchCode({
  recruiterDesignation,
  selectedAgencyType,
  recruiterBranchCode,
  selectedBranchCode,
  branchList,
}: {
  recruiterDesignation: Designation;
  selectedAgencyType: AgencyType;
  recruiterBranchCode: string;
  selectedBranchCode: string;
  branchList: {
    label: string;
    value: string;
  }[];
}) {
  const isValidRecruiterBranchCode = getLabelFromValue(
    branchList,
    recruiterBranchCode,
  );
  const isValidSelectedBranchCode = getLabelFromValue(
    branchList,
    selectedBranchCode,
  );

  if (selectedAgencyType === 'FA' || selectedBranchCode === 'FA') {
    return 'FA';
  }
  if (recruiterDesignation === 'RD' && !selectedBranchCode) {
    // recruiter designation is RD and no selected branch code
    return '';
  }
  // return selectedBranchCode if it is not empty or return recruiterBranchCode
  return (
    (isValidSelectedBranchCode && selectedBranchCode) ||
    (isValidRecruiterBranchCode && recruiterBranchCode) ||
    ''
  );
}

export function buildWorkExperience(
  input: InferType<typeof otherDetailsSchema>,
) {
  const workingExp = [
    {
      isHaveExpGeneInsurance:
        input?.insuranceExperience?.haveGeneralInsuranceExp,
      isHaveExpLifeInsurance: input?.insuranceExperience?.haveLifeInsuranceExp,
    },
  ];

  return workingExp satisfies Omit<
    IDNWorkingExperience,
    'yearOfExperience' | 'presentOccupation' | 'industry' | 'type'
  >[];
}
