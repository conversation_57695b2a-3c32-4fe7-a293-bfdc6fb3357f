import { t } from 'i18next';
import { object, string, boolean, array, number, TestContext } from 'yup';
import { IDNRegulatorysQuestionsOnly } from 'types/eRecruit';

export const INVALID_INPUT = t('form.invalidFormat');

// Number Constants
export const maxNameLength = 30;
export const maxIcNumberLength = 12;
const MAX_ADDRESS_LINE_LENGTH = 30;

// Regex Constants
export const isZeroToNine = /^[0-9]+$/;
// const addressLinesRegex = /^\d[0-9\s\-.]*\d$/;
const addressLinesRegex = /^[a-zA-Z0-9,.\s/-]*$/;
const districtRegex = /^[a-zA-Z0-9,./.\-\s]*$/;

// Error Messages
export const icNumberErrorMsg = t('form.icNumberLimitCharacters');
export const icNumberOnly = t('form.inputNumberOnly');
const invalidFormatErr = t('form.invalidInputFormat');
// const ADDRESS_LENGTH_ERROR = `Address length should not exceed ${MAX_ADDRESS_LINE_LENGTH} characters`;
const addressLengthErrorBuilder = (len: number) =>
  t('eRecruit:validation.address.notExceed', {
    length: len,
  });
const REQUIRED_INPUT = t('validation.required');
export const normalizedSpaceString = () =>
  string().matches(/^((?!(^\s+|[\s*]{2,}|\s+$)).)*$/, {
    message: t('form.invalidFormat'),
    excludeEmptyString: true,
  });

export const normalizedSpaceStringIncludeLineBreak = () =>
  string().matches(/^((?!(^\s+|[\s*]{2,}|\s+$)).)*$/s, {
    message: INVALID_INPUT,
    excludeEmptyString: true,
  });

export const percentageGeneralMsg = t(
  'eRecruit:validation.invalidPercentRange',
);

// Common Schema
const COIMaxStringInput = normalizedSpaceString()
  .max(255, t('form.shouldNotExceedSomeNumber', { number: 255 }))
  .required(REQUIRED_INPUT);
const COIOtherDetailsMaxStringInput = normalizedSpaceStringIncludeLineBreak()
  .max(255, t('form.shouldNotExceedSomeNumber', { number: 255 }))
  .required(REQUIRED_INPUT);

// --------- address Schema START -----------

const createAddressSchema = ({ isMandatory }: { isMandatory: boolean }) => {
  const baseStringSchema = (
    maxLength: number,
    regex?: RegExp,
    customMessage?: string,
  ) => {
    let schema = string().max(maxLength, addressLengthErrorBuilder(maxLength));

    if (regex) {
      schema = schema.matches(regex, {
        message: customMessage || t('form.invalidFormat'),
        excludeEmptyString: true,
      });
    }

    return isMandatory
      ? schema.required(REQUIRED_INPUT)
      : schema.nullable().notRequired();
  };

  const baseNumberStringSchema = (config: NumberStringSchemaConfig) => {
    const { minLength, maxLength, customValidations } = config;

    const regex = config.type === 'skipRegex' ? undefined : config.regex;
    const errorMessage =
      config.type === 'skipRegex' ? undefined : config.errorMessage;

    let schema = string();
    if (regex) {
      schema = schema.matches(regex, errorMessage);
    }
    if (isMandatory) {
      schema = schema.required(REQUIRED_INPUT);
    } else {
      schema = schema
        .transform(value => (value === '' ? null : value))
        .nullable()
        .notRequired() as any;
    }

    return schema
      .min(
        minLength,
        customValidations?.minLengthMessage ||
          t('form.mustBeAtLeast', { minPhoneNumberlength: minLength }),
      )
      .max(
        maxLength,
        customValidations?.maxLengthMessage ||
          t('form.shouldNotExceedSomeNumber', { number: maxLength }),
      );
  };

  return object().shape({
    line1: baseStringSchema(MAX_ADDRESS_LINE_LENGTH, addressLinesRegex),
    line2: string()
      .nullable()
      .notRequired()
      .matches(addressLinesRegex, {
        message: t('form.invalidFormat'),
        excludeEmptyString: true,
      })
      .max(
        MAX_ADDRESS_LINE_LENGTH,
        addressLengthErrorBuilder(MAX_ADDRESS_LINE_LENGTH),
      ),

    neighborhoodAssociation: baseNumberStringSchema({
      minLength: 1,
      maxLength: 5,
      regex: isZeroToNine,
      errorMessage: icNumberOnly,
    }),

    communityAssociation: baseNumberStringSchema({
      minLength: 1,
      maxLength: 5,
      regex: isZeroToNine,
      errorMessage: icNumberOnly,
    }),

    subDistrict: baseStringSchema(30, districtRegex),
    district: baseStringSchema(30, districtRegex),
    province: isMandatory
      ? string().required(REQUIRED_INPUT)
      : string().nullable().notRequired(),
    city: isMandatory
      ? string().required(REQUIRED_INPUT)
      : string().nullable().notRequired(),

    postCode: baseNumberStringSchema({
      type: 'skipRegex',
      minLength: 1,
      maxLength: 10,
      customValidations: {
        minLengthMessage: t('form.mustBeAtLeast', { minPhoneNumberlength: 1 }),
        maxLengthMessage: t('form.shouldNotExceedSomeNumber', { number: 10 }),
      },
    }).matches(/^[0-9]+$/, invalidFormatErr),
  });
};

type RegulatoryDetailRequirement =
  | 'checkedTrueToShow'
  | 'checkedFalseToShow'
  | 'never';

export function getRegulatorysScheme(requirement: RegulatoryDetailRequirement) {
  switch (requirement) {
    case 'checkedTrueToShow':
      return object().shape({
        checked: boolean().required(REQUIRED_INPUT),
        detail: string().when('checked', {
          is: true,
          then: schema =>
            schema
              .required(REQUIRED_INPUT)
              .transform(value => (value ? value : '')),
          otherwise: schema => schema.nullable(),
        }),
      });
    case 'checkedFalseToShow':
      return object().shape({
        checked: boolean().required(REQUIRED_INPUT),
        detail: string().when('checked', {
          is: false,
          then: schema =>
            schema
              .required(REQUIRED_INPUT)
              .transform(value => (value ? value : '')),
          otherwise: schema => schema.nullable(),
        }),
      });
    case 'never':
    default:
      return object().shape({
        checked: boolean().required(REQUIRED_INPUT),
        detail: string().nullable(),
      });
  }
}

export const insuranceExpShape = {
  haveLifeInsuranceExp: boolean().required(REQUIRED_INPUT),
  haveGeneralInsuranceExp: boolean().required(REQUIRED_INPUT),
};
export const coiShape = {
  ownershipInterest: boolean().required(REQUIRED_INPUT),
  externalEmployment: boolean().required(REQUIRED_INPUT),
  businessAffiliationInterest: boolean().required(REQUIRED_INPUT),
  relationshipGovernmentOfficial: boolean().required(REQUIRED_INPUT),
  otherInterest: boolean().required(REQUIRED_INPUT),
  ownershipInterests: array()
    .of(
      object().shape({
        nameOfBusiness: COIMaxStringInput.validateCompanyName(INVALID_INPUT),
        natureOfBusiness: COIMaxStringInput,
        nameOfOwner: COIMaxStringInput,
        // relationship: COIMaxStringInput,
        percentageOfOwnership: number()
          .typeError(percentageGeneralMsg)
          .max(100, percentageGeneralMsg)
          .min(5, percentageGeneralMsg)
          .required(REQUIRED_INPUT),
        dateAcquired: COIMaxStringInput,
      }),
    )
    .when('ownershipInterest', {
      is: true,
      then: schema => schema.min(1, REQUIRED_INPUT),
      otherwise: schema => schema.notRequired(),
    }),
  externalEmployments: array()
    .of(
      object().shape({
        nameOfBusiness: COIMaxStringInput.validateCompanyName(INVALID_INPUT),
        natureOfBusiness: COIMaxStringInput,
        position: COIMaxStringInput,
        details: COIMaxStringInput,
        compensationReceived: boolean().nullable().required(REQUIRED_INPUT),
      }),
    )
    .when('externalEmployment', {
      is: true,
      then: schema => schema.min(1, REQUIRED_INPUT),
      otherwise: schema => schema.notRequired(),
    }),
  businessAffiliationInterests: array()
    .of(
      object().shape({
        nameOfBusiness: COIMaxStringInput.validateCompanyName(INVALID_INPUT),
        natureOfBusiness: COIMaxStringInput,
        nameOfFamilyMember: COIMaxStringInput,
        // relationship: COIMaxStringInput,
        positionDepartment: COIMaxStringInput,
        dateCommencementEmployment: COIMaxStringInput,
      }),
    )
    .when('businessAffiliationInterest', {
      is: true,
      then: schema => schema.min(1, REQUIRED_INPUT),
      otherwise: schema => schema.notRequired(),
    }),
  relationshipGovernmentOfficials: array()
    .of(
      object().shape({
        nameOfGovernment: COIMaxStringInput.validateCompanyName(INVALID_INPUT),
        positionDepartment: COIMaxStringInput,
        relationship: COIMaxStringInput,
      }),
    )
    .when('relationshipGovernmentOfficial', {
      is: true,
      then: schema => schema.min(1, REQUIRED_INPUT),
      otherwise: schema => schema.notRequired(),
    }),
  otherInterests: array()
    .of(
      object().shape({
        details: COIOtherDetailsMaxStringInput,
      }),
    )
    .when('otherInterest', {
      is: true,
      then: schema => schema.min(1, REQUIRED_INPUT),
      otherwise: schema => schema.notRequired(),
    }),
};

export function doneChecker(
  mode: RegulatoryDetailRequirement,
  data: {
    detail?: string | null | undefined;
    checked: NonNullable<boolean | undefined>;
  },
) {
  if (mode == 'checkedFalseToShow') {
    return (
      data.checked == true || Boolean(data.checked == false && data.detail)
    );
  }
  if (mode == 'checkedTrueToShow') {
    return data.checked == false || Boolean(data.checked && data.detail);
  }

  return false;
}

export const financialRegulatory = {
  'S-1-1': getRegulatorysScheme('checkedTrueToShow'),
  'S-1-2': getRegulatorysScheme('checkedTrueToShow'),
};
export const compliRegulatory = {
  'S-2-1': getRegulatorysScheme('checkedTrueToShow'),
  'S-2-2': getRegulatorysScheme('checkedTrueToShow'),
  'S-2-3': getRegulatorysScheme('checkedTrueToShow'),
};
export const healthRegulatory = {
  'S-4-1': getRegulatorysScheme('checkedTrueToShow'),
};
export const amlRegulatory = {
  'S-5-1': getRegulatorysScheme('checkedTrueToShow'),
  'S-5-2': getRegulatorysScheme('checkedFalseToShow'),
  'S-5-3': getRegulatorysScheme('never'),
  'S-5-4': getRegulatorysScheme('never'),
  'S-5-5': getRegulatorysScheme('never'),
  'S-5-6': getRegulatorysScheme('never'),
};

export const regulatoryShape = {
  // S-1 financial
  ...financialRegulatory,
  // S-2 compli
  ...compliRegulatory,
  // S-4 health
  ...healthRegulatory,
  // S-5 AmlAndOthers
  ...amlRegulatory,
} satisfies Record<keyof IDNRegulatorysQuestionsOnly, any>;

export const remarkOptionalShape = {
  optionalComment: string()
    .nullable()
    .max(255, t('form.shouldNotExceedSomeNumber', { number: 255 }))
    .notRequired(),
};
export const otherDetailsSchema = object().shape({
  insuranceExperience: object().shape(insuranceExpShape),
  conflictOfInterest: object().shape(coiShape),
  regulatorys: object().shape(regulatoryShape),
  candidatePosition: object().shape(remarkOptionalShape),
});

type NumberStringSchemaConfig =
  | {
      type?: 'default';
      minLength: number;
      maxLength: number;
      regex: RegExp;
      errorMessage: string;
      customValidations?: {
        minLengthMessage?: string;
        maxLengthMessage?: string;
      };
    }
  | {
      type: 'skipRegex';
      minLength: number;
      maxLength: number;

      customValidations?: {
        minLengthMessage?: string;
        maxLengthMessage?: string;
      };
    };
