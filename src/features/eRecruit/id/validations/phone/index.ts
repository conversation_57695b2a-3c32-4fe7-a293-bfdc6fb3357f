import {
  identityShape,
  personalInfoShape,
  contactShape,
  candidatePositionShape,
  emergencyContactShape,
  bankInfoShape,
} from 'features/eRecruit/id/validations/personalDetailsSchema';

import { InferType, object } from 'yup';
import {
  coiShape,
  insuranceExpShape,
  remarkOptionalShape,
  healthRegulatory,
  financialRegulatory,
  compliRegulatory,
  amlRegulatory,
} from '../otherDetailsSchema';

export const essentialInformationSchema = object().shape({
  // Create a new 'identity' object with only the fields you need
  identity: object().shape({
    fullName: identityShape.fullName,
    gender: identityShape.gender,
    dateOfBirth: identityShape.dateOfBirth,
  }),

  // Create a new 'contact' object with only the fields you need
  contact: object().shape({
    countryCode: contactShape.countryCode,
    phoneNumber: contactShape.phoneNumber,
    email: contactShape.email,
    officeNumberCountryCode: contactShape.officeNumberCountryCode,
    officePhoneNumber: contactShape.officePhoneNumber,
  }),
});

export const backgroundDetailsSchema = object().shape({
  identity: object().shape({
    identity: identityShape.identity,
    idNumber: identityShape.idNumber,
    birthPlace: identityShape.birthPlace,
    religion: identityShape.religion,
    maritalStatus: identityShape.maritalStatus,
    numberOfDependence: identityShape.numberOfDependence,
  }),

  personalInformation: object().shape({
    education: personalInfoShape.education,
    industry: personalInfoShape.industry,
    presentOccupation: personalInfoShape.presentOccupation,
    npwp: personalInfoShape.npwp,
  }),
});

export const addressInformationSchema = object().shape({
  contact: object().shape({
    address: contactShape.address,
    isBusinessSameAsResidentialAddress:
      contactShape.isBusinessSameAsResidentialAddress,
    businessAddress: contactShape.businessAddress,
  }),
});

export const candidatePositionSchema = object().shape({
  candidatePosition: object().shape(candidatePositionShape),
});

export const emergencyContactSchema = object().shape({
  emergencyContact: object().shape(emergencyContactShape),
});

export const bankInformationSchema = object().shape({
  bankInformation: object().shape(bankInfoShape),
});

export const insuranceExperienceSchema = object().shape({
  insuranceExperience: object().shape(insuranceExpShape),
});
// ----VVVVV regulatoryShape---
export const healthConditionSchema = object().shape(healthRegulatory);
export const financialConditionSchema = object().shape(financialRegulatory);
export const complianceAndReputationSchema = object().shape(compliRegulatory);
export const amlAndOtherSchema = object().shape(amlRegulatory);
// ----regulatoryShape ^^^^ ---

export const declarationOfInterestSchema = object().shape({
  conflictOfInterest: object().shape(coiShape),
});
export const remarkOptionSchema = object().shape(remarkOptionalShape);
