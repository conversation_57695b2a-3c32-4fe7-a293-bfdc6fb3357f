import { SvgIconProps } from 'cube-ui-components';

interface ProgressBase<Key> {
  routeKey: Key;
  title: string;
  disabled?: boolean;
  completed?: boolean;
  withDot?: boolean;
  icon?: React.ComponentType<SvgIconProps> | React.ReactElement<SvgIconProps>;
}

export interface ProgressItem<K = never>
  extends ProgressBase<RouteItemKey | K> {
  error?: boolean;
  barTitle?: string;
  pressable?: boolean;
  hidden?: boolean;
}

export interface ProgressSubgroup<K = never>
  extends ProgressBase<RouteSubgroupKey | K> {
  items: Array<ProgressItem>;
}

export interface ProgressGroup<K = never> extends ProgressBase<RouteGroupKey> {
  items: Array<ProgressSubgroup<K> | ProgressItem<K>>;
  showSubProgress?: boolean;
  full?: boolean;
}

export interface ProgressGroupWithItems<K = never>
  extends ProgressBase<RouteGroupKey | K> {
  items: Array<ProgressSubgroup<K> | ProgressItem<K>>;
  showSubProgress?: boolean;
  full?: boolean;
}

export type RouteGroupKey = IBRouteGroupKey;
export type RouteSubgroupKey = IBRouteSubgroupKey | IdnRouteSubgroupKey;
export type RouteItemKey = IBRouteItemKey;
export type CombinedRouteKey = `${RouteGroupKey | ReviewInfoKey | ''}-${
  | RouteSubgroupKey
  | ''}-${RouteItemKey | ''}`;

export interface CheckApplicationProgressItem
  extends ProgressBase<CheckApplicationRouteItemKey> {
  error?: boolean;
  barTitle?: string;
  pressable?: boolean;
  hidden?: boolean;
}

export interface CheckApplicationProgressSubgroup
  extends ProgressBase<CheckApplicationRouteSubgroupKey> {
  items: Array<CheckApplicationProgressItem>;
}

export interface CheckApplicationProgressGroup
  extends ProgressBase<CheckApplicationRouteGroupKey> {
  items: Array<CheckApplicationProgressSubgroup | CheckApplicationProgressItem>;
  showSubProgress?: boolean;
  full?: boolean;
}

export type CheckApplicationRouteGroupKey = IBCheckApplicationRouteGroupKey;
export type CheckApplicationRouteSubgroupKey =
  IBCheckApplicationRouteSubgroupKey;
export type CheckApplicationRouteItemKey = IBCheckApplicationRouteItemKey;
export type CheckApplicationCombinedRouteKey = `${
  | CheckApplicationRouteGroupKey
  | ''}-${CheckApplicationRouteSubgroupKey | ''}-${
  | CheckApplicationRouteItemKey
  | ''}`;

// IB
export type IBRouteGroupKey =
  | 'personalDetails'
  | 'occupationDetails'
  | 'otherDetails'
  | 'documentUpload'
  | 'consents';
export type IBRouteSubgroupKey =
  | 'personalDetails'
  | 'occupationDetails'
  | 'otherDetails'
  | 'documentUpload'
  | 'consents';

export type IdnRouteSubgroupKey =
  | 'essentialInformation'
  | 'backgroundDetails'
  | 'addressDetails'
  | 'candidatePosition'
  | 'emergencyContact'
  | 'bankAccInfo'
  | 'insuranceExperience'
  | 'healthCondition'
  | 'financialCondition'
  | 'complianceAndReputationRecords'
  | 'amlAndOthers'
  | 'declarationOfCOI'
  | 'remarkOptional'
  | ReviewInfoKey
  | IBRouteSubgroupKey;

export type ReviewInfoKey = 'reviewInfo';

export type IBRouteItemKey =
  | 'personalDetails'
  | 'occupationDetails'
  | 'otherDetails'
  | 'documentUpload'
  | 'consents';

export type IDNCombinedRouteKey = `${IBRouteGroupKey | ReviewInfoKey | ''}-${
  | IdnRouteSubgroupKey
  | ''}-${IBRouteItemKey | ''}`;
export type IBCombinedRouteKey = `${IBRouteGroupKey | ''}-${
  | IBRouteSubgroupKey
  | ''}-${IBRouteItemKey | ''}`;

export type IBCheckApplicationRouteGroupKey =
  | 'additionalInformation'
  | 'review'
  | 'documentUpload';
export type IBCheckApplicationRouteSubgroupKey =
  | 'additionalInformation'
  | 'review'
  | 'documentUpload';
export type IBCheckApplicationRouteItemKey =
  | 'additionalInformation'
  | 'review'
  | 'documentUpload';

export type IBCheckApplicationCombinedRouteKey = `${
  | IBCheckApplicationRouteGroupKey
  | ''}-${IBCheckApplicationRouteSubgroupKey | ''}-${
  | IBCheckApplicationRouteItemKey
  | ''}`;

export type IBReviewApplicationRouteGroupKey =
  | 'personalDetails'
  | 'documentUpload';

export type IBReviewApplicationRouteSubgroupKey =
  | 'personalDetails'
  | 'documentUpload';

export type IBReviewApplicationRouteItemKey =
  | 'personalDetails'
  | 'otherDetails'
  | 'documentUpload';

export type IBReviewApplicationCombinedRouteKey = `${
  | IBReviewApplicationRouteGroupKey
  | ''}-${IBReviewApplicationRouteSubgroupKey | ''}-${
  | IBReviewApplicationRouteItemKey
  | ''}`;

export interface ReviewApplicationProgressSubgroup
  extends ProgressBase<IBReviewApplicationRouteSubgroupKey> {
  items: Array<ReviewApplicationProgressItem>;
}

export interface ReviewApplicationProgressItem
  extends ProgressBase<IBReviewApplicationRouteItemKey> {
  error?: boolean;
  barTitle?: string;
  pressable?: boolean;
  hidden?: boolean;
}

export interface ReviewApplicationProgressGroup
  extends ProgressBase<IBReviewApplicationRouteGroupKey> {
  items: Array<
    ReviewApplicationProgressSubgroup | ReviewApplicationProgressItem
  >;
  showSubProgress?: boolean;
  full?: boolean;
}
