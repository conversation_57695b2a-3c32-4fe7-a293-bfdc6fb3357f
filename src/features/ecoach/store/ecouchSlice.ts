import { EcoachSlice, EcoachState, StoreSlice } from 'types';
import { StateCreator } from 'zustand';

export const DEFAULT_QUICKFIRE_COMPLETED_HEARTS = 40;

export enum ProductFlowType {
  QUICKFIRE = 'quickfire',
  FULL_EXPERIENCE = 'full_experience',
  APPOINTMENT = 'appointment',
  OBJECTION_HANDLING = 'objection_handling',
}

export const createEcoachSlice: StateCreator<
  StoreSlice,
  [],
  [],
  EcoachSlice
> = set => ({
  ecoach: { ...initialState },
  ecoachActions: {
    setupConfiguration: ({
      isQuickfire,
      quickfireVideoUrl,
      homepageBackground,
      quickfireCompletedHearts,
      productConfig,
      quickfireProductConfig,
      appointmentProductConfig,
      moduleAvailability,
      videoToAudioSpeed,
      objectionHandlingProductConfig,
      trainerGuruAvailability,
    }) =>
      set(state => {
        state.ecoach.isQuickfire = isQuickfire;
        state.ecoach.quickfireVideoUrl = quickfireVideoUrl;
        state.ecoach.homepageBackground = homepageBackground;
        state.ecoach.quickfireCompletedHearts = quickfireCompletedHearts;
        state.ecoach.productConfig = productConfig;
        state.ecoach.quickfireProductConfig = quickfireProductConfig;
        state.ecoach.appointmentProductConfig = appointmentProductConfig;
        state.ecoach.moduleAvailability = moduleAvailability;
        state.ecoach.videoToAudioSpeed = videoToAudioSpeed;
        state.ecoach.objectionHandlingProductConfig = objectionHandlingProductConfig;
        state.ecoach.trainerGuruAvailability = trainerGuruAvailability;
        return state;
      }),
    setHistoryTabIndex: (index: number) => set(state => {
      state.ecoach.historyTabIndex = index;
      return state;
    }),
    setHistoryTabScrollX: (x: number) => set(state => {
      state.ecoach.historyTabScrollX = x;
      return state;
    }),
  },
});

const initialState: EcoachState = {
  isQuickfire: true,
  quickfireVideoUrl: null,
  quickfireCompletedHearts: DEFAULT_QUICKFIRE_COMPLETED_HEARTS,
  videoToAudioSpeed: 10, // Default value in Mbps
  objectionHandlingProductConfig: undefined,
  historyTabIndex: 0,
  historyTabScrollX: 0,
};
