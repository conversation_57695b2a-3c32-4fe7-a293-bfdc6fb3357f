import React, { useMemo, useState } from 'react';
import {
  ImageBackground,
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import styled from '@emotion/native';
import { H1, H4, H5, H6, H7, Icon, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { EcoachParamList, RootStackParamList } from 'types';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AppointmentReportMenuItem } from './AppointmentSummary';
import StarRating from 'features/ecoach/components/StarRating';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import CaretRightIcon from 'features/ecoach/components/icons/CaretRightIcon';
import DoubleQuotesRightIcon from 'features/ecoach/components/icons/DoubleQuotesRightIcon';
import { tAppointmentRPBG } from 'features/ecoach/assets';
import { extractScore } from 'features/ecoach/ultils/extractScore';
import { useQuery } from '@tanstack/react-query';
import {
  ConversationData,
  getConversationData,
} from 'features/ecoach/api/conversationApi';
import LoadingPage from 'features/ecoach/screens/loading/Loading';
import { StatusBar } from 'expo-status-bar';
import ReportReady from 'features/ecoach/screens/ReportReady';
import FeedbackModal from 'features/ecoach/components/modals/FeedbackModal';

const PageBGImg = styled(ImageBackground)(() => ({
  flex: 1,
}));
const ContentContainer = styled(ScrollView)({
  flex: 1,
  height: '100%',
  backgroundColor: 'transparent',
});

const Header = styled(View)(({ theme: { space } }) => ({
  paddingTop: space[4],
  paddingLeft: '5%',
  paddingRight: space[4],
  flexDirection: 'row',
  justifyContent: 'space-between',
}));

const OverallScoreContainer = styled(View)(() => ({
  justifyContent: 'center',
  alignItems: 'center',
}));

const StarContainer = styled.View(() => ({
  marginBottom: sizes[4],
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'row',
}));
const ScoreText = styled(H6)(() => ({
  textAlign: 'center',
  marginTop: sizes[2],
}));

const HeartScore = styled.View(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'baseline',
  justifyContent: 'center',
}));

const CurrentScore = styled(H1)(() => ({
  textAlign: 'center',
  marginRight: sizes[1],
}));

const TotalScore = styled(H5)(() => ({
  textAlign: 'center',
}));

const Title = styled(H4)(({ theme: { colors } }) => ({
  color: colors.secondary,
  textAlign: 'left',
}));

const TabContainer = styled(View)(() => ({
  // marginTop: '5%',
  flexDirection: 'row',
  justifyContent: 'center',
  alignItems: 'center',
}));
const Tab = styled(TouchableOpacity)<{ active: boolean }>(
  ({ active, theme: { space, borderRadius } }) => ({
    flex: 1,
    backgroundColor: active ? colors.fwdOrange[5] : colors.alertGreenLight,
    paddingVertical: space[3],
    alignItems: 'center',
    justifyContent: 'center',
    borderTopLeftRadius: active ? borderRadius.medium : 0,
    borderTopRightRadius: active ? borderRadius.medium : 0,
    maxWidth: 143,
  }),
);

const TabNumber = styled(H4)<{ active: boolean }>(
  ({ active, theme: { colors } }) => ({
    color: active ? colors.palette.alertRed : colors.palette.alertGreen,
  }),
);

const TabText = styled(Typography.Label)<{ active: boolean }>(
  ({ active, theme: { colors } }) => ({
    color: active ? colors.palette.alertRed : colors.palette.alertGreen,
  }),
);

const MainContent = styled(View)(({ theme: { space, borderRadius } }) => ({
  flexDirection: 'row',
  marginHorizontal: '5%',
  borderRadius: borderRadius.large,
  padding: space[7],
  backgroundColor: colors.fwdOrange[5],
  height: '100%',
}));

const LeftContent = styled(View)(({ theme: { space } }) => ({
  width: '40%',
  backgroundColor: colors.fwdOrange[5],
  gap: space[3],
}));

const RightContent = styled(View)<{ active: boolean }>(
  ({ active, theme: { borderRadius, space } }) => ({
    flex: 1,
    backgroundColor: active ? colors.fwdLightGreen[50] : colors.fwdOrange[20],
    padding: space[4],
    borderBottomLeftRadius: borderRadius.medium,
    borderTopRightRadius: borderRadius.medium,
    borderBottomRightRadius: borderRadius.medium,
    gap: sizes[4],
  }),
);

const HighLightItem = styled(View)(({ theme: { space, borderRadius } }) => ({
  backgroundColor: colors.white,
  padding: space[4],
  borderRadius: borderRadius.small,
  flexDirection: 'row',
  justifyContent: 'space-between',
}));

const InsideText = styled(Typography.LargeBody)(() => ({
  color: colors.fwdDarkGreen[100],
}));
const ObservationText = styled(Typography.SmallBody)(() => ({
  color: colors.fwdDarkGreen[100],
}));

const ObservationView = styled(View)<{ active: boolean }>(
  ({ active }) => ({
    borderLeftWidth: 1,
    borderLeftColor: active ? colors.white : colors.fwdGrey[100],
    paddingHorizontal: sizes[3],
  }),
);

const HighlightText = styled(Typography.SmallBody)(({ theme: { colors } }) => ({
  color: colors.secondary,
  maxWidth: '90%',
}));

const ItemContainer = styled(View)<{ isSelected: boolean; active: boolean }>(
  ({ isSelected, active, theme: { space, borderRadius } }) => ({
    paddingHorizontal: space[7],
    paddingVertical: space[3],
    flexDirection: 'row',
    alignItems: 'center',
    borderTopLeftRadius: borderRadius.medium,
    borderBottomLeftRadius: borderRadius.medium,
    borderTopRightRadius: isSelected ? 0 : borderRadius.medium,
    borderBottomRightRadius: isSelected ? 0 : borderRadius.medium,
    backgroundColor: isSelected
      ? active
        ? colors.fwdLightGreen[50]
        : colors.fwdOrange[20]
      : colors.white,
    borderWidth: isSelected ? 0 : 1,
    borderColor: colors.fwdGrey[100],
    marginRight: isSelected ? 0 : space[3],
  }),
);

const ItemNumber = styled(View)<{ active: boolean }>(
  ({ active, theme: { space, colors, borderRadius } }) => ({
    backgroundColor: active ?  colors.palette.alertGreenLight : colors.palette.alertRedLight,
    width: 22,
    height: 31,
    borderRadius: borderRadius['x-small'],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: space[2],
  }),
);

const ItemNumberText = styled(H7)<{ active: boolean }>(({active, theme: { colors } }) => ({
  color: active ? colors.palette.alertGreen : colors.palette.alertRed,
}));

const ItemTitle = styled(H7)(({ theme: { colors } }) => ({
  color: colors.secondary,
  flex: 1,
}));

const LoadingContainer = styled(View)(() => ({
  flex: 1,
}));

const BackBtn = styled(TouchableOpacity)(() => ({
  paddingHorizontal: sizes[4],
}));

const AppointmentSummaryTablet = () => {
  const { t } = useTranslation('ecoach');
  const { top } = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const route = useRoute<RouteProp<EcoachParamList, 'AppointmentSummary'>>();
  const { conversationId, session } = route.params;
  const [activeTab, setActiveTab] = useState<'FEEDBACK' | 'PRAISE'>('FEEDBACK');

  const [summaryInfo, setSummaryInfo] = useState<ConversationData | undefined>(
    session,
  );
  const [showReportReady, setShowReportReady] = useState<boolean>(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState<boolean>(!session);
  const [currentItem, setCurrentItem] =
    useState<AppointmentReportMenuItem | null>(null);

  // Fetch conversation data
  useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: () => getConversationData(conversationId),
    enabled: !session,
    refetchInterval: summaryInfo ? false : 5000,
    onSuccess: data => {
      // console.log(
      //   'AppointmentSummaryTablet getConversationData onSuccess',
      //   JSON.stringify(data),
      // );
      if (data.report_is_ready === 'true') {
        setSummaryInfo(data);
      }
    },
    onError: error => {
      console.log(
        'AppointmentSummaryTablet getConversationData error',
        JSON.stringify(error),
      );
    },
  });

  const displayData = useMemo(() => {
    if (!summaryInfo?.report || !Array.isArray(summaryInfo.report)) return [];
    return summaryInfo.report.filter(item => item.cardType === activeTab);
  }, [activeTab, summaryInfo]);

  const feedbackCount = useMemo(() => {
    if (!summaryInfo?.report || !Array.isArray(summaryInfo.report)) return 0;
    return summaryInfo.report.filter(item => item.cardType === 'FEEDBACK')
      .length;
  }, [summaryInfo]);

  const praiseCount = useMemo(() => {
    if (!summaryInfo?.report || !Array.isArray(summaryInfo.report)) return 0;
    return summaryInfo.report.filter(item => item.cardType === 'PRAISE').length;
  }, [summaryInfo]);

  // Set current item when data is loaded
  React.useEffect(() => {
    if (displayData.length > 0) {
      setCurrentItem(displayData[0]);
    }
  }, [displayData]);

  const handleChangeTab = (tab: 'FEEDBACK' | 'PRAISE') => {
    setActiveTab(tab);
    const filteredItems = displayData.filter(item => item.cardType === tab);
    if (filteredItems.length > 0) {
      setCurrentItem(filteredItems[0]);
    } else {
      setCurrentItem(null)
    }
  };

  const exitBtn = () => {
    if(session) {
      navigation.goBack();
    } else {
      navigation.navigate('EcoachHome');
    }
  };

  // Show loading page if data is still loading
  const handleUserUpdate = () => {
    !showFeedbackModal && setShowFeedbackModal(true);
  };

  if (!summaryInfo || showFeedbackModal || showReportReady) {
    return (
      <LoadingContainer>
        {!summaryInfo ? (
          <LoadingPage />
        ) : showReportReady ? (
          <ReportReady hideReadyReport={() => setShowReportReady(false)} />
        ) : null}
        <FeedbackModal
          conversationId={conversationId}
          visible={showFeedbackModal}
          setVisible={setShowFeedbackModal}
          handleUserUpdate={handleUserUpdate}
        />
      </LoadingContainer>
    );
  }

  // For this new API response format, we'll use a default score of 85 since
  // the overall_score is not provided in the new format
  const overall_score = summaryInfo.score?.toString() || '85';

  return (
    <PageBGImg source={tAppointmentRPBG} resizeMode={'stretch'}>
      <StatusBar hidden />
      <ContentContainer
        style={{ marginTop: Platform.OS === 'ios' ? top : 0 }}>
        <Header>
          <Title fontWeight="bold">{t('myReport')}</Title>
          {/*<TranscriptButton onPress={handleViewTranscript}>*/}
          {/*  <TranscriptText fontWeight="bold">*/}
          {/*    {t('seeFullTranscript')}*/}
          {/*  </TranscriptText>*/}
          {/*</TranscriptButton>*/}

          <BackBtn onPress={exitBtn}>
            <Icon.Close fill={colors.fwdDarkGreen[100]} />
          </BackBtn>
        </Header>
        <OverallScoreContainer>
          <StarContainer>
            <StarRating overallScore={extractScore(overall_score)} />
          </StarContainer>
          <ScoreText fontWeight={'bold'} color={colors.fwdDarkGreen[50]}>
            {t('totalScore')}
          </ScoreText>
          <HeartScore>
            <CurrentScore color={colors.black} fontWeight={'bold'}>
              {extractScore(overall_score)}
            </CurrentScore>
            <TotalScore color={colors.fwdDarkGreen[50]} fontWeight={'bold'}>
              /100
            </TotalScore>
          </HeartScore>
        </OverallScoreContainer>
        <TabContainer>
          <Tab active={true} onPress={() => handleChangeTab('FEEDBACK')}>
            <TabNumber fontWeight="bold" active={true}>
              {feedbackCount}
            </TabNumber>
            <TabText fontWeight={'normal'} active={true}>
              {t('thingsToImprove')}
            </TabText>
          </Tab>
          <Tab active={false} onPress={() => handleChangeTab('PRAISE')}>
            <TabNumber fontWeight="bold" active={false}>
              {praiseCount}
            </TabNumber>
            <TabText fontWeight={'normal'} active={false}>
              {t('thingsWeNailed')}
            </TabText>
          </Tab>
        </TabContainer>

        <MainContent>
          <LeftContent>
            {displayData.map((item, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => setCurrentItem(item)}>
                <ItemContainer
                  active={activeTab === 'PRAISE'}
                  isSelected={item.title === currentItem?.title}>
                  <ItemNumber active={activeTab === 'PRAISE'}>
                    <ItemNumberText fontWeight="bold" active={activeTab === 'PRAISE'}>
                      {index + 1}
                    </ItemNumberText>
                  </ItemNumber>
                  <ItemTitle fontWeight="bold">{item.title}</ItemTitle>
                  {currentItem && item.title !== currentItem.title && (
                    <CaretRightIcon />
                  )}
                </ItemContainer>
              </TouchableOpacity>
            ))}
          </LeftContent>

          <RightContent active={activeTab === 'PRAISE'}>
            {currentItem && (
              <>
                <InsideText>{currentItem.insight}</InsideText>
                <ObservationView active={activeTab === 'PRAISE'}>
                  <ObservationText>
                    {`“${currentItem.observation}”`}
                  </ObservationText>
                </ObservationView>
                {activeTab === 'FEEDBACK' &&
                  <HighLightItem>
                    <View>
                      <ItemTitle fontWeight="bold">
                        {t('suggestedNextMove')}
                      </ItemTitle>

                      <HighlightText>{currentItem.action}</HighlightText>
                    </View>
                    <DoubleQuotesRightIcon />
                  </HighLightItem>
                }
              </>
            )}
          </RightContent>
        </MainContent>
      </ContentContainer>
    </PageBGImg>
  );
};

export default AppointmentSummaryTablet;