import React, { useMemo, useState } from 'react';
import {
  ImageBackground,
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import styled from '@emotion/native';
import { H1, H5, H6, Icon, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { EcoachParamList, RootStackParamList } from 'types';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import MenuItem from '../../components/MenuItem';
import { mAppointmentRPBG } from 'features/ecoach/assets';
import { extractScore } from 'features/ecoach/ultils/extractScore';
import StarRating from 'features/ecoach/components/StarRating';
import { useQuery } from '@tanstack/react-query';
import {
  ConversationData,
  getConversationData,
} from 'features/ecoach/api/conversationApi';
import LoadingPage from 'features/ecoach/screens/loading/Loading';
import { StatusBar } from 'expo-status-bar';
import ReportReady from 'features/ecoach/screens/ReportReady';
import FeedbackModal from 'features/ecoach/components/modals/FeedbackModal';

export type AppointmentReportMenuItem = {
  cardType: string;
  title: string;
  observation: string;
  insight: string;
  action: string;
};
const PageBGImg = styled(ImageBackground)(() => ({
  flex: 1,
}));
const Container = styled(View)(() => ({
  flex: 1,
  backgroundColor: 'transparent',
}));

const ContentContainer = styled(ScrollView)({
  flex: 1,
});

const Header = styled(View)(({ theme: { space } }) => ({
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingTop: space[4],
  paddingHorizontal: space[4],
  paddingBottom: space[2],
}));

const BackBtn = styled(TouchableOpacity)(() => ({
  paddingTop: Platform.OS === 'ios' ? 0 : sizes[5],
  paddingHorizontal: sizes[4],
  backgroundColor: colors.white,
}));

const OverallScoreContainer = styled(View)(() => ({
  justifyContent: 'center',
  alignItems: 'center',
}));

const StarContainer = styled.View(() => ({
  marginBottom: sizes[4],
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'row',
}));
const ScoreText = styled(H6)(() => ({
  textAlign: 'center',
  marginTop: sizes[2],
}));

const HeartScore = styled.View(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'baseline',
  justifyContent: 'center',
}));

const CurrentScore = styled(H1)(() => ({
  textAlign: 'center',
  marginRight: sizes[1],
}));

const TotalScore = styled(H5)(() => ({
  textAlign: 'center',
}));

const Title = styled(H5)(({ theme: { space, colors } }) => ({
  marginBottom: space[4],
  marginTop: space[2],
  color: colors.secondary,
}));

const TabContainer = styled(View)(() => ({
  flexDirection: 'row',
  marginHorizontal: '10%',
}));

const Tab = styled(TouchableOpacity)<{ active: boolean }>(
  ({ active, theme: { space, borderRadius } }) => ({
    flex: 1,
    backgroundColor: active ? colors.fwdOrange[5] : colors.alertGreenLight,
    paddingVertical: space[3],
    alignItems: 'center',
    justifyContent: 'center',
    borderTopLeftRadius: borderRadius.medium,
    borderTopRightRadius: borderRadius.medium,
  }),
);

const TabText = styled(Typography.SmallBody)<{ active: boolean }>(
  ({ active, theme: { colors } }) => ({
    color: active ? colors.palette.alertRed : colors.palette.alertGreen,
  }),
);

const TabNumber = styled(H6)<{ active: boolean }>(
  ({ active, theme: { colors } }) => ({
    color: active ? colors.palette.alertRed : colors.palette.alertGreen,
  }),
);

const SectionContainer = styled(View)<{ active: boolean }>(
  ({ active, theme: { space, borderRadius } }) => ({
    backgroundColor: active ? '#F2F9F6' : colors.fwdOrange[5],
    marginBottom: space[4],
    borderRadius: borderRadius.small,
    padding: space[4],
  }),
);

const LoadingContainer = styled(View)(() => ({
  flex: 1,
}));

const AppointmentSummary = () => {
  const { t } = useTranslation('ecoach');
  const { top } = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const route = useRoute<RouteProp<EcoachParamList, 'AppointmentSummary'>>();
  const { conversationId, session } = route.params;

  const [activeTab, setActiveTab] = useState<'FEEDBACK' | 'PRAISE'>('FEEDBACK');
  const [summaryInfo, setSummaryInfo] = useState<ConversationData | undefined>(
    session,
  );
  const [showReportReady, setShowReportReady] = useState<boolean>(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState<boolean>(!session);
  // Fetch conversation data
  useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: () => getConversationData(conversationId),
    enabled: !session,
    refetchInterval: summaryInfo ? false : 5000,
    onSuccess: data => {
      // console.log(
      //   'AppointmentSummaryTablet getConversationData onSuccess',
      //   JSON.stringify(data),
      // );
      if (data.report_is_ready === 'true') {
        setSummaryInfo(data);
      }
    },
    onError: error => {
      console.log(
        'AppointmentSummaryTablet getConversationData error',
        JSON.stringify(error),
      );
    },
  });

  const displayData = useMemo(() => {
    if (!summaryInfo?.report || !Array.isArray(summaryInfo.report)) return [];
    return summaryInfo.report.filter(item => item.cardType === activeTab);
  }, [activeTab, summaryInfo]);

  const feedbackCount = useMemo(() => {
    if (!summaryInfo?.report || !Array.isArray(summaryInfo.report)) return 0;
    return summaryInfo.report.filter(item => item.cardType === 'FEEDBACK')
      .length;
  }, [summaryInfo]);

  const praiseCount = useMemo(() => {
    if (!summaryInfo?.report || !Array.isArray(summaryInfo.report)) return 0;
    return summaryInfo.report.filter(item => item.cardType === 'PRAISE').length;
  }, [summaryInfo]);

  const exitBtn = () => {
    if(session) {
      navigation.goBack();
    } else {
      navigation.navigate('EcoachHome');
    }
  };

  const handleUserUpdate = () => {
    !showFeedbackModal && setShowFeedbackModal(true);
  };

  if (!summaryInfo || showFeedbackModal || showReportReady) {
    return (
      <LoadingContainer>
        {!summaryInfo ? (
          <LoadingPage />
        ) : showReportReady ? (
          <ReportReady hideReadyReport={() => setShowReportReady(false)} />
        ) : null}
        <FeedbackModal
          conversationId={conversationId}
          visible={showFeedbackModal}
          setVisible={setShowFeedbackModal}
          handleUserUpdate={handleUserUpdate}
        />
      </LoadingContainer>
    );
  }

  // For this new API response format, we'll use a default score of 85 since
  // the overall_score is not provided in the new format
  const overall_score = summaryInfo.score?.toString() || '85';

  return (
    <PageBGImg source={mAppointmentRPBG} resizeMode={'stretch'}>
      <StatusBar hidden />
      <Container>
        <ContentContainer
          style={{ marginTop: Platform.OS === 'ios' ? top : 0 }}>
          <BackBtn onPress={exitBtn}>
            {session ? <Icon.ArrowLeft fill={colors.fwdDarkGreen[100]} /> : <Icon.Close fill={colors.fwdDarkGreen[100]} />}
          </BackBtn>
          <Header>
            <Title fontWeight="bold">{t('myReport')}</Title>
            {/*<TranscriptButton onPress={handleViewTranscript}>*/}
            {/*  <TranscriptText fontWeight="bold">*/}
            {/*    {t('seeFullTranscript')}*/}
            {/*  </TranscriptText>*/}
            {/*</TranscriptButton>*/}
          </Header>
          <OverallScoreContainer>
            <StarContainer>
              <StarRating overallScore={extractScore(overall_score)} />
            </StarContainer>
            <ScoreText fontWeight={'bold'} color={colors.fwdDarkGreen[50]}>
              {t('totalScore')}
            </ScoreText>
            <HeartScore>
              <CurrentScore color={colors.black} fontWeight={'bold'}>
                {extractScore(overall_score)}
              </CurrentScore>
              <TotalScore color={colors.fwdDarkGreen[50]} fontWeight={'bold'}>
                /100
              </TotalScore>
            </HeartScore>
          </OverallScoreContainer>

          <TabContainer>
            <Tab active={true} onPress={() => setActiveTab('FEEDBACK')}>
              <TabNumber
                fontWeight="bold"
                active={true}
                color={colors.fwdOrange[5]}>
                {feedbackCount}
              </TabNumber>
              <TabText fontWeight={'normal'} active={true}>
                {t('thingsToImprove')}
              </TabText>
            </Tab>
            <Tab active={false} onPress={() => setActiveTab('PRAISE')}>
              <TabNumber fontWeight="bold" active={false}>
                {praiseCount}
              </TabNumber>
              <TabText fontWeight={'normal'} active={false}>
                {t('thingsWeNailed')}
              </TabText>
            </Tab>
          </TabContainer>

          <SectionContainer active={activeTab === 'PRAISE'}>
            {displayData.map((item, index) => (
              <MenuItem
                active={activeTab === 'PRAISE'}
                key={index}
                index={index}
                title={item.title}
                observation={item.observation}
                insight={item.insight}
                action={item.action}
              />
            ))}
          </SectionContainer>
        </ContentContainer>
      </Container>
    </PageBGImg>
  );
};

export default AppointmentSummary;
