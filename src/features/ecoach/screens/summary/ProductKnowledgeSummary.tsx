import styled from '@emotion/native';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { StatusBar } from 'expo-status-bar';
import {
  ConversationData,
  getConversationData,
  QuestionAndAssessment,
} from 'features/ecoach/api/conversationApi';
import { mAppointmentRPBG } from 'features/ecoach/assets';
import React, { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ImageBackground,
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { EcoachParamList, RootStackParamList } from 'types';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import {
  Button,
  ExtraSmallLabel,
  H1,
  H5,
  H6,
  H7,
  Icon,
  Label,
  LargeBody,
} from 'cube-ui-components';
import StarRating from 'features/ecoach/components/StarRating';
import LoadingPage from '../loading/Loading';
import { ProductFlowType } from 'features/ecoach/store/ecouchSlice';
import ReportReady from 'features/ecoach/screens/ReportReady';
import FeedbackModal from 'features/ecoach/components/modals/FeedbackModal';

const MAX_SCORE = 100;

const PageBGImg = styled(ImageBackground)(() => ({
  flex: 1,
  marginTop: -sizes[8],
  marginBottom: -sizes[12],
}));

const Container = styled(View)(() => ({
  flex: 1,
  backgroundColor: 'transparent',
  zIndex: 2,
}));

const LoadingContainer = styled(View)(() => ({
  flex: 1,
}));

const ScreenContainer = styled(View)({
  flex: 1,
});

const ContentContainer = styled(ScrollView)({
  flex: 1,
  paddingBottom: sizes[16],
  marginBottom: sizes[16],
  paddingHorizontal: sizes[4],
});

const BackBtn = styled(TouchableOpacity)(() => ({}));

const Header = styled(View)(({ theme: { space } }) => ({
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingTop: space[4],
  paddingHorizontal: space[4],
  paddingBottom: space[2],
}));

const Title = styled(H5)(({ theme: { space, colors } }) => ({
  marginBottom: space[4],
  marginTop: space[2],
  color: colors.secondary,
}));

const OverallScoreContainer = styled(View)(() => ({
  justifyContent: 'center',
  alignItems: 'center',
}));

const StarContainer = styled.View(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'row',
  transform: [{ scale: 1.5 }],
  marginBottom: sizes[2],
  marginTop: sizes[1],
}));
const ScoreText = styled(H7)(() => ({
  textAlign: 'center',
}));

const HeartScore = styled.View(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'baseline',
  justifyContent: 'center',
}));

const CurrentScore = styled(H1)(() => ({
  textAlign: 'center',
  marginRight: sizes[1],
}));

const TotalScore = styled(H5)(() => ({
  textAlign: 'center',
}));

const BackgroundBottomCover = styled(View)(() => ({
  position: 'absolute',
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: colors.white,
  opacity: 0.8,
  zIndex: 1,
  width: '100%',
  height: '40%',
}));

const CTAGroup = styled(View)(() => ({
  gap: sizes[2],
}));

const CTAButton = styled(Button)(() => ({
  width: '100%',
}));

const ConvoIDText = styled(ExtraSmallLabel)(() => ({
  textAlign: 'center',
  marginBottom: sizes[4],
  color: colors.black,
  width: '100%',
  opacity: 0.35,
  marginTop: sizes[8],
}));

const QuestionItemContainer = styled(View)(() => ({
  backgroundColor: colors.white,
  borderRadius: sizes[4],
  padding: sizes[4],
  borderColor: colors.fwdGrey[100],
  borderWidth: 2,
  gap: sizes[2],
  overflow: 'hidden',
  marginBottom: sizes[5],
}));

const QuestionWrapper = styled(View)(() => ({
  flexDirection: 'row',
  gap: sizes[2],
  width: '100%',
  overflow: 'hidden',
}));

const AnswerWrapper = styled(View)(() => ({
  flexDirection: 'row',
  gap: sizes[2],
  backgroundColor: colors.fwdGrey[20],
  paddingHorizontal: sizes[4],
  paddingVertical: sizes[4],
  borderRadius: sizes[2],
}));

const ReasoningWrapper = styled(View)(() => ({
  gap: sizes[2],
}));

const ProductKnowledgeSummary = () => {
  const { t } = useTranslation('ecoach');
  const { top } = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const route =
    useRoute<RouteProp<EcoachParamList, 'ProductKnowledgeSummary'>>();
  const { conversationId, session } = route.params;

  const [summaryInfo, setSummaryInfo] = useState<ConversationData | undefined>(
    session,
  );
  const [showReportReady, setShowReportReady] = useState<boolean>(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState<boolean>(!session);
  // Fetch conversation data
  useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: () => getConversationData(conversationId),
    cacheTime: 0,
    staleTime: 0,
    enabled: !session,
    refetchInterval: summaryInfo ? false : 5000,
    onSuccess: data => {
      console.log(
        'ProductKnowledgeSummary getConversationData onSuccess',
        JSON.stringify(data),
      );
      if (data.report_is_ready === 'true') {
        setSummaryInfo(data);
      }
    },
    onError: error => {
      console.log(
        'ProductKnowledgeSummary getConversationData error',
        JSON.stringify(error),
      );
    },
  });

  const exitBtn = () => {
    if (session) {
      navigation.goBack();
    } else {
      navigation.navigate('EcoachHome');
    }
  };

  const handleUserUpdate = () => {
    !showFeedbackModal && setShowFeedbackModal(true);
  };

  if (!summaryInfo || showFeedbackModal || showReportReady) {
    return (
      <LoadingContainer>
        {!summaryInfo ? (
          <LoadingPage />
        ) : showReportReady ? (
          <ReportReady hideReadyReport={() => setShowReportReady(false)} />
        ) : null}
        <FeedbackModal
          conversationId={conversationId}
          visible={showFeedbackModal}
          setVisible={setShowFeedbackModal}
          handleUserUpdate={handleUserUpdate}
        />
      </LoadingContainer>
    );
  }

  const questions_and_assessments =
    summaryInfo.report.questions_and_assessments || [];

  const answeredCorrectlyCount = questions_and_assessments.filter(
    item => item.answered_correctly === 'Yes',
  ).length;
  const overallScore = Math.round(
    (answeredCorrectlyCount / questions_and_assessments.length) * MAX_SCORE,
  );

  return (
    <PageBGImg source={mAppointmentRPBG} resizeMode={'stretch'}>
      <StatusBar hidden />
      <BackgroundBottomCover />
      <Container>
        <ScreenContainer style={{ marginTop: Platform.OS === 'ios' ? top : 0 }}>
          <Header>
            <Title fontWeight="bold">{t('yourReport')}</Title>
            <BackBtn onPress={exitBtn}>
              <Icon.Close fill={colors.fwdDarkGreen[100]} />
            </BackBtn>
          </Header>
          <ContentContainer showsVerticalScrollIndicator={false}>
            <OverallScoreContainer>
              <StarContainer>
                <StarRating overallScore={overallScore} />
              </StarContainer>
              <ScoreText color={colors.fwdDarkGreen[50]}>
                {t('yourScore')}
              </ScoreText>
              <HeartScore>
                <CurrentScore color={colors.black} fontWeight={'bold'}>
                  {overallScore}
                </CurrentScore>
                <TotalScore color={colors.fwdDarkGreen[50]} fontWeight={'bold'}>
                  /100
                </TotalScore>
              </HeartScore>
            </OverallScoreContainer>
            {questions_and_assessments.map((item, index) => (
              <QuestionItem key={index} item={item} index={index} />
            ))}
            <CTAGroup>
              <CTAButton
                variant="primary"
                onPress={() => {
                  navigation.navigate('SelectPolicy', {
                    productFlowType: ProductFlowType.QUICKFIRE,
                  });
                }}
                text={t('letsTryAgain')}
              />
              <CTAButton
                variant="secondary"
                onPress={() => {
                  navigation.navigate('EcoachHome');
                }}
                text={t('exit')}
              />
            </CTAGroup>
            <ConvoIDText>
              {t('convoID')}: {summaryInfo.conversation_id}
            </ConvoIDText>
          </ContentContainer>
        </ScreenContainer>
      </Container>
    </PageBGImg>
  );
};

export type QuestionItemProps = { item: QuestionAndAssessment; index: number };

export const QuestionItem: FC<QuestionItemProps> = ({ item, index }) => {
  const { t } = useTranslation('ecoach');
  return (
    <QuestionItemContainer key={index}>
      <QuestionWrapper>
        <H6 fontWeight="medium">{index + 1}.</H6>
        <H6 fontWeight="medium" style={{ flex: 1 }}>
          {item.question}
        </H6>
      </QuestionWrapper>
      <AnswerWrapper>
        {item.answered_correctly === 'Yes' ? (
          <Icon.TickCircleFill fill={colors.alertGreen} size={sizes[5]} />
        ) : (
          <Icon.CloseCircleFill fill={colors.alertRed} size={sizes[5]} />
        )}
        <Label
          style={{
            flex: 1,
            color: colors.fwdGreyDarker,
            marginTop: 2,
          }}>
          {item.answer}
        </Label>
      </AnswerWrapper>
      {item.answered_correctly !== 'Yes' && item.reasoning && (
        <ReasoningWrapper>
          <H7 fontWeight="bold">{t('explanation')}</H7>
          <LargeBody>{item.reasoning}</LargeBody>
        </ReasoningWrapper>
      )}
    </QuestionItemContainer>
  );
};

export default ProductKnowledgeSummary;
