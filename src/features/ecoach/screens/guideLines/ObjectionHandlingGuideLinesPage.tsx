import React, { useCallback } from 'react';
import {
  ImageBackground,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import {
  Body,
  Button,
  ExtraLargeBody,
  H5,
  Icon,
  Label,
  Typography,
} from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { EcoachParamList, RootStackParamList } from 'types';
import { cafeBGMobile, cafeBGTablet } from 'features/ecoach/assets';
import { StatusBar } from 'expo-status-bar';
import { BlurView } from 'expo-blur';

const HomeBGImg = styled(ImageBackground)(() => ({
  width: '100%',
  height: '100%',
  flex: 1,
  backgroundColor: colors.black,
}));

const Container = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    paddingHorizontal: isTabletMode ? '10%' : sizes[4],
    justifyContent: 'center',
    flexGrow: 1,
    backgroundColor:
      'linear-gradient(90deg, rgba(1, 1, 1, 0.65) 33.19%, rgba(1, 1, 1, 0.65) 93.58%)',
    gap: sizes[6],
  }),
);

const GoBackBtn = styled(TouchableOpacity)({
  paddingHorizontal: sizes[4],
  position: 'absolute',
  top: sizes[10],
  left: sizes[1],
  zIndex: 1,
});
const StartBtnView = styled(View)(() => ({
  width: '100%',
  justifyContent: 'center',
  alignItems: 'center',
}));

const WhiteBGLabel = styled(View)({
  alignSelf: 'center',
  display: 'flex',
  flexDirection: 'row',
  gap: sizes[2],
  backgroundColor: colors.white,
  borderRadius: sizes[5],
  paddingVertical: 2,
  paddingHorizontal: sizes[2],
});

const ObjectionHandlingGuideLinesPage = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { params } = useRoute<RouteProp<EcoachParamList, 'GuideLinesPage'>>();
  const { t } = useTranslation('ecoach');
  const { isTabletMode } = useLayoutAdoptionCheck();

  const goVideoCallPage = useCallback(() => {
    navigation.push('VideoCallPage', { ...params });
  }, [navigation, params]);

  const goBack = () => {
    navigation.goBack();
  };

  return (
    <HomeBGImg
      source={isTabletMode ? cafeBGTablet : cafeBGMobile}
      resizeMode={isTabletMode ? 'cover' : 'cover'}>
      <BlurView intensity={50} tint="dark" style={StyleSheet.absoluteFill} />
      <StatusBar hidden />
      <GoBackBtn onPress={goBack}>
        <Icon.ArrowLeft size={sizes[6]} fill={colors.white} />
      </GoBackBtn>
      <Container isTabletMode={isTabletMode}>
        <WhiteBGLabel>
          {isTabletMode ? (
            <Typography.SmallBody
              fontWeight={'bold'}
              color={colors.fwdAlternativeOrange[100]}>
              {t('hereYourGoal')}
            </Typography.SmallBody>
          ) : (
            <>
              <Label fontWeight="bold" color={colors.fwdOrange[100]}>
                {t('ohMin')}
              </Label>
              <Label fontWeight="bold" color={colors.fwdOrange[100]}>
                |
              </Label>
              <Label fontWeight={'bold'} color={colors.fwdOrange[100]}>
                {t('objectionHandling')}
              </Label>
            </>
          )}
        </WhiteBGLabel>

        {isTabletMode ? (
          <ExtraLargeBody
            color={colors.white}
            style={{ textAlign: 'center', maxWidth: 796 }}>
            {t('objectionHandlingHeadline')}
          </ExtraLargeBody>
        ) : (
          <Body
            color={colors.white}
            style={{ textAlign: 'center', maxWidth: 796 }}>
            {t('objectionHandlingHeadline')}
          </Body>
        )}

        {isTabletMode ? (
          <ExtraLargeBody
            fontWeight={'bold'}
            color={colors.white}
            style={{ textAlign: 'center' }}>
            <H5 fontWeight={'bold'} color={colors.fwdOrange[100]}>
              {t('a')}
            </H5>
            {t('cknowledge')}
            <H5 fontWeight={'bold'} color={colors.fwdOrange[100]}>
              {t('a')}
            </H5>
            {t('skQuestions')}
            <H5 fontWeight={'bold'} color={colors.fwdOrange[100]}>
              {t('a')}
            </H5>
            {t('nswer')}
          </ExtraLargeBody>
        ) : (
          <Body
            fontWeight={'bold'}
            color={colors.white}
            style={{ textAlign: 'center' }}>
            <ExtraLargeBody fontWeight={'bold'} color={colors.fwdOrange[100]}>
              {t('a')}
            </ExtraLargeBody>
            {t('cknowledge')}
            <ExtraLargeBody fontWeight={'bold'} color={colors.fwdOrange[100]}>
              {t('a')}
            </ExtraLargeBody>
            {t('skQuestions')}
            <ExtraLargeBody fontWeight={'bold'} color={colors.fwdOrange[100]}>
              {t('a')}
            </ExtraLargeBody>
            {t('nswer')}
          </Body>
        )}

        <StartBtnView>
          <Button
            variant={'primary'}
            onPress={goVideoCallPage}
            style={{ width: 343 }}
            text={t('startTheChat')}
          />
        </StartBtnView>
      </Container>
    </HomeBGImg>
  );
};
export default ObjectionHandlingGuideLinesPage;
