import { useQuery } from '@tanstack/react-query';
import {
  Config,
  getConfigurationData,
} from 'features/ecoach/api/configurationApi';
import useBoundStore from 'hooks/useBoundStore';
import { DEFAULT_QUICKFIRE_COMPLETED_HEARTS } from 'features/ecoach/store/ecouchSlice';
import {
  AppointmentProductConfig,
  BuildCountry,
  EcoachProductConfig,
  ModuleAvailability,
  TrainerGuruAvailability,
} from 'types';
import { country } from 'utils/context';

const useEcoachConfiguration = () => {
  const setupConfiguration = useBoundStore(
    state => state.ecoachActions.setupConfiguration,
  );

  return useQuery({
    queryKey: ['ECoachHomeGetConfigurationData'],
    queryFn: getConfigurationData,
    cacheTime: 0,
    staleTime: 0,
    onSuccess: data => {
      const oldHomeConfig = data?.find(
        (item: Config) =>
          item.key === 'homepage_config' && item.value !== 'quickfire',
      );
      const homepageBackgroundData = data?.find(
        (item: Config) => item.key === 'homepage_background',
      );
      let homepageBackground;
      if (homepageBackgroundData?.value) {
        homepageBackground = homepageBackgroundData.value.find(
          bg => bg.background_image_tablet && bg.background_image_mobile,
        );
      }
      const quickfireUrl = data?.find(
        (item: Config) => item.key === 'trainer_guru_tutorial_video',
      );

      const quickfireCompletedHearts = data?.find(
        (item: Config) => item.key === 'quickfire_completed_hearts',
      );

      const productConfig: Record<BuildCountry, EcoachProductConfig[]> =
        {} as Record<BuildCountry, EcoachProductConfig[]>;
      const quickfireProductConfig: Record<
        BuildCountry,
        EcoachProductConfig[]
      > = {} as Record<BuildCountry, EcoachProductConfig[]>;

      const appointmentProductConfig: Record<
        BuildCountry,
        AppointmentProductConfig[]
      > = {} as Record<BuildCountry, AppointmentProductConfig[]>;

      const config = data?.find(
        (item: Config) => item.key === `product_selection`,
      );

      if (config) {
        productConfig[country] = config.value as EcoachProductConfig[];
      }

      const qfConfig = data?.find(
        (item: Config) => item.key === `quickfire_product_selection`,
      );

      if (qfConfig) {
        quickfireProductConfig[country] =
          qfConfig.value as EcoachProductConfig[];
      }

      const apConfig = data?.find(
        (item: Config) => item.key === `appointment_setting_product_selection`,
      );

      if (apConfig) {
        appointmentProductConfig[country] = apConfig.value;
      }

      // Extract objection_handling_product_selection configuration
      const objectionHandlingConfig = data?.find(
        (item: Config) => item.key === 'objection_handling_product_selection',
      );
      const objectionHandlingProductConfig: Record<BuildCountry, EcoachProductConfig[]> = {} as Record<BuildCountry, EcoachProductConfig[]>;
      if (objectionHandlingConfig) {
        objectionHandlingProductConfig[country] = objectionHandlingConfig.value as EcoachProductConfig[];
      }

      // Extract module_availability configuration
      const moduleAvailabilityConfig = data?.find(
        (item: Config) => item.key === 'module_availability',
      );

      let moduleAvailability: ModuleAvailability | undefined;
      if (moduleAvailabilityConfig?.value) {
        moduleAvailability = moduleAvailabilityConfig.value as ModuleAvailability;
      }

      // Extract video_to_audio_speed configuration
      const videoToAudioSpeedConfig = data?.find(
        (item: Config) => item.key === 'video_to_audio_speed',
      );
      const videoToAudioSpeed = videoToAudioSpeedConfig?.value
        ? Number(videoToAudioSpeedConfig.value)
        : 10; // Default to 10 Mbps if not found

      // Extract trainer_guru_availability configuration
      const trainerGuruAvailabilityConfig = data?.find(
        (item: Config) => item.key === 'trainer_guru_availability',
      );
      const trainerGuruAvailability = trainerGuruAvailabilityConfig?.value || undefined;

      setupConfiguration({
        isQuickfire: !oldHomeConfig,
        quickfireVideoUrl: quickfireUrl ? quickfireUrl?.value : null,
        homepageBackground,
        quickfireCompletedHearts:
          parseInt(
            quickfireCompletedHearts?.value ||
              `${DEFAULT_QUICKFIRE_COMPLETED_HEARTS}`,
            10,
          ) ?? DEFAULT_QUICKFIRE_COMPLETED_HEARTS,
        productConfig,
        quickfireProductConfig,
        appointmentProductConfig,
        moduleAvailability,
        videoToAudioSpeed,
        objectionHandlingProductConfig,
        trainerGuruAvailability,
      });
    },
    onError: error => {
      console.log(
        'ECoachHomePage getConfigurationData error',
        JSON.stringify(error),
      );
    },
  });
};

export default useEcoachConfiguration;
