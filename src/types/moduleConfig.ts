import { ConcernId } from 'features/fna/types/concern';
import { LifeJourneyQuestion, LifeStage } from 'features/fna/types/lifeJourney';
import { AdviceType } from 'features/fna/utils/store/fnaStore';
import { CHANNELS_OUTCOMES } from 'types/channel';
import {
  DashboardCardsKeys,
  TodayTasksConfig,
  TodayTasksSectionsKeys,
} from 'types/home';
import { MainTabParamList } from 'types/navigation';
import { CaseStatus, DependentRelationship } from './case';
import { PartyType } from './party';
import { ProfileLanguage, ProfileToneOfVoice } from './agent';

// * please go to src/utils/config/module/<The Targeted Region>/sellerExp/index.ts
type MainTabsConfig = Record<keyof MainTabParamList, boolean> &
  Record<'myLMS', boolean>;

export type SellerExpModuleConfig = Partial<
  {
    isLoginActiveChecked: boolean;
    dashboard: Record<
      DashboardCardsKeys,
      | Record<'isShown', boolean>
      // * Performance
      | Record<'isShown' | 'isMdrtShown', boolean>
    >;
    tasks: Record<TodayTasksSectionsKeys, boolean> & TodayTasksConfig;
    home: {
      businessOppCard: {
        isReminderVisible: boolean;
        hasViewAll: boolean;
      };
      showTermsAndConditions?: boolean;
    };
    lead: {
      leadActivityModal: boolean;
      policyModal: boolean;
      today?: {
        sortByNewest: boolean;
      };
      recommendedProducts: boolean;
    };
    performance: {
      tipYourPerformance: boolean;
      metricOne: PerformanceMetrics;
      metricTwo: PerformanceMetrics;
      TargetSetting: boolean;
      targetOneField: TargetField;
      completionOneField: completionField;
      submissionOneField: submissionField;
      submissionListField: submissionListField;
      submissionItemSubField: submissionItemSubField;
      completionListField: completionListField;
      completionItemSubField: completionItemSubField;
      isRankingShown: boolean;
      isRecognitionShown: boolean;
      isPerformanceBSCShown: boolean;
      isEliteShownInRecognition?: boolean;
    };
    isLoggedInUntilAppKilled: boolean;
    showPromptLibrary: boolean;
    contactOptions: string[];
    salesConnect?: boolean;
    pintr?: boolean;
  } & MainTabsConfig
>;

export type TargetField =
  | 'apeTarget'
  | 'fycTarget'
  | 'targetACE'
  | 'caseTarget'
  | 'afypTarget';
type completionField =
  | 'apeCompletion'
  | 'fycCompletion'
  | 'fypCompletion'
  | 'aceCompletion'
  | 'caseCompletion'
  | 'afypCompletion';

type submissionField =
  | 'apeSubmission'
  | 'fycSubmission'
  | 'fypSubmission'
  | 'aceSubmission'
  | 'caseSubmission';
type submissionListField =
  | 'apeSubmissionList'
  | 'fypSubmissionList'
  | 'aceSubmissionList'
  | 'caseSubmissionList';
type submissionItemSubField = 'fyp' | 'ace' | 'apeSub' | 'aceSub' | 'caseSub';
type completionListField =
  | 'apeCompletionList'
  | 'fypCompletionList'
  | 'aceCompletionList'
  | 'caseCompletionList'
  | 'afypCompletionList';
type completionItemSubField = 'fyp' | 'ape' | 'ace' | 'caseComp' | 'afyp';
export type PerformanceMetrics =
  | 'FYP'
  | 'ACE'
  | 'Case'
  | 'APE'
  | 'FYC'
  | 'AFYP';

export type SavedProposalsConfig = {
  stages: {
    [key in
      | CaseStatus.QUICK_SI
      | CaseStatus.FNA
      | CaseStatus.CFF
      | CaseStatus.FULL_SI
      | CaseStatus.IN_APP]: boolean;
  };
  clientType: {
    [key in PartyType]: boolean;
  };
  // config applicable to tablet only (for now)
  saveNewQuotation: {
    onCreateQuickSi: boolean;
    onCreateFullSi: boolean;
    fromSavedQuickSi: boolean;
    fromSavedFullSi: boolean;
  };
};

export type EAppConfig = {
  debugPolicyNo: boolean;
  debugUWMe: boolean;
  ocrInfoIcon: boolean;
  shouldCloseHQBeforeDecision: boolean;
  hasSignaturePlaceholder: boolean;
  disableFormAfterDecision: boolean;
  maintainSignaturePlaceOfSigning: boolean;
  documentUpload: {
    selectFromLibrary: boolean;
    attachFile: boolean;
  };
  shouldTrackGeoLocation: boolean;
  uwmeDismissible: boolean;
  checkSubmissionReadiness: boolean;
  // https://fwdnextgen.atlassian.net/browse/CUBEMY-4337
  verifyReplacementInfoInHealthQuestion: boolean;
};

export type EcoachConfig = {
  [key in CHANNELS_OUTCOMES]?: {
    tablet: boolean;
    mobile: boolean;
  };
};

export type FNAConfig = {
  nameInputMode: 'text' | 'modal';
  hasDefaultGoalValue: boolean;
  hasGoalPlan: boolean;
  hasGoalTooltip: boolean;
  hasDisclaimer: boolean;
  hasVulnerableCustomerCheck: boolean;
  hasShortcut: boolean;
  dependentRelationships: DependentRelationship[];
  lifeStageItems: Partial<Record<LifeStage, LifeJourneyQuestion[]>>;
  concerns: {
    minSelection: number;
    maxSelection: number;
    items: ConcernId[];
    additionalItems: ConcernId[]; // not showing in goal selection
  };
  goalGroupUIRatio: {
    saving: number;
    protection: number;
  };
  compulsory?: Record<
    AdviceType,
    {
      byPriorities?: number;
      byConcerns?: ConcernId[];
    }
  >;
  incomeType: 'monthly' | 'annually';
  // due to different requirements for different regions
  // we need to define 2 income options for monthly and annually
  // monthlyIncome and annuallyIncome values will either be selected or calculated
  // for example, in PH, if incomeType is monthly, and user selects { from: 50000, to: 100000 } for monthlyIncome,
  // the annuallyIncome will be { from: null, to: 2000000 } (based on predefined list)
  incomeOptions: Record<
    'monthly' | 'annually',
    { from: number | null; to: number | null }[]
  >;
  insuranceProtectionPeriodOptions?: {
    from: number | null;
    to: number | null;
  }[];
  previewFNAMandatory?: boolean;
  previewFNASharable?: boolean;
  productSelectionGuruFAB?: boolean;
  shouldDisplaySummaryNeeds: boolean;
  incomeTooltip: string[];
  shouldHideProgressBarFor0Needs: boolean;
  isProductReselectionEnabled: boolean;
};

export type ProfileConfig = {
  defaultLanguage: ProfileLanguage;
  defaultToneOfVoice: ProfileToneOfVoice;
  profileLanguages: ProfileLanguage[];
  profileToneOfVoices: ProfileToneOfVoice[];
};

export type PostingChannel = 'facebook' | 'instagram' | 'tiktok';

export type SocialMarketingConfig = {
  defaultPostingChannel: PostingChannel;
  postingChannels: PostingChannel[];
};
