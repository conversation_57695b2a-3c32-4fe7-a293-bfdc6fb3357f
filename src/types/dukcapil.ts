export type DukcapilRequestBody =
  | ({
      type?: 'default';
      caseId: string;
    } & IDInfo)
  | IDInfoOnlyRequestBody;

type IDInfoOnlyRequestBody = {
  type: 'idInfoOnly';
} & IDInfo;

type IDInfo = {
  nationalId: string;
  fullName: string;
  dob: Date | string;
  gender: 'Perempuan' | 'Laki <PERSON>ki'; // values provided by IDN local team
};

export type DukcapilValidationResponse = {
  code: 'NOT_ALLOW_PROCEED' | 'ALLOW_PROCEED';
  flag: string | null;
  errorMsgCode:
    | 'NRIC_MISMATCH'
    | 'RESIDENCY_STATUS_DECEASED'
    | 'RESIDENCY_STATUS_INACTIVE'
    | 'SOME_DATA_MISMATCH'
    | null;

  mismatches: string[] | null;
};

export type BlacklistCheckingResponse = {
  code: 'ALLOW_PROCEED' | 'NOT_ALLOW_PROCEED';
  flag: string | null;
  errorMsgCode?: 'BLACKLIST_CHECK_FAILED' | 'USER_IS_BLACKLISTED' | null;
  mismatches?: string[] | null;
};
