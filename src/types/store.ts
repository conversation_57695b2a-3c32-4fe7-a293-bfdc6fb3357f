import { NotificationRequest } from 'expo-notifications';
import { CandidateListItem } from 'features/eRecruit/ph/types';
import { NBLeaderReviewTableContent } from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/NewBusinessLeaderReview';
import { QuotationSlice } from 'features/proposal/types';
import { TeamCompareByOptionKey } from 'features/teamManagement/types';
import {
  BioMetricInfo,
  BuildCountry,
  Customer,
  Lead,
  LeadFormValues,
  LeadsFilters,
} from 'types';
import { DashboardInfo, TodayTasksInfo } from './home';
import { MainTabParamList, RootStackParamList } from './navigation';
import { TeamPerformanceViewType } from './team';
import { EcoachModuleType } from 'features/ecoach/constants/moduleTypes';

// Types for Zustand store
export interface StoreSlice
  extends AppSlice,
  AuthSlice,
  LeadSlice,
  CandidateSlice,
  HomeSlice,
  QuotationSlice,
  TeamSlice,
  NewsSlice,
  PerformanceSlice,
  EcoachSlice,
  PolicySlice,
  CaseSlice { }

export interface AppSlice extends AppState {
  appActions: {
    setAppLoading: () => void;
    setAppIdle: () => void;
    setAppError: () => void;
    setIsSearchingModalOn: ({
      bool,
      triggeredFrom,
    }: {
      bool: boolean;
      triggeredFrom: AppState['searchTriggeredFrom'] | null;
    }) => void;
    setBottomBarHeightInZus: (height: number) => void;
    setTogglePushNotificationEnabled: (enabled: boolean) => void;
    setIsTablet: (isTablet: boolean) => void;
    setFirstLoginTime: (timestamp: string | null) => void;
    setLastSurveyTriggerTime: (timestamp: string | null) => void;
    setAppLanguage: (language: string) => void;
    setDeviceId: (deviceId: string) => void;
  };
}

export type LanguageCodes = BuildCountry | 'en';

export interface AppState {
  loadingStatus: LOADING_STATUS;
  isSearchingModalOn: boolean;
  bottomBarHeightInZus: number;
  searchTriggeredFrom: keyof (RootStackParamList & MainTabParamList) | null;
  pushNotificationEnabled: boolean;
  isTablet: boolean;
  firstLoginTime: string | null;
  lastSurveyTriggerTime: string | null;
  language: LanguageCodes | string;
  deviceId: string | null;
}

export enum LOADING_STATUS {
  LOADING = 'loading',
  IDLE = 'idle',
  ERROR = 'error',
}

export enum AgentAction {
  CONFIRM_SI = 'CONFIRM_SI',
  SIGN_PO = 'SIGN_PO',
  SELECT_PAYMENT = 'SELECT_PAYMENT',
}

export interface AuthSlice {
  auth: AuthState;
  authActions: {
    login: (
      accessToken: string,
      refreshToken: string,
      idToken?: string,
    ) => void;
    logout: () => void;
    saveBiometricInfo: (biometricInfo: BioMetricInfo) => void;
    enableBiometric: () => void;
    disableBiometric: () => void;
  };
}

export interface AuthState {
  agentCode: string | null;
  previousAgentCode: string | null;
  authInfo: AuthInfo;
  agentInfo: AgentInfo;
  channel?: string;
  bioMetricEnabled: boolean | null;
  bioMetricInfo: BioMetricInfo;
  isLoggedInUntilAppKilled: boolean;
  // awardsConfig: AwardsConfig;
}

type AuthInfo = {
  accessToken: string | null;
  refreshToken: string | null;
  idToken?: string;
};

type AgentInfo = {
  profilePic: string;
  personalDetails: AgentDetails;
  licenses: AgentLicense[];
  contacts: AgentContact;
  achievements: {
    isMDRT: boolean;
    isELITE: boolean;
  };
  companyTitle: string;
  agentStatus: {
    isAgentLicenseActive: boolean | null;
    isAgentLicenseExpired: boolean | null;
    canSellUnitlinkProduct: boolean | null;
  };
  trainingCourses: string[];
};
type AgentContact = {
  email: string;
  code?: string;
  mobilePhone: string;
  facebook?: string;
  linkedin?: string;
};

type AgentDetails = {
  title: string;
  firstName: string;
  lastName: string;
  fullName: string;
  dateOfBirth: string;
};

type AgentLicense = {
  id: string;
  type: {
    name: string;
    code: string;
  };
  isActive: boolean;
  startDate: string;
  endDate: string;
};

export type AgentReward = {
  isShown: boolean;
  contentId: string | null;
  dismissUntil: Date | null;
};

export type LogLocation = {
  action: string;
  latitude: number;
  longitude: number;
  createdDT: string | Date;
}

export interface LeadSlice {
  lead: LeadState;
  leadActions: {
    today: {
      toggleSort: () => void;
      updateFilteredLength: (filteredLength: number) => void;
      updateFilter: (leadFilters: LeadsFilters) => void;
      setShowToolTip: (show: boolean) => void;
    };
    others: {
      toggleSort: () => void;
      updateFilteredLength: (filteredLength: number) => void;
      updateFilter: (leadFilters: LeadsFilters) => void;
    };
    search: {
      updateFilter: (leadFilters: LeadsFilters) => void;
    };
    updateSearchBarHeight: (height: number) => void;
    updatePrimaryBarHeight: (height: number) => void;
    updateSecondaryBarHeight: (height: number) => void;
    updateTertiaryBarHeight: (height: number) => void;
    resetFilters: () => void;
    resetTodayFilters: () => void;
    activities: {
      toggleSort: () => void;
      resetSort: () => void;
    };
    addLeadForm: {
      saveDraft: (formData: LeadFormValues) => void;
      clearDraft: () => void;
    };
  };
  leadSearchActions: {
    setSearchModalShow: () => void;
    showHeaderSearch: () => void;
    hideHeaderSearch: () => void;
    updateRecentLeadSearch: (item: Lead) => void;
    updateRecentCustomerSearch: (item: Customer) => void;
    trimRecentSearch: () => void;
  };
  leadSearch: LeadState['leadSearch'];
  leadProfileActions: {
    updateInquiringLeadId: (leadId: string) => void;
    updateTopBarHeight: (height: number) => void;
  };
  expiredLeadActions: {
    setCheckedExpiredLeads: () => void;
    setExpiredLeadSkippedTime: () => void;
  };
}

export interface LeadState {
  today: {
    sortByNewest: boolean;
    filteredLength?: number;
    filters: LeadsFilters;
    showToolTip: boolean;
  };
  others: {
    sortByNewest: boolean;
    filteredLength?: number;
    filters: LeadsFilters;
  };
  leadSearch: {
    searchModalShow: boolean;
    isHeaderSearchShow: boolean;
    recentSearchLeadItem: Lead[];
    recentSearchCustomerItem: Customer[];
    filters: LeadsFilters;
  };
  profile: {
    topBarHeight: number;
  };
  // UI related
  searchBarHeight: number;
  primaryBarHeight: number;
  secondaryBarHeight: number;
  tertiaryBarHeight: number;
  // Lead profile related
  inquiringLeadId: string;
  //expired Lead checked
  isExpiredLeadChecked: boolean;
  expiredLeadSkippedTime: string | null;
  activities: {
    sortByNewest: boolean;
  };
  draftAddLeadForm: {
    data: LeadFormValues;
    savedAt: string;
  } | null;
}

export interface CandidateSlice {
  candidate: CandidateState;
  candidateActions: {
    updateSearchBarHeight: (height: number) => void;
    updatePrimaryBarHeight: (height: number) => void;
    updateSecondaryBarHeight: (height: number) => void;
    updateTertiaryBarHeight: (height: number) => void;
  };
  candidateSearchActions: {
    setSearchModalShow: () => void;
    showHeaderSearch: () => void;
    hideHeaderSearch: () => void;
    updateRecentCandidateSearch: (item: CandidateListItem) => void;
    trimRecentSearch: () => void;
  };
  candidateSearch: CandidateState['candidateSearch'];
}

export interface CandidateState {
  candidateSearch: {
    searchModalShow: boolean;
    isHeaderSearchShow: boolean;
    recentSearchCandidateItem: CandidateListItem[];
  };
  // UI related
  searchBarHeight: number;
  primaryBarHeight: number;
  secondaryBarHeight: number;
  tertiaryBarHeight: number;
}

export interface HomeSlice {
  home: HomeState;
  homeActions: {
    onCloseReorderTooltip: () => void;
    disableMockIsLoading: () => void;
    checkIsMorningMode: () => void;
    overView: { setNewCardsOrder: (newCardsOrder: DashboardInfo[]) => void };
    myTasks: {
      setNewSectionsOrder: (newSectionsOrder: TodayTasksInfo[]) => void;
      setScheduledNotifications: (
        newScheduledNotifications: NotificationRequest[],
      ) => void;
    };
    setIsShowWhiteBottom: (enabled: boolean) => void;
    setAgentPopupIsShown: (isShown: boolean) => void;
    setDismissAgentRewardUntill: (args: Omit<AgentReward, 'isShown'>) => void;
    onAddLeadDialogConfirm: () => void;
    onTermsAndConditionsAgree: () => void;
  };
}

export interface HomeState {
  mockIsLoading: boolean; // TODO-Mario: Remove after integration - Mock skeleton loading
  isMorningMode: boolean;
  showReorderTooltip: boolean;
  overView: { cardsOrder: DashboardInfo[] };
  myTasks: {
    sectionsOrder: TodayTasksInfo[];
    scheduledNotifications: NotificationRequest[];
  };
  isShowWhiteBottom: boolean;
  agentReward: AgentReward;
  addLeadPrivacyDialogConfirmed: boolean;
  showTermsAndConditions: boolean;
}

export interface NewsSlice {
  news: NewsState;
  newsActions: {
    updateBookmarkedNewsIdMap: (id: string, markAs: boolean) => void;
  };
}

export type NewsState = {
  bookmarkedNewsIdMap: Record<string, boolean>;
};

export interface TeamSlice {
  team: TeamState;
  teamActions: {
    updateTeamManagementViewType: (viewType: TeamPerformanceViewType) => void;
    updateTeamManagementCompareByOptionKey: (
      key: TeamCompareByOptionKey,
    ) => void;
  };
}

export type TeamState = {
  teamManagementViewType: TeamPerformanceViewType;
  teamManagementCompareByOptionKey: TeamCompareByOptionKey;
};

export interface PerformanceSlice {
  performance: PerformanceState;
  performanceActions: {
    updateTipYourPerformance: () => void;
  };
}

export type PerformanceState = {
  tipYourPerformance: boolean;
};

export interface CaseSlice {
  case: CaseState;
  caseActions: {
    setActiveCase: (caseId: string) => void;
    clearActiveCase: () => void;
  };
}

export type CaseState = {
  caseId?: string;
};

export type HomepageBackgroundType = {
  background_image_tablet: string;
  background_image_mobile: string;
};

export interface EcoachSlice {
  ecoach: EcoachState;
  ecoachActions: {
    setupConfiguration: ({
      isQuickfire,
      quickfireVideoUrl,
      homepageBackground,
      quickfireCompletedHearts,
      productConfig,
      quickfireProductConfig,
      appointmentProductConfig,
      moduleAvailability,
      videoToAudioSpeed,
      objectionHandlingProductConfig,
      trainerGuruAvailability,
    }: {
      isQuickfire: boolean;
      quickfireVideoUrl: string;
      homepageBackground?: HomepageBackgroundType;
      quickfireCompletedHearts: number;
      productConfig?: Record<BuildCountry, EcoachProductConfig[]>;
      quickfireProductConfig?: Record<BuildCountry, EcoachProductConfig[]>;
      appointmentProductConfig?: Record<BuildCountry, AppointmentProductConfig[]>;
      moduleAvailability?: ModuleAvailability;
      videoToAudioSpeed: number;
      objectionHandlingProductConfig?: Record<BuildCountry, EcoachProductConfig[]>;
      trainerGuruAvailability?: TrainerGuruAvailability;
    }) => void;
    setHistoryTabIndex: (index: number) => void;
    setHistoryTabScrollX: (x: number) => void;
  };
}

export interface PolicyState {
  reviewPolicyList: NBLeaderReviewTableContent[];
}
export type PolicySlice = {
  policy: PolicyState;
  policyActions: {
    updateReviewPolicyList: (list: NBLeaderReviewTableContent[]) => void;
  };
};

export type ModuleAvailability = {
  [EcoachModuleType.FACE_TO_FACE_MEETING]?: boolean;
  [EcoachModuleType.APPOINTMENT_SETTING]?: boolean;
  [EcoachModuleType.PRODUCT_KNOWLEDGE]?: boolean;
  [EcoachModuleType.OBJECTION_HANDLING]?: boolean;
};

export type TrainerGuruAvailability = {
  agency?: boolean;
  banca?: boolean;
  affinity?: boolean;
};

export type EcoachState = {
  isQuickfire: boolean;
  quickfireVideoUrl?: string | null;
  homepageBackground?: HomepageBackgroundType;
  quickfireCompletedHearts: number;
  productConfig?: Record<BuildCountry, EcoachProductConfig[]>;
  quickfireProductConfig?: Record<BuildCountry, EcoachProductConfig[]>;
  videoToAudioSpeed: number;
  appointmentProductConfig?: Record<BuildCountry, AppointmentProductConfig[]>;
  moduleAvailability?: ModuleAvailability;
  objectionHandlingProductConfig?: Record<BuildCountry, EcoachProductConfig[]>;
  trainerGuruAvailability?: TrainerGuruAvailability;
  historyTabIndex?: number;
  historyTabScrollX?: number;
};

export type EcoachProductConfig = {
  product_name: string;
  product_description: string;
  product_code: string;
  policy_type?: string;
  order: number;
  product_image?: string;
  product_image_base64?: string;
  clickable: boolean;
  channel?: string;
  language?: string;
};

export type Personas = {
  persona_age: number;
  persona_gender: string;
  persona_name: string;
};

export type AppointmentProductConfig = {
  product_group: string;
  product_group_description: string;
  clickable: boolean;
  use_elevenlabs?: boolean;
  elevenlabs_product_code?: string;
  personaSelection: string;
  product_description: string;
  product_code: string;
  product_name: string;
  personas: Personas;
  language?: string;
  order: number;
  cai_metadata: {
    mode: string;
    context: {
      customer_name: string;
      conversation_scenario: string;
      gender: string;
      product_name: string;
      age_group: string;
    };
    subcomponents: {
      country: string;
      scenario: string;
      resistance_type: string;
      communication_style: string;
      language: string;
      personas: string;
      customer_segment: string;
      products: string;
    };
  };
};
