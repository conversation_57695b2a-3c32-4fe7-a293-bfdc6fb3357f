import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { H6, H7, LargeBody, Row } from 'cube-ui-components';
import { differenceInYears } from 'date-fns';
import useGetNatureOfBusinessOption from 'features/lead/hooks/useGetNatureOfBusinessOption';
import { useMappedLeadProfile } from 'features/lead/ib/LeadProfile/tablet/config';
import { getSourceLabels } from 'features/lead/utils';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import { useGetOptionList } from 'hooks/useGetOptionList';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CHANNELS } from 'types/channel';
import { country } from 'utils/context';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import getLabelFromValue from 'utils/helper/getLabelFromValue';

const getSalutation = ({
  genderCode,
  gender,
  maritalStatusCode,
}: {
  genderCode?: string;
  gender?: string;
  maritalStatusCode?: string;
}) => {
  if (!genderCode && !gender) return '--';
  if (genderCode === 'M' || gender == 'Male') return 'Mr';
  if (maritalStatusCode === 'M') return 'Mrs';
  return 'Miss';
};

export default function ProfileDetails() {
  const { t } = useTranslation(['lead', 'leadProfile']);
  const { colors, space } = useTheme();
  const insets = useSafeAreaInsets();

  const leadId = useBoundStore.getState().lead.inquiringLeadId;
  const { data: lead } = useGetLeadByLeadId(leadId);

  const { data: optionList } = useGetOptionList();

  const channel = useGetCubeChannel();
  const showReferralDetail =
    channel === CHANNELS.BANCA || channel === CHANNELS.AFFINITY;

  const isIndividualLead = lead?.isIndividual;

  /**
   * Lead profile data
   */
  const salutation =
    getLabelFromValue(
      optionList?.TITLE?.options,
      lead?.salutation?.toUpperCase(),
    ) ??
    getSalutation({
      genderCode: lead?.genderCode,
      maritalStatusCode: lead?.maritalStatusCode,
    }) ??
    '--';
  const fullName = `${lead?.firstName ?? ''} ${lead?.middleName ?? ''} ${
    lead?.lastName ?? ''
  }`;
  const extensionName =
    getLabelFromValue(optionList?.EXTENSION?.options, lead?.nameExtension) ??
    '--';
  const gender =
    getLabelFromValue(optionList?.GENDER?.options, lead?.genderCode) ?? '--';
  const dateOfBirth = lead?.birthDate ? dateFormatUtil(lead?.birthDate) : '--';
  const age = lead?.birthDate
    ? differenceInYears(new Date(), new Date(lead?.birthDate)) + ' y.o.'
    : '--';
  const maritalStatus =
    getLabelFromValue(
      optionList?.MARITAL_STATUS?.options,
      lead?.maritalStatusCode,
    ) ?? '--';
  const leadSource = lead?.sourceIds ?? [];
  const mappedLeadSource = getSourceLabels(leadSource)
    .map(label => t(`lead:source.${label}`))
    .join(', ');
  const mobilePhoneCountryCode = lead?.mobilePhoneCountryCode
    ? '+' + lead?.mobilePhoneCountryCode + ' '
    : '';
  const mobilePhoneNumber = lead?.mobilePhoneNumber ?? '';
  const secondaryMobilePhoneCountryCode = lead?.workPhoneCountryCode
    ? '+' + lead?.workPhoneCountryCode + ' '
    : lead?.homePhoneCountryCode
    ? '+' + lead?.homePhoneCountryCode + ' '
    : '';
  const secondaryMobilePhoneNumber = lead?.workPhoneNumber
    ? lead.workPhoneNumber
    : lead?.homePhoneNumber ?? '';
  const mobileNumber = mobilePhoneCountryCode + mobilePhoneNumber;
  const secondaryMobileNumber =
    secondaryMobilePhoneCountryCode + secondaryMobilePhoneNumber;
  const email = lead?.email ?? '--';
  const affiliateCode = lead?.extra?.affiliate_code ?? '--';
  const bltsRefNumber = lead?.extra?.alts_blts_ref_num ?? '--';
  const servicingBranch = lead?.extra?.service_branch ?? '--';
  const bankCustomerId = lead?.extra?.bank_customer_id ?? '--';
  const referrerCode = lead?.extra?.referrer_code ?? '--';
  const campaignCode = '--'; // pending for API response update

  // * Entity fields
  // const typeOfEntity = lead?.typeOfEntity ?? '--';
  const entityName = lead?.companyName ?? '--';
  const natureOfBusiness = lead?.occupationIndustryCode ?? '--';
  const jobTitle = lead?.jobTitle ?? '--';
  const natureOfBusinessOptions = useGetNatureOfBusinessOption();

  if (country === 'ib' || country === 'id') {
    return (
      <>
        <ScreenHeader
          route={'ProfileDetails'}
          isLeftCrossBackShown
          showBottomSeparator={false}
        />
        <ProfileDetailSectionV2 />
      </>
    );
  }

  /**
   * Entity lead profile
   */
  if (country === 'ph' && !isIndividualLead) {
    return (
      <>
        <ScreenHeader
          route={'ProfileDetails'}
          isLeftCrossBackShown
          showBottomSeparator={false}
        />

        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ backgroundColor: colors.background }}
          contentContainerStyle={{ paddingBottom: insets.bottom }}>
          <SectionContainer>
            <Title fontWeight="bold">
              {t('leadProfile:leadProfile.profileDetails.personalInfo')}
            </Title>
            <FieldsContainer>
              {/* <InfoField
                fieldName={t(
                  'leadProfile:leadProfile.profileDetails.typeOfEntity',
                )}
                fieldValue={typeOfEntity}
              /> */}
              <InfoField
                fieldName={t(
                  'leadProfile:leadProfile.profileDetails.entityName',
                )}
                fieldValue={entityName}
              />
              <InfoField
                fieldName={t(
                  'leadProfile:leadProfile.profileDetails.natureOfBusiness',
                )}
                fieldValue={natureOfBusiness}
              />
              <InfoField
                fieldName={t(
                  'leadProfile:leadProfile.profileDetails.leadSource',
                )}
                fieldValue={mappedLeadSource}
              />
            </FieldsContainer>
          </SectionContainer>

          <SectionContainer>
            <Title fontWeight="bold">
              {t('leadProfile:leadProfile.profileDetails.contactDetails')}
            </Title>
            <FieldsContainer style={{ paddingBottom: space[4] }}>
              <SecondaryTitle fontWeight="bold">
                {t(
                  'leadProfile:leadProfile.profileDetails.primaryContactDetails',
                )}
              </SecondaryTitle>
              <InfoField
                fieldName={t(
                  'leadProfile:leadProfile.profileDetails.primaryMobileNumber',
                )}
                fieldValue={mobileNumber}
              />
              <InfoField
                fieldName={t('leadProfile:leadProfile.profileDetails.email')}
                fieldValue={email}
              />
            </FieldsContainer>
            {Boolean(lead?.workPhoneNumber || lead?.homePhoneNumber) && (
              <FieldsContainer>
                <SecondaryTitle fontWeight="bold">
                  {t(
                    'leadProfile:leadProfile.profileDetails.secondaryContactDetails',
                  )}
                </SecondaryTitle>
                <InfoField
                  fieldName={t(
                    'leadProfile:leadProfile.profileDetails.secondaryMobileNumber',
                  )}
                  fieldValue={secondaryMobileNumber}
                />
              </FieldsContainer>
            )}
          </SectionContainer>

          <SectionContainer>
            <Title fontWeight="bold">
              {t('leadProfile:leadProfile.profileDetails.authorizedDetails')}
            </Title>
            <FieldsContainer>
              <InfoField
                fieldName={t(
                  'leadProfile:leadProfile.profileDetails.salutation',
                )}
                fieldValue={salutation}
              />
              <InfoField
                fieldName={t('leadProfile:leadProfile.profileDetails.fullName')}
                fieldValue={fullName}
              />
              {country === 'ph' && (
                <InfoField
                  fieldName={t(
                    'leadProfile:leadProfile.profileDetails.extensionName',
                  )}
                  fieldValue={extensionName}
                />
              )}
              <InfoField
                fieldName={t('leadProfile:leadProfile.profileDetails.position')}
                fieldValue={jobTitle}
              />
            </FieldsContainer>
          </SectionContainer>

          {showReferralDetail && (
            <SectionContainer>
              <Title fontWeight="bold">
                {t('leadProfile:leadProfile.profileDetails.referralDetails')}
              </Title>
              <FieldsContainer>
                <InfoField
                  fieldName={t(
                    'leadProfile:leadProfile.profileDetails.bltsRefNumber',
                  )}
                  fieldValue={bltsRefNumber}
                />
                <InfoField
                  fieldName={t(
                    'leadProfile:leadProfile.profileDetails.servicingBranch',
                  )}
                  fieldValue={servicingBranch}
                />
                <InfoField
                  fieldName={t(
                    'leadProfile:leadProfile.profileDetails.referrerCode',
                  )}
                  fieldValue={referrerCode}
                />
                <InfoField
                  fieldName={t(
                    'leadProfile:leadProfile.profileDetails.bankCustomerId',
                  )}
                  fieldValue={bankCustomerId}
                />
              </FieldsContainer>
            </SectionContainer>
          )}
        </ScrollView>
      </>
    );
  }

  /**
   * Individual lead profile
   */
  return (
    <>
      <ScreenHeader
        route={'ProfileDetails'}
        isLeftCrossBackShown
        showBottomSeparator={false}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{ backgroundColor: colors.background }}
        contentContainerStyle={{ paddingBottom: insets.bottom }}>
        <SectionContainer>
          <Title fontWeight="bold">
            {t('leadProfile:leadProfile.profileDetails.personalInfo')}
          </Title>
          <FieldsContainer>
            <InfoField
              fieldName={t('leadProfile:leadProfile.profileDetails.salutation')}
              fieldValue={salutation}
            />
            <InfoField
              fieldName={t('leadProfile:leadProfile.profileDetails.fullName')}
              fieldValue={fullName}
            />
            <InfoField
              fieldName={t(
                'leadProfile:leadProfile.profileDetails.extensionName',
              )}
              fieldValue={extensionName}
            />
            <InfoField
              fieldName={t('leadProfile:leadProfile.profileDetails.gender')}
              fieldValue={gender}
            />
            <InfoField
              fieldName={t('leadProfile:leadProfile.profileDetails.dob')}
              fieldValue={dateOfBirth}
            />
            <InfoField
              fieldName={t('leadProfile:leadProfile.profileDetails.age')}
              fieldValue={age}
            />
            <InfoField
              fieldName={t(
                'leadProfile:leadProfile.profileDetails.maritalStatus',
              )}
              fieldValue={maritalStatus}
            />
            <InfoField
              fieldName={t('leadProfile:leadProfile.profileDetails.leadSource')}
              fieldValue={mappedLeadSource}
            />
          </FieldsContainer>
        </SectionContainer>

        <SectionContainer>
          <Title fontWeight="bold">
            {t('leadProfile:leadProfile.profileDetails.contactDetails')}
          </Title>
          <FieldsContainer>
            <InfoField
              fieldName={t(
                'leadProfile:leadProfile.profileDetails.mobileNumber',
              )}
              fieldValue={mobileNumber}
            />
            <InfoField
              fieldName={t('leadProfile:leadProfile.profileDetails.email')}
              fieldValue={email}
            />
          </FieldsContainer>
        </SectionContainer>

        <SectionContainer>
          <Title fontWeight="bold">
            {t('leadProfile:leadProfile.profileDetails.referralDetails')}
          </Title>
          <FieldsContainer>
            <InfoField
              fieldName={t(
                'leadProfile:leadProfile.profileDetails.affiliateCode',
              )}
              fieldValue={affiliateCode}
            />
            <InfoField
              fieldName={t(
                'leadProfile:leadProfile.profileDetails.campaignCode',
              )}
              fieldValue={campaignCode}
            />
            <InfoField
              fieldName={t(
                'leadProfile:leadProfile.profileDetails.bltsRefNumber',
              )}
              fieldValue={bltsRefNumber}
            />
            <InfoField
              fieldName={t(
                'leadProfile:leadProfile.profileDetails.servicingBranch',
              )}
              fieldValue={servicingBranch}
            />
            <InfoField
              fieldName={t(
                'leadProfile:leadProfile.profileDetails.bankCustomerId',
              )}
              fieldValue={bankCustomerId}
            />
            <InfoField
              fieldName={t(
                'leadProfile:leadProfile.profileDetails.referrerCode',
              )}
              fieldValue={referrerCode}
            />
          </FieldsContainer>
        </SectionContainer>
      </ScrollView>
    </>
  );
}

function ProfileDetailSectionV2() {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['lead', 'leadProfile']);
  const { bottom } = useSafeAreaInsets();

  const leadId = useBoundStore.getState().lead.inquiringLeadId;
  const { data: lead } = useGetLeadByLeadId(leadId);

  const { data: optionList } = useGetOptionList();
  const isIndividualLead = lead?.isIndividual;

  const { contactDetailData, rolePersonalInfoData, referralDetailData } =
    useMappedLeadProfile({
      lead,
      isIndividualLead,
      dobTypes: 'standaloneAgeField',
    });

  const leadActivityList = lead
    ? lead?.transactions.filter(activity => {
        return (
          activity.action === 'contact' || activity.action === 'appointment'
        );
      })
    : [];

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{ backgroundColor: colors.background }}
      contentContainerStyle={{ paddingBottom: bottom }}>
      <SectionContainer>
        <Title fontWeight="bold">
          {t('leadProfile:leadProfile.profileDetails.personalInfo')}
        </Title>
        <FieldsContainer>
          {rolePersonalInfoData?.map(person => (
            <InfoField
              key={person?.label}
              fieldName={person?.label}
              fieldValue={person?.content}
            />
          ))}
        </FieldsContainer>
      </SectionContainer>

      <SectionContainer>
        <Title fontWeight="bold">
          {t('leadProfile:leadProfile.profileDetails.contactDetails')}
        </Title>
        <FieldsContainer style={{ paddingBottom: space[4] }}>
          {contactDetailData?.map(contact => (
            <InfoField
              key={contact?.label}
              fieldName={contact?.label}
              fieldValue={contact?.content}
            />
          ))}
        </FieldsContainer>
      </SectionContainer>

      <SectionContainer>
        <Title fontWeight="bold">
          {country === 'id'
            ? t('leadProfile:leadProfile.profileDetails.sourceDetails')
            : t('leadProfile:leadProfile.profileDetails.referralDetails')}
        </Title>
        <FieldsContainer style={{ paddingBottom: space[4] }}>
          {referralDetailData?.map(referral => (
            <InfoField
              key={referral?.label}
              fieldName={referral?.label}
              fieldValue={referral?.content}
            />
          ))}
        </FieldsContainer>
      </SectionContainer>

      <SectionContainer>
        <Title fontWeight="bold">
          {t('leadProfile:leadProfile.profileDetails.remark')}
        </Title>
        {leadActivityList.length == 0 && (
          <LargeBody color={colors.palette.fwdGreyDarkest}>--</LargeBody>
        )}
        {leadActivityList
          .sort(
            (a, b) =>
              new Date(b.actionAt).getTime() - new Date(a.actionAt).getTime(),
          )
          .map(item => {
            const value =
              t(
                `leadProfile:leadProfile.activityRecord.action.${
                  item?.action as 'contact' | 'appointment'
                }`,
              ) +
              ` - ${
                item?.extra?.feedback
                  ? t(
                      `leadProfile:leadProfile.activityRecord.feedback.${item?.extra?.feedback}`,
                    )
                  : t(
                      `leadProfile:leadProfile.activityRecord.action.${
                        item?.action as 'contact' | 'appointment'
                      }`,
                    )
              }`;
            return (
              <Fragment key={item.id}>
                <InfoField
                  fieldName={dateFormatUtil(item?.actionAt)}
                  fieldValue={value}
                />
              </Fragment>
            );
          })}
      </SectionContainer>
    </ScrollView>
  );
}

function InfoField({
  fieldName,
  fieldValue,
}: {
  fieldName?: string | null;
  fieldValue?: string | string[] | number | null;
}) {
  const { colors, space } = useTheme();
  return (
    <Row style={{ alignItems: 'flex-start', gap: space[4] }}>
      <View style={{ flex: 1 }}>
        <LargeBody color={colors.palette.fwdGreyDarkest}>{fieldName}</LargeBody>
      </View>
      <View style={{ flex: 1 }}>
        <LargeBody>{fieldValue}</LargeBody>
      </View>
    </Row>
  );
}

const SectionContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  padding: theme.space[4],
}));

const Title = styled(H6)(({ theme }) => ({
  marginBottom: theme.space[4],
}));

const FieldsContainer = styled.View(({ theme }) => ({
  gap: theme.space[2],
}));

const SecondaryTitle = styled(H7)(({ theme }) => ({
  color: theme.colors.primary,
}));
