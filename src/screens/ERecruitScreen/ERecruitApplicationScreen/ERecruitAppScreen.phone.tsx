import React from 'react';
import { country } from 'utils/context';
import ApplicationForm from 'features/eRecruit/ib/phone/ApplicationForm';
import IDNApplicationForm from 'features/eRecruit/id/phone/ApplicationForm';
import ApplicationScreen from 'features/eRecruit/ph/phone/ApplicationScreen';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function ERecruitAppScreenPhone() {
  switch (country) {
    case 'ph':
      return <ApplicationScreen />;
    case 'my':
    case 'ib':
      return <ApplicationForm />;
    case 'id':
      return <IDNApplicationForm />;
    default:
      return <NotFoundScreen />;
  }
}
