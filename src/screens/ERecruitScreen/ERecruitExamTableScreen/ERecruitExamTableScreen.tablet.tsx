import React from 'react';
import { country } from 'utils/context';
import ExamTableScreenPH from 'features/eRecruit/ph/ExamTableScreen';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function ERecruitExamTableScreenTablet() {
  switch (country) {
    case 'ph':
      return <ExamTableScreenPH />;
    case 'my':
      return <NotFoundScreen />;
    case 'ib':
      return <NotFoundScreen />;
    case 'id':
      return <NotFoundScreen />;
    default:
      return <NotFoundScreen />;
  }
}
