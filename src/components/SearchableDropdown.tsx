import React, {
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
  useMemo,
} from 'react';
import { Keyboard, Pressable, StyleProp, TextStyle, View, ViewProps, ViewStyle } from 'react-native';
import { TextField, TextFieldRef, Icon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import useLatest from 'hooks/useLatest';
import SearchableDropdownPanel from 'components/SearchableDropdownPanel';
import SearchableDropdownModal from 'components/SearchableDropdownModal';

export type SearchableDropdownProps<T, V> = ViewProps & {
  label?: string;
  modalTitle?: string;
  actionLabel?: string;
  disabled?: boolean;
  preventPopup?: boolean;
  hint?: string;
  error?: string;
  isError?: boolean;
  isModal?: boolean;
  data: T[];
  getItemLabel: (item: T) => string;
  getItemValue: (item: T) => V;
  getDisplayedLabel?: (item: T) => string;
  getExternalDisplayedLabel?: () => string;
  searchable?: boolean;
  searchMode?: 'auto' | 'manual';
  searchLabel?: string;
  onQuery?: (text: string) => void;
  multiline?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  dropDownModalStyle?: ViewStyle;
  inputStyle?: StyleProp<TextStyle>;
  highlight?: boolean;
  getItemDisabled?: (item: T) => boolean;
  keyExtractor?: (item: T) => string;
} & (
    | {
        type?: 'single';
        value?: V;
        onChange?: (value: V) => void;
      }
    | {
        type?: 'multiple';
        value?: V[];
        onChange?: (value: V[]) => void;
      }
  );

export type SearchableDropdownRef = TextFieldRef;

function SearchableDropdownInner<T, V>(
  {
    label,
    modalTitle,
    actionLabel,
    disabled,
    preventPopup,
    hint,
    error,
    isError,
    isModal,
    type,
    value,
    onChange,
    data,
    getItemLabel,
    getItemValue,
    getDisplayedLabel,
    getExternalDisplayedLabel,
    searchable,
    searchMode,
    searchLabel,
    onQuery,
    multiline,
    onFocus,
    onBlur,
    inputStyle,
    dropDownModalStyle,
    highlight,
    getItemDisabled,
    keyExtractor,
    ...viewProps
  }: SearchableDropdownProps<T, V>,
  ref: React.ForwardedRef<SearchableDropdownRef>,
) {
  type ||= 'single'; // because default param not working
  const { colors } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const textFieldRef = useRef<TextFieldRef>(null);
  const getItemValueRef = useLatest(getItemValue);

  const getExternalDisplayedLabelRef = useLatest(getExternalDisplayedLabel);
  const getDisplayedLabelRef = useLatest(getDisplayedLabel || getItemLabel);
  const displayedLabel = useMemo(() => {
    if (getExternalDisplayedLabelRef.current) {
      return getExternalDisplayedLabelRef.current();
    }
    if (type === 'single') {
      const selectedItem = data?.find?.(
        item => getItemValueRef.current?.(item) === value,
      );
      return selectedItem ? getDisplayedLabelRef.current?.(selectedItem) : '';
    } else if (type === 'multiple') {
      return data
        ?.filter?.(item => value?.includes(getItemValueRef.current?.(item)))
        .map(i => getDisplayedLabelRef.current?.(i))
        .join(', ');
    }
  }, [
    data,
    getDisplayedLabelRef,
    getExternalDisplayedLabelRef,
    getItemValueRef,
    type,
    value,
  ]);

  useImperativeHandle(ref, () => textFieldRef.current as SearchableDropdownRef);

  const renderSearchPanel = () => {
    if (isModal) {
      return (
        <SearchableDropdownModal
          visible={modalVisible}
          title={modalTitle ?? label}
          data={data}
          searchable={searchable}
          searchLabel={searchLabel ?? `Search for ${label}`}
          getItemValue={getItemValue}
          getItemLabel={getItemLabel}
          onDismiss={() => {
            setModalVisible(false);
            onQuery?.('');
            textFieldRef.current?.blur();
            onBlur?.();
          }}
          value={value as V}
          onDone={(item: V) => onChange?.(item)}
          dropDownModalStyle={dropDownModalStyle}
          getItemDisabled={getItemDisabled}
        />
      );
    }
    return (
      modalVisible && (
        <SearchableDropdownPanel
          keyExtractor={keyExtractor}
          title={modalTitle ?? label}
          actionLabel={actionLabel}
          data={data}
          searchable={searchable}
          searchMode={searchMode}
          searchLabel={searchLabel ?? label}
          onQuery={onQuery}
          getItemValue={getItemValue}
          getItemLabel={getItemLabel}
          getItemDisabled={getItemDisabled}
          onDismiss={() => {
            setModalVisible(false);
            onQuery?.('');
            textFieldRef.current?.blur();
            onBlur?.();
          }}
          {...(type === 'single'
            ? {
                type: 'single',
                value: value as V,
                onDone: (item: V) => onChange?.(item),
              }
            : {
                type: 'multiple',
                value: (value || []) as V[],
                onDone: (items: V[]) =>
                  (onChange as (value: V[]) => void)?.(items),
              })}
        />
      )
    );
  };

  return (
    <View {...viewProps}>
      <TextField
        ref={textFieldRef}
        label={label}
        value={displayedLabel}
        disabled={disabled}
        hint={hint}
        error={error}
        isError={isError}
        onFocus={() => {
          if (!preventPopup) {
            setModalVisible(true);
            onFocus?.();
          }
          Keyboard.dismiss();
        }}
        onBlur={Keyboard.dismiss}
        right={
          disabled ? null : (
            <Pressable
              hitSlop={ICON_HIT_SLOP}
              onPress={() => textFieldRef.current?.focus()}>
              <Icon.Dropdown
                fill={disabled ? colors.primaryVariant : colors.primary}
              />
            </Pressable>
          )
        }
        showSoftInputOnFocus={false}
        multiline={multiline}
        autoExpand={multiline}
        highlight={highlight}
        inputStyle={inputStyle}
      />
      {renderSearchPanel()}
    </View>
  );
}

const SearchableDropdown = forwardRef(SearchableDropdownInner) as <T, V>(
  props: SearchableDropdownProps<T, V> & {
    ref?: React.ForwardedRef<SearchableDropdownRef>;
  },
) => ReturnType<typeof SearchableDropdownInner>;
export default SearchableDropdown;
