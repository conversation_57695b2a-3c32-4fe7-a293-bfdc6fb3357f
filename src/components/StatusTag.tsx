import styled from '@emotion/native';

import ResponsiveText from 'components/ResponsiveTypography';
import { Row, Typography } from 'cube-ui-components';
import { StyleProp, ViewStyle } from 'react-native';

interface TagProps {
  text: string;
  backgroundColor: string;
  textColor: string;
  left?: React.ReactNode;
  containerStyle?: StyleProp<ViewStyle>;
}

const StatusTag = ({
  text,
  backgroundColor,
  textColor,
  left,
  containerStyle,
}: TagProps) => {
  return (
    <TagContainer backgroundColor={backgroundColor} style={containerStyle}>
      <Row alignItems="center">
        {left}
        <ResponsiveText
          TypographyDefault={Typography.SmallLabel}
          TypographyWide={Typography.Label}
          color={textColor}
          fontWeight="medium">
          {text}
        </ResponsiveText>
      </Row>
    </TagContainer>
  );
};

const TagContainer = styled.View<{ backgroundColor: string }>(
  ({ theme, backgroundColor }) => ({
    backgroundColor: backgroundColor,
    paddingHorizontal: theme.sizes[2],
    paddingVertical: theme.space[1],
    borderRadius: 2,
  }),
);

export default StatusTag;
