import { addErrorBottomToast } from 'cube-ui-components';
import { useDocumentOcr } from 'hooks/useDocumentOcr';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
} from 'react';
import { OcrResult } from 'types/ocr';
import IdUpload, { IdUploadProps, IdUploadRef } from './IdUpload';
import { DocumentType } from 'types/document';
import { country } from 'utils/context';
import { BuildCountry } from 'types';
import { OptionList } from 'types/optionList';
import { withTiming } from 'react-native-reanimated';
import FrontIdIcon from './FrontIdIcon';
import { useOcrDataHandler } from 'hooks/useOcrDataHandler';
import { OcrFeatureProps } from 'components/ImagePicker/utils';

export interface OcrData {
  data: OcrResult['extract'];
  type: string;
  image: { base64: string; name: string; thumbnail?: string };
}

interface Props
  extends Omit<IdUploadProps, 'onFinish' | 'docType'>,
    OcrFeatureProps {
  caseId?: string;
  partyId?: string;
  disabled?: boolean;
  onFinish?: (params: OcrData) => void;
}

export interface OcrRef {
  resetAndOpen: () => void;
  reset: () => void;
}

const Ocr = forwardRef<OcrRef, Props>(
  (
    { onFinish, disabled, ...props }: Props,
    ref: React.ForwardedRef<OcrRef>,
  ) => {
    const {
      mutateAsync: uploadAndScanDocument,
      reset: resetApi,
      isError,
    } = useDocumentOcr();
    const idUploadRef = useRef<IdUploadRef>(null);
    const reset = useCallback(async () => {
      idUploadRef.current?.reset();
      resetApi();
    }, [resetApi]);
    useEffect(() => {
      if (isError) {
        addErrorBottomToast([
          {
            message: 'Upload document failed',
          },
        ]);
        reset();
      }
    }, [isError, reset]);

    useImperativeHandle(ref, () => ({
      resetAndOpen: () => {
        resetApi();
        idUploadRef.current?.resetAndOpen();
      },
      reset,
    }));

    const ocrDataHandler = useOcrDataHandler();

    const onRetake = useCallback(async () => {
      resetApi();
      idUploadRef.current?.resetAndOpen();
    }, [resetApi]);

    return (
      <IdUpload
        ref={idUploadRef}
        variant="highlight"
        logo={FrontIdIcon}
        allowToRetake={!disabled}
        disabled={disabled}
        docType={DocumentType.FrontID}
        {...props}
        onFinish={async (image, progress) => {
          const data = await uploadAndScanDocument({
            body: {
              base64Image: image.base64,
              caseId: props.caseId,
              partyId: props.partyId,
            },
            onProgress: prog => {
              progress.value = withTiming(prog);
            },
          });
          let documentType = '';
          const idTypeMap: Record<BuildCountry, keyof OptionList> = {
            ph: 'PRIMARY_ID_TYPE',
            ib: 'ID_TYPE',
            id: 'ID_TYPE',
            my: 'ID_TYPE',
          };
          if (idTypeMap[country]) {
            switch (data.type) {
              case 'bir':
                documentType = 'TN';
                break;
              case 'drivers_license':
                documentType = 'DL';
                break;
              case 'passport':
                documentType = 'RP';
                break;
              case 'prc':
                documentType = 'PR';
                break;
              case 'sss':
                documentType = 'SS';
                break;
              case 'umid':
                documentType = 'UD';
                break;
              default:
                documentType = data.type;
            }
          }
          idUploadRef.current?.setStatus('finished');
          const valid = await ocrDataHandler(data.extract, onRetake, reset);
          if (!valid) {
            return;
          }
          onFinish?.({
            data: data.extract,
            type: documentType,
            image: {
              base64: image.base64,
              name: image.name,
              thumbnail: image.thumbnail,
            },
          });
        }}
      />
    );
  },
);
export default Ocr;
