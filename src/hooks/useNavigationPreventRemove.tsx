import { NavigationAction, NavigationProp } from '@react-navigation/native';
import React from 'react';
import { RootStackParamList } from 'types';
import { useRootStackNavigation } from './useRootStack';

/**
 * A custom hook that prevents the user from leaving the current screen when a back or pop action is triggered.
 *
 * Behavior:
 * - When `preventRemove` is `true`, any attempt to leave the screen will trigger the `handler` callback instead of navigating away immediately.
 * - Applicable to navigation actions such as:
 *   - Hardware back button (Android)
 *   - `navigation.goBack()` or `navigation.pop()`
 *
 * Limitations:
 * - Does not intercept the native swipe-back gesture on iOS when using `@react-navigation/native-stack` versions below v7.x.x.
 *   This limitation is resolved starting from `@react-navigation/native-stack@v7.x.x`.
 *
 * @param preventRemove - If `true`, navigation away from the screen will be blocked and the `handler` will be invoked.
 * @param handler - Callback invoked when a navigation attempt is blocked.
 *                  Use `navigation.dispatch(data.action)` inside this callback to manually continue the navigation.
 */
export default function useNavigationPreventRemove(
  preventRemove: boolean,
  handler: (
    data: Readonly<{
      action: NavigationAction;
    }>,
    navigation: NavigationProp<RootStackParamList>,
  ) => void,
) {
  const navigation = useRootStackNavigation();

  React.useEffect(
    () =>
      navigation.addListener('beforeRemove', event => {
        if (!preventRemove) {
          // If we don't have unsaved changes, then we don't need to do anything
          return;
        }

        const actionType = event.data.action.type;

        console.log('Before navigation remove action: ', actionType);
        // Allow replace, popToTop, reset... to go through
        if (actionType !== 'GO_BACK' && actionType !== 'POP') {
          return;
        }

        // Prevent default behavior of leaving the screen
        event.preventDefault();

        // Prompt the user before leaving the screen
        handler(event.data, navigation);
      }),
    [navigation, preventRemove, handler],
  );
}
