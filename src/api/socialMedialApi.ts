import { FacebookPage } from 'features/socialMarketing/types';
import { cubeClient } from './cubeClient';
import useBoundStore from 'hooks/useBoundStore';

export type VerifySocialMediaTokenResponse = {
  agentId: string;
  expiredAt: number;
  facebookPageId: string;
};

type PollStatusResponse =
  | 'pending'
  | {
      agentId: string;
      success: boolean;
      accessToken: string;
      error?: null | string;
    };

export const getFacebookPage = () => {
  return cubeClient.get<FacebookPage[]>(
    'proc/agent/social-media/facebook/pages',
  );
};

export const connectFacebookPage = (pageId: string) => {
  return cubeClient.postDirectPayLoad<{ pageId: string }, unknown>(
    'proc/agent/social-media/facebook/page/connect',
    {
      pageId,
    },
  );
};

export const verifyFacebookToken = () => {
  return cubeClient.get<VerifySocialMediaTokenResponse>(
    '/proc/agent/social-media/facebook',
  );
};

export const initiateFacebookPoll = () => {
  return cubeClient.postDirectPayLoad<unknown, string>(
    '/proc/agent/social-media/auth/facebook/initiate',
    {},
  );
};

export const checkFacebookPollStatus = (sessionId: string) => {
  return cubeClient.get<PollStatusResponse>(
    `/proc/agent/social-media/auth/facebook/check/${sessionId}`,
  );
};

export const postToFacebook = (postId: string) => {
  return cubeClient.postDirectPayLoad(`/proc/agent/post/${postId}/share/facebook`, {});
};

export const initiateLinkedInPoll = () => {
  return cubeClient.postDirectPayLoad<unknown, string>(
    '/proc/agent/social-media/auth/linkedin/initiate',
    {},
  );
};

/**
 * Long polling waits up to 60 seconds for a result - Much more efficient than short polling!
 * @param sessionId {string}
 * @returns {Promise<PollStatusResponse>}
 */
export const checkLinkedInLongPollingStatus = (sessionId: string) => {
  return cubeClient.get<PollStatusResponse>(
    `/proc/agent/social-media/auth/linkedin/status/${sessionId}`,
  );
};

/**
 * **Poll every 10 seconds** until you get a completed result
 * @param sessionId {string}
 * @returns {Promise<PollStatusResponse>}
 */
export const checkLinkedInShortPollingStatus = (sessionId: string) => {
  return cubeClient.get<PollStatusResponse>(
    `/proc/agent/social-media/auth/linkedin/check/${sessionId}`,
  );
};

export const verifyLinkedInToken = () => {
  return cubeClient.get<VerifySocialMediaTokenResponse>(
    '/proc/agent/social-media/linkedin',
  );
};
