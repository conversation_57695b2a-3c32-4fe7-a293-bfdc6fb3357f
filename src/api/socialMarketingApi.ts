import * as FileSystem from 'expo-file-system';
import {
  GetAgentPostsListParams,
  GetAiAvatarsResponse,
  GetMarketingActivityStatictics,
  PostPaginationResponse,
  SocialMarketingCreatePostPayload,
  SocialMarketingGeneratePostCaptionPayload,
  SocialMarketingGeneratePostResponse,
  SocialMarketingPost,
  SocialMarketingPostType,
  SocialMarketingRegeneratePostPayload,
  SocialMarketingTemplate,
  SocialMarketingUpdatePostCaptionPayload,
} from 'features/socialMarketing/types';
import useBoundStore from 'hooks/useBoundStore';
import { CONTENT_TYPES } from 'hooks/useContentStack';
import {
  baseUrl,
  contentStackDeliveryToken,
  contentStackEnvironment,
  contentStackKey,
  country,
} from 'utils/context';
import { ensureDirectoryExists } from 'utils/helper/fileUtils';
import apiClient from './apiClient';
import { cubeClient } from './cubeClient';

const AGENT_PROFILE_API = '/proc/agent';

async function fetchAndCacheImage(imagePath: string): Promise<string> {
  const localUri = FileSystem.cacheDirectory + imagePath;

  await ensureDirectoryExists(localUri);

  await FileSystem.downloadAsync(
    `${baseUrl}/api-gateway${imagePath}`,
    localUri,
    {
      headers: {
        Authorization: `Bearer ${
          useBoundStore.getState().auth.authInfo?.accessToken
        }`,
      },
    },
  );

  return localUri;
}

async function parsePostImageFromResponse(
  post: SocialMarketingPost,
): Promise<SocialMarketingPost> {
  try {
    if (
      post.mediaType === SocialMarketingPostType.Image &&
      post.mediaUrl &&
      !post.mediaUrl.startsWith('http')
    ) {
      // Fetch the image and cache it locally
      // This will return the local URI of the cached image
      // or the original URL if caching fails
      const localUrl = await fetchAndCacheImage(post.mediaUrl);
      return {
        ...post,
        localUrl,
      };
    }
  } catch (error) {
    console.error('Error fetching image:', error);
  }

  return post;
}

async function createPost(payload: SocialMarketingCreatePostPayload) {
  const response = await cubeClient.postDirectPayLoad<
    SocialMarketingCreatePostPayload,
    SocialMarketingGeneratePostResponse
  >(`${AGENT_PROFILE_API}/post/generate`, payload);

  const post = await parsePostImageFromResponse(response.post);

  return {
    ...response,
    post,
  };
}

async function regeneratePost(
  postId: string,
  payload: SocialMarketingRegeneratePostPayload,
) {
  const response = await cubeClient.postDirectPayLoad<
    SocialMarketingRegeneratePostPayload,
    SocialMarketingGeneratePostResponse
  >(`${AGENT_PROFILE_API}/post/${postId}/regenerate`, payload);

  const post = await parsePostImageFromResponse(response.post);

  return {
    ...response,
    post,
  };
}

async function getPostSessionStatus(
  postId: string,
  sessionId: string,
): Promise<SocialMarketingGeneratePostResponse | null> {
  const response = await cubeClient.get<SocialMarketingGeneratePostResponse>(
    `${AGENT_PROFILE_API}/post/${postId}/generate/status?sessionId=${sessionId}`,
  );
  const post = await parsePostImageFromResponse(response.post);

  return {
    ...response,
    post,
  };
}

async function getTemplates() {
  const ENDPOINT = `https://cdn.contentstack.io/v3/content_types/${CONTENT_TYPES.IGNITE_TEMPLATE}/entries?environment=${contentStackEnvironment}`;
  const headers: Record<string, string> = {
    api_key: contentStackKey,
    access_token: contentStackDeliveryToken,
  };

  if (['my', 'ib'].includes(country)) {
    headers.branch = 'capricorn';
  }

  const response = await apiClient.get<{
    entries: {
      template: SocialMarketingTemplate[];
    }[];
  }>(ENDPOINT, {
    headers,
  });

  return response?.data?.entries?.[0]?.template ?? [];
}

async function generatePromptFromTopic(
  mediaType: SocialMarketingPostType,
  topic: string,
): Promise<string> {
  const response = await cubeClient.post<
    {
      mediaType: SocialMarketingPostType;
      topic: string;
    },
    string
  >(`${AGENT_PROFILE_API}/post/generate-prompt`, {
    mediaType,
    topic,
  });

  return response;
}

async function generatePostCaption(
  postId: string,
  payload: SocialMarketingGeneratePostCaptionPayload,
): Promise<SocialMarketingPost> {
  return cubeClient.postDirectPayLoad<Object, SocialMarketingPost>(
    `${AGENT_PROFILE_API}/post/${postId}/generate-caption`,
    payload,
  );
}

async function updatePostCaption(
  postId: string,
  payload: SocialMarketingUpdatePostCaptionPayload,
): Promise<SocialMarketingPost> {
  return cubeClient.postDirectPayLoad<
    SocialMarketingUpdatePostCaptionPayload,
    SocialMarketingPost
  >(`${AGENT_PROFILE_API}/post/${postId}/update-caption`, payload);
}

async function getAiAvatars(page: number = 0) {
  const response = await cubeClient.get<GetAiAvatarsResponse>(
    `${AGENT_PROFILE_API}/avatars`,
    {
      params: {
        page,
        mock: 1, // parameter for testing and demo purpose.
      },
    },
  );

  const content = await Promise.all(
    response.content.map(async item => {
      const localUrl = await fetchAndCacheImage(item.avatarUrl);

      return {
        ...item,
        localUrl,
      };
    }),
  );

  return {
    ...response,
    content,
  };
}

async function getMarketingActivityStatistics() {
  const response = await cubeClient.get<GetMarketingActivityStatictics>(
    `${AGENT_PROFILE_API}/marketing-activity/performance`,
  );

  return response;
}

async function getAgentPosts({
  page = 1,
}: GetAgentPostsListParams): Promise<PostPaginationResponse> {
  return cubeClient.get<PostPaginationResponse>(`${AGENT_PROFILE_API}/post`, {
    params: { page },
  });
}

async function getPostStatus(
  postId: string,
): Promise<SocialMarketingGeneratePostResponse> {
  return cubeClient.get<SocialMarketingGeneratePostResponse>(
    `${AGENT_PROFILE_API}/post/${postId}/generate/status`,
  );
}

async function getPostById(postId: string): Promise<SocialMarketingPost> {
  return cubeClient.get<SocialMarketingPost>(
    `${AGENT_PROFILE_API}/post/${postId}`,
  );
}

async function deletePost(postId: string): Promise<string> {
  return cubeClient.delete<string>(`${AGENT_PROFILE_API}/post/${postId}`);
}

export default {
  createPost,
  regeneratePost,
  getTemplates,
  generatePostCaption,
  updatePostCaption,
  getPostSessionStatus,
  generatePromptFromTopic,
  getAiAvatars,
  getMarketingActivityStatistics,
  deletePost,
  getAgentPosts,
  getPostStatus,
  getPostById,
};
