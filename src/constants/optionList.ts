import { BuildCountry } from 'types';
import { CountryCode, OptionList } from 'types/optionList';
import { PartyType } from 'types/party';
import { country } from 'utils/context';

export const CUSTOMER_TYPES = [
  { label: 'Individual', value: PartyType.INDIVIDUAL },
  { label: 'Entity', value: PartyType.ENTITY },
];

export const getCountryCodeValue = (item: CountryCode) => item.value;
export const getCountryCodeDisplayedLabel = (item: CountryCode) =>
  `+${item?.value.split(' - ')[0]}`;

export const MAIN_INSURED_ADDRESS_OPTION = 'mainInsured';
export const OWNER_ADDRESS_OPTION = 'owner';
export const CORRESPONDENCE_ADDRESS_OPTION = 'correspondence';
export const NEW_ADDRESS_OPTION = 'new';
export const SAME_AS_HOME_ADDRESS_OPTION = 'sameAsHomeAddress';

export const getOptionListLabel = (i: { label: string }) => i.label;
export const getOptionListValue = (i: { value: string }) => i.value;

// PH
export const PH_OPTION_LIST = {
  BUSINESS_ADDRESSES: [
    { label: 'New address', value: NEW_ADDRESS_OPTION },
    { label: 'Same as current address', value: CORRESPONDENCE_ADDRESS_OPTION },
  ],
  BENEFICIARY_DESIGNATIONS: [
    { value: 'Revocable', label: 'Revocable' },
    { value: 'Irrevocable', label: 'Irrevocable' },
  ],
  POLICY_DELIVERY_MODES: [{ label: 'Digital', value: 'Digital' }],
  RENEWAL_PAYMENT_METHODS: [
    'aca',
    'adda',
    'cash',
    'pos',
    'cheque',
    'NA',
    '',
  ] as const,
  INITIAL_PAYMENT_OPTION: {
    dragonPayBanking: 'DPB',
    dragonPayCards: 'DPC',
    otcBillsPayment: {
      securityBank: 'PSB',
      metroBank: 'PMB',
      bdo: 'BDO',
      bpi: 'BPI',
      lbc: 'PLE',
      unionBank: 'PUB',
      cebuanaLhuillierBranches: 'PCB',
    },
    gcash: 'GCH',
    onlineBanking: {
      securityBank: 'BSB',
      bdo: 'BDO',
      bpi: 'BPI',
      metroBankDirect: 'BMD',
      bancNetOnline: 'BBO',
      landBank: 'BLB',
    },
    creditCardOption: 'CCD',
    pos: 'FPM',
    paymaya: 'PAY',
  },
};

export const PH_DEFAULT_POLICY_DELIVERY_MODE =
  PH_OPTION_LIST.POLICY_DELIVERY_MODES[0].value;

export const PH_MOBILE_COUNTRY_CODE = '63';
export const PH_MOBILE_CODE = '63 - Philippines';
export const PH_COUNTRY = 'RP';

export const PH_ONLINE_PAYMENT = 'PW';
export const PH_OFFLINE_PAYMENT = 'BI';

// MYS
export const MY_MOBILE_CODE = '60 - Malaysia';
export const MY_COUNTRY = 'MYS';
export const MALAYSIAN = 'MYS';
export const INCOME_GREATER_THAN_200K = '08';
export const NEW_NRIC = 'nricNew';
export const PASSPORT = 'passport';
export const NON_INCOME_OCC_GROUP = '0006';
export const MY_ELECTRONIC_COPY = 'electronicCopy';
export const MY_ENGLISH_LANGUAGE = 'english';
export const MY_EMAIL_CONTACT_MODE = 'email';
export const MY_MOBILE_CONTACT_MODE = 'mobile';
export const MY_CHILD_RELATIONSHIP = 'Child';
export const MY_LEGAL_GUARDIANEE_RELATIONSHIP = 'LEGALGUARDIANEE';

// ID
export const ID_MOBILE_CODE = '62 - Indonesia';
export const ID_COUNTRY = 'RI';
export enum IDN_ID_TYPE {
  BirthCertificate = 'BC',
  KTP = 'ID',
  Passport = 'PP',
}
export enum ID_MARITAL_STATUS {
  Single = 'S',
  Married = 'M',
  Widowed = 'W',
}

export const MY_OPTION_LIST = {
  CORRESPONDENCE_ADDRESSES: [
    {
      value: OWNER_ADDRESS_OPTION,
      label: 'correspondenceAddress.certificate',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'correspondenceAddress.new',
    },
  ],
  IB_CORRESPONDENCE_ADDRESSES: [
    {
      value: OWNER_ADDRESS_OPTION,
      label: 'correspondenceAddress.same.po',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'correspondenceAddress.new',
    },
  ],
  RESIDENTIAL_ADDRESSES: [
    {
      value: CORRESPONDENCE_ADDRESS_OPTION,
      label: 'residentialAddress.correspondence',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'residentialAddress.new',
    },
  ],
  ID_RESIDENTIAL_ADDRESSES: [
    {
      value: CORRESPONDENCE_ADDRESS_OPTION,
      label: 'residentialAddress.correspondence',
    },
    { value: NEW_ADDRESS_OPTION, label: 'residentialAddress.new' },
  ],
  BUSINESS_ADDRESSES: [
    {
      value: NEW_ADDRESS_OPTION,
      label: 'residentialAddress.new',
    },
  ],
  IB_BUSINESS_ADDRESSES: [
    {
      value: CORRESPONDENCE_ADDRESS_OPTION,
      label: 'residentialAddress.correspondence',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'residentialAddress.new',
    },
  ],
  ID_BUSINESS_ADDRESSES: [
    {
      value: CORRESPONDENCE_ADDRESS_OPTION,
      label: 'residentialAddress.correspondence',
    },
    {
      value: SAME_AS_HOME_ADDRESS_OPTION,
      label: 'residentialAddress.sameAsHomeAddress',
    },
    { value: NEW_ADDRESS_OPTION, label: 'residentialAddress.new' },
  ],
  IB_ENTITY_BUSINESS_ADDRESSES: [
    {
      value: OWNER_ADDRESS_OPTION,
      label: 'certificate.residentialAddress.owner',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'residentialAddress.new',
    },
  ],
  ENTITY_CORRESPONDENCE_ADDRESSES: [
    {
      value: MAIN_INSURED_ADDRESS_OPTION,
      label: 'correspondenceAddress.mainInsured',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'correspondenceAddress.new',
    },
  ],
  ENTITY_RESIDENTIAL_ADDRESSES: [
    {
      value: CORRESPONDENCE_ADDRESS_OPTION,
      label: 'residentialAddress.correspondence',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'residentialAddress.new',
    },
  ],
  ENTITY_BUSINESS_ADDRESSES: [
    {
      value: NEW_ADDRESS_OPTION,
      label: 'residentialAddress.new',
    },
  ],
  PAYOR_CORRESPONDENCE_ADDRESSES: [
    {
      value: OWNER_ADDRESS_OPTION,
      label: 'correspondenceAddress.certificate',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'payor.correspondenceAddress.new',
    },
  ],
  PAYOR_RESIDENTIAL_ADDRESSES: [
    {
      value: CORRESPONDENCE_ADDRESS_OPTION,
      label: 'residentialAddress.correspondence',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'payor.residentialAddress.new',
    },
  ],
  PAYOR_BUSINESS_ADDRESSES: [
    {
      value: NEW_ADDRESS_OPTION,
      label: 'payor.residentialAddress.new',
    },
  ],
  ENTITY_PAYOR_ADDRESSES: [
    {
      value: NEW_ADDRESS_OPTION,
      label: 'payor.residentialAddress.new',
    },
  ],
  MAIN_INSURED_ENTITY_BUSINESS_ADDRESSES: [
    {
      value: OWNER_ADDRESS_OPTION,
      label: 'residentialAddress.owner',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'residentialAddress.new',
    },
  ],
  CHILD_BUSINESS_ADDRESSES: [
    {
      value: CORRESPONDENCE_ADDRESS_OPTION,
      label: 'residentialAddress.correspondence',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'residentialAddress.new',
    },
  ],
  PREF_COPY: [
    {
      value: MY_ELECTRONIC_COPY,
      label: 'electronicCopy',
    },
  ],
};

export const MY_MARITAL_STATUS: { [key: string]: string } = {
  U: 'leadProfile.maritalStatus.Unknown',
  S: 'leadProfile.maritalStatus.Single',
  M: 'leadProfile.maritalStatus.Married',
  A: 'leadProfile.maritalStatus.Annulled',
  D: 'leadProfile.maritalStatus.Divorced',
  X: 'leadProfile.maritalStatus.Separated',
  V: 'leadProfile.maritalStatus.Widower',
  W: 'leadProfile.maritalStatus.Widowed',
  C: 'leadProfile.maritalStatus.Company',
  Z: 'leadProfile.maritalStatus.Others',
};

export const IB_CAMPAIGN_CODE_VALUES = {
  JFW2024: 'JFW2024',
};
// IB
export const IB_CAMPAIGN_CODES = [
  {
    label: 'JFW2024',
    value: IB_CAMPAIGN_CODE_VALUES.JFW2024,
  },
];
export const SGP_COUNTRY = 'SGP';
export const LOW_INCOME_RANGES = ['01', '02', '03', '04'];
export const HIGH_INCOME_RANGES = ['05', '06', '07', '08'];

// https://fwdnextgen.atlassian.net/browse/CUBEFIB-4573
export const getOriginalCountryCode = (
  countryCode?: string,
  optionList?: OptionList,
) =>
  optionList?.COUNTRY_CODE.options.find(
    o => o.value.split(' - ')[0] === countryCode?.split(' - ')[0],
  )?.value;

export enum EntityRelationshipType {
  EMPLOYEE = 'EEE',
  EMPLOYEE_KEYMAN = 'EEEK',
  BUSINESS_PARTNER = 'BP',
}

export enum RENEWAL_PAYMENT_METHOD {
  AUTO_DEBIT = 'AUTODEBIT',
  CREDIT_CARD = 'CREDITCARD',
  VIRTUAL_ACCOUNT = 'VIRTUALACCOUNT',
  BANK_TRANSFER = 'BANKTRANSFER',
  NA = '',
}

export const ID_OPTION_LIST = {
  CORRESPONDENCE_ADDRESSES: [
    {
      value: OWNER_ADDRESS_OPTION,
      label: 'correspondenceAddress.same.po',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'correspondenceAddress.new',
    },
  ],
  RESIDENTIAL_ADDRESSES: [
    {
      value: CORRESPONDENCE_ADDRESS_OPTION,
      label: 'residentialAddress.correspondence',
    },
    { value: NEW_ADDRESS_OPTION, label: 'residentialAddress.new' },
  ],
  BUSINESS_ADDRESSES: [
    {
      value: CORRESPONDENCE_ADDRESS_OPTION,
      label: 'residentialAddress.correspondence',
    },
    {
      value: SAME_AS_HOME_ADDRESS_OPTION,
      label: 'residentialAddress.sameAsHomeAddress',
    },
    { value: NEW_ADDRESS_OPTION, label: 'residentialAddress.new' },
  ],
  ENTITY_BUSINESS_ADDRESSES: [
    {
      value: CORRESPONDENCE_ADDRESS_OPTION,
      label: 'certificate.residentialAddress.owner',
    },
    {
      value: NEW_ADDRESS_OPTION,
      label: 'residentialAddress.new',
    },
  ],
  COMMUNICATION_LANE: [
    {
      value: 'MAIN',
      label: 'communicationLane.main',
    },
    {
      value: 'HOME',
      label: 'communicationLane.home',
    },
    {
      value: 'WORK',
      label: 'communicationLane.work',
    },
  ],
  RENEWAL_PAYMENT_METHODS: [
    RENEWAL_PAYMENT_METHOD.CREDIT_CARD,
    RENEWAL_PAYMENT_METHOD.AUTO_DEBIT,
    RENEWAL_PAYMENT_METHOD.VIRTUAL_ACCOUNT,
    RENEWAL_PAYMENT_METHOD.BANK_TRANSFER,
  ],
};

export const mobileCodeConfig: Record<BuildCountry, string> = {
  ph: PH_MOBILE_COUNTRY_CODE,
  my: MY_MOBILE_CODE,
  ib: MY_MOBILE_CODE,
  id: ID_MOBILE_CODE,
};

export const mobileCodeByCountry = mobileCodeConfig[country];
