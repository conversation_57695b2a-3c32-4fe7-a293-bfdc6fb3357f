import { createNativeStackNavigator } from '@react-navigation/native-stack';
import useCheckIsLoggedIn from 'hooks/useCheckIsLoggedIn';
import React from 'react';
import { View } from 'react-native';
import { RootStackParamListMap } from 'types/navigation';
import { countryModuleSellerConfig } from 'utils/config/module';

import PolicyDetailScreen from 'features/policy/components/PolicyDetails';
import MainNavigator from 'navigation/MainNavigator/MainNavigator';
import AgentProfileScreen from 'screens/AgentProfileScreen/AgentProfileScreen';
import PersonalDetailsScreen from 'screens/AgentProfileScreen/PersonalDetailsScreen';
import SettingScreen from 'screens/AgentProfileScreen/SettingScreen';
import BirthdayCardScreen from 'screens/BirthdayCardScreen';
import CoverageDetailsScreen from 'screens/CoverageDetailsScreen';
import CustomerFactFindScreen from 'screens/CustomerFactFindScreen';
import EAppScreen from 'screens/EAppScreen';
import FnaScreen from 'screens/FnaScreen';
import LoginScreen from 'screens/LoginScreen';
import PdfViewerScreen from 'screens/PdfViewerScreen';
import ProductSelection from 'screens/ProductSelection';
import SalesIllustrationForm from 'screens/SalesIllustrationForm';
import { SimulationTableScreen } from 'screens/SimulationTableScreen';
import { BirthdayTasksScreen } from 'screens/TasksScreen';

import AddCandidateScreen from 'features/eRecruit/ib/phone/components/AddCandidate/AddCandidateScreen';
import CandidatesSearchScreen from 'features/eRecruit/ib/phone/components/CandidateSearch/CandidatesSearchScreen';
import SellerExpScreens from 'screens/SellerExperienceScreens';

import HealthQuestionsReview from 'features/eAppV2/common/components/review/healthQuestions/HealthQuestionsReview';
import PersonalInformationReview from 'features/eAppV2/common/components/review/personalInformationReview/PersonalInformationReview';
import ClosingAgentReview from 'features/eAppV2/id/components/reviewSummary/sections/consentAndDeclarationSummary/screens/ClosingAgentReview';
import FatcaReview from 'features/eAppV2/id/components/reviewSummary/sections/consentAndDeclarationSummary/screens/FatcaReview';
import PassionSurveyReview from 'features/eAppV2/id/components/reviewSummary/sections/consentAndDeclarationSummary/screens/PassionSurveyReview';
import StatementAndPowerOfAttorneyReview from 'features/eAppV2/id/components/reviewSummary/sections/consentAndDeclarationSummary/screens/StatementAndPowerOfAttorneyReview';
import StatementOfTruthReview from 'features/eAppV2/id/components/reviewSummary/sections/consentAndDeclarationSummary/screens/StatementOfTruthReview';
import TemporaryCoverageReview from 'features/eAppV2/id/components/reviewSummary/sections/consentAndDeclarationSummary/screens/TemporaryCoverageReview';
import UnderwritingDecisionReview from 'features/eAppV2/id/components/reviewSummary/sections/consentAndDeclarationSummary/screens/UnderwritingDecisionReview';
import Submission from 'features/eAppV2/id/components/submission/Submission';
import useIgniteStackScreens from 'features/socialMarketing/hooks/useIgniteStackScreens';
import ProtectionGoal from 'features/fna/components/goals/protection/ProtectionGoal';
import SavingsGoal from 'features/fna/components/goals/savings/SavingsGoal';
import { useHasPermission } from 'hooks/useCheckClientScope';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetTabsMobile } from 'navigation/components/TabsConfig';
import ImageListScreen from 'screens/ImageListScreen';
import NotificationScreen from 'screens/NotificationScreen';
import ProductRecommendationScreen from 'screens/ProductRecommendationScreen';
import RPQQuestionFormScreen from 'screens/RpqQuestionFormScreen';
import RpqResultScreen from 'screens/RpqResultScreen';
import { country } from 'utils/context';

const Stack = createNativeStackNavigator<RootStackParamListMap['id']>();

export default function IDStackNavigator() {
  const { isLoggedIn } = useCheckIsLoggedIn();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const hasPermission = useHasPermission();

  const igniteStackScreens = useIgniteStackScreens<'id'>(Stack);
  const otherMenuTabsMobile = useGetTabsMobile().otherMenuTabs.filter(item => {
    if (!item.feature) {
      return true;
    }
    return hasPermission(item.feature);
  });

  return (
    <View style={{ flex: 1 }}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          animation: 'slide_from_right',
          headerTitleAlign: 'center',
        }}>
        {isLoggedIn ? (
          <>
            <Stack.Screen name="Main">{() => <MainNavigator />}</Stack.Screen>
            <Stack.Screen
              name="NotificationScreen"
              component={NotificationScreen}
              options={{
                headerShown: false,
              }}
            />
            <Stack.Group>
              <Stack.Screen
                name="AgentProfile"
                component={AgentProfileScreen}
              />
              <Stack.Screen
                name="PersonalDetails"
                component={PersonalDetailsScreen}
              />
              <Stack.Screen name="Setting" component={SettingScreen} />
            </Stack.Group>

            {/* // SI Screens */}
            <Stack.Group>
              <Stack.Screen
                name="SalesIllustrationForm"
                component={SalesIllustrationForm}
              />
              <Stack.Screen
                name="RPQQuestionForm"
                component={RPQQuestionFormScreen}
              />
              <Stack.Screen name="RPQResult" component={RpqResultScreen} />
              <Stack.Screen
                name="CoverageDetailsScreen"
                component={CoverageDetailsScreen}
              />
              <Stack.Screen
                name="ProductSelection"
                component={ProductSelection}
              />

              <Stack.Screen
                name="SimulationTable"
                component={SimulationTableScreen}
                options={{
                  animation: 'fade',
                  orientation: isTabletMode ? 'landscape' : 'landscape_left',
                }}
              />
            </Stack.Group>

            <Stack.Screen
              name="PdfViewer"
              component={PdfViewerScreen}
              options={{ animation: 'slide_from_bottom' }}
            />

            <Stack.Screen name="ImageList" component={ImageListScreen} />

            {/* // App Review Screens */}
            <Stack.Group screenOptions={{ headerShown: false }}>
              <Stack.Screen name="FatcaReview" component={FatcaReview} />
              <Stack.Screen
                name="PassionSurveyReview"
                component={PassionSurveyReview}
              />
              <Stack.Screen
                name="UnderwritingDecisionReview"
                component={UnderwritingDecisionReview}
              />
              <Stack.Screen
                name="ClosingAgentReview"
                component={ClosingAgentReview}
              />
              <Stack.Screen
                name="StatementAndPowerOfAttorneyReview"
                component={StatementAndPowerOfAttorneyReview}
              />
              <Stack.Screen
                name="StatementOfTruthReview"
                component={StatementOfTruthReview}
              />
              <Stack.Screen
                name="TemporaryCoverageReview"
                component={TemporaryCoverageReview}
              />

              <Stack.Screen
                name="PersonalInformationReview"
                component={PersonalInformationReview}
              />

              <Stack.Screen
                name="HealthQuestionsReview"
                component={HealthQuestionsReview}
              />
            </Stack.Group>

            {/* FWD News */}
            <Stack.Group>
              <Stack.Screen
                name="FWDNews"
                component={SellerExpScreens.FWDNewsScreen}
                options={{
                  headerShown: false,
                  animation: 'fade',
                }}
              />
              <Stack.Screen
                name="FWDNewsDetails"
                component={SellerExpScreens.FWDNewsDetailsScreen}
                options={{
                  headerShown: false,
                  animation: 'fade',
                }}
              />
              <Stack.Screen
                name="FWDNewsBookmarks"
                component={SellerExpScreens.FWDNewsBookmarkScreen}
                options={{
                  headerShown: false,
                  animation: 'fade',
                }}
              />
            </Stack.Group>

            {/* // Task Screens */}
            <Stack.Group>
              <Stack.Screen
                name="BirthdayTasksScreen"
                component={BirthdayTasksScreen}
                options={{
                  headerShown: false,
                }}
              />
            </Stack.Group>

            {/* // Birthday Card Screens */}
            <Stack.Group>
              <Stack.Screen
                name="BirthdayCardScreen"
                component={BirthdayCardScreen}
                options={{
                  headerShown: false,
                  animation: isTabletMode
                    ? 'slide_from_right'
                    : 'slide_from_bottom',
                }}
              />
            </Stack.Group>

            {/* // Lead Screens */}
            <Stack.Group>
              <Stack.Screen
                name="LeadProfile"
                component={SellerExpScreens.LeadProfileScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="ProfileDetails"
                component={SellerExpScreens.LeadProfileDetailsScreen}
                options={{
                  animation: country === 'id' ? 'slide_from_right' : 'fade',
                }}
              />
              <Stack.Screen
                name="CustomerProfileDetails"
                component={SellerExpScreens.CustomerProfileDetailsScreen}
              />
              <Stack.Screen
                name="AddNewLeadOrEntity"
                component={SellerExpScreens.AddNewLeadOrEntityScreen}
                options={{
                  animation: 'slide_from_bottom',
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="ContactBook"
                component={SellerExpScreens.ContactBookScreen}
                options={{
                  animation: 'fade',
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="LeadAndCustomerSearch"
                component={SellerExpScreens.LeadAndCustomerSearch}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="LogActivity"
                component={SellerExpScreens.LogActivityScreen}
                options={{
                  animation: 'fade',
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="ExistingPolicyDetail"
                component={PolicyDetailScreen}
              />
            </Stack.Group>

            {/* // Policies */}
            <Stack.Group>
              <Stack.Screen
                name="PoliciesNewBusiness"
                component={SellerExpScreens.PoliciesNewBusinessNavigator}
              />
            </Stack.Group>

            {/* // Pos */}
            <Stack.Group>
              <Stack.Screen
                name="PoliciesPOS"
                component={SellerExpScreens.POSNavigator}
              />
            </Stack.Group>
            <Stack.Screen name="EApp" component={EAppScreen} />

            {/* // Report Screens */}
            <Stack.Group>
              <Stack.Screen
                name="CustomerFactFind"
                component={CustomerFactFindScreen}
              />
            </Stack.Group>

            {/* // FNA */}
            <Stack.Group>
              <Stack.Screen
                name="Fna"
                component={FnaScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="SavingsGoal"
                component={SavingsGoal}
                options={{
                  headerShown: false,
                  animation: 'slide_from_right',
                }}
              />
              <Stack.Screen
                name="ProtectionGoal"
                component={ProtectionGoal}
                options={{
                  headerShown: false,
                  animation: 'slide_from_right',
                }}
              />

              <Stack.Screen
                name="ProductRecommendation"
                component={ProductRecommendationScreen}
                options={{
                  headerShown: false,
                }}
              />
            </Stack.Group>

            {/* // * ERecruit */}
            {countryModuleSellerConfig.ERecruit && (
              <>
                <Stack.Screen
                  name="ERecruitApplication"
                  component={SellerExpScreens.ERecruitAppScreen}
                />
                <Stack.Screen
                  name="ERecruitApplicationStatus"
                  component={SellerExpScreens.ERecruitAppStatusScreen}
                />
                <Stack.Screen
                  name="ERecruitCandidateProfile"
                  component={SellerExpScreens.ERecruitCandidateProfileScreen}
                />
                <Stack.Screen
                  name="ERecruitCheckApplication"
                  component={SellerExpScreens.ERecruitCheckAppScreen}
                />
                <Stack.Screen
                  name="CandidatesSearch"
                  component={CandidatesSearchScreen}
                />
                <Stack.Screen
                  name="AddCandidate"
                  component={AddCandidateScreen}
                  options={{
                    animation: 'fade',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ERecruitReviewAgentsSubmission"
                  component={
                    SellerExpScreens.ERecruitReviewAgentsSubmissionScreen
                  }
                />
                <Stack.Screen
                  name="ERecruitReviewAgentsApplication"
                  component={
                    SellerExpScreens.ERecruitReviewAgentsApplicationScreen
                  }
                />
                <Stack.Screen
                  name="ERecruitSignature"
                  component={SellerExpScreens.ERecruitSignatureScreen}
                />
                <Stack.Screen
                  name="ERecruitRemoteSignature"
                  component={SellerExpScreens.ERecruitRemoteSignatureScreen}
                />

                <Stack.Screen
                  name="ERecruitCheckApplicationRemoteSignature"
                  component={
                    SellerExpScreens.ERecruitCheckApplicationRemoteSignatureScreen
                  }
                />
                <Stack.Screen
                  name="SellerExpImageList"
                  component={SellerExpScreens.ImageListScreen}
                />
                <Stack.Screen
                  name="Materials"
                  component={SellerExpScreens.ERecruitMaterialsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="MaterialDetails"
                  component={SellerExpScreens.ERecruitMaterialDetailsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </>
            )}

            {/* // Performance Screens */}
            <Stack.Group>
              <Stack.Screen
                name="RecognitionDetails"
                component={SellerExpScreens.RecognitionDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="PerformanceDetails"
                component={SellerExpScreens.PerformanceDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="PerformanceTarget"
                component={SellerExpScreens.PerformanceTargetScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="CampaignsDetails"
                component={SellerExpScreens.CampaignsDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="SummitClubsDetails"
                component={SellerExpScreens.SummitClubsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="EliteAgencyDetails"
                component={SellerExpScreens.EliteAgencyDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="EliteAgencyRequirements"
                component={SellerExpScreens.EliteAgencyRequirementsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="EliteAgencyBenefits"
                component={SellerExpScreens.EliteAgencyBenefitsScreen}
                options={{
                  headerShown: false,
                }}
              />
            </Stack.Group>

            {/* // Group Payment Screen */}
            <Stack.Group>
              <Stack.Screen name="Submission" component={Submission} />
            </Stack.Group>

            {/* Ignite feature screen group */}
            {igniteStackScreens}

            {otherMenuTabsMobile
              .filter(menuItem => menuItem.navigationType === 'stack')
              .map(({ name, component }) => (
                <Stack.Screen
                  key={'otherMenuTabsMobile_' + name}
                  name={name}
                  component={component}
                />
              ))}
          </>
        ) : (
          <Stack.Screen
            name="Login"
            component={LoginScreen}
            options={{
              animationTypeForReplace: !isLoggedIn ? 'pop' : 'push',
            }}
          />
        )}
      </Stack.Navigator>
    </View>
  );
}
