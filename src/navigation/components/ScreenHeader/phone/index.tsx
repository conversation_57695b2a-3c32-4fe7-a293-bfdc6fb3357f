import {
  SafeAreaView,
  StyleProp,
  TextStyle,
  TouchableOpacityProps,
  View,
} from 'react-native';
import React from 'react';
import { Theme, useTheme } from '@emotion/react';
import { Icon, Typography } from 'cube-ui-components';
import { MainTabParamList, RootStackParamList } from 'types';
import styled from '@emotion/native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { HIT_SLOP_SPACE } from 'constants/hitSlop';
import {
  headerDefaultTitleMap,
  AllRouteTitleKeys,
} from 'navigation/components/ScreenHeader/constant';
import { TouchableOpacity } from 'react-native';

const HIT_SLOP_SPACE_ONE = HIT_SLOP_SPACE(1);

type ScreenHeaderProps = {
  route: AllRouteTitleKeys;
  customTitle?: string;
  leftChildren?: JSX.Element;
  rightChildren?: JSX.Element;
  showBottomSeparator?: boolean;
  customBackgroundColor?: string;
  isLeftCrossBackShown?: boolean;
  isLeftArrowBackShown?: boolean;
  customTitleStyle?: StyleProp<TextStyle>;
  onPressDefaultLeftButton?: () => void;
};

export default function ScreenHeader({
  leftChildren,
  rightChildren,
  route,
  showBottomSeparator,
  customTitle,
  customBackgroundColor,
  isLeftCrossBackShown,
  isLeftArrowBackShown,
  customTitleStyle,
  onPressDefaultLeftButton,
}: ScreenHeaderProps) {
  const theme = useTheme();
  const { sizes } = theme;
  const { top } = useSafeAreaInsets();
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();

  const titleLabelHandler = (): string => {
    if (customTitle) {
      return customTitle;
    }
    if (
      route in headerDefaultTitleMap &&
      headerDefaultTitleMap[route].length > 0
    ) {
      return headerDefaultTitleMap[route];
    }
    return '';
  };

  const paddingTopForWideScreen = isWideScreen ? sizes[3] : undefined;

  return (
    <SafeAreaView
      style={{
        backgroundColor: customBackgroundColor ?? theme.colors.background,
        paddingTop: top,
      }}>
      <HeaderStyledView
        isWideScreen={isWideScreen}
        showBottomSeparator={showBottomSeparator}
        paddingTopForWideScreen={paddingTopForWideScreen}>
        <View
          style={{
            position: 'absolute',
            left: sizes[isNarrowScreen ? 3 : 4],
            top: paddingTopForWideScreen,
          }}>
          {leftChildren ? (
            leftChildren
          ) : (
            <DefaultLeftButton
              onPressDefaultLeftButton={onPressDefaultLeftButton}
              isLeftCrossBackShown={isLeftCrossBackShown}
              isLeftArrowBackShown={isLeftArrowBackShown}
            />
          )}
        </View>
        {isWideScreen ? (
          <Typography.H6
            fontWeight="bold"
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[{ maxWidth: '60%' }, customTitleStyle]}>
            {titleLabelHandler()}
          </Typography.H6>
        ) : (
          <Typography.H7
            fontWeight="bold"
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[{ maxWidth: '80%' }, customTitleStyle]}>
            {titleLabelHandler()}
          </Typography.H7>
        )}
        <View
          style={{
            position: 'absolute',
            right: sizes[4],
            top: paddingTopForWideScreen,
          }}>
          {rightChildren}
        </View>
      </HeaderStyledView>
    </SafeAreaView>
  );
}

type HeaderStyledViewProps = {
  paddingTopForWideScreen: number | undefined;
  isWideScreen: boolean;
  showBottomSeparator?: boolean;
  theme?: Theme;
};

const HeaderStyledView = styled.View(
  ({
    showBottomSeparator = true,
    theme,
    isWideScreen,
    paddingTopForWideScreen,
  }: HeaderStyledViewProps) => ({
    height: theme?.sizes[11],
    width: '100%',
    alignItems: 'center',
    borderBottomColor: theme?.colors.surface,
    justifyContent: isWideScreen ? undefined : 'center',
    borderBottomWidth: showBottomSeparator ? 1 : 0,
    paddingTop: paddingTopForWideScreen,
    paddingBottom: isWideScreen ? theme?.sizes[2] : undefined,
  }),
);

const DefaultLeftButton = ({
  isLeftCrossBackShown,
  isLeftArrowBackShown,
  onPressDefaultLeftButton,
}: {
  isLeftCrossBackShown: boolean | undefined;
  isLeftArrowBackShown: boolean | undefined;
  onPressDefaultLeftButton?: () => void;
}) => {
  const { sizes, colors } = useTheme();

  if (isLeftCrossBackShown) {
    return (
      <GoBackButton onPress={onPressDefaultLeftButton}>
        <Icon.Close size={sizes[6]} fill={colors.secondary} />
      </GoBackButton>
    );
  }
  if (isLeftArrowBackShown) {
    return (
      <GoBackButton onPress={onPressDefaultLeftButton}>
        <Icon.ArrowLeft size={sizes[6]} fill={colors.secondary} />
      </GoBackButton>
    );
  }
  return <></>;
};

const GoBackButton = (props: TouchableOpacityProps) => {
  const { colors, sizes } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { onPress, children, ...rest } = props;
  return (
    <TouchableOpacity
      hitSlop={HIT_SLOP_SPACE_ONE}
      style={{ marginRight: sizes[9] }}
      onPress={e => {
        onPress
          ? onPress(e)
          : navigation.canGoBack()
          ? navigation.goBack()
          : console.log('~~~~~cannot go back');
      }}
      {...rest}>
      {/* <Icon.Close size={sizes[5]} fill={colors.secondary} /> */}
      {children}
    </TouchableOpacity>
  );
};
