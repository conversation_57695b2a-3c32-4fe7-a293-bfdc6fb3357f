trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - package.json
      - eas.json

variables:
  - group: CUBE_PH_DEV
  - name: androidTarget
    value: '$(DEVELOP_ANDROID)'
  - name: iosTarget
    value: '$(DEVELOP_IOS)'
  - name: iOSDistCert
    value: 'FWD_iOS_Enterprise_Certificate_2023.p12'
  - name: iOSProvCert
    value: 'DEVphcomfwdcubedev.mobileprovision'
  - name: Build_Env
    value: 'phDevClient'

pool:
  vmImage: 'macOS-15'

stages:
  - stage: Prepare
    displayName: Prepare
    # condition: false
    jobs:
      - job: Prepare
        displayName: Prepare
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
          - task: UseNode@1
            inputs:
              version: '22.13.1'
          - script: |
              git checkout $(Build.SourceBranchName)
              commitId=$(git rev-parse HEAD)
              echo "##vso[task.setvariable variable=Commit_ID;isOutput=true]$commitId"
              echo "##vso[task.setvariable variable=Commit_Message;isOutput=true]$(Build.SourceVersionMessage)"
            displayName: 'Get commit info'
            name: 'GetVersion'

  # ========================== Android build task: START =============================
  - stage: BuildAPK
    displayName: Build APK
    dependsOn:
      - Prepare
    jobs:
      - job: BuildAPK
        timeoutInMinutes: 0
        displayName: Build APK
        variables:
          - name: Commit_ID
            value: $[ stageDependencies.Prepare.Prepare.outputs['GetVersion.Commit_ID'] ]
          - name: Commit_Message
            value: $[ stageDependencies.Prepare.Prepare.outputs['GetVersion.Commit_Message'] ]

        steps:
          - task: UseNode@1
            inputs:
              version: '22.13.1'

          - task: DownloadSecureFile@1
            name: npmrc
            displayName: 'Download credentials'
            inputs:
              secureFile: 'cubeui_20250802.npmrc'
              retryCount: '3'
          - bash: |
              echo Copying $(npmrc.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(npmrc.secureFilePath) $(System.DefaultWorkingDirectory)/.npmrc
            displayName: 'Copy npmrc'
          - script: |
              yarn cache clean
              npm install -g eas-cli
              yarn install --network-timeout 500000
            retryCountOnTaskFailure: 5

          - script: |
              echo "Verify project info ✅"
              npx expo-env-info
            retryCountOnTaskFailure: 5
            displayName: 'Project Info'
          - script: |
              echo Update build Number
              sed -i .bak -e "s/versionCode: 1/versionCode: $(Build.BuildId)/g" app.config.js
            displayName: 'Set Build number'

          - task: JavaToolInstaller@0
            displayName: 'Set Java 17'
            inputs:
              versionSpec: '17'
              jdkArchitectureOption: 'x64'
              jdkSourceOption: 'PreInstalled'
          - task: DownloadSecureFile@1
            name: yarnrc
            displayName: 'Download yarnrc.yml'
            inputs:
              secureFile: 'yarnrc_20250802.yml'
              retryCount: '3'
          - bash: |
              echo Copying $(yarnrc.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(yarnrc.secureFilePath) $(System.DefaultWorkingDirectory)/yarnrc.yml
            displayName: 'Verify yarnrc.yml'
          - task: DownloadSecureFile@1
            name: credentials
            displayName: 'Download credentials'
            inputs:
              secureFile: 'credentials_newP12.json'
              retryCount: '3'
          - bash: |
              echo Copying $(credentials.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(credentials.secureFilePath) $(System.DefaultWorkingDirectory)/credentials.json
            displayName: 'Verify credentials.json'

          - task: DownloadSecureFile@1
            name: androidKeystore
            displayName: 'Downlaod keystore file'
            inputs:
              secureFile: 'my-upload-key.keystore'
              retryCount: '3'
          - bash: |
              echo Copying $(androidKeystore.secureFilePath) to $(System.DefaultWorkingDirectory)/certs/
              cp $(androidKeystore.secureFilePath) $(System.DefaultWorkingDirectory)/certs/my-upload-key.keystore
              echo "Verify certs folder ✅"
              ls -al $(System.DefaultWorkingDirectory)/certs/
            displayName: 'Verify certs'

          - bash: |
              node -v
              echo "SET TOKEN"
              export EXPO_TOKEN=$(EXPO_TOKEN)
              rm $(Build.SourcesDirectory)/.npmrc
              echo Build.SourcesDirectory = $(Build.SourcesDirectory)
              ls -l $(Build.SourcesDirectory)

              echo Build_Env = $(Build_Env) 
              echo "⏰⏰⏰ Start building standalone APK"
              npx eas-cli build --local --platform android --profile $(Build_Env) --no-wait --non-interactive

              echo "🥱🥱🥱 Check APK exist❓❓❓"
              export APK_FILE=$(ls *.apk)
              if [ ! -f "$APK_FILE" ];
              then
                echo "❌❌❌ APK file not found 💔💔💔"
                echo "##vso[task.setvariable variable=APK_Build]false"
                exit 1
              else
                echo "✅✅✅ APK File exists 👍👍👍"
                echo "##vso[task.setvariable variable=androidArtifact]$APK_FILE"
                echo "##vso[task.setvariable variable=APK_Build]true"
                echo "File name to publish Android"
                echo $APK_FILE
              fi
            displayName: 'Build APK'
          - task: PublishBuildArtifacts@1
            condition: eq(variables.APK_Build, 'true')
            displayName: 'Publish APK to artifacts'
            inputs:
              PathtoPublish: '$(androidArtifact)'
              ArtifactName: 'android'
              publishLocation: 'Container'

          # - task: AppCenterDistribute@3
          #   inputs:
          #     serverEndpoint: 'App Center'
          #     appSlug: '$(APK_SLUG)'
          #     appFile: '$(androidArtifact)'
          #     symbolsOption: 'Android'
          #     releaseNotesOption: 'input'
          #     releaseNotesInput: |+
          #       Android APK Distribution
          #       ---

          #       - **Build Number**      : $(Build.BuildId)
          #       - **Source Branch**     : $(Build.sourceBranch)
          #       - **Commit SHA**        : $(Commit_ID)
          #       - **Release Notes**     : $(Commit_Message)
          #     destinationType: 'groups'
          #     distributionGroupId: '82e3d86e-3e26-42a8-8ea5-d81bf057140e'
          #     isSlient: true

          - task: Bash@3
            condition: eq(variables.APK_Build, 'true')
            inputs:
              targetType: 'filePath'
              filePath: scripts/appcircle.sh
              failOnStderr: false
              arguments: '"$(androidArtifact)" "$(APPCIRCLE_PAT)" "$(APPCIRCLE)" $(Build.BuildId) $(Commit_ID)'
            displayName: 'Upload appcircle'

  # ========================== Android build task: END ===========================

  # ========================== iOS build task: START =============================
  - stage: BuildIPA
    displayName: Build IPA
    dependsOn:
      - Prepare
    # condition: false
    jobs:
      - job: BuildIPA
        timeoutInMinutes: 0
        displayName: Build IPA
        variables:
          - name: Commit_ID
            value: $[ stageDependencies.Prepare.Prepare.outputs['GetVersion.Commit_ID'] ]
          - name: Commit_Message
            value: $[ stageDependencies.Prepare.Prepare.outputs['GetVersion.Commit_Message'] ]

        steps:
          - task: UseNode@1
            inputs:
              version: '22.13.1'
          - task: DownloadSecureFile@1
            name: npmrc
            displayName: 'Downlaod credentials'
            inputs:
              secureFile: 'cubeui_20250802.npmrc'
              retryCount: '3'
          - bash: |
              echo Copying $(npmrc.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(npmrc.secureFilePath) $(System.DefaultWorkingDirectory)/.npmrc
            displayName: 'Copy npmrc'
          - script: |
              yarn cache clean
              npm install -g eas-cli
              yarn install --network-timeout 500000
            retryCountOnTaskFailure: 5

          - script: |
              echo "Verify project info ✅"
              npx expo-env-info
            retryCountOnTaskFailure: 5
            displayName: 'Project Info'
          - script: |
              echo Update build Number
              sed -i .bak -e "s/buildNumber: '1'/buildNumber: '$(Build.BuildId)'/g" app.config.js
            displayName: 'Set Build number'

          - task: DownloadSecureFile@1
            name: yarnrc
            displayName: 'Downlaod yarnrc.yml'
            inputs:
              secureFile: 'yarnrc_20250802.yml'
              retryCount: '3'
          - bash: |
              echo Copying $(yarnrc.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(yarnrc.secureFilePath) $(System.DefaultWorkingDirectory)/yarnrc.yml
            displayName: 'Verify yarnrc.yml'

          - task: DownloadSecureFile@1
            name: credentials
            displayName: 'Downlaod credentials'
            inputs:
              secureFile: 'credentials_newP12.json'
              retryCount: '3'
          - bash: |
              echo Copying $(credentials.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(credentials.secureFilePath) $(System.DefaultWorkingDirectory)/credentials.json
            displayName: 'Verify credentials.json'
          - task: DownloadSecureFile@1
            name: p12Cert
            displayName: 'Download P12 cert'
            inputs:
              secureFile: '$(iOSDistCert)'
              retryCount: '3'
          - task: DownloadSecureFile@1
            name: iOSProvCert
            displayName: 'Get mobileprovision cert'
            inputs:
              secureFile: '$(iOSProvCert)'
              retryCount: '3'
          - bash: |
              echo Copying DistCert and mobile provision to $(System.DefaultWorkingDirectory)/certs/
              cp $(p12Cert.secureFilePath) $(System.DefaultWorkingDirectory)/certs/DistCert.p12
              cp $(iOSProvCert.secureFilePath) $(System.DefaultWorkingDirectory)/certs/profile.mobileprovision
              echo "Verify certs folder ✅"
              ls -al $(System.DefaultWorkingDirectory)/certs/
            displayName: 'Verify certs'
          - bash: |
              node -v
              echo "SET TOKEN"
              export EXPO_TOKEN=$(EXPO_TOKEN)
              rm $(Build.SourcesDirectory)/.npmrc
              echo Build.SourcesDirectory = $(Build.SourcesDirectory)
              ls -l $(Build.SourcesDirectory)

              echo Build_Env = $(Build_Env) 
              echo "⏰⏰⏰ Start building dev client IPA for simulator"
              npx eas-cli build --local --platform ios --profile $(Build_Env) --no-wait --non-interactive

              echo "🥱🥱🥱 Check Tar.gz exist❓❓❓"
              export TAR_GZ_FILE=$(ls *.tar.gz)
              if [ ! -f "$TAR_GZ_FILE" ];
              then
                echo "❌❌❌ Tar.gz file not found 💔💔💔"
                echo "##vso[task.setvariable variable=TAR_GZ_FILE]false"
                exit 1
              else
                echo "✅✅✅ TAR_GZ_FILE File exists 👍👍👍"
                echo "##vso[task.setvariable variable=simulatorArtifact]$TAR_GZ_FILE"
                echo "##vso[task.setvariable variable=SIMULATOR_Build]true"
                echo "File name to upload to artifacts iOS"
                echo $TAR_GZ_FILE
              fi

              sed -i .bak -e "s/\"simulator\": true/\"simulator\": false/g" eas.json

              echo "⏰⏰⏰ Start building dev client IPA for physical device"

              npx eas-cli build --local --platform ios --profile $(Build_Env) --no-wait --non-interactive

              echo "🥱🥱🥱 Check IPA exist❓❓❓"
              export IPA_FILE=$(ls *.ipa)
              if [ ! -f "$IPA_FILE" ];
              then
                echo "❌❌❌ Tar.gz file not found 💔💔💔"
                echo "##vso[task.setvariable variable=IPA_FILE]false"
                exit 1
              else
                echo "✅✅✅ IPA_FILE File exists 👍👍👍"
                echo "##vso[task.setvariable variable=iosArtifact]$IPA_FILE"
                echo "##vso[task.setvariable variable=IPA_Build]true"
                echo "File name to publish to iOS app center"
                echo $IPA_FILE
              fi
            displayName: 'Build IPA'
          - task: PublishBuildArtifacts@1
            condition: eq(variables.SIMULATOR_Build, 'true')
            displayName: 'Publish IPA to artifacts'
            inputs:
              PathtoPublish: '$(simulatorArtifact)'
              ArtifactName: 'ios'
              publishLocation: 'Container'

          # - task: AppCenterDistribute@3
          #   inputs:
          #     serverEndpoint: 'App Center'
          #     appSlug: '$(IPA_SLUG)'
          #     appFile: '$(iosArtifact)'
          #     releaseNotesOption: 'input'
          #     releaseNotesInput: |+
          #       iOS IPA Distribution
          #       ---

          #       - **Build Number**      : $(Build.BuildId)
          #       - **Source Branch**     : $(Build.sourceBranch)
          #       - **Commit SHA**        : $(Commit_ID)
          #       - **Release Notes**     : $(Commit_Message)
          #     destinationType: 'groups'
          #     distributionGroupId: 'c8aa81d2-5928-42f6-bc4e-d0c3bbf3b568'
          #     isSlient: true

          - task: Bash@3
            condition: eq(variables.IPA_Build, 'true')
            inputs:
              targetType: 'filePath'
              filePath: scripts/appcircle.sh
              failOnStderr: false
              arguments: '"$(iosArtifact)" "$(APPCIRCLE_PAT)" "$(APPCIRCLE)" $(Build.BuildId) $(Commit_ID)'
            displayName: 'Upload appcircle'
  # ============================ iOS build task: END =============================
